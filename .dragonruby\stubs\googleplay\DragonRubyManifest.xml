<?xml version="1.0" encoding="utf-8"?>
<!--
Do not edit this file!

If you want to add stuff to your app's Android manifest, if the file
 metadata/googleplay-manifest-additions.xml exists in your game, its contents
 will be appended to this file to replace the $DRGTK_MANIFEST_ADDITIONS
 string. The settings in here are common to every game, and are mostly things
 that the engine itself needs to be set in order to properly function.

If you want to completely replace this file: if the file
 metadata/googleplay-manifest.xml exists in your game it will be used in place
 of this file.

In either case, don't do it if you don't know what you're doing, and do so
 AT YOUR OWN RISK!
-->
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    android:versionCode="$DRGTK_VERSION_CODE"
    android:versionName="$DRGTK_VERSION"
    android:installLocation="auto"
    android:compileSdkVersion="35"
    android:compileSdkVersionCodename="15"
    package="$DRGTK_PACKAGEID"
    platformBuildVersionCode="35"
    platformBuildVersionName="15">

    <!-- This will increase later as necessary. -->
    <uses-sdk android:minSdkVersion="26" android:targetSdkVersion="35" />

    <!-- OpenGL ES 2.0 -->
    <uses-feature android:glEsVersion="0x00020000" />

    <!-- Touchscreen support -->
    <uses-feature
        android:name="android.hardware.touchscreen"
        android:required="false" />

    <!-- Game controller support -->
    <uses-feature
        android:name="android.hardware.bluetooth"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.gamepad"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.usb.host"
        android:required="false" />

    <!-- External mouse input events -->
    <uses-feature
        android:name="android.hardware.type.pc"
        android:required="false" />

    <!-- Audio recording support -->
    <!-- if you want to capture audio, uncomment this. -->
    <!-- <uses-feature
        android:name="android.hardware.microphone"
        android:required="false" /> -->

    <!-- Allow writing to external storage -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <!-- Allow access to Bluetooth devices -->
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <!-- Allow access to the vibrator -->
    <uses-permission android:name="android.permission.VIBRATE" />
    <!-- Allow access to sockets -->
    <uses-permission android:name="android.permission.INTERNET" />

    <!-- if you want to capture audio, uncomment this. -->
    <!-- <uses-permission android:name="android.permission.RECORD_AUDIO" /> -->

    <!-- Create a Java class extending SDLActivity and place it in a
         directory under app/src/main/java matching the package, e.g. app/src/main/java/com/gamemaker/game/MyGame.java

         then replace "gtkActivity" with the name of your class (e.g. "MyGame")
         in the XML below.

         An example Java class can be found in README-android.md
    -->

    <application
        android:theme="@android:style/Theme.Black.NoTitleBar.Fullscreen"
        android:label="@string/app_name"
        android:icon="@mipmap/ic_launcher"
        android:allowBackup="true"
        android:hardwareAccelerated="true"
        android:usesCleartextTraffic="$DRGTK_USES_CLEAR_TEXT_TRAFFIC">

        <!-- Example of setting SDL hints from AndroidManifest.xml:
        <meta-data android:name="SDL_ENV.SDL_ACCELEROMETER_AS_JOYSTICK" android:value="0"/>
         -->

        <activity
            android:label="@string/app_name"
            android:name="$DRGTK_PACKAGEID.DragonRubyActivity"
	    android:exported="true"
            android:launchMode="singleInstance"
            android:configChanges="layoutDirection|locale|orientation|uiMode|screenLayout|screenSize|smallestScreenSize|keyboard|keyboardHidden|navigation"
            android:alwaysRetainTaskState="true">

            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <!-- Drop file event -->
            <!--
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="*/*" />
            </intent-filter>
            -->
        </activity>
    </application>

    <!-- dragonruby-publish will replace this string with the contents of your
         game's metadata/googleplay-manifest-additions.xml file, if it exists. -->

$DRGTK_MANIFEST_ADDITIONS

</manifest>

<!-- end of DragonRubyManifest.xml ... -->
