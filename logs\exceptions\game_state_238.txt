# * EXCEPTION POSSIBLY CAUSED BY CALLING ~gtk.reset~: maybe use ~$gtk.reset_next_tick~ instead.
# An exception occurred soon after ~$gtk.reset~ was invoked.
# 
# Keep in mind that ~$gtk.reset~ doesn't stop the rest of your ~tick~ method from being invoked.
# 
# You may have attempted to interact with properties on ~args.state~ that no longer have a value.
# 
# There are a few ways to fix exceptions that fall into this category:
# 
# 1. Wherever you are using ~$gtk.reset~ within ~tick~, replace
# with ~$gtk.reset_next_tick~ (this function will tell DragonRuby to
# reset on the next frame before invoking your ~tick~ method).
# 2. Anywhere you are using ~$gtk.reset~, immediately set default values for
# properties on ~args.state~ that are needed to complete the execution
# of the rest of your ~tick~ function successfully.
# 3. Do not use ~$gtk.reset~ and instead create a function that reinitializes your game
# and ensures it has a consistent/valid state.
# 
# Take a look at the following sample apps for insights on how to do this:
# - =./samples/99_genre_boss_battle/boss_battle_game_jam/app/main.rb=
# - =./samples/99_genre_platformer/shadows/app/main.rb=
# 
# ** NOTE:
# The options above are ordered by quickest to implement (but probably not
# bulletproof), to more work to implement (but a better design long term).
# 
# * EXCEPTION THROWN:
# ** Failed to load/reload app/main.rb.
# line 61:9: syntax error, unexpected end of file, expecting keyword_end
# 
# 
# 
# ** Backtrace:
# 
# * Backtrace ~$gtk.reset~ last invocation:
# 

Game State:
{:state=>{:tick_count=>238, :primitive_marker=>:sprite}, :temp_state=>{}, :inputs=>{:controller_one=>{:left_analog_x_raw=>0, :left_analog_y_raw=>0, :left_analog_x_perc=>0.0, :left_analog_y_perc=>0.0, :right_analog_x_raw=>0, :right_analog_y_raw=>0, :right_analog_x_perc=>0.0, :right_analog_y_perc=>0.0, :active=>false, :key_down=>{}, :key_held=>{}, :key_up=>{}, :left_analog_angle=>nil, :right_analog_angle=>nil, :left_analog_active=>false, :right_analog_active=>false}, :controller_two=>{:left_analog_x_raw=>0, :left_analog_y_raw=>0, :left_analog_x_perc=>0.0, :left_analog_y_perc=>0.0, :right_analog_x_raw=>0, :right_analog_y_raw=>0, :right_analog_x_perc=>0.0, :right_analog_y_perc=>0.0, :active=>false, :key_down=>{}, :key_held=>{}, :key_up=>{}, :left_analog_angle=>nil, :right_analog_angle=>nil, :left_analog_active=>false, :right_analog_active=>false}, :controller_three=>{:left_analog_x_raw=>0, :left_analog_y_raw=>0, :left_analog_x_perc=>0.0, :left_analog_y_perc=>0.0, :right_analog_x_raw=>0, :right_analog_y_raw=>0, :right_analog_x_perc=>0.0, :right_analog_y_perc=>0.0, :active=>false, :key_down=>{}, :key_held=>{}, :key_up=>{}, :left_analog_angle=>nil, :right_analog_angle=>nil, :left_analog_active=>false, :right_analog_active=>false}, :controller_four=>{:left_analog_x_raw=>0, :left_analog_y_raw=>0, :left_analog_x_perc=>0.0, :left_analog_y_perc=>0.0, :right_analog_x_raw=>0, :right_analog_y_raw=>0, :right_analog_x_perc=>0.0, :right_analog_y_perc=>0.0, :active=>false, :key_down=>{}, :key_held=>{}, :key_up=>{}, :left_analog_angle=>nil, :right_analog_angle=>nil, :left_analog_active=>false, :right_analog_active=>false}, :keyboard=>{:key_up=>{:keycodes=>[], :truthy_keys=>[]}, :key_held=>{:keycodes=>[], :truthy_keys=>[]}, :key_down=>{:keycodes=>[], :truthy_keys=>[]}, :has_focus=>false}, :mouse=>{:x=>1044.0, :y=>0.0, :moved=>nil, :moved_at=>259, :has_focus=>false}, :text=>[], :application_control=>"nil"}, :passes=>[{:solids=>[], :sprites=>[{:tick_count=>238, :primitive_marker=>:sprite}], :lines=>[], :labels=>[], :sounds=>[], :borders=>[], :primitives=>[], :static_solids=>[], :static_borders=>[], :static_sprites=>[], :static_lines=>[], :static_labels=>[], :static_primitives=>[]}], :outputs=>{:solids=>[], :sprites=>[{:tick_count=>238, :primitive_marker=>:sprite}], :lines=>[], :labels=>[], :sounds=>[], :borders=>[], :primitives=>[], :static_solids=>[], :static_borders=>[], :static_sprites=>[], :static_lines=>[], :static_labels=>[], :static_primitives=>[]}, :grid=>{:right_px=>1280, :w_px=>1280, :ffi_draw=>"", :runtime=>{:argv=>"C:\\Users\\<USER>\\work\\the_necromancers_cube\\dragonruby.exe", :platform=>"Windows", :required_files=>["app/main.rb"], :reload_list_history=>{"app/main.rb"=>{:current=>{:path=>"app/main.rb", :global_at=>1491, :event=>:reload_completed}, :history=>[{:path=>"app/main.rb", :global_at=>-1, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>-1, :event=>:processing}, {:path=>"app/main.rb", :global_at=>-1, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>1193, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>1193, :event=>:processing}, {:path=>"app/main.rb", :global_at=>1193, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>1491, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>1491, :event=>:processing}, {:path=>"app/main.rb", :global_at=>1491, :event=>:reload_completed}]}}}, :h_px=>720, :allscreen_w=>1280, :right=>1280, :render_origin_x=>0.0, :allscreen_h=>720, :render_origin_y=>720, :allscreen_top=>720, :high_dpi_scale=>1.0, :allscreen_h_px=>720, :h=>720, :texture_scale_enum=>100, :allscreen_right_px=>1280, :y=>0.0, :allscreen_w_px=>1280, :left=>0, :texture_scale=>1.0, :allscreen_right=>1280, :letterbox=>true, :render_scale=>1.0, :center=>[[:x, 640.0], [:y, 360.0]], :allscreen_offset_x_px=>0, :x=>0.0, :origin_x=>0.0, :allscreen_offset_y=>0, :w=>1280, :left_px=>0, :origin_y=>0.0, :bottom_px=>0, :center_y=>360.0, :allscreen_offset_y_px=>0, :rect=>[[:x, 0.0], [:y, 0.0], [:w, 1280], [:h, 720]], :native_scale=>1.0, :top_px=>720, :center_x=>640.0, :allscreen_w_pt=>1280, :allscreen_bottom_px=>0, :bottom=>0, :allscreen_top_px=>720, :allscreen_bottom=>0, :allscreen_left_px=>0, :top=>720, :allscreen_offset_x=>0, :allscreen_left=>0, :origin_name=>:bottom_left, :allscreen_h_pt=>720}}