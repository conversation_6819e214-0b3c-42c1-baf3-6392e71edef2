commit 0deb7943124866b7e73b8d4007615dfa8faaf596
Author: <PERSON> <<EMAIL>>
Date:   Tue Sep 24 20:14:04 2024 -0500

    [mRuby] state.c: check irep reference count overflow.

    - <https://github.com/mruby/mruby/commit/30b1287df5d2dc23b797a578e4916199715754ef>

    Repro:

    ```
    def tick args
      if args.inputs.keyboard.key_down.space
        x = 400_000.times.map do |i|
          { f: 100.times.map { i.to_f }, a: [i] * 100, s: "a" * 100 }
        end

        puts "HELLO"
      end
    end
    ```

diff --git a/mruby/src/state.c b/mruby/src/state.c
index f99292d90..8ecf5e0d0 100644
--- a/mruby/src/state.c
+++ b/mruby/src/state.c
@@ -4,6 +4,7 @@
 ** See Copyright Notice in mruby.h
 */

+#include <stdint.h>
 #include <stdlib.h>
 #include <string.h>
 #include <mruby.h>
@@ -110,5 +111,11 @@ void
 mrb_irep_incref(mrb_state *mrb, mrb_irep *irep)
 {
   if (irep->flags & MRB_IREP_NO_FREE) return;
+  if (irep->refcnt == UINT16_MAX) {
+    mrb_garbage_collect(mrb);
+    if (irep->refcnt == UINT16_MAX) {
+      mrb_raise(mrb, E_RUNTIME_ERROR, "too many irep references");
+    }
+  }
   irep->refcnt++;
 }
