function GROWABLE_HEAP_I8(){if(wasmMemory.buffer!=HEAP8.buffer){updateMemoryViews()}return HEAP8}function GROWABLE_HEAP_U8(){if(wasmMemory.buffer!=HEAP8.buffer){updateMemoryViews()}return HEAPU8}function GROWABLE_HEAP_I16(){if(wasmMemory.buffer!=HEAP8.buffer){updateMemoryViews()}return HEAP16}function GROWABLE_HEAP_U16(){if(wasmMemory.buffer!=HEAP8.buffer){updateMemoryViews()}return HEAPU16}function GROWABLE_HEAP_I32(){if(wasmMemory.buffer!=HEAP8.buffer){updateMemoryViews()}return HEAP32}function GROWABLE_HEAP_U32(){if(wasmMemory.buffer!=HEAP8.buffer){updateMemoryViews()}return HEAPU32}function GROWABLE_HEAP_F32(){if(wasmMemory.buffer!=HEAP8.buffer){updateMemoryViews()}return HEAPF32}function GROWABLE_HEAP_F64(){if(wasmMemory.buffer!=HEAP8.buffer){updateMemoryViews()}return HEAPF64}var Module=typeof Module!="undefined"?Module:{};var moduleOverrides=Object.assign({},Module);var arguments_=[];var thisProgram="./this.program";var quit_=(status,toThrow)=>{throw toThrow};var ENVIRONMENT_IS_WEB=typeof window=="object";var ENVIRONMENT_IS_WORKER=typeof importScripts=="function";var ENVIRONMENT_IS_NODE=typeof process=="object"&&typeof process.versions=="object"&&typeof process.versions.node=="string";var ENVIRONMENT_IS_PTHREAD=Module["ENVIRONMENT_IS_PTHREAD"]||false;var _scriptDir=typeof document!="undefined"&&document.currentScript?document.currentScript.src:undefined;if(ENVIRONMENT_IS_WORKER){_scriptDir=self.location.href}else if(ENVIRONMENT_IS_NODE){_scriptDir=__filename}var scriptDirectory="";function locateFile(path){if(Module["locateFile"]){return Module["locateFile"](path,scriptDirectory)}return scriptDirectory+path}var read_,readAsync,readBinary,setWindowTitle;if(ENVIRONMENT_IS_NODE){var fs=require("fs");var nodePath=require("path");if(ENVIRONMENT_IS_WORKER){scriptDirectory=nodePath.dirname(scriptDirectory)+"/"}else{scriptDirectory=__dirname+"/"}read_=(filename,binary)=>{filename=isFileURI(filename)?new URL(filename):nodePath.normalize(filename);return fs.readFileSync(filename,binary?undefined:"utf8")};readBinary=filename=>{var ret=read_(filename,true);if(!ret.buffer){ret=new Uint8Array(ret)}return ret};readAsync=(filename,onload,onerror,binary=true)=>{filename=isFileURI(filename)?new URL(filename):nodePath.normalize(filename);fs.readFile(filename,binary?undefined:"utf8",(err,data)=>{if(err)onerror(err);else onload(binary?data.buffer:data)})};if(!Module["thisProgram"]&&process.argv.length>1){thisProgram=process.argv[1].replace(/\\/g,"/")}arguments_=process.argv.slice(2);if(typeof module!="undefined"){module["exports"]=Module}process.on("uncaughtException",ex=>{if(ex!=="unwind"&&!(ex instanceof ExitStatus)&&!(ex.context instanceof ExitStatus)){throw ex}});var nodeMajor=process.versions.node.split(".")[0];if(nodeMajor<15){process.on("unhandledRejection",reason=>{throw reason})}quit_=(status,toThrow)=>{process.exitCode=status;throw toThrow};Module["inspect"]=()=>"[Emscripten Module object]";let nodeWorkerThreads;try{nodeWorkerThreads=require("worker_threads")}catch(e){console.error('The "worker_threads" module is not supported in this node.js build - perhaps a newer version is needed?');throw e}global.Worker=nodeWorkerThreads.Worker}else if(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER){if(ENVIRONMENT_IS_WORKER){scriptDirectory=self.location.href}else if(typeof document!="undefined"&&document.currentScript){scriptDirectory=document.currentScript.src}if(scriptDirectory.indexOf("blob:")!==0){scriptDirectory=scriptDirectory.substr(0,scriptDirectory.replace(/[?#].*/,"").lastIndexOf("/")+1)}else{scriptDirectory=""}if(!ENVIRONMENT_IS_NODE){read_=url=>{var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.send(null);return xhr.responseText};if(ENVIRONMENT_IS_WORKER){readBinary=url=>{var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.responseType="arraybuffer";xhr.send(null);return new Uint8Array(xhr.response)}}readAsync=(url,onload,onerror)=>{var xhr=new XMLHttpRequest;xhr.open("GET",url,true);xhr.responseType="arraybuffer";xhr.onload=()=>{if(xhr.status==200||xhr.status==0&&xhr.response){onload(xhr.response);return}onerror()};xhr.onerror=onerror;xhr.send(null)}}setWindowTitle=title=>document.title=title}else{}if(ENVIRONMENT_IS_NODE){if(typeof performance=="undefined"){global.performance=require("perf_hooks").performance}}var defaultPrint=console.log.bind(console);var defaultPrintErr=console.error.bind(console);if(ENVIRONMENT_IS_NODE){defaultPrint=(...args)=>fs.writeSync(1,args.join(" ")+"\n");defaultPrintErr=(...args)=>fs.writeSync(2,args.join(" ")+"\n")}var out=Module["print"]||defaultPrint;var err=Module["printErr"]||defaultPrintErr;Object.assign(Module,moduleOverrides);moduleOverrides=null;if(Module["arguments"])arguments_=Module["arguments"];if(Module["thisProgram"])thisProgram=Module["thisProgram"];if(Module["quit"])quit_=Module["quit"];var dynamicLibraries=Module["dynamicLibraries"]||[];var wasmBinary;if(Module["wasmBinary"])wasmBinary=Module["wasmBinary"];var noExitRuntime=Module["noExitRuntime"]||true;if(typeof WebAssembly!="object"){abort("no native wasm support detected")}var wasmMemory;var wasmModule;var ABORT=false;var EXITSTATUS;function assert(condition,text){if(!condition){abort(text)}}var HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64;function updateMemoryViews(){var b=wasmMemory.buffer;Module["HEAP8"]=HEAP8=new Int8Array(b);Module["HEAP16"]=HEAP16=new Int16Array(b);Module["HEAP32"]=HEAP32=new Int32Array(b);Module["HEAPU8"]=HEAPU8=new Uint8Array(b);Module["HEAPU16"]=HEAPU16=new Uint16Array(b);Module["HEAPU32"]=HEAPU32=new Uint32Array(b);Module["HEAPF32"]=HEAPF32=new Float32Array(b);Module["HEAPF64"]=HEAPF64=new Float64Array(b)}var INITIAL_MEMORY=Module["INITIAL_MEMORY"]||16777216;assert(INITIAL_MEMORY>=65536,"INITIAL_MEMORY should be larger than STACK_SIZE, was "+INITIAL_MEMORY+"! (STACK_SIZE="+65536+")");if(ENVIRONMENT_IS_PTHREAD){wasmMemory=Module["wasmMemory"]}else{if(Module["wasmMemory"]){wasmMemory=Module["wasmMemory"]}else{wasmMemory=new WebAssembly.Memory({"initial":INITIAL_MEMORY/65536,"maximum":1073741824/65536,"shared":true});if(!(wasmMemory.buffer instanceof SharedArrayBuffer)){err("requested a shared WebAssembly.Memory but the returned buffer is not a SharedArrayBuffer, indicating that while the browser has SharedArrayBuffer it does not have WebAssembly threads support - you may need to set a flag");if(ENVIRONMENT_IS_NODE){err("(on node you may need: --experimental-wasm-threads --experimental-wasm-bulk-memory and/or recent version)")}throw Error("bad memory")}}}updateMemoryViews();INITIAL_MEMORY=wasmMemory.buffer.byteLength;var wasmTable=new WebAssembly.Table({"initial":2085,"element":"anyfunc"});var __ATPRERUN__=[];var __ATINIT__=[];var __ATMAIN__=[];var __ATEXIT__=[];var __ATPOSTRUN__=[];var __RELOC_FUNCS__=[];var runtimeInitialized=false;var runtimeKeepaliveCounter=0;function keepRuntimeAlive(){return noExitRuntime||runtimeKeepaliveCounter>0}function preRun(){if(Module["preRun"]){if(typeof Module["preRun"]=="function")Module["preRun"]=[Module["preRun"]];while(Module["preRun"].length){addOnPreRun(Module["preRun"].shift())}}callRuntimeCallbacks(__ATPRERUN__)}function initRuntime(){runtimeInitialized=true;if(ENVIRONMENT_IS_PTHREAD)return;callRuntimeCallbacks(__RELOC_FUNCS__);if(!Module["noFSInit"]&&!FS.init.initialized)FS.init();FS.ignorePermissions=false;TTY.init();SOCKFS.root=FS.mount(SOCKFS,{},null);PIPEFS.root=FS.mount(PIPEFS,{},null);callRuntimeCallbacks(__ATINIT__)}function preMain(){if(ENVIRONMENT_IS_PTHREAD)return;callRuntimeCallbacks(__ATMAIN__)}function postRun(){if(ENVIRONMENT_IS_PTHREAD)return;if(Module["postRun"]){if(typeof Module["postRun"]=="function")Module["postRun"]=[Module["postRun"]];while(Module["postRun"].length){addOnPostRun(Module["postRun"].shift())}}callRuntimeCallbacks(__ATPOSTRUN__)}function addOnPreRun(cb){__ATPRERUN__.unshift(cb)}function addOnInit(cb){__ATINIT__.unshift(cb)}function addOnPostRun(cb){__ATPOSTRUN__.unshift(cb)}var runDependencies=0;var runDependencyWatcher=null;var dependenciesFulfilled=null;function getUniqueRunDependency(id){return id}function addRunDependency(id){runDependencies++;if(Module["monitorRunDependencies"]){Module["monitorRunDependencies"](runDependencies)}}function removeRunDependency(id){runDependencies--;if(Module["monitorRunDependencies"]){Module["monitorRunDependencies"](runDependencies)}if(runDependencies==0){if(runDependencyWatcher!==null){clearInterval(runDependencyWatcher);runDependencyWatcher=null}if(dependenciesFulfilled){var callback=dependenciesFulfilled;dependenciesFulfilled=null;callback()}}}function abort(what){if(Module["onAbort"]){Module["onAbort"](what)}what="Aborted("+what+")";err(what);ABORT=true;EXITSTATUS=1;what+=". Build with -sASSERTIONS for more info.";var e=new WebAssembly.RuntimeError(what);throw e}var dataURIPrefix="data:application/octet-stream;base64,";function isDataURI(filename){return filename.startsWith(dataURIPrefix)}function isFileURI(filename){return filename.startsWith("file://")}var wasmBinaryFile;wasmBinaryFile="dragonruby-wasm.wasm";if(!isDataURI(wasmBinaryFile)){wasmBinaryFile=locateFile(wasmBinaryFile)}function getBinary(file){try{if(file==wasmBinaryFile&&wasmBinary){return new Uint8Array(wasmBinary)}if(readBinary){return readBinary(file)}throw"both async and sync fetching of the wasm failed"}catch(err){abort(err)}}function getBinaryPromise(binaryFile){if(!wasmBinary&&(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER)){if(typeof fetch=="function"&&!isFileURI(binaryFile)){return fetch(binaryFile,{credentials:"same-origin"}).then(response=>{if(!response["ok"]){throw"failed to load wasm binary file at '"+binaryFile+"'"}return response["arrayBuffer"]()}).catch(()=>getBinary(binaryFile))}else{if(readAsync){return new Promise((resolve,reject)=>{readAsync(binaryFile,response=>resolve(new Uint8Array(response)),reject)})}}}return Promise.resolve().then(()=>getBinary(binaryFile))}function instantiateArrayBuffer(binaryFile,imports,receiver){return getBinaryPromise(binaryFile).then(binary=>{return WebAssembly.instantiate(binary,imports)}).then(instance=>{return instance}).then(receiver,reason=>{err("failed to asynchronously prepare wasm: "+reason);abort(reason)})}function instantiateAsync(binary,binaryFile,imports,callback){if(!binary&&typeof WebAssembly.instantiateStreaming=="function"&&!isDataURI(binaryFile)&&!isFileURI(binaryFile)&&!ENVIRONMENT_IS_NODE&&typeof fetch=="function"){return fetch(binaryFile,{credentials:"same-origin"}).then(response=>{var result=WebAssembly.instantiateStreaming(response,imports);return result.then(callback,function(reason){err("wasm streaming compile failed: "+reason);err("falling back to ArrayBuffer instantiation");return instantiateArrayBuffer(binaryFile,imports,callback)})})}else{return instantiateArrayBuffer(binaryFile,imports,callback)}}function createWasm(){var info={"env":wasmImports,"wasi_snapshot_preview1":wasmImports,"GOT.mem":new Proxy(wasmImports,GOTHandler),"GOT.func":new Proxy(wasmImports,GOTHandler)};function receiveInstance(instance,module){var exports=instance.exports;exports=relocateExports(exports,1024);var metadata=getDylinkMetadata(module);if(metadata.neededDynlibs){dynamicLibraries=metadata.neededDynlibs.concat(dynamicLibraries)}mergeLibSymbols(exports,"main");LDSO.init();loadDylibs();Module["asm"]=exports;registerTLSInit(Module["asm"]["_emscripten_tls_init"],instance.exports,metadata);addOnInit(Module["asm"]["__wasm_call_ctors"]);__RELOC_FUNCS__.push(Module["asm"]["__wasm_apply_data_relocs"]);wasmModule=module;removeRunDependency("wasm-instantiate");return exports}addRunDependency("wasm-instantiate");function receiveInstantiationResult(result){receiveInstance(result["instance"],result["module"])}if(Module["instantiateWasm"]){try{return Module["instantiateWasm"](info,receiveInstance)}catch(e){err("Module.instantiateWasm callback failed with error: "+e);return false}}instantiateAsync(wasmBinary,wasmBinaryFile,info,receiveInstantiationResult);return{}}var tempDouble;var tempI64;var ASM_CONSTS={1253304:$0=>{var str=UTF8ToString($0)+"\n\n"+"Abort/Retry/Ignore/AlwaysIgnore? [ariA] :";var reply=window.prompt(str,"i");if(reply===null){reply="i"}return allocate(intArrayFromString(reply),"i8",ALLOC_NORMAL)},1253529:($0,$1)=>{alert(UTF8ToString($0)+"\n\n"+UTF8ToString($1))},1253586:($0,$1,$2)=>{var w=$0;var h=$1;var pixels=$2;if(!Module["SDL2"])Module["SDL2"]={};var SDL2=Module["SDL2"];if(SDL2.ctxCanvas!==Module["canvas"]){SDL2.ctx=Module["createContext"](Module["canvas"],false,true);SDL2.ctxCanvas=Module["canvas"]}if(SDL2.w!==w||SDL2.h!==h||SDL2.imageCtx!==SDL2.ctx){SDL2.image=SDL2.ctx.createImageData(w,h);SDL2.w=w;SDL2.h=h;SDL2.imageCtx=SDL2.ctx}var data=SDL2.image.data;var src=pixels>>2;var dst=0;var num;if(typeof CanvasPixelArray!=="undefined"&&data instanceof CanvasPixelArray){num=data.length;while(dst<num){var val=GROWABLE_HEAP_I32()[src];data[dst]=val&255;data[dst+1]=val>>8&255;data[dst+2]=val>>16&255;data[dst+3]=255;src++;dst+=4}}else{if(SDL2.data32Data!==data){SDL2.data32=new Int32Array(data.buffer);SDL2.data8=new Uint8Array(data.buffer);SDL2.data32Data=data}var data32=SDL2.data32;num=data32.length;data32.set(GROWABLE_HEAP_I32().subarray(src,src+num));var data8=SDL2.data8;var i=3;var j=i+4*num;if(num%8==0){while(i<j){data8[i]=255;i=i+4|0;data8[i]=255;i=i+4|0;data8[i]=255;i=i+4|0;data8[i]=255;i=i+4|0;data8[i]=255;i=i+4|0;data8[i]=255;i=i+4|0;data8[i]=255;i=i+4|0;data8[i]=255;i=i+4|0}}else{while(i<j){data8[i]=255;i=i+4|0}}}SDL2.ctx.putImageData(SDL2.image,0,0)},1255055:($0,$1,$2,$3,$4)=>{var w=$0;var h=$1;var hot_x=$2;var hot_y=$3;var pixels=$4;var canvas=document.createElement("canvas");canvas.width=w;canvas.height=h;var ctx=canvas.getContext("2d");var image=ctx.createImageData(w,h);var data=image.data;var src=pixels>>2;var dst=0;var num;if(typeof CanvasPixelArray!=="undefined"&&data instanceof CanvasPixelArray){num=data.length;while(dst<num){var val=GROWABLE_HEAP_I32()[src];data[dst]=val&255;data[dst+1]=val>>8&255;data[dst+2]=val>>16&255;data[dst+3]=val>>24&255;src++;dst+=4}}else{var data32=new Int32Array(data.buffer);num=data32.length;data32.set(GROWABLE_HEAP_I32().subarray(src,src+num))}ctx.putImageData(image,0,0);var url=hot_x===0&&hot_y===0?"url("+canvas.toDataURL()+"), auto":"url("+canvas.toDataURL()+") "+hot_x+" "+hot_y+", auto";var urlBuf=_malloc(url.length+1);stringToUTF8(url,urlBuf,url.length+1);return urlBuf},1256044:$0=>{if(Module["canvas"]){Module["canvas"].style["cursor"]=UTF8ToString($0)}},1256127:()=>{if(Module["canvas"]){Module["canvas"].style["cursor"]="none"}},1256196:()=>{return window.innerWidth},1256226:()=>{return window.innerHeight},1256257:()=>{if(typeof AudioContext!=="undefined"){return true}else if(typeof webkitAudioContext!=="undefined"){return true}return false},1256404:()=>{if(typeof navigator.mediaDevices!=="undefined"&&typeof navigator.mediaDevices.getUserMedia!=="undefined"){return true}else if(typeof navigator.webkitGetUserMedia!=="undefined"){return true}return false},1256638:$0=>{if(typeof Module["SDL2"]==="undefined"){Module["SDL2"]={}}var SDL2=Module["SDL2"];if(!$0){SDL2.audio={}}else{SDL2.capture={}}if(!SDL2.audioContext){if(typeof AudioContext!=="undefined"){SDL2.audioContext=new AudioContext}else if(typeof webkitAudioContext!=="undefined"){SDL2.audioContext=new webkitAudioContext}if(SDL2.audioContext){autoResumeAudioContext(SDL2.audioContext)}}return SDL2.audioContext===undefined?-1:0},1257131:()=>{var SDL2=Module["SDL2"];return SDL2.audioContext.sampleRate},1257199:($0,$1,$2,$3)=>{var SDL2=Module["SDL2"];var have_microphone=function(stream){if(SDL2.capture.silenceTimer!==undefined){clearTimeout(SDL2.capture.silenceTimer);SDL2.capture.silenceTimer=undefined}SDL2.capture.mediaStreamNode=SDL2.audioContext.createMediaStreamSource(stream);SDL2.capture.scriptProcessorNode=SDL2.audioContext.createScriptProcessor($1,$0,1);SDL2.capture.scriptProcessorNode.onaudioprocess=function(audioProcessingEvent){if(SDL2===undefined||SDL2.capture===undefined){return}audioProcessingEvent.outputBuffer.getChannelData(0).fill(0);SDL2.capture.currentCaptureBuffer=audioProcessingEvent.inputBuffer;dynCall("vi",$2,[$3])};SDL2.capture.mediaStreamNode.connect(SDL2.capture.scriptProcessorNode);SDL2.capture.scriptProcessorNode.connect(SDL2.audioContext.destination);SDL2.capture.stream=stream};var no_microphone=function(error){};SDL2.capture.silenceBuffer=SDL2.audioContext.createBuffer($0,$1,SDL2.audioContext.sampleRate);SDL2.capture.silenceBuffer.getChannelData(0).fill(0);var silence_callback=function(){SDL2.capture.currentCaptureBuffer=SDL2.capture.silenceBuffer;dynCall("vi",$2,[$3])};SDL2.capture.silenceTimer=setTimeout(silence_callback,$1/SDL2.audioContext.sampleRate*1e3);if(navigator.mediaDevices!==undefined&&navigator.mediaDevices.getUserMedia!==undefined){navigator.mediaDevices.getUserMedia({audio:true,video:false}).then(have_microphone).catch(no_microphone)}else if(navigator.webkitGetUserMedia!==undefined){navigator.webkitGetUserMedia({audio:true,video:false},have_microphone,no_microphone)}},1258851:($0,$1,$2,$3)=>{var SDL2=Module["SDL2"];SDL2.audio.scriptProcessorNode=SDL2.audioContext["createScriptProcessor"]($1,0,$0);SDL2.audio.scriptProcessorNode["onaudioprocess"]=function(e){if(SDL2===undefined||SDL2.audio===undefined){return}SDL2.audio.currentOutputBuffer=e["outputBuffer"];dynCall("vi",$2,[$3])};SDL2.audio.scriptProcessorNode["connect"](SDL2.audioContext["destination"])},1259261:($0,$1)=>{var SDL2=Module["SDL2"];var numChannels=SDL2.capture.currentCaptureBuffer.numberOfChannels;for(var c=0;c<numChannels;++c){var channelData=SDL2.capture.currentCaptureBuffer.getChannelData(c);if(channelData.length!=$1){throw"Web Audio capture buffer length mismatch! Destination size: "+channelData.length+" samples vs expected "+$1+" samples!"}if(numChannels==1){for(var j=0;j<$1;++j){setValue($0+j*4,channelData[j],"float")}}else{for(var j=0;j<$1;++j){setValue($0+(j*numChannels+c)*4,channelData[j],"float")}}}},1259866:($0,$1)=>{var SDL2=Module["SDL2"];var numChannels=SDL2.audio.currentOutputBuffer["numberOfChannels"];for(var c=0;c<numChannels;++c){var channelData=SDL2.audio.currentOutputBuffer["getChannelData"](c);if(channelData.length!=$1){throw"Web Audio output buffer length mismatch! Destination size: "+channelData.length+" samples vs expected "+$1+" samples!"}for(var j=0;j<$1;++j){channelData[j]=GROWABLE_HEAP_F32()[$0+(j*numChannels+c<<2)>>2]}}},1260346:$0=>{var SDL2=Module["SDL2"];if($0){if(SDL2.capture.silenceTimer!==undefined){clearTimeout(SDL2.capture.silenceTimer)}if(SDL2.capture.stream!==undefined){var tracks=SDL2.capture.stream.getAudioTracks();for(var i=0;i<tracks.length;i++){SDL2.capture.stream.removeTrack(tracks[i])}SDL2.capture.stream=undefined}if(SDL2.capture.scriptProcessorNode!==undefined){SDL2.capture.scriptProcessorNode.onaudioprocess=function(audioProcessingEvent){};SDL2.capture.scriptProcessorNode.disconnect();SDL2.capture.scriptProcessorNode=undefined}if(SDL2.capture.mediaStreamNode!==undefined){SDL2.capture.mediaStreamNode.disconnect();SDL2.capture.mediaStreamNode=undefined}if(SDL2.capture.silenceBuffer!==undefined){SDL2.capture.silenceBuffer=undefined}SDL2.capture=undefined}else{if(SDL2.audio.scriptProcessorNode!=undefined){SDL2.audio.scriptProcessorNode.disconnect();SDL2.audio.scriptProcessorNode=undefined}SDL2.audio=undefined}if(SDL2.audioContext!==undefined&&SDL2.audio===undefined&&SDL2.capture===undefined){SDL2.audioContext.close();SDL2.audioContext=undefined}},1261518:$0=>{window.open(UTF8ToString($0),"_blank")},1261563:($0,$1)=>{var buf=$0;var buflen=$1;var list=undefined;if(navigator.languages&&navigator.languages.length){list=navigator.languages}else{var oneOfThese=navigator.userLanguage||navigator.language||navigator.browserLanguage||navigator.systemLanguage;if(oneOfThese!==undefined){list=[oneOfThese]}}if(list===undefined){return}var str="";for(var i=0;i<list.length;i++){var item=list[i];if(str.length+item.length+1>buflen){break}if(str.length>0){str+=","}str+=item}str=str.replace(/-/g,"_");if(buflen>str.length){buflen=str.length}for(var i=0;i<buflen;i++){setValue(buf+i,str.charCodeAt(i),"i8")}},1262271:()=>{return/iPhone|iPad|iPod/i.test(navigator.userAgent)&&!window.MSStream?1:0},1262361:()=>{return/Android/i.test(navigator.userAgent)&&!window.MSStream?1:0},1262442:()=>{GDragonRubyEngineJS={}},1262472:()=>{GDragonRubyEngineJS={}},1262502:$0=>{var p_commit_persistent_fs_in_progress=$0;if(GDragonRubyEngineJS.sync_writedir===undefined){GDragonRubyEngineJS.sync_writedir=true}if(GDragonRubyEngineJS.sync_writedir){FS.syncfs(false,function(err){if(err){console.log("Uhoh, couldn't commit persistent data to write dir!");console.log("Further attempts will not be made. Save games, etc, WILL BE LOST!");GDragonRubyEngineJS.sync_writedir=false}setValue(p_commit_persistent_fs_in_progress,0,"i32")})}},1262993:()=>{return window.dragonruby_window_width||window.innerWidth},1263057:()=>{return window.dragonruby_window_height||window.innerHeight},1263123:()=>{return window.dragonruby_logical_width||window.innerWidth},1263188:()=>{return window.dragonruby_logical_height||window.innerHeight},1263255:($0,$1,$2,$3,$4,$5,$6,$7,$8)=>{var url=UTF8ToString($0);var method=UTF8ToString($1);var mimetype=$2?UTF8ToString($2):undefined;var c_ary_headers=$3;var method_data=$4;var cb=$5;var putdata=$6;var putdatalen=$7;var sizeofptr=$8;var headers={};if(c_ary_headers){for(var ptr;ptr=getValue(c_ary_headers,"i8*");){var str=UTF8ToString(ptr);var idx=str.indexOf(":");if(idx!=-1){var key=str.substr(0,idx).trim();var value=str.substr(idx+1).trim();headers[key]=value}c_ary_headers+=4}}if(mimetype){headers["Content-Type"]=mimetype}var body=undefined;if(method=="POST"){headers["Expect"]="";body=new FormData;if(method_data){for(var ptr;ptr=getValue(method_data,"i8*");){var key=UTF8ToString(ptr);method_data+=sizeofptr;ptr=getValue(method_data,"i8*");var value=ptr?UTF8ToString(ptr):"";body.append(key,value);if(!ptr){break}method_data+=4}}}else if(method=="POST_BODY"){method="POST";body=new ArrayBuffer(putdatalen);src=new Uint8Array(Module.HEAPU8.buffer,method_data,putdatalen);dst=new Uint8Array(body);dst.set(src)}else if(method=="PUT"){body=new ArrayBuffer(putdatalen);src=new Uint8Array(Module.HEAPU8.buffer,putdata,putdatalen);dst=new Uint8Array(body);dst.set(src)}var abort_controller=new AbortController;var abort_signal=abort_controller.signal;if(GDragonRubyEngineJS.fetches===undefined){GDragonRubyEngineJS.fetches=new Map}var state={};state.complete=false;state.aborted=false;state.response_code=0;state.response_headers=undefined;state.abort_controller=abort_controller;state.chunks=[];GDragonRubyEngineJS.fetches.set(cb,state);var promise=fetch(url,{method:method,headers:headers,signal:abort_signal,body:body}).then(response=>{var totallen=response.headers.get("Content-Length");state.response_code=response.status;_http_update_callback_initial_from_javascript(cb,state.response_code,totallen===undefined?0:parseInt(totallen));state.response_headers=response.headers;var reader=response.body.getReader();return reader.read().then(function processResult(result){if(result.done){state.complete=true;return}state.chunks.push(result.value);return reader.read().then(processResult)})}).catch(function(e){state.response_code=-1;state.complete=true});return promise!==undefined},1265702:()=>{if(GDragonRubyEngineJS.fetches===undefined){return}GDragonRubyEngineJS.fetches.forEach(function(state,cb){if(!state.aborted){if(state.response_headers!==undefined){for(var pair of state.response_headers.entries()){var keystr=pair[0].trim();var valstr=pair[1].trim();var karray=intArrayFromString(keystr);var key=_malloc(karray.length);var varray=intArrayFromString(valstr);var value=_malloc(varray.length);if(key==0||value==0){state.aborted=true}else{Module.HEAPU8.set(karray,key);Module.HEAPU8.set(varray,value);_http_new_header_from_javascript(cb,key,value)==0}_free(key);_free(value)}state.response_headers=undefined}}if(!state.aborted){var chunk;while((chunk=state.chunks.shift())!==undefined){var ptr=_malloc(chunk.length);if(ptr==0){state.aborted=true}else{Module.HEAPU8.set(chunk,ptr);_http_new_data_from_javascript(cb,ptr,chunk.length)==0;_free(ptr)}}}if(state.aborted){if(state.abort_controller!==undefined){state.abort_controller.abort();state.abort_controller=undefined}}if(state.complete){state.complete=false;_http_mark_callback_complete_from_javascript(cb,state.response_code)}})},1266945:$0=>{var cb=$0;if(GDragonRubyEngineJS.fetches===undefined){return}var state=GDragonRubyEngineJS.fetches.get(cb);if(state!==undefined){state.aborted=true;if(state.abort_controller!==undefined){state.abort_controller.abort();state.abort_controller=undefined}GDragonRubyEngineJS.fetches.delete(cb)}if(GDragonRubyEngineJS.fetches.size==0){GDragonRubyEngineJS.fetches=undefined}},1267363:()=>{if(GDragonRubyEngineJS.fetches!==undefined){GDragonRubyEngineJS.fetches.forEach(function(state,cb){if(state.abort_controller!==undefined){state.abort_controller.abort()}});GDragonRubyEngineJS.fetches=undefined}},1267599:()=>{return/Android/i.test(navigator.userAgent)},1267650:()=>{return window.innerWidth},1267680:()=>{return window.innerHeight},1267711:()=>{if(window.GFullscreenMode===undefined){window.GFullscreenMode=typeof document.fullscreen!=="undefined"&&document.fullscreen||typeof document.mozFullScreen!=="undefined"&&document.mozFullScreen||typeof document.webkitIsFullScreen!=="undefined"&&document.webkitIsFullScreen||typeof document.msFullscreenElement!=="undefined"&&document.msFullscreenElement;document.addEventListener("fullscreenchange",function(){GFullscreenMode=document.fullscreen},false);document.addEventListener("mozfullscreenchange",function(){GFullscreenMode=document.mozFullScreen},false);document.addEventListener("webkitfullscreenchange",function(){GFullscreenMode=document.webkitIsFullScreen},false);document.addEventListener("msfullscreenchange",function(){GFullscreenMode=document.msFullscreenElement},false)}},1268591:()=>{window.focus()}};function ExitStatus(status){this.name="ExitStatus";this.message=`Program terminated with exit(${status})`;this.status=status}var GOT={};var currentModuleWeakSymbols=new Set([]);var GOTHandler={get:function(obj,symName){var rtn=GOT[symName];if(!rtn){rtn=GOT[symName]=new WebAssembly.Global({"value":"i32","mutable":true})}if(!currentModuleWeakSymbols.has(symName)){rtn.required=true}return rtn}};function terminateWorker(worker){worker.terminate();worker.onmessage=e=>{}}function killThread(pthread_ptr){var worker=PThread.pthreads[pthread_ptr];delete PThread.pthreads[pthread_ptr];terminateWorker(worker);__emscripten_thread_free_data(pthread_ptr);PThread.runningWorkers.splice(PThread.runningWorkers.indexOf(worker),1);worker.pthread_ptr=0}function cancelThread(pthread_ptr){var worker=PThread.pthreads[pthread_ptr];worker.postMessage({"cmd":"cancel"})}function cleanupThread(pthread_ptr){var worker=PThread.pthreads[pthread_ptr];PThread.finishedThreads.delete(pthread_ptr);if(pthread_ptr in PThread.outstandingPromises){PThread.outstandingPromises[pthread_ptr].resolve()}assert(worker);PThread.returnWorkerToPool(worker)}function zeroMemory(address,size){GROWABLE_HEAP_U8().fill(0,address,address+size);return address}function markAsFinshed(pthread_ptr){PThread.finishedThreads.add(pthread_ptr);if(pthread_ptr in PThread.outstandingPromises){PThread.outstandingPromises[pthread_ptr].resolve()}}function spawnThread(threadParams){var worker=PThread.getNewWorker();if(!worker){return 6}PThread.runningWorkers.push(worker);PThread.pthreads[threadParams.pthread_ptr]=worker;worker.pthread_ptr=threadParams.pthread_ptr;var msg={"cmd":"run","start_routine":threadParams.startRoutine,"arg":threadParams.arg,"pthread_ptr":threadParams.pthread_ptr};if(ENVIRONMENT_IS_NODE){worker.unref()}worker.postMessage(msg,threadParams.transferList);return 0}var PATH={isAbs:path=>path.charAt(0)==="/",splitPath:filename=>{var splitPathRe=/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/;return splitPathRe.exec(filename).slice(1)},normalizeArray:(parts,allowAboveRoot)=>{var up=0;for(var i=parts.length-1;i>=0;i--){var last=parts[i];if(last==="."){parts.splice(i,1)}else if(last===".."){parts.splice(i,1);up++}else if(up){parts.splice(i,1);up--}}if(allowAboveRoot){for(;up;up--){parts.unshift("..")}}return parts},normalize:path=>{var isAbsolute=PATH.isAbs(path),trailingSlash=path.substr(-1)==="/";path=PATH.normalizeArray(path.split("/").filter(p=>!!p),!isAbsolute).join("/");if(!path&&!isAbsolute){path="."}if(path&&trailingSlash){path+="/"}return(isAbsolute?"/":"")+path},dirname:path=>{var result=PATH.splitPath(path),root=result[0],dir=result[1];if(!root&&!dir){return"."}if(dir){dir=dir.substr(0,dir.length-1)}return root+dir},basename:path=>{if(path==="/")return"/";path=PATH.normalize(path);path=path.replace(/\/$/,"");var lastSlash=path.lastIndexOf("/");if(lastSlash===-1)return path;return path.substr(lastSlash+1)},join:function(){var paths=Array.prototype.slice.call(arguments);return PATH.normalize(paths.join("/"))},join2:(l,r)=>{return PATH.normalize(l+"/"+r)}};function initRandomFill(){if(typeof crypto=="object"&&typeof crypto["getRandomValues"]=="function"){return view=>(view.set(crypto.getRandomValues(new Uint8Array(view.byteLength))),view)}else if(ENVIRONMENT_IS_NODE){try{var crypto_module=require("crypto");var randomFillSync=crypto_module["randomFillSync"];if(randomFillSync){return view=>crypto_module["randomFillSync"](view)}var randomBytes=crypto_module["randomBytes"];return view=>(view.set(randomBytes(view.byteLength)),view)}catch(e){}}abort("initRandomDevice")}function randomFill(view){return(randomFill=initRandomFill())(view)}var PATH_FS={resolve:function(){var resolvedPath="",resolvedAbsolute=false;for(var i=arguments.length-1;i>=-1&&!resolvedAbsolute;i--){var path=i>=0?arguments[i]:FS.cwd();if(typeof path!="string"){throw new TypeError("Arguments to path.resolve must be strings")}else if(!path){return""}resolvedPath=path+"/"+resolvedPath;resolvedAbsolute=PATH.isAbs(path)}resolvedPath=PATH.normalizeArray(resolvedPath.split("/").filter(p=>!!p),!resolvedAbsolute).join("/");return(resolvedAbsolute?"/":"")+resolvedPath||"."},relative:(from,to)=>{from=PATH_FS.resolve(from).substr(1);to=PATH_FS.resolve(to).substr(1);function trim(arr){var start=0;for(;start<arr.length;start++){if(arr[start]!=="")break}var end=arr.length-1;for(;end>=0;end--){if(arr[end]!=="")break}if(start>end)return[];return arr.slice(start,end-start+1)}var fromParts=trim(from.split("/"));var toParts=trim(to.split("/"));var length=Math.min(fromParts.length,toParts.length);var samePartsLength=length;for(var i=0;i<length;i++){if(fromParts[i]!==toParts[i]){samePartsLength=i;break}}var outputParts=[];for(var i=samePartsLength;i<fromParts.length;i++){outputParts.push("..")}outputParts=outputParts.concat(toParts.slice(samePartsLength));return outputParts.join("/")}};function lengthBytesUTF8(str){var len=0;for(var i=0;i<str.length;++i){var c=str.charCodeAt(i);if(c<=127){len++}else if(c<=2047){len+=2}else if(c>=55296&&c<=57343){len+=4;++i}else{len+=3}}return len}function stringToUTF8Array(str,heap,outIdx,maxBytesToWrite){if(!(maxBytesToWrite>0))return 0;var startIdx=outIdx;var endIdx=outIdx+maxBytesToWrite-1;for(var i=0;i<str.length;++i){var u=str.charCodeAt(i);if(u>=55296&&u<=57343){var u1=str.charCodeAt(++i);u=65536+((u&1023)<<10)|u1&1023}if(u<=127){if(outIdx>=endIdx)break;heap[outIdx++]=u}else if(u<=2047){if(outIdx+1>=endIdx)break;heap[outIdx++]=192|u>>6;heap[outIdx++]=128|u&63}else if(u<=65535){if(outIdx+2>=endIdx)break;heap[outIdx++]=224|u>>12;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}else{if(outIdx+3>=endIdx)break;heap[outIdx++]=240|u>>18;heap[outIdx++]=128|u>>12&63;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}}heap[outIdx]=0;return outIdx-startIdx}function intArrayFromString(stringy,dontAddNull,length){var len=length>0?length:lengthBytesUTF8(stringy)+1;var u8array=new Array(len);var numBytesWritten=stringToUTF8Array(stringy,u8array,0,u8array.length);if(dontAddNull)u8array.length=numBytesWritten;return u8array}var UTF8Decoder=typeof TextDecoder!="undefined"?new TextDecoder("utf8"):undefined;function UTF8ArrayToString(heapOrArray,idx,maxBytesToRead){var endIdx=idx+maxBytesToRead;var endPtr=idx;while(heapOrArray[endPtr]&&!(endPtr>=endIdx))++endPtr;if(endPtr-idx>16&&heapOrArray.buffer&&UTF8Decoder){return UTF8Decoder.decode(heapOrArray.buffer instanceof SharedArrayBuffer?heapOrArray.slice(idx,endPtr):heapOrArray.subarray(idx,endPtr))}var str="";while(idx<endPtr){var u0=heapOrArray[idx++];if(!(u0&128)){str+=String.fromCharCode(u0);continue}var u1=heapOrArray[idx++]&63;if((u0&224)==192){str+=String.fromCharCode((u0&31)<<6|u1);continue}var u2=heapOrArray[idx++]&63;if((u0&240)==224){u0=(u0&15)<<12|u1<<6|u2}else{u0=(u0&7)<<18|u1<<12|u2<<6|heapOrArray[idx++]&63}if(u0<65536){str+=String.fromCharCode(u0)}else{var ch=u0-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}}return str}var TTY={ttys:[],init:function(){},shutdown:function(){},register:function(dev,ops){TTY.ttys[dev]={input:[],output:[],ops:ops};FS.registerDevice(dev,TTY.stream_ops)},stream_ops:{open:function(stream){var tty=TTY.ttys[stream.node.rdev];if(!tty){throw new FS.ErrnoError(43)}stream.tty=tty;stream.seekable=false},close:function(stream){stream.tty.ops.fsync(stream.tty)},fsync:function(stream){stream.tty.ops.fsync(stream.tty)},read:function(stream,buffer,offset,length,pos){if(!stream.tty||!stream.tty.ops.get_char){throw new FS.ErrnoError(60)}var bytesRead=0;for(var i=0;i<length;i++){var result;try{result=stream.tty.ops.get_char(stream.tty)}catch(e){throw new FS.ErrnoError(29)}if(result===undefined&&bytesRead===0){throw new FS.ErrnoError(6)}if(result===null||result===undefined)break;bytesRead++;buffer[offset+i]=result}if(bytesRead){stream.node.timestamp=Date.now()}return bytesRead},write:function(stream,buffer,offset,length,pos){if(!stream.tty||!stream.tty.ops.put_char){throw new FS.ErrnoError(60)}try{for(var i=0;i<length;i++){stream.tty.ops.put_char(stream.tty,buffer[offset+i])}}catch(e){throw new FS.ErrnoError(29)}if(length){stream.node.timestamp=Date.now()}return i}},default_tty_ops:{get_char:function(tty){if(!tty.input.length){var result=null;if(ENVIRONMENT_IS_NODE){var BUFSIZE=256;var buf=Buffer.alloc(BUFSIZE);var bytesRead=0;try{bytesRead=fs.readSync(process.stdin.fd,buf,0,BUFSIZE,-1)}catch(e){if(e.toString().includes("EOF"))bytesRead=0;else throw e}if(bytesRead>0){result=buf.slice(0,bytesRead).toString("utf-8")}else{result=null}}else if(typeof window!="undefined"&&typeof window.prompt=="function"){result=window.prompt("Input: ");if(result!==null){result+="\n"}}else if(typeof readline=="function"){result=readline();if(result!==null){result+="\n"}}if(!result){return null}tty.input=intArrayFromString(result,true)}return tty.input.shift()},put_char:function(tty,val){if(val===null||val===10){out(UTF8ArrayToString(tty.output,0));tty.output=[]}else{if(val!=0)tty.output.push(val)}},fsync:function(tty){if(tty.output&&tty.output.length>0){out(UTF8ArrayToString(tty.output,0));tty.output=[]}}},default_tty1_ops:{put_char:function(tty,val){if(val===null||val===10){err(UTF8ArrayToString(tty.output,0));tty.output=[]}else{if(val!=0)tty.output.push(val)}},fsync:function(tty){if(tty.output&&tty.output.length>0){err(UTF8ArrayToString(tty.output,0));tty.output=[]}}}};function alignMemory(size,alignment){return Math.ceil(size/alignment)*alignment}function mmapAlloc(size){abort()}var MEMFS={ops_table:null,mount:function(mount){return MEMFS.createNode(null,"/",16384|511,0)},createNode:function(parent,name,mode,dev){if(FS.isBlkdev(mode)||FS.isFIFO(mode)){throw new FS.ErrnoError(63)}if(!MEMFS.ops_table){MEMFS.ops_table={dir:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr,lookup:MEMFS.node_ops.lookup,mknod:MEMFS.node_ops.mknod,rename:MEMFS.node_ops.rename,unlink:MEMFS.node_ops.unlink,rmdir:MEMFS.node_ops.rmdir,readdir:MEMFS.node_ops.readdir,symlink:MEMFS.node_ops.symlink},stream:{llseek:MEMFS.stream_ops.llseek}},file:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr},stream:{llseek:MEMFS.stream_ops.llseek,read:MEMFS.stream_ops.read,write:MEMFS.stream_ops.write,allocate:MEMFS.stream_ops.allocate,mmap:MEMFS.stream_ops.mmap,msync:MEMFS.stream_ops.msync}},link:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr,readlink:MEMFS.node_ops.readlink},stream:{}},chrdev:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr},stream:FS.chrdev_stream_ops}}}var node=FS.createNode(parent,name,mode,dev);if(FS.isDir(node.mode)){node.node_ops=MEMFS.ops_table.dir.node;node.stream_ops=MEMFS.ops_table.dir.stream;node.contents={}}else if(FS.isFile(node.mode)){node.node_ops=MEMFS.ops_table.file.node;node.stream_ops=MEMFS.ops_table.file.stream;node.usedBytes=0;node.contents=null}else if(FS.isLink(node.mode)){node.node_ops=MEMFS.ops_table.link.node;node.stream_ops=MEMFS.ops_table.link.stream}else if(FS.isChrdev(node.mode)){node.node_ops=MEMFS.ops_table.chrdev.node;node.stream_ops=MEMFS.ops_table.chrdev.stream}node.timestamp=Date.now();if(parent){parent.contents[name]=node;parent.timestamp=node.timestamp}return node},getFileDataAsTypedArray:function(node){if(!node.contents)return new Uint8Array(0);if(node.contents.subarray)return node.contents.subarray(0,node.usedBytes);return new Uint8Array(node.contents)},expandFileStorage:function(node,newCapacity){var prevCapacity=node.contents?node.contents.length:0;if(prevCapacity>=newCapacity)return;var CAPACITY_DOUBLING_MAX=1024*1024;newCapacity=Math.max(newCapacity,prevCapacity*(prevCapacity<CAPACITY_DOUBLING_MAX?2:1.125)>>>0);if(prevCapacity!=0)newCapacity=Math.max(newCapacity,256);var oldContents=node.contents;node.contents=new Uint8Array(newCapacity);if(node.usedBytes>0)node.contents.set(oldContents.subarray(0,node.usedBytes),0)},resizeFileStorage:function(node,newSize){if(node.usedBytes==newSize)return;if(newSize==0){node.contents=null;node.usedBytes=0}else{var oldContents=node.contents;node.contents=new Uint8Array(newSize);if(oldContents){node.contents.set(oldContents.subarray(0,Math.min(newSize,node.usedBytes)))}node.usedBytes=newSize}},node_ops:{getattr:function(node){var attr={};attr.dev=FS.isChrdev(node.mode)?node.id:1;attr.ino=node.id;attr.mode=node.mode;attr.nlink=1;attr.uid=0;attr.gid=0;attr.rdev=node.rdev;if(FS.isDir(node.mode)){attr.size=4096}else if(FS.isFile(node.mode)){attr.size=node.usedBytes}else if(FS.isLink(node.mode)){attr.size=node.link.length}else{attr.size=0}attr.atime=new Date(node.timestamp);attr.mtime=new Date(node.timestamp);attr.ctime=new Date(node.timestamp);attr.blksize=4096;attr.blocks=Math.ceil(attr.size/attr.blksize);return attr},setattr:function(node,attr){if(attr.mode!==undefined){node.mode=attr.mode}if(attr.timestamp!==undefined){node.timestamp=attr.timestamp}if(attr.size!==undefined){MEMFS.resizeFileStorage(node,attr.size)}},lookup:function(parent,name){throw FS.genericErrors[44]},mknod:function(parent,name,mode,dev){return MEMFS.createNode(parent,name,mode,dev)},rename:function(old_node,new_dir,new_name){if(FS.isDir(old_node.mode)){var new_node;try{new_node=FS.lookupNode(new_dir,new_name)}catch(e){}if(new_node){for(var i in new_node.contents){throw new FS.ErrnoError(55)}}}delete old_node.parent.contents[old_node.name];old_node.parent.timestamp=Date.now();old_node.name=new_name;new_dir.contents[new_name]=old_node;new_dir.timestamp=old_node.parent.timestamp;old_node.parent=new_dir},unlink:function(parent,name){delete parent.contents[name];parent.timestamp=Date.now()},rmdir:function(parent,name){var node=FS.lookupNode(parent,name);for(var i in node.contents){throw new FS.ErrnoError(55)}delete parent.contents[name];parent.timestamp=Date.now()},readdir:function(node){var entries=[".",".."];for(var key in node.contents){if(!node.contents.hasOwnProperty(key)){continue}entries.push(key)}return entries},symlink:function(parent,newname,oldpath){var node=MEMFS.createNode(parent,newname,511|40960,0);node.link=oldpath;return node},readlink:function(node){if(!FS.isLink(node.mode)){throw new FS.ErrnoError(28)}return node.link}},stream_ops:{read:function(stream,buffer,offset,length,position){var contents=stream.node.contents;if(position>=stream.node.usedBytes)return 0;var size=Math.min(stream.node.usedBytes-position,length);if(size>8&&contents.subarray){buffer.set(contents.subarray(position,position+size),offset)}else{for(var i=0;i<size;i++)buffer[offset+i]=contents[position+i]}return size},write:function(stream,buffer,offset,length,position,canOwn){if(buffer.buffer===GROWABLE_HEAP_I8().buffer){canOwn=false}if(!length)return 0;var node=stream.node;node.timestamp=Date.now();if(buffer.subarray&&(!node.contents||node.contents.subarray)){if(canOwn){node.contents=buffer.subarray(offset,offset+length);node.usedBytes=length;return length}else if(node.usedBytes===0&&position===0){node.contents=buffer.slice(offset,offset+length);node.usedBytes=length;return length}else if(position+length<=node.usedBytes){node.contents.set(buffer.subarray(offset,offset+length),position);return length}}MEMFS.expandFileStorage(node,position+length);if(node.contents.subarray&&buffer.subarray){node.contents.set(buffer.subarray(offset,offset+length),position)}else{for(var i=0;i<length;i++){node.contents[position+i]=buffer[offset+i]}}node.usedBytes=Math.max(node.usedBytes,position+length);return length},llseek:function(stream,offset,whence){var position=offset;if(whence===1){position+=stream.position}else if(whence===2){if(FS.isFile(stream.node.mode)){position+=stream.node.usedBytes}}if(position<0){throw new FS.ErrnoError(28)}return position},allocate:function(stream,offset,length){MEMFS.expandFileStorage(stream.node,offset+length);stream.node.usedBytes=Math.max(stream.node.usedBytes,offset+length)},mmap:function(stream,length,position,prot,flags){if(!FS.isFile(stream.node.mode)){throw new FS.ErrnoError(43)}var ptr;var allocated;var contents=stream.node.contents;if(!(flags&2)&&contents.buffer===GROWABLE_HEAP_I8().buffer){allocated=false;ptr=contents.byteOffset}else{if(position>0||position+length<contents.length){if(contents.subarray){contents=contents.subarray(position,position+length)}else{contents=Array.prototype.slice.call(contents,position,position+length)}}allocated=true;ptr=mmapAlloc(length);if(!ptr){throw new FS.ErrnoError(48)}GROWABLE_HEAP_I8().set(contents,ptr)}return{ptr:ptr,allocated:allocated}},msync:function(stream,buffer,offset,length,mmapFlags){MEMFS.stream_ops.write(stream,buffer,0,length,offset,false);return 0}}};function asyncLoad(url,onload,onerror,noRunDep){var dep=!noRunDep?getUniqueRunDependency(`al ${url}`):"";readAsync(url,arrayBuffer=>{assert(arrayBuffer,`Loading data file "${url}" failed (no arrayBuffer).`);onload(new Uint8Array(arrayBuffer));if(dep)removeRunDependency(dep)},event=>{if(onerror){onerror()}else{throw`Loading data file "${url}" failed.`}});if(dep)addRunDependency(dep)}var preloadPlugins=Module["preloadPlugins"]||[];function FS_handledByPreloadPlugin(byteArray,fullname,finish,onerror){if(typeof Browser!="undefined")Browser.init();var handled=false;preloadPlugins.forEach(function(plugin){if(handled)return;if(plugin["canHandle"](fullname)){plugin["handle"](byteArray,fullname,finish,onerror);handled=true}});return handled}function FS_createPreloadedFile(parent,name,url,canRead,canWrite,onload,onerror,dontCreateFile,canOwn,preFinish){var fullname=name?PATH_FS.resolve(PATH.join2(parent,name)):parent;var dep=getUniqueRunDependency(`cp ${fullname}`);function processData(byteArray){function finish(byteArray){if(preFinish)preFinish();if(!dontCreateFile){FS.createDataFile(parent,name,byteArray,canRead,canWrite,canOwn)}if(onload)onload();removeRunDependency(dep)}if(FS_handledByPreloadPlugin(byteArray,fullname,finish,()=>{if(onerror)onerror();removeRunDependency(dep)})){return}finish(byteArray)}addRunDependency(dep);if(typeof url=="string"){asyncLoad(url,byteArray=>processData(byteArray),onerror)}else{processData(url)}}function FS_modeStringToFlags(str){var flagModes={"r":0,"r+":2,"w":512|64|1,"w+":512|64|2,"a":1024|64|1,"a+":1024|64|2};var flags=flagModes[str];if(typeof flags=="undefined"){throw new Error(`Unknown file open mode: ${str}`)}return flags}function FS_getMode(canRead,canWrite){var mode=0;if(canRead)mode|=292|73;if(canWrite)mode|=146;return mode}var IDBFS={dbs:{},indexedDB:()=>{if(typeof indexedDB!="undefined")return indexedDB;var ret=null;if(typeof window=="object")ret=window.indexedDB||window.mozIndexedDB||window.webkitIndexedDB||window.msIndexedDB;assert(ret,"IDBFS used, but indexedDB not supported");return ret},DB_VERSION:21,DB_STORE_NAME:"FILE_DATA",mount:function(mount){return MEMFS.mount.apply(null,arguments)},syncfs:(mount,populate,callback)=>{IDBFS.getLocalSet(mount,(err,local)=>{if(err)return callback(err);IDBFS.getRemoteSet(mount,(err,remote)=>{if(err)return callback(err);var src=populate?remote:local;var dst=populate?local:remote;IDBFS.reconcile(src,dst,callback)})})},quit:()=>{Object.values(IDBFS.dbs).forEach(value=>value.close());IDBFS.dbs={}},getDB:(name,callback)=>{var db=IDBFS.dbs[name];if(db){return callback(null,db)}var req;try{req=IDBFS.indexedDB().open(name,IDBFS.DB_VERSION)}catch(e){return callback(e)}if(!req){return callback("Unable to connect to IndexedDB")}req.onupgradeneeded=e=>{var db=e.target.result;var transaction=e.target.transaction;var fileStore;if(db.objectStoreNames.contains(IDBFS.DB_STORE_NAME)){fileStore=transaction.objectStore(IDBFS.DB_STORE_NAME)}else{fileStore=db.createObjectStore(IDBFS.DB_STORE_NAME)}if(!fileStore.indexNames.contains("timestamp")){fileStore.createIndex("timestamp","timestamp",{unique:false})}};req.onsuccess=()=>{db=req.result;IDBFS.dbs[name]=db;callback(null,db)};req.onerror=e=>{callback(this.error);e.preventDefault()}},getLocalSet:(mount,callback)=>{var entries={};function isRealDir(p){return p!=="."&&p!==".."}function toAbsolute(root){return p=>{return PATH.join2(root,p)}}var check=FS.readdir(mount.mountpoint).filter(isRealDir).map(toAbsolute(mount.mountpoint));while(check.length){var path=check.pop();var stat;try{stat=FS.stat(path)}catch(e){return callback(e)}if(FS.isDir(stat.mode)){check.push.apply(check,FS.readdir(path).filter(isRealDir).map(toAbsolute(path)))}entries[path]={"timestamp":stat.mtime}}return callback(null,{type:"local",entries:entries})},getRemoteSet:(mount,callback)=>{var entries={};IDBFS.getDB(mount.mountpoint,(err,db)=>{if(err)return callback(err);try{var transaction=db.transaction([IDBFS.DB_STORE_NAME],"readonly");transaction.onerror=e=>{callback(this.error);e.preventDefault()};var store=transaction.objectStore(IDBFS.DB_STORE_NAME);var index=store.index("timestamp");index.openKeyCursor().onsuccess=event=>{var cursor=event.target.result;if(!cursor){return callback(null,{type:"remote",db:db,entries:entries})}entries[cursor.primaryKey]={"timestamp":cursor.key};cursor.continue()}}catch(e){return callback(e)}})},loadLocalEntry:(path,callback)=>{var stat,node;try{var lookup=FS.lookupPath(path);node=lookup.node;stat=FS.stat(path)}catch(e){return callback(e)}if(FS.isDir(stat.mode)){return callback(null,{"timestamp":stat.mtime,"mode":stat.mode})}else if(FS.isFile(stat.mode)){node.contents=MEMFS.getFileDataAsTypedArray(node);return callback(null,{"timestamp":stat.mtime,"mode":stat.mode,"contents":node.contents})}else{return callback(new Error("node type not supported"))}},storeLocalEntry:(path,entry,callback)=>{try{if(FS.isDir(entry["mode"])){FS.mkdirTree(path,entry["mode"])}else if(FS.isFile(entry["mode"])){FS.writeFile(path,entry["contents"],{canOwn:true})}else{return callback(new Error("node type not supported"))}FS.chmod(path,entry["mode"]);FS.utime(path,entry["timestamp"],entry["timestamp"])}catch(e){return callback(e)}callback(null)},removeLocalEntry:(path,callback)=>{try{var stat=FS.stat(path);if(FS.isDir(stat.mode)){FS.rmdir(path)}else if(FS.isFile(stat.mode)){FS.unlink(path)}}catch(e){return callback(e)}callback(null)},loadRemoteEntry:(store,path,callback)=>{var req=store.get(path);req.onsuccess=event=>{callback(null,event.target.result)};req.onerror=e=>{callback(this.error);e.preventDefault()}},storeRemoteEntry:(store,path,entry,callback)=>{try{var req=store.put(entry,path)}catch(e){callback(e);return}req.onsuccess=()=>{callback(null)};req.onerror=e=>{callback(this.error);e.preventDefault()}},removeRemoteEntry:(store,path,callback)=>{var req=store.delete(path);req.onsuccess=()=>{callback(null)};req.onerror=e=>{callback(this.error);e.preventDefault()}},reconcile:(src,dst,callback)=>{var total=0;var create=[];Object.keys(src.entries).forEach(function(key){var e=src.entries[key];var e2=dst.entries[key];if(!e2||e["timestamp"].getTime()!=e2["timestamp"].getTime()){create.push(key);total++}});var remove=[];Object.keys(dst.entries).forEach(function(key){if(!src.entries[key]){remove.push(key);total++}});if(!total){return callback(null)}var errored=false;var db=src.type==="remote"?src.db:dst.db;var transaction=db.transaction([IDBFS.DB_STORE_NAME],"readwrite");var store=transaction.objectStore(IDBFS.DB_STORE_NAME);function done(err){if(err&&!errored){errored=true;return callback(err)}}transaction.onerror=e=>{done(this.error);e.preventDefault()};transaction.oncomplete=e=>{if(!errored){callback(null)}};create.sort().forEach(path=>{if(dst.type==="local"){IDBFS.loadRemoteEntry(store,path,(err,entry)=>{if(err)return done(err);IDBFS.storeLocalEntry(path,entry,done)})}else{IDBFS.loadLocalEntry(path,(err,entry)=>{if(err)return done(err);IDBFS.storeRemoteEntry(store,path,entry,done)})}});remove.sort().reverse().forEach(path=>{if(dst.type==="local"){IDBFS.removeLocalEntry(path,done)}else{IDBFS.removeRemoteEntry(store,path,done)}})}};var FS={root:null,mounts:[],devices:{},streams:[],nextInode:1,nameTable:null,currentPath:"/",initialized:false,ignorePermissions:true,ErrnoError:null,genericErrors:{},filesystems:null,syncFSRequests:0,lookupPath:(path,opts={})=>{path=PATH_FS.resolve(path);if(!path)return{path:"",node:null};var defaults={follow_mount:true,recurse_count:0};opts=Object.assign(defaults,opts);if(opts.recurse_count>8){throw new FS.ErrnoError(32)}var parts=path.split("/").filter(p=>!!p);var current=FS.root;var current_path="/";for(var i=0;i<parts.length;i++){var islast=i===parts.length-1;if(islast&&opts.parent){break}current=FS.lookupNode(current,parts[i]);current_path=PATH.join2(current_path,parts[i]);if(FS.isMountpoint(current)){if(!islast||islast&&opts.follow_mount){current=current.mounted.root}}if(!islast||opts.follow){var count=0;while(FS.isLink(current.mode)){var link=FS.readlink(current_path);current_path=PATH_FS.resolve(PATH.dirname(current_path),link);var lookup=FS.lookupPath(current_path,{recurse_count:opts.recurse_count+1});current=lookup.node;if(count++>40){throw new FS.ErrnoError(32)}}}}return{path:current_path,node:current}},getPath:node=>{var path;while(true){if(FS.isRoot(node)){var mount=node.mount.mountpoint;if(!path)return mount;return mount[mount.length-1]!=="/"?`${mount}/${path}`:mount+path}path=path?`${node.name}/${path}`:node.name;node=node.parent}},hashName:(parentid,name)=>{var hash=0;for(var i=0;i<name.length;i++){hash=(hash<<5)-hash+name.charCodeAt(i)|0}return(parentid+hash>>>0)%FS.nameTable.length},hashAddNode:node=>{var hash=FS.hashName(node.parent.id,node.name);node.name_next=FS.nameTable[hash];FS.nameTable[hash]=node},hashRemoveNode:node=>{var hash=FS.hashName(node.parent.id,node.name);if(FS.nameTable[hash]===node){FS.nameTable[hash]=node.name_next}else{var current=FS.nameTable[hash];while(current){if(current.name_next===node){current.name_next=node.name_next;break}current=current.name_next}}},lookupNode:(parent,name)=>{var errCode=FS.mayLookup(parent);if(errCode){throw new FS.ErrnoError(errCode,parent)}var hash=FS.hashName(parent.id,name);for(var node=FS.nameTable[hash];node;node=node.name_next){var nodeName=node.name;if(node.parent.id===parent.id&&nodeName===name){return node}}return FS.lookup(parent,name)},createNode:(parent,name,mode,rdev)=>{var node=new FS.FSNode(parent,name,mode,rdev);FS.hashAddNode(node);return node},destroyNode:node=>{FS.hashRemoveNode(node)},isRoot:node=>{return node===node.parent},isMountpoint:node=>{return!!node.mounted},isFile:mode=>{return(mode&61440)===32768},isDir:mode=>{return(mode&61440)===16384},isLink:mode=>{return(mode&61440)===40960},isChrdev:mode=>{return(mode&61440)===8192},isBlkdev:mode=>{return(mode&61440)===24576},isFIFO:mode=>{return(mode&61440)===4096},isSocket:mode=>{return(mode&49152)===49152},flagsToPermissionString:flag=>{var perms=["r","w","rw"][flag&3];if(flag&512){perms+="w"}return perms},nodePermissions:(node,perms)=>{if(FS.ignorePermissions){return 0}if(perms.includes("r")&&!(node.mode&292)){return 2}else if(perms.includes("w")&&!(node.mode&146)){return 2}else if(perms.includes("x")&&!(node.mode&73)){return 2}return 0},mayLookup:dir=>{var errCode=FS.nodePermissions(dir,"x");if(errCode)return errCode;if(!dir.node_ops.lookup)return 2;return 0},mayCreate:(dir,name)=>{try{var node=FS.lookupNode(dir,name);return 20}catch(e){}return FS.nodePermissions(dir,"wx")},mayDelete:(dir,name,isdir)=>{var node;try{node=FS.lookupNode(dir,name)}catch(e){return e.errno}var errCode=FS.nodePermissions(dir,"wx");if(errCode){return errCode}if(isdir){if(!FS.isDir(node.mode)){return 54}if(FS.isRoot(node)||FS.getPath(node)===FS.cwd()){return 10}}else{if(FS.isDir(node.mode)){return 31}}return 0},mayOpen:(node,flags)=>{if(!node){return 44}if(FS.isLink(node.mode)){return 32}else if(FS.isDir(node.mode)){if(FS.flagsToPermissionString(flags)!=="r"||flags&512){return 31}}return FS.nodePermissions(node,FS.flagsToPermissionString(flags))},MAX_OPEN_FDS:4096,nextfd:()=>{for(var fd=0;fd<=FS.MAX_OPEN_FDS;fd++){if(!FS.streams[fd]){return fd}}throw new FS.ErrnoError(33)},getStream:fd=>FS.streams[fd],createStream:(stream,fd=-1)=>{if(!FS.FSStream){FS.FSStream=function(){this.shared={}};FS.FSStream.prototype={};Object.defineProperties(FS.FSStream.prototype,{object:{get:function(){return this.node},set:function(val){this.node=val}},isRead:{get:function(){return(this.flags&2097155)!==1}},isWrite:{get:function(){return(this.flags&2097155)!==0}},isAppend:{get:function(){return this.flags&1024}},flags:{get:function(){return this.shared.flags},set:function(val){this.shared.flags=val}},position:{get:function(){return this.shared.position},set:function(val){this.shared.position=val}}})}stream=Object.assign(new FS.FSStream,stream);if(fd==-1){fd=FS.nextfd()}stream.fd=fd;FS.streams[fd]=stream;return stream},closeStream:fd=>{FS.streams[fd]=null},chrdev_stream_ops:{open:stream=>{var device=FS.getDevice(stream.node.rdev);stream.stream_ops=device.stream_ops;if(stream.stream_ops.open){stream.stream_ops.open(stream)}},llseek:()=>{throw new FS.ErrnoError(70)}},major:dev=>dev>>8,minor:dev=>dev&255,makedev:(ma,mi)=>ma<<8|mi,registerDevice:(dev,ops)=>{FS.devices[dev]={stream_ops:ops}},getDevice:dev=>FS.devices[dev],getMounts:mount=>{var mounts=[];var check=[mount];while(check.length){var m=check.pop();mounts.push(m);check.push.apply(check,m.mounts)}return mounts},syncfs:(populate,callback)=>{if(typeof populate=="function"){callback=populate;populate=false}FS.syncFSRequests++;if(FS.syncFSRequests>1){err(`warning: ${FS.syncFSRequests} FS.syncfs operations in flight at once, probably just doing extra work`)}var mounts=FS.getMounts(FS.root.mount);var completed=0;function doCallback(errCode){FS.syncFSRequests--;return callback(errCode)}function done(errCode){if(errCode){if(!done.errored){done.errored=true;return doCallback(errCode)}return}if(++completed>=mounts.length){doCallback(null)}}mounts.forEach(mount=>{if(!mount.type.syncfs){return done(null)}mount.type.syncfs(mount,populate,done)})},mount:(type,opts,mountpoint)=>{var root=mountpoint==="/";var pseudo=!mountpoint;var node;if(root&&FS.root){throw new FS.ErrnoError(10)}else if(!root&&!pseudo){var lookup=FS.lookupPath(mountpoint,{follow_mount:false});mountpoint=lookup.path;node=lookup.node;if(FS.isMountpoint(node)){throw new FS.ErrnoError(10)}if(!FS.isDir(node.mode)){throw new FS.ErrnoError(54)}}var mount={type:type,opts:opts,mountpoint:mountpoint,mounts:[]};var mountRoot=type.mount(mount);mountRoot.mount=mount;mount.root=mountRoot;if(root){FS.root=mountRoot}else if(node){node.mounted=mount;if(node.mount){node.mount.mounts.push(mount)}}return mountRoot},unmount:mountpoint=>{var lookup=FS.lookupPath(mountpoint,{follow_mount:false});if(!FS.isMountpoint(lookup.node)){throw new FS.ErrnoError(28)}var node=lookup.node;var mount=node.mounted;var mounts=FS.getMounts(mount);Object.keys(FS.nameTable).forEach(hash=>{var current=FS.nameTable[hash];while(current){var next=current.name_next;if(mounts.includes(current.mount)){FS.destroyNode(current)}current=next}});node.mounted=null;var idx=node.mount.mounts.indexOf(mount);node.mount.mounts.splice(idx,1)},lookup:(parent,name)=>{return parent.node_ops.lookup(parent,name)},mknod:(path,mode,dev)=>{var lookup=FS.lookupPath(path,{parent:true});var parent=lookup.node;var name=PATH.basename(path);if(!name||name==="."||name===".."){throw new FS.ErrnoError(28)}var errCode=FS.mayCreate(parent,name);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.mknod){throw new FS.ErrnoError(63)}return parent.node_ops.mknod(parent,name,mode,dev)},create:(path,mode)=>{mode=mode!==undefined?mode:438;mode&=4095;mode|=32768;return FS.mknod(path,mode,0)},mkdir:(path,mode)=>{mode=mode!==undefined?mode:511;mode&=511|512;mode|=16384;return FS.mknod(path,mode,0)},mkdirTree:(path,mode)=>{var dirs=path.split("/");var d="";for(var i=0;i<dirs.length;++i){if(!dirs[i])continue;d+="/"+dirs[i];try{FS.mkdir(d,mode)}catch(e){if(e.errno!=20)throw e}}},mkdev:(path,mode,dev)=>{if(typeof dev=="undefined"){dev=mode;mode=438}mode|=8192;return FS.mknod(path,mode,dev)},symlink:(oldpath,newpath)=>{if(!PATH_FS.resolve(oldpath)){throw new FS.ErrnoError(44)}var lookup=FS.lookupPath(newpath,{parent:true});var parent=lookup.node;if(!parent){throw new FS.ErrnoError(44)}var newname=PATH.basename(newpath);var errCode=FS.mayCreate(parent,newname);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.symlink){throw new FS.ErrnoError(63)}return parent.node_ops.symlink(parent,newname,oldpath)},rename:(old_path,new_path)=>{var old_dirname=PATH.dirname(old_path);var new_dirname=PATH.dirname(new_path);var old_name=PATH.basename(old_path);var new_name=PATH.basename(new_path);var lookup,old_dir,new_dir;lookup=FS.lookupPath(old_path,{parent:true});old_dir=lookup.node;lookup=FS.lookupPath(new_path,{parent:true});new_dir=lookup.node;if(!old_dir||!new_dir)throw new FS.ErrnoError(44);if(old_dir.mount!==new_dir.mount){throw new FS.ErrnoError(75)}var old_node=FS.lookupNode(old_dir,old_name);var relative=PATH_FS.relative(old_path,new_dirname);if(relative.charAt(0)!=="."){throw new FS.ErrnoError(28)}relative=PATH_FS.relative(new_path,old_dirname);if(relative.charAt(0)!=="."){throw new FS.ErrnoError(55)}var new_node;try{new_node=FS.lookupNode(new_dir,new_name)}catch(e){}if(old_node===new_node){return}var isdir=FS.isDir(old_node.mode);var errCode=FS.mayDelete(old_dir,old_name,isdir);if(errCode){throw new FS.ErrnoError(errCode)}errCode=new_node?FS.mayDelete(new_dir,new_name,isdir):FS.mayCreate(new_dir,new_name);if(errCode){throw new FS.ErrnoError(errCode)}if(!old_dir.node_ops.rename){throw new FS.ErrnoError(63)}if(FS.isMountpoint(old_node)||new_node&&FS.isMountpoint(new_node)){throw new FS.ErrnoError(10)}if(new_dir!==old_dir){errCode=FS.nodePermissions(old_dir,"w");if(errCode){throw new FS.ErrnoError(errCode)}}FS.hashRemoveNode(old_node);try{old_dir.node_ops.rename(old_node,new_dir,new_name)}catch(e){throw e}finally{FS.hashAddNode(old_node)}},rmdir:path=>{var lookup=FS.lookupPath(path,{parent:true});var parent=lookup.node;var name=PATH.basename(path);var node=FS.lookupNode(parent,name);var errCode=FS.mayDelete(parent,name,true);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.rmdir){throw new FS.ErrnoError(63)}if(FS.isMountpoint(node)){throw new FS.ErrnoError(10)}parent.node_ops.rmdir(parent,name);FS.destroyNode(node)},readdir:path=>{var lookup=FS.lookupPath(path,{follow:true});var node=lookup.node;if(!node.node_ops.readdir){throw new FS.ErrnoError(54)}return node.node_ops.readdir(node)},unlink:path=>{var lookup=FS.lookupPath(path,{parent:true});var parent=lookup.node;if(!parent){throw new FS.ErrnoError(44)}var name=PATH.basename(path);var node=FS.lookupNode(parent,name);var errCode=FS.mayDelete(parent,name,false);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.unlink){throw new FS.ErrnoError(63)}if(FS.isMountpoint(node)){throw new FS.ErrnoError(10)}parent.node_ops.unlink(parent,name);FS.destroyNode(node)},readlink:path=>{var lookup=FS.lookupPath(path);var link=lookup.node;if(!link){throw new FS.ErrnoError(44)}if(!link.node_ops.readlink){throw new FS.ErrnoError(28)}return PATH_FS.resolve(FS.getPath(link.parent),link.node_ops.readlink(link))},stat:(path,dontFollow)=>{var lookup=FS.lookupPath(path,{follow:!dontFollow});var node=lookup.node;if(!node){throw new FS.ErrnoError(44)}if(!node.node_ops.getattr){throw new FS.ErrnoError(63)}return node.node_ops.getattr(node)},lstat:path=>{return FS.stat(path,true)},chmod:(path,mode,dontFollow)=>{var node;if(typeof path=="string"){var lookup=FS.lookupPath(path,{follow:!dontFollow});node=lookup.node}else{node=path}if(!node.node_ops.setattr){throw new FS.ErrnoError(63)}node.node_ops.setattr(node,{mode:mode&4095|node.mode&~4095,timestamp:Date.now()})},lchmod:(path,mode)=>{FS.chmod(path,mode,true)},fchmod:(fd,mode)=>{var stream=FS.getStream(fd);if(!stream){throw new FS.ErrnoError(8)}FS.chmod(stream.node,mode)},chown:(path,uid,gid,dontFollow)=>{var node;if(typeof path=="string"){var lookup=FS.lookupPath(path,{follow:!dontFollow});node=lookup.node}else{node=path}if(!node.node_ops.setattr){throw new FS.ErrnoError(63)}node.node_ops.setattr(node,{timestamp:Date.now()})},lchown:(path,uid,gid)=>{FS.chown(path,uid,gid,true)},fchown:(fd,uid,gid)=>{var stream=FS.getStream(fd);if(!stream){throw new FS.ErrnoError(8)}FS.chown(stream.node,uid,gid)},truncate:(path,len)=>{if(len<0){throw new FS.ErrnoError(28)}var node;if(typeof path=="string"){var lookup=FS.lookupPath(path,{follow:true});node=lookup.node}else{node=path}if(!node.node_ops.setattr){throw new FS.ErrnoError(63)}if(FS.isDir(node.mode)){throw new FS.ErrnoError(31)}if(!FS.isFile(node.mode)){throw new FS.ErrnoError(28)}var errCode=FS.nodePermissions(node,"w");if(errCode){throw new FS.ErrnoError(errCode)}node.node_ops.setattr(node,{size:len,timestamp:Date.now()})},ftruncate:(fd,len)=>{var stream=FS.getStream(fd);if(!stream){throw new FS.ErrnoError(8)}if((stream.flags&2097155)===0){throw new FS.ErrnoError(28)}FS.truncate(stream.node,len)},utime:(path,atime,mtime)=>{var lookup=FS.lookupPath(path,{follow:true});var node=lookup.node;node.node_ops.setattr(node,{timestamp:Math.max(atime,mtime)})},open:(path,flags,mode)=>{if(path===""){throw new FS.ErrnoError(44)}flags=typeof flags=="string"?FS_modeStringToFlags(flags):flags;mode=typeof mode=="undefined"?438:mode;if(flags&64){mode=mode&4095|32768}else{mode=0}var node;if(typeof path=="object"){node=path}else{path=PATH.normalize(path);try{var lookup=FS.lookupPath(path,{follow:!(flags&131072)});node=lookup.node}catch(e){}}var created=false;if(flags&64){if(node){if(flags&128){throw new FS.ErrnoError(20)}}else{node=FS.mknod(path,mode,0);created=true}}if(!node){throw new FS.ErrnoError(44)}if(FS.isChrdev(node.mode)){flags&=~512}if(flags&65536&&!FS.isDir(node.mode)){throw new FS.ErrnoError(54)}if(!created){var errCode=FS.mayOpen(node,flags);if(errCode){throw new FS.ErrnoError(errCode)}}if(flags&512&&!created){FS.truncate(node,0)}flags&=~(128|512|131072);var stream=FS.createStream({node:node,path:FS.getPath(node),flags:flags,seekable:true,position:0,stream_ops:node.stream_ops,ungotten:[],error:false});if(stream.stream_ops.open){stream.stream_ops.open(stream)}if(Module["logReadFiles"]&&!(flags&1)){if(!FS.readFiles)FS.readFiles={};if(!(path in FS.readFiles)){FS.readFiles[path]=1}}return stream},close:stream=>{if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if(stream.getdents)stream.getdents=null;try{if(stream.stream_ops.close){stream.stream_ops.close(stream)}}catch(e){throw e}finally{FS.closeStream(stream.fd)}stream.fd=null},isClosed:stream=>{return stream.fd===null},llseek:(stream,offset,whence)=>{if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if(!stream.seekable||!stream.stream_ops.llseek){throw new FS.ErrnoError(70)}if(whence!=0&&whence!=1&&whence!=2){throw new FS.ErrnoError(28)}stream.position=stream.stream_ops.llseek(stream,offset,whence);stream.ungotten=[];return stream.position},read:(stream,buffer,offset,length,position)=>{if(length<0||position<0){throw new FS.ErrnoError(28)}if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if((stream.flags&2097155)===1){throw new FS.ErrnoError(8)}if(FS.isDir(stream.node.mode)){throw new FS.ErrnoError(31)}if(!stream.stream_ops.read){throw new FS.ErrnoError(28)}var seeking=typeof position!="undefined";if(!seeking){position=stream.position}else if(!stream.seekable){throw new FS.ErrnoError(70)}var bytesRead=stream.stream_ops.read(stream,buffer,offset,length,position);if(!seeking)stream.position+=bytesRead;return bytesRead},write:(stream,buffer,offset,length,position,canOwn)=>{if(length<0||position<0){throw new FS.ErrnoError(28)}if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if((stream.flags&2097155)===0){throw new FS.ErrnoError(8)}if(FS.isDir(stream.node.mode)){throw new FS.ErrnoError(31)}if(!stream.stream_ops.write){throw new FS.ErrnoError(28)}if(stream.seekable&&stream.flags&1024){FS.llseek(stream,0,2)}var seeking=typeof position!="undefined";if(!seeking){position=stream.position}else if(!stream.seekable){throw new FS.ErrnoError(70)}var bytesWritten=stream.stream_ops.write(stream,buffer,offset,length,position,canOwn);if(!seeking)stream.position+=bytesWritten;return bytesWritten},allocate:(stream,offset,length)=>{if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if(offset<0||length<=0){throw new FS.ErrnoError(28)}if((stream.flags&2097155)===0){throw new FS.ErrnoError(8)}if(!FS.isFile(stream.node.mode)&&!FS.isDir(stream.node.mode)){throw new FS.ErrnoError(43)}if(!stream.stream_ops.allocate){throw new FS.ErrnoError(138)}stream.stream_ops.allocate(stream,offset,length)},mmap:(stream,length,position,prot,flags)=>{if((prot&2)!==0&&(flags&2)===0&&(stream.flags&2097155)!==2){throw new FS.ErrnoError(2)}if((stream.flags&2097155)===1){throw new FS.ErrnoError(2)}if(!stream.stream_ops.mmap){throw new FS.ErrnoError(43)}return stream.stream_ops.mmap(stream,length,position,prot,flags)},msync:(stream,buffer,offset,length,mmapFlags)=>{if(!stream.stream_ops.msync){return 0}return stream.stream_ops.msync(stream,buffer,offset,length,mmapFlags)},munmap:stream=>0,ioctl:(stream,cmd,arg)=>{if(!stream.stream_ops.ioctl){throw new FS.ErrnoError(59)}return stream.stream_ops.ioctl(stream,cmd,arg)},readFile:(path,opts={})=>{opts.flags=opts.flags||0;opts.encoding=opts.encoding||"binary";if(opts.encoding!=="utf8"&&opts.encoding!=="binary"){throw new Error(`Invalid encoding type "${opts.encoding}"`)}var ret;var stream=FS.open(path,opts.flags);var stat=FS.stat(path);var length=stat.size;var buf=new Uint8Array(length);FS.read(stream,buf,0,length,0);if(opts.encoding==="utf8"){ret=UTF8ArrayToString(buf,0)}else if(opts.encoding==="binary"){ret=buf}FS.close(stream);return ret},writeFile:(path,data,opts={})=>{opts.flags=opts.flags||577;var stream=FS.open(path,opts.flags,opts.mode);if(typeof data=="string"){var buf=new Uint8Array(lengthBytesUTF8(data)+1);var actualNumBytes=stringToUTF8Array(data,buf,0,buf.length);FS.write(stream,buf,0,actualNumBytes,undefined,opts.canOwn)}else if(ArrayBuffer.isView(data)){FS.write(stream,data,0,data.byteLength,undefined,opts.canOwn)}else{throw new Error("Unsupported data type")}FS.close(stream)},cwd:()=>FS.currentPath,chdir:path=>{var lookup=FS.lookupPath(path,{follow:true});if(lookup.node===null){throw new FS.ErrnoError(44)}if(!FS.isDir(lookup.node.mode)){throw new FS.ErrnoError(54)}var errCode=FS.nodePermissions(lookup.node,"x");if(errCode){throw new FS.ErrnoError(errCode)}FS.currentPath=lookup.path},createDefaultDirectories:()=>{FS.mkdir("/tmp");FS.mkdir("/home");FS.mkdir("/home/<USER>")},createDefaultDevices:()=>{FS.mkdir("/dev");FS.registerDevice(FS.makedev(1,3),{read:()=>0,write:(stream,buffer,offset,length,pos)=>length});FS.mkdev("/dev/null",FS.makedev(1,3));TTY.register(FS.makedev(5,0),TTY.default_tty_ops);TTY.register(FS.makedev(6,0),TTY.default_tty1_ops);FS.mkdev("/dev/tty",FS.makedev(5,0));FS.mkdev("/dev/tty1",FS.makedev(6,0));var randomBuffer=new Uint8Array(1024),randomLeft=0;var randomByte=()=>{if(randomLeft===0){randomLeft=randomFill(randomBuffer).byteLength}return randomBuffer[--randomLeft]};FS.createDevice("/dev","random",randomByte);FS.createDevice("/dev","urandom",randomByte);FS.mkdir("/dev/shm");FS.mkdir("/dev/shm/tmp")},createSpecialDirectories:()=>{FS.mkdir("/proc");var proc_self=FS.mkdir("/proc/self");FS.mkdir("/proc/self/fd");FS.mount({mount:()=>{var node=FS.createNode(proc_self,"fd",16384|511,73);node.node_ops={lookup:(parent,name)=>{var fd=+name;var stream=FS.getStream(fd);if(!stream)throw new FS.ErrnoError(8);var ret={parent:null,mount:{mountpoint:"fake"},node_ops:{readlink:()=>stream.path}};ret.parent=ret;return ret}};return node}},{},"/proc/self/fd")},createStandardStreams:()=>{if(Module["stdin"]){FS.createDevice("/dev","stdin",Module["stdin"])}else{FS.symlink("/dev/tty","/dev/stdin")}if(Module["stdout"]){FS.createDevice("/dev","stdout",null,Module["stdout"])}else{FS.symlink("/dev/tty","/dev/stdout")}if(Module["stderr"]){FS.createDevice("/dev","stderr",null,Module["stderr"])}else{FS.symlink("/dev/tty1","/dev/stderr")}var stdin=FS.open("/dev/stdin",0);var stdout=FS.open("/dev/stdout",1);var stderr=FS.open("/dev/stderr",1)},ensureErrnoError:()=>{if(FS.ErrnoError)return;FS.ErrnoError=function ErrnoError(errno,node){this.name="ErrnoError";this.node=node;this.setErrno=function(errno){this.errno=errno};this.setErrno(errno);this.message="FS error"};FS.ErrnoError.prototype=new Error;FS.ErrnoError.prototype.constructor=FS.ErrnoError;[44].forEach(code=>{FS.genericErrors[code]=new FS.ErrnoError(code);FS.genericErrors[code].stack="<generic error, no stack>"})},staticInit:()=>{FS.ensureErrnoError();FS.nameTable=new Array(4096);FS.mount(MEMFS,{},"/");FS.createDefaultDirectories();FS.createDefaultDevices();FS.createSpecialDirectories();FS.filesystems={"MEMFS":MEMFS,"IDBFS":IDBFS}},init:(input,output,error)=>{FS.init.initialized=true;FS.ensureErrnoError();Module["stdin"]=input||Module["stdin"];Module["stdout"]=output||Module["stdout"];Module["stderr"]=error||Module["stderr"];FS.createStandardStreams()},quit:()=>{FS.init.initialized=false;for(var i=0;i<FS.streams.length;i++){var stream=FS.streams[i];if(!stream){continue}FS.close(stream)}},findObject:(path,dontResolveLastLink)=>{var ret=FS.analyzePath(path,dontResolveLastLink);if(!ret.exists){return null}return ret.object},analyzePath:(path,dontResolveLastLink)=>{try{var lookup=FS.lookupPath(path,{follow:!dontResolveLastLink});path=lookup.path}catch(e){}var ret={isRoot:false,exists:false,error:0,name:null,path:null,object:null,parentExists:false,parentPath:null,parentObject:null};try{var lookup=FS.lookupPath(path,{parent:true});ret.parentExists=true;ret.parentPath=lookup.path;ret.parentObject=lookup.node;ret.name=PATH.basename(path);lookup=FS.lookupPath(path,{follow:!dontResolveLastLink});ret.exists=true;ret.path=lookup.path;ret.object=lookup.node;ret.name=lookup.node.name;ret.isRoot=lookup.path==="/"}catch(e){ret.error=e.errno}return ret},createPath:(parent,path,canRead,canWrite)=>{parent=typeof parent=="string"?parent:FS.getPath(parent);var parts=path.split("/").reverse();while(parts.length){var part=parts.pop();if(!part)continue;var current=PATH.join2(parent,part);try{FS.mkdir(current)}catch(e){}parent=current}return current},createFile:(parent,name,properties,canRead,canWrite)=>{var path=PATH.join2(typeof parent=="string"?parent:FS.getPath(parent),name);var mode=FS_getMode(canRead,canWrite);return FS.create(path,mode)},createDataFile:(parent,name,data,canRead,canWrite,canOwn)=>{var path=name;if(parent){parent=typeof parent=="string"?parent:FS.getPath(parent);path=name?PATH.join2(parent,name):parent}var mode=FS_getMode(canRead,canWrite);var node=FS.create(path,mode);if(data){if(typeof data=="string"){var arr=new Array(data.length);for(var i=0,len=data.length;i<len;++i)arr[i]=data.charCodeAt(i);data=arr}FS.chmod(node,mode|146);var stream=FS.open(node,577);FS.write(stream,data,0,data.length,0,canOwn);FS.close(stream);FS.chmod(node,mode)}return node},createDevice:(parent,name,input,output)=>{var path=PATH.join2(typeof parent=="string"?parent:FS.getPath(parent),name);var mode=FS_getMode(!!input,!!output);if(!FS.createDevice.major)FS.createDevice.major=64;var dev=FS.makedev(FS.createDevice.major++,0);FS.registerDevice(dev,{open:stream=>{stream.seekable=false},close:stream=>{if(output&&output.buffer&&output.buffer.length){output(10)}},read:(stream,buffer,offset,length,pos)=>{var bytesRead=0;for(var i=0;i<length;i++){var result;try{result=input()}catch(e){throw new FS.ErrnoError(29)}if(result===undefined&&bytesRead===0){throw new FS.ErrnoError(6)}if(result===null||result===undefined)break;bytesRead++;buffer[offset+i]=result}if(bytesRead){stream.node.timestamp=Date.now()}return bytesRead},write:(stream,buffer,offset,length,pos)=>{for(var i=0;i<length;i++){try{output(buffer[offset+i])}catch(e){throw new FS.ErrnoError(29)}}if(length){stream.node.timestamp=Date.now()}return i}});return FS.mkdev(path,mode,dev)},forceLoadFile:obj=>{if(obj.isDevice||obj.isFolder||obj.link||obj.contents)return true;if(typeof XMLHttpRequest!="undefined"){throw new Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.")}else if(read_){try{obj.contents=intArrayFromString(read_(obj.url),true);obj.usedBytes=obj.contents.length}catch(e){throw new FS.ErrnoError(29)}}else{throw new Error("Cannot load without read() or XMLHttpRequest.")}},createLazyFile:(parent,name,url,canRead,canWrite)=>{function LazyUint8Array(){this.lengthKnown=false;this.chunks=[]}LazyUint8Array.prototype.get=function LazyUint8Array_get(idx){if(idx>this.length-1||idx<0){return undefined}var chunkOffset=idx%this.chunkSize;var chunkNum=idx/this.chunkSize|0;return this.getter(chunkNum)[chunkOffset]};LazyUint8Array.prototype.setDataGetter=function LazyUint8Array_setDataGetter(getter){this.getter=getter};LazyUint8Array.prototype.cacheLength=function LazyUint8Array_cacheLength(){var xhr=new XMLHttpRequest;xhr.open("HEAD",url,false);xhr.send(null);if(!(xhr.status>=200&&xhr.status<300||xhr.status===304))throw new Error("Couldn't load "+url+". Status: "+xhr.status);var datalength=Number(xhr.getResponseHeader("Content-length"));var header;var hasByteServing=(header=xhr.getResponseHeader("Accept-Ranges"))&&header==="bytes";var usesGzip=(header=xhr.getResponseHeader("Content-Encoding"))&&header==="gzip";var chunkSize=1024*1024;if(!hasByteServing)chunkSize=datalength;var doXHR=(from,to)=>{if(from>to)throw new Error("invalid range ("+from+", "+to+") or no bytes requested!");if(to>datalength-1)throw new Error("only "+datalength+" bytes available! programmer error!");var xhr=new XMLHttpRequest;xhr.open("GET",url,false);if(datalength!==chunkSize)xhr.setRequestHeader("Range","bytes="+from+"-"+to);xhr.responseType="arraybuffer";if(xhr.overrideMimeType){xhr.overrideMimeType("text/plain; charset=x-user-defined")}xhr.send(null);if(!(xhr.status>=200&&xhr.status<300||xhr.status===304))throw new Error("Couldn't load "+url+". Status: "+xhr.status);if(xhr.response!==undefined){return new Uint8Array(xhr.response||[])}return intArrayFromString(xhr.responseText||"",true)};var lazyArray=this;lazyArray.setDataGetter(chunkNum=>{var start=chunkNum*chunkSize;var end=(chunkNum+1)*chunkSize-1;end=Math.min(end,datalength-1);if(typeof lazyArray.chunks[chunkNum]=="undefined"){lazyArray.chunks[chunkNum]=doXHR(start,end)}if(typeof lazyArray.chunks[chunkNum]=="undefined")throw new Error("doXHR failed!");return lazyArray.chunks[chunkNum]});if(usesGzip||!datalength){chunkSize=datalength=1;datalength=this.getter(0).length;chunkSize=datalength;out("LazyFiles on gzip forces download of the whole file when length is accessed")}this._length=datalength;this._chunkSize=chunkSize;this.lengthKnown=true};if(typeof XMLHttpRequest!="undefined"){if(!ENVIRONMENT_IS_WORKER)throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";var lazyArray=new LazyUint8Array;Object.defineProperties(lazyArray,{length:{get:function(){if(!this.lengthKnown){this.cacheLength()}return this._length}},chunkSize:{get:function(){if(!this.lengthKnown){this.cacheLength()}return this._chunkSize}}});var properties={isDevice:false,contents:lazyArray}}else{var properties={isDevice:false,url:url}}var node=FS.createFile(parent,name,properties,canRead,canWrite);if(properties.contents){node.contents=properties.contents}else if(properties.url){node.contents=null;node.url=properties.url}Object.defineProperties(node,{usedBytes:{get:function(){return this.contents.length}}});var stream_ops={};var keys=Object.keys(node.stream_ops);keys.forEach(key=>{var fn=node.stream_ops[key];stream_ops[key]=function forceLoadLazyFile(){FS.forceLoadFile(node);return fn.apply(null,arguments)}});function writeChunks(stream,buffer,offset,length,position){var contents=stream.node.contents;if(position>=contents.length)return 0;var size=Math.min(contents.length-position,length);if(contents.slice){for(var i=0;i<size;i++){buffer[offset+i]=contents[position+i]}}else{for(var i=0;i<size;i++){buffer[offset+i]=contents.get(position+i)}}return size}stream_ops.read=(stream,buffer,offset,length,position)=>{FS.forceLoadFile(node);return writeChunks(stream,buffer,offset,length,position)};stream_ops.mmap=(stream,length,position,prot,flags)=>{FS.forceLoadFile(node);var ptr=mmapAlloc(length);if(!ptr){throw new FS.ErrnoError(48)}writeChunks(stream,GROWABLE_HEAP_I8(),ptr,length,position);return{ptr:ptr,allocated:true}};node.stream_ops=stream_ops;return node}};function UTF8ToString(ptr,maxBytesToRead){return ptr?UTF8ArrayToString(GROWABLE_HEAP_U8(),ptr,maxBytesToRead):""}var SYSCALLS={DEFAULT_POLLMASK:5,calculateAt:function(dirfd,path,allowEmpty){if(PATH.isAbs(path)){return path}var dir;if(dirfd===-100){dir=FS.cwd()}else{var dirstream=SYSCALLS.getStreamFromFD(dirfd);dir=dirstream.path}if(path.length==0){if(!allowEmpty){throw new FS.ErrnoError(44)}return dir}return PATH.join2(dir,path)},doStat:function(func,path,buf){try{var stat=func(path)}catch(e){if(e&&e.node&&PATH.normalize(path)!==PATH.normalize(FS.getPath(e.node))){return-54}throw e}GROWABLE_HEAP_I32()[buf>>2]=stat.dev;GROWABLE_HEAP_I32()[buf+8>>2]=stat.ino;GROWABLE_HEAP_I32()[buf+12>>2]=stat.mode;GROWABLE_HEAP_U32()[buf+16>>2]=stat.nlink;GROWABLE_HEAP_I32()[buf+20>>2]=stat.uid;GROWABLE_HEAP_I32()[buf+24>>2]=stat.gid;GROWABLE_HEAP_I32()[buf+28>>2]=stat.rdev;tempI64=[stat.size>>>0,(tempDouble=stat.size,+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],GROWABLE_HEAP_I32()[buf+40>>2]=tempI64[0],GROWABLE_HEAP_I32()[buf+44>>2]=tempI64[1];GROWABLE_HEAP_I32()[buf+48>>2]=4096;GROWABLE_HEAP_I32()[buf+52>>2]=stat.blocks;var atime=stat.atime.getTime();var mtime=stat.mtime.getTime();var ctime=stat.ctime.getTime();tempI64=[Math.floor(atime/1e3)>>>0,(tempDouble=Math.floor(atime/1e3),+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],GROWABLE_HEAP_I32()[buf+56>>2]=tempI64[0],GROWABLE_HEAP_I32()[buf+60>>2]=tempI64[1];GROWABLE_HEAP_U32()[buf+64>>2]=atime%1e3*1e3;tempI64=[Math.floor(mtime/1e3)>>>0,(tempDouble=Math.floor(mtime/1e3),+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],GROWABLE_HEAP_I32()[buf+72>>2]=tempI64[0],GROWABLE_HEAP_I32()[buf+76>>2]=tempI64[1];GROWABLE_HEAP_U32()[buf+80>>2]=mtime%1e3*1e3;tempI64=[Math.floor(ctime/1e3)>>>0,(tempDouble=Math.floor(ctime/1e3),+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],GROWABLE_HEAP_I32()[buf+88>>2]=tempI64[0],GROWABLE_HEAP_I32()[buf+92>>2]=tempI64[1];GROWABLE_HEAP_U32()[buf+96>>2]=ctime%1e3*1e3;tempI64=[stat.ino>>>0,(tempDouble=stat.ino,+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],GROWABLE_HEAP_I32()[buf+104>>2]=tempI64[0],GROWABLE_HEAP_I32()[buf+108>>2]=tempI64[1];return 0},doMsync:function(addr,stream,len,flags,offset){if(!FS.isFile(stream.node.mode)){throw new FS.ErrnoError(43)}if(flags&2){return 0}var buffer=GROWABLE_HEAP_U8().slice(addr,addr+len);FS.msync(stream,buffer,offset,len,flags)},varargs:undefined,get:function(){SYSCALLS.varargs+=4;var ret=GROWABLE_HEAP_I32()[SYSCALLS.varargs-4>>2];return ret},getStr:function(ptr){var ret=UTF8ToString(ptr);return ret},getStreamFromFD:function(fd){var stream=FS.getStream(fd);if(!stream)throw new FS.ErrnoError(8);return stream}};function _proc_exit(code){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(1,1,code);EXITSTATUS=code;if(!keepRuntimeAlive()){PThread.terminateAllThreads();if(Module["onExit"])Module["onExit"](code);ABORT=true}quit_(code,new ExitStatus(code))}_proc_exit.sig="vi";function exitJS(status,implicit){EXITSTATUS=status;if(ENVIRONMENT_IS_PTHREAD){exitOnMainThread(status);throw"unwind"}_proc_exit(status)}var _exit=exitJS;_exit.sig="vi";function handleException(e){if(e instanceof ExitStatus||e=="unwind"){return EXITSTATUS}quit_(1,e)}var PThread={unusedWorkers:[],runningWorkers:[],tlsInitFunctions:[],pthreads:{},init:function(){if(ENVIRONMENT_IS_PTHREAD){PThread.initWorker()}else{PThread.initMainThread()}},initMainThread:function(){addOnPreRun(()=>{addRunDependency("loading-workers");PThread.loadWasmModuleToAllWorkers(()=>removeRunDependency("loading-workers"))});PThread.outstandingPromises={};PThread.finishedThreads=new Set},initWorker:function(){noExitRuntime=false},setExitStatus:function(status){EXITSTATUS=status},terminateAllThreads__deps:["$terminateWorker"],terminateAllThreads:function(){for(var worker of PThread.runningWorkers){terminateWorker(worker)}for(var worker of PThread.unusedWorkers){terminateWorker(worker)}PThread.unusedWorkers=[];PThread.runningWorkers=[];PThread.pthreads=[]},returnWorkerToPool:function(worker){var pthread_ptr=worker.pthread_ptr;delete PThread.pthreads[pthread_ptr];PThread.unusedWorkers.push(worker);PThread.runningWorkers.splice(PThread.runningWorkers.indexOf(worker),1);worker.pthread_ptr=0;__emscripten_thread_free_data(pthread_ptr)},receiveObjectTransfer:function(data){},threadInitTLS:function(){PThread.tlsInitFunctions.forEach(f=>f())},loadWasmModuleToWorker:worker=>new Promise(onFinishedLoading=>{worker.onmessage=e=>{var d=e["data"];var cmd=d["cmd"];if(worker.pthread_ptr)PThread.currentProxiedOperationCallerThread=worker.pthread_ptr;if(d["targetThread"]&&d["targetThread"]!=_pthread_self()){var targetWorker=PThread.pthreads[d.targetThread];if(targetWorker){targetWorker.postMessage(d,d["transferList"])}else{err('Internal error! Worker sent a message "'+cmd+'" to target pthread '+d["targetThread"]+", but that thread no longer exists!")}PThread.currentProxiedOperationCallerThread=undefined;return}if(cmd==="checkMailbox"){checkMailbox()}else if(cmd==="spawnThread"){spawnThread(d)}else if(cmd==="cleanupThread"){cleanupThread(d["thread"])}else if(cmd==="markAsFinshed"){markAsFinshed(d["thread"])}else if(cmd==="killThread"){killThread(d["thread"])}else if(cmd==="cancelThread"){cancelThread(d["thread"])}else if(cmd==="loaded"){worker.loaded=true;onFinishedLoading(worker)}else if(cmd==="print"){out("Thread "+d["threadId"]+": "+d["text"])}else if(cmd==="printErr"){err("Thread "+d["threadId"]+": "+d["text"])}else if(cmd==="alert"){alert("Thread "+d["threadId"]+": "+d["text"])}else if(d.target==="setimmediate"){worker.postMessage(d)}else if(cmd==="callHandler"){Module[d["handler"]](...d["args"])}else if(cmd){err("worker sent an unknown command "+cmd)}PThread.currentProxiedOperationCallerThread=undefined};worker.onerror=e=>{var message="worker sent an error!";err(message+" "+e.filename+":"+e.lineno+": "+e.message);throw e};if(ENVIRONMENT_IS_NODE){worker.on("message",function(data){worker.onmessage({data:data})});worker.on("error",function(e){worker.onerror(e)})}var handlers=[];var knownHandlers=["onExit","onAbort","print","printErr"];for(var handler of knownHandlers){if(Module.hasOwnProperty(handler)){handlers.push(handler)}}worker.postMessage({"cmd":"load","handlers":handlers,"urlOrBlob":Module["mainScriptUrlOrBlob"]||_scriptDir,"wasmMemory":wasmMemory,"wasmModule":wasmModule,"sharedModules":sharedModules})}),loadWasmModuleToAllWorkers:function(onMaybeReady){onMaybeReady()},allocateUnusedWorker:function(){var worker;var pthreadMainJs=locateFile("dragonruby-wasm.worker.js");worker=new Worker(pthreadMainJs);PThread.unusedWorkers.push(worker)},getNewWorker:function(){if(PThread.unusedWorkers.length==0){PThread.allocateUnusedWorker();PThread.loadWasmModuleToWorker(PThread.unusedWorkers[0])}return PThread.unusedWorkers.pop()}};Module["PThread"]=PThread;function listenOnce(object,event,func){object.addEventListener(event,func,{"once":true})}function autoResumeAudioContext(ctx,elements){if(!elements){elements=[document,document.getElementById("canvas")]}["keydown","mousedown","touchstart"].forEach(event=>{elements.forEach(element=>{if(element){listenOnce(element,event,()=>{if(ctx.state==="suspended")ctx.resume()})}})})}function callRuntimeCallbacks(callbacks){while(callbacks.length>0){callbacks.shift()(Module)}}function dynCallLegacy(sig,ptr,args){var f=Module["dynCall_"+sig];return args&&args.length?f.apply(null,[ptr].concat(args)):f.call(null,ptr)}var wasmTableMirror=[];function getWasmTableEntry(funcPtr){var func=wasmTableMirror[funcPtr];if(!func){if(funcPtr>=wasmTableMirror.length)wasmTableMirror.length=funcPtr+1;wasmTableMirror[funcPtr]=func=wasmTable.get(funcPtr)}return func}function dynCall(sig,ptr,args){if(sig.includes("j")){return dynCallLegacy(sig,ptr,args)}var rtn=getWasmTableEntry(ptr).apply(null,args);return rtn}function establishStackSpace(){var pthread_ptr=_pthread_self();var stackHigh=GROWABLE_HEAP_I32()[pthread_ptr+52>>2];var stackSize=GROWABLE_HEAP_I32()[pthread_ptr+56>>2];var stackLow=stackHigh-stackSize;_emscripten_stack_set_limits(stackHigh,stackLow);stackRestore(stackHigh)}Module["establishStackSpace"]=establishStackSpace;function exitOnMainThread(returnCode){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(2,0,returnCode);_exit(returnCode)}function getDylinkMetadata(binary){var offset=0;var end=0;function getU8(){return binary[offset++]}function getLEB(){var ret=0;var mul=1;while(1){var byte=binary[offset++];ret+=(byte&127)*mul;mul*=128;if(!(byte&128))break}return ret}function getString(){var len=getLEB();offset+=len;return UTF8ArrayToString(binary,offset-len,len)}function failIf(condition,message){if(condition)throw new Error(message)}var name="dylink.0";if(binary instanceof WebAssembly.Module){var dylinkSection=WebAssembly.Module.customSections(binary,name);if(dylinkSection.length===0){name="dylink";dylinkSection=WebAssembly.Module.customSections(binary,name)}failIf(dylinkSection.length===0,"need dylink section");binary=new Uint8Array(dylinkSection[0]);end=binary.length}else{var int32View=new Uint32Array(new Uint8Array(binary.subarray(0,24)).buffer);var magicNumberFound=int32View[0]==1836278016;failIf(!magicNumberFound,"need to see wasm magic number");failIf(binary[8]!==0,"need the dylink section to be first");offset=9;var section_size=getLEB();end=offset+section_size;name=getString()}var customSection={neededDynlibs:[],tlsExports:new Set,weakImports:new Set};if(name=="dylink"){customSection.memorySize=getLEB();customSection.memoryAlign=getLEB();customSection.tableSize=getLEB();customSection.tableAlign=getLEB();var neededDynlibsCount=getLEB();for(var i=0;i<neededDynlibsCount;++i){var libname=getString();customSection.neededDynlibs.push(libname)}}else{failIf(name!=="dylink.0");var WASM_DYLINK_MEM_INFO=1;var WASM_DYLINK_NEEDED=2;var WASM_DYLINK_EXPORT_INFO=3;var WASM_DYLINK_IMPORT_INFO=4;var WASM_SYMBOL_TLS=256;var WASM_SYMBOL_BINDING_MASK=3;var WASM_SYMBOL_BINDING_WEAK=1;while(offset<end){var subsectionType=getU8();var subsectionSize=getLEB();if(subsectionType===WASM_DYLINK_MEM_INFO){customSection.memorySize=getLEB();customSection.memoryAlign=getLEB();customSection.tableSize=getLEB();customSection.tableAlign=getLEB()}else if(subsectionType===WASM_DYLINK_NEEDED){var neededDynlibsCount=getLEB();for(var i=0;i<neededDynlibsCount;++i){libname=getString();customSection.neededDynlibs.push(libname)}}else if(subsectionType===WASM_DYLINK_EXPORT_INFO){var count=getLEB();while(count--){var symname=getString();var flags=getLEB();if(flags&WASM_SYMBOL_TLS){customSection.tlsExports.add(symname)}}}else if(subsectionType===WASM_DYLINK_IMPORT_INFO){var count=getLEB();while(count--){var modname=getString();var symname=getString();var flags=getLEB();if((flags&WASM_SYMBOL_BINDING_MASK)==WASM_SYMBOL_BINDING_WEAK){customSection.weakImports.add(symname)}}}else{offset+=subsectionSize}}}return customSection}function getValue(ptr,type="i8"){if(type.endsWith("*"))type="*";switch(type){case"i1":return GROWABLE_HEAP_I8()[ptr>>0];case"i8":return GROWABLE_HEAP_I8()[ptr>>0];case"i16":return GROWABLE_HEAP_I16()[ptr>>1];case"i32":return GROWABLE_HEAP_I32()[ptr>>2];case"i64":abort("to do getValue(i64) use WASM_BIGINT");case"float":return GROWABLE_HEAP_F32()[ptr>>2];case"double":return GROWABLE_HEAP_F64()[ptr>>3];case"*":return GROWABLE_HEAP_U32()[ptr>>2];default:abort(`invalid type for getValue: ${type}`)}}function invokeEntryPoint(ptr,arg){__emscripten_dlsync_self();var result=getWasmTableEntry(ptr)(arg);if(keepRuntimeAlive()){PThread.setExitStatus(result)}else{__emscripten_thread_exit(result)}}Module["invokeEntryPoint"]=invokeEntryPoint;function newDSO(name,handle,syms){var dso={refcount:Infinity,name:name,exports:syms,global:true};LDSO.loadedLibsByName[name]=dso;if(handle!=undefined){LDSO.loadedLibsByHandle[handle]=dso}return dso}var LDSO={loadedLibsByName:{},loadedLibsByHandle:{},init:()=>{newDSO("__main__",0,wasmImports)}};var ___heap_base=1359584;function getMemory(size){if(runtimeInitialized){return zeroMemory(_malloc(size),size)}var ret=___heap_base;var end=ret+size+15&-16;___heap_base=end;GOT["__heap_base"].value=end;return ret}function isInternalSym(symName){return["__cpp_exception","__c_longjmp","__wasm_apply_data_relocs","__dso_handle","__tls_size","__tls_align","__set_stack_limits","_emscripten_tls_init","__wasm_init_tls","__wasm_call_ctors","__start_em_asm","__stop_em_asm"].includes(symName)}function uleb128Encode(n,target){if(n<128){target.push(n)}else{target.push(n%128|128,n>>7)}}function sigToWasmTypes(sig){var typeNames={"i":"i32","j":"i64","f":"f32","d":"f64","p":"i32"};var type={parameters:[],results:sig[0]=="v"?[]:[typeNames[sig[0]]]};for(var i=1;i<sig.length;++i){type.parameters.push(typeNames[sig[i]])}return type}function generateFuncType(sig,target){var sigRet=sig.slice(0,1);var sigParam=sig.slice(1);var typeCodes={"i":127,"p":127,"j":126,"f":125,"d":124};target.push(96);uleb128Encode(sigParam.length,target);for(var i=0;i<sigParam.length;++i){target.push(typeCodes[sigParam[i]])}if(sigRet=="v"){target.push(0)}else{target.push(1,typeCodes[sigRet])}}function convertJsFunctionToWasm(func,sig){if(typeof WebAssembly.Function=="function"){return new WebAssembly.Function(sigToWasmTypes(sig),func)}var typeSectionBody=[1];generateFuncType(sig,typeSectionBody);var bytes=[0,97,115,109,1,0,0,0,1];uleb128Encode(typeSectionBody.length,bytes);bytes.push.apply(bytes,typeSectionBody);bytes.push(2,7,1,1,101,1,102,0,0,7,5,1,1,102,0,0);var module=new WebAssembly.Module(new Uint8Array(bytes));var instance=new WebAssembly.Instance(module,{"e":{"f":func}});var wrappedFunc=instance.exports["f"];return wrappedFunc}function updateTableMap(offset,count){if(functionsInTableMap){for(var i=offset;i<offset+count;i++){var item=getWasmTableEntry(i);if(item){functionsInTableMap.set(item,i)}}}}var functionsInTableMap=undefined;function getFunctionAddress(func){if(!functionsInTableMap){functionsInTableMap=new WeakMap;updateTableMap(0,wasmTable.length)}return functionsInTableMap.get(func)||0}var freeTableIndexes=[];function getEmptyTableSlot(){if(freeTableIndexes.length){return freeTableIndexes.pop()}try{wasmTable.grow(1)}catch(err){if(!(err instanceof RangeError)){throw err}throw"Unable to grow wasm table. Set ALLOW_TABLE_GROWTH."}return wasmTable.length-1}function setWasmTableEntry(idx,func){wasmTable.set(idx,func);wasmTableMirror[idx]=wasmTable.get(idx)}function addFunction(func,sig){var rtn=getFunctionAddress(func);if(rtn){return rtn}var ret=getEmptyTableSlot();try{setWasmTableEntry(ret,func)}catch(err){if(!(err instanceof TypeError)){throw err}var wrapped=convertJsFunctionToWasm(func,sig);setWasmTableEntry(ret,wrapped)}functionsInTableMap.set(func,ret);return ret}function updateGOT(exports,replace){for(var symName in exports){if(isInternalSym(symName)){continue}var value=exports[symName];if(symName.startsWith("orig$")){symName=symName.split("$")[1];replace=true}if(!GOT[symName]){GOT[symName]=new WebAssembly.Global({"value":"i32","mutable":true})}if(replace||GOT[symName].value==0){if(typeof value=="function"){GOT[symName].value=addFunction(value)}else if(typeof value=="number"){GOT[symName].value=value}else{err(`unhandled export type for '${symName}': ${typeof value}`)}}}}function relocateExports(exports,memoryBase,replace){var relocated={};for(var e in exports){var value=exports[e];if(typeof value=="object"){value=value.value}if(typeof value=="number"){value+=memoryBase}relocated[e]=value}updateGOT(relocated,replace);return relocated}function isSymbolDefined(symName){var existing=wasmImports[symName];if(!existing||existing.stub){return false}return true}function createInvokeFunction(sig){return function(){var sp=stackSave();try{return dynCall(sig,arguments[0],Array.prototype.slice.call(arguments,1))}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}}function resolveGlobalSymbol(symName,direct=false){var sym;if(direct&&"orig$"+symName in wasmImports){symName="orig$"+symName}if(isSymbolDefined(symName)){sym=wasmImports[symName]}else if(symName.startsWith("invoke_")){sym=wasmImports[symName]=createInvokeFunction(symName.split("_")[1])}return{sym:sym,name:symName}}function loadWebAssemblyModule(binary,flags,libName,localScope,handle){var metadata=getDylinkMetadata(binary);currentModuleWeakSymbols=metadata.weakImports;function loadModule(){var firstLoad=!handle||!GROWABLE_HEAP_I8()[handle+8>>0];if(firstLoad){var memAlign=Math.pow(2,metadata.memoryAlign);memAlign=Math.max(memAlign,16);var memoryBase=metadata.memorySize?alignMemory(getMemory(metadata.memorySize+memAlign),memAlign):0;var tableBase=metadata.tableSize?wasmTable.length:0;if(handle){GROWABLE_HEAP_I8()[handle+8>>0]=1;GROWABLE_HEAP_U32()[handle+12>>2]=memoryBase;GROWABLE_HEAP_I32()[handle+16>>2]=metadata.memorySize;GROWABLE_HEAP_U32()[handle+20>>2]=tableBase;GROWABLE_HEAP_I32()[handle+24>>2]=metadata.tableSize}}else{memoryBase=GROWABLE_HEAP_U32()[handle+12>>2];tableBase=GROWABLE_HEAP_U32()[handle+20>>2]}var tableGrowthNeeded=tableBase+metadata.tableSize-wasmTable.length;if(tableGrowthNeeded>0){wasmTable.grow(tableGrowthNeeded)}var moduleExports;function resolveSymbol(sym){var resolved=resolveGlobalSymbol(sym).sym;if(!resolved&&localScope){resolved=localScope[sym]}if(!resolved){resolved=moduleExports[sym]}return resolved}var proxyHandler={"get":function(stubs,prop){switch(prop){case"__memory_base":return memoryBase;case"__table_base":return tableBase}if(prop in wasmImports&&!wasmImports[prop].stub){return wasmImports[prop]}if(!(prop in stubs)){var resolved;stubs[prop]=function(){if(!resolved)resolved=resolveSymbol(prop);return resolved.apply(null,arguments)}}return stubs[prop]}};var proxy=new Proxy({},proxyHandler);var info={"GOT.mem":new Proxy({},GOTHandler),"GOT.func":new Proxy({},GOTHandler),"env":proxy,"wasi_snapshot_preview1":proxy};function postInstantiation(module,instance){if(!ENVIRONMENT_IS_PTHREAD&&libName){sharedModules[libName]=module}updateTableMap(tableBase,metadata.tableSize);moduleExports=relocateExports(instance.exports,memoryBase);if(!flags.allowUndefined){reportUndefinedSymbols()}function addEmAsm(addr,body){var args=[];var arity=0;for(;arity<16;arity++){if(body.indexOf("$"+arity)!=-1){args.push("$"+arity)}else{break}}args=args.join(",");var func=`(${args}) => { ${body} };`;ASM_CONSTS[start]=eval(func)}if("__start_em_asm"in moduleExports){var start=moduleExports["__start_em_asm"];var stop=moduleExports["__stop_em_asm"];while(start<stop){var jsString=UTF8ToString(start);addEmAsm(start,jsString);start=GROWABLE_HEAP_U8().indexOf(0,start)+1}}registerTLSInit(moduleExports["_emscripten_tls_init"],instance.exports,metadata);if(firstLoad){var applyRelocs=moduleExports["__wasm_apply_data_relocs"];if(applyRelocs){if(runtimeInitialized){applyRelocs()}else{__RELOC_FUNCS__.push(applyRelocs)}}var init=moduleExports["__wasm_call_ctors"];if(init){if(runtimeInitialized){init()}else{__ATINIT__.push(init)}}}return moduleExports}if(flags.loadAsync){if(binary instanceof WebAssembly.Module){var instance=new WebAssembly.Instance(binary,info);return Promise.resolve(postInstantiation(binary,instance))}return WebAssembly.instantiate(binary,info).then(result=>postInstantiation(result.module,result.instance))}var module=binary instanceof WebAssembly.Module?binary:new WebAssembly.Module(binary);var instance=new WebAssembly.Instance(module,info);return postInstantiation(module,instance)}if(flags.loadAsync){return metadata.neededDynlibs.reduce((chain,dynNeeded)=>{return chain.then(()=>{return loadDynamicLibrary(dynNeeded,flags)})},Promise.resolve()).then(loadModule)}metadata.neededDynlibs.forEach(needed=>loadDynamicLibrary(needed,flags,localScope));return loadModule()}function mergeLibSymbols(exports,libName){for(var sym in exports){if(!exports.hasOwnProperty(sym)){continue}const setImport=target=>{if(!isSymbolDefined(target)){wasmImports[target]=exports[sym]}};setImport(sym);const main_alias="__main_argc_argv";if(sym=="main"){setImport(main_alias)}if(sym==main_alias){setImport("main")}if(sym.startsWith("dynCall_")&&!Module.hasOwnProperty(sym)){Module[sym]=exports[sym]}}}function registerWasmPlugin(){var wasmPlugin={"promiseChainEnd":Promise.resolve(),"canHandle":function(name){return!Module.noWasmDecoding&&name.endsWith(".so")},"handle":function(byteArray,name,onload,onerror){wasmPlugin["promiseChainEnd"]=wasmPlugin["promiseChainEnd"].then(()=>loadWebAssemblyModule(byteArray,{loadAsync:true,nodelete:true},name)).then(exports=>{preloadedWasm[name]=exports;onload(byteArray)},error=>{err(`failed to instantiate wasm: ${name}: ${error}`);onerror()})}};preloadPlugins.push(wasmPlugin)}var preloadedWasm={};function loadDynamicLibrary(libName,flags={global:true,nodelete:true},localScope,handle){var dso=LDSO.loadedLibsByName[libName];if(dso){if(flags.global&&!dso.global){dso.global=true;if(dso.exports!=="loading"){mergeLibSymbols(dso.exports,libName)}}if(flags.nodelete&&dso.refcount!==Infinity){dso.refcount=Infinity}dso.refcount++;if(handle){LDSO.loadedLibsByHandle[handle]=dso}return flags.loadAsync?Promise.resolve(true):true}dso=newDSO(libName,handle,"loading");dso.refcount=flags.nodelete?Infinity:1;dso.global=flags.global;function loadLibData(){var sharedMod=sharedModules[libName];if(sharedMod){return flags.loadAsync?Promise.resolve(sharedMod):sharedMod}if(handle){var data=GROWABLE_HEAP_U32()[handle+28>>2];var dataSize=GROWABLE_HEAP_U32()[handle+32>>2];if(data&&dataSize){var libData=GROWABLE_HEAP_I8().slice(data,data+dataSize);return flags.loadAsync?Promise.resolve(libData):libData}}var libFile=locateFile(libName);if(flags.loadAsync){return new Promise(function(resolve,reject){asyncLoad(libFile,data=>resolve(data),reject)})}if(!readBinary){throw new Error(`${libFile}: file not found, and synchronous loading of external files is not available`)}return readBinary(libFile)}function getExports(){var preloaded=preloadedWasm[libName];if(preloaded){return flags.loadAsync?Promise.resolve(preloaded):preloaded}if(flags.loadAsync){return loadLibData().then(libData=>loadWebAssemblyModule(libData,flags,libName,localScope,handle))}return loadWebAssemblyModule(loadLibData(),flags,libName,localScope,handle)}function moduleLoaded(exports){if(dso.global){mergeLibSymbols(exports,libName)}else if(localScope){Object.assign(localScope,exports)}dso.exports=exports}if(flags.loadAsync){return getExports().then(exports=>{moduleLoaded(exports);return true})}moduleLoaded(getExports());return true}function reportUndefinedSymbols(){for(var symName in GOT){if(GOT[symName].value==0){var value=resolveGlobalSymbol(symName,true).sym;if(!value&&!GOT[symName].required){continue}if(typeof value=="function"){GOT[symName].value=addFunction(value,value.sig)}else if(typeof value=="number"){GOT[symName].value=value}else{throw new Error(`bad export type for '${symName}': ${typeof value}`)}}}}function loadDylibs(){if(!dynamicLibraries.length){reportUndefinedSymbols();return}addRunDependency("loadDylibs");dynamicLibraries.reduce((chain,lib)=>{return chain.then(()=>{return loadDynamicLibrary(lib,{loadAsync:true,global:true,nodelete:true,allowUndefined:true})})},Promise.resolve()).then(()=>{reportUndefinedSymbols();removeRunDependency("loadDylibs")})}function registerTLSInit(tlsInitFunc,moduleExports,metadata){function tlsInitWrapper(){var __tls_base=tlsInitFunc();if(!__tls_base){return}var tlsExports={};metadata.tlsExports.forEach(s=>tlsExports[s]=moduleExports[s]);relocateExports(tlsExports,__tls_base,true)}PThread.tlsInitFunctions.push(tlsInitWrapper);if(runtimeInitialized){tlsInitWrapper()}}function setValue(ptr,value,type="i8"){if(type.endsWith("*"))type="*";switch(type){case"i1":GROWABLE_HEAP_I8()[ptr>>0]=value;break;case"i8":GROWABLE_HEAP_I8()[ptr>>0]=value;break;case"i16":GROWABLE_HEAP_I16()[ptr>>1]=value;break;case"i32":GROWABLE_HEAP_I32()[ptr>>2]=value;break;case"i64":abort("to do setValue(i64) use WASM_BIGINT");case"float":GROWABLE_HEAP_F32()[ptr>>2]=value;break;case"double":GROWABLE_HEAP_F64()[ptr>>3]=value;break;case"*":GROWABLE_HEAP_U32()[ptr>>2]=value;break;default:abort(`invalid type for setValue: ${type}`)}}function ___call_sighandler(fp,sig){getWasmTableEntry(fp)(sig)}___call_sighandler.sig="vpi";function ___emscripten_init_main_thread_js(tb){__emscripten_thread_init(tb,!ENVIRONMENT_IS_WORKER,1,!ENVIRONMENT_IS_WEB,65536);PThread.threadInitTLS()}___emscripten_init_main_thread_js.sig="vp";function ___emscripten_thread_cleanup(thread){if(!ENVIRONMENT_IS_PTHREAD)cleanupThread(thread);else postMessage({"cmd":"cleanupThread","thread":thread})}___emscripten_thread_cleanup.sig="vp";var ___memory_base=new WebAssembly.Global({"value":"i32","mutable":false},1024);function pthreadCreateProxied(pthread_ptr,attr,startRoutine,arg){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(3,1,pthread_ptr,attr,startRoutine,arg);return ___pthread_create_js(pthread_ptr,attr,startRoutine,arg)}function ___pthread_create_js(pthread_ptr,attr,startRoutine,arg){if(typeof SharedArrayBuffer=="undefined"){err("Current environment does not support SharedArrayBuffer, pthreads are not available!");return 6}var transferList=[];var error=0;if(ENVIRONMENT_IS_PTHREAD&&(transferList.length===0||error)){return pthreadCreateProxied(pthread_ptr,attr,startRoutine,arg)}if(error)return error;var threadParams={startRoutine:startRoutine,pthread_ptr:pthread_ptr,arg:arg,transferList:transferList};if(ENVIRONMENT_IS_PTHREAD){threadParams.cmd="spawnThread";postMessage(threadParams,transferList);return 0}return spawnThread(threadParams)}___pthread_create_js.sig="ipppp";var ___stack_high=1359584;var ___stack_low=1294048;var ___stack_pointer=new WebAssembly.Global({"value":"i32","mutable":true},1359584);function ___syscall__newselect(nfds,readfds,writefds,exceptfds,timeout){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(4,1,nfds,readfds,writefds,exceptfds,timeout);try{var total=0;var srcReadLow=readfds?GROWABLE_HEAP_I32()[readfds>>2]:0,srcReadHigh=readfds?GROWABLE_HEAP_I32()[readfds+4>>2]:0;var srcWriteLow=writefds?GROWABLE_HEAP_I32()[writefds>>2]:0,srcWriteHigh=writefds?GROWABLE_HEAP_I32()[writefds+4>>2]:0;var srcExceptLow=exceptfds?GROWABLE_HEAP_I32()[exceptfds>>2]:0,srcExceptHigh=exceptfds?GROWABLE_HEAP_I32()[exceptfds+4>>2]:0;var dstReadLow=0,dstReadHigh=0;var dstWriteLow=0,dstWriteHigh=0;var dstExceptLow=0,dstExceptHigh=0;var allLow=(readfds?GROWABLE_HEAP_I32()[readfds>>2]:0)|(writefds?GROWABLE_HEAP_I32()[writefds>>2]:0)|(exceptfds?GROWABLE_HEAP_I32()[exceptfds>>2]:0);var allHigh=(readfds?GROWABLE_HEAP_I32()[readfds+4>>2]:0)|(writefds?GROWABLE_HEAP_I32()[writefds+4>>2]:0)|(exceptfds?GROWABLE_HEAP_I32()[exceptfds+4>>2]:0);var check=function(fd,low,high,val){return fd<32?low&val:high&val};for(var fd=0;fd<nfds;fd++){var mask=1<<fd%32;if(!check(fd,allLow,allHigh,mask)){continue}var stream=SYSCALLS.getStreamFromFD(fd);var flags=SYSCALLS.DEFAULT_POLLMASK;if(stream.stream_ops.poll){flags=stream.stream_ops.poll(stream)}if(flags&1&&check(fd,srcReadLow,srcReadHigh,mask)){fd<32?dstReadLow=dstReadLow|mask:dstReadHigh=dstReadHigh|mask;total++}if(flags&4&&check(fd,srcWriteLow,srcWriteHigh,mask)){fd<32?dstWriteLow=dstWriteLow|mask:dstWriteHigh=dstWriteHigh|mask;total++}if(flags&2&&check(fd,srcExceptLow,srcExceptHigh,mask)){fd<32?dstExceptLow=dstExceptLow|mask:dstExceptHigh=dstExceptHigh|mask;total++}}if(readfds){GROWABLE_HEAP_I32()[readfds>>2]=dstReadLow;GROWABLE_HEAP_I32()[readfds+4>>2]=dstReadHigh}if(writefds){GROWABLE_HEAP_I32()[writefds>>2]=dstWriteLow;GROWABLE_HEAP_I32()[writefds+4>>2]=dstWriteHigh}if(exceptfds){GROWABLE_HEAP_I32()[exceptfds>>2]=dstExceptLow;GROWABLE_HEAP_I32()[exceptfds+4>>2]=dstExceptHigh}return total}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}___syscall__newselect.sig="iipppp";var SOCKFS={mount:function(mount){Module["websocket"]=Module["websocket"]&&"object"===typeof Module["websocket"]?Module["websocket"]:{};Module["websocket"]._callbacks={};Module["websocket"]["on"]=function(event,callback){if("function"===typeof callback){this._callbacks[event]=callback}return this};Module["websocket"].emit=function(event,param){if("function"===typeof this._callbacks[event]){this._callbacks[event].call(this,param)}};return FS.createNode(null,"/",16384|511,0)},createSocket:function(family,type,protocol){type&=~526336;var streaming=type==1;if(streaming&&protocol&&protocol!=6){throw new FS.ErrnoError(66)}var sock={family:family,type:type,protocol:protocol,server:null,error:null,peers:{},pending:[],recv_queue:[],sock_ops:SOCKFS.websocket_sock_ops};var name=SOCKFS.nextname();var node=FS.createNode(SOCKFS.root,name,49152,0);node.sock=sock;var stream=FS.createStream({path:name,node:node,flags:2,seekable:false,stream_ops:SOCKFS.stream_ops});sock.stream=stream;return sock},getSocket:function(fd){var stream=FS.getStream(fd);if(!stream||!FS.isSocket(stream.node.mode)){return null}return stream.node.sock},stream_ops:{poll:function(stream){var sock=stream.node.sock;return sock.sock_ops.poll(sock)},ioctl:function(stream,request,varargs){var sock=stream.node.sock;return sock.sock_ops.ioctl(sock,request,varargs)},read:function(stream,buffer,offset,length,position){var sock=stream.node.sock;var msg=sock.sock_ops.recvmsg(sock,length);if(!msg){return 0}buffer.set(msg.buffer,offset);return msg.buffer.length},write:function(stream,buffer,offset,length,position){var sock=stream.node.sock;return sock.sock_ops.sendmsg(sock,buffer,offset,length)},close:function(stream){var sock=stream.node.sock;sock.sock_ops.close(sock)}},nextname:function(){if(!SOCKFS.nextname.current){SOCKFS.nextname.current=0}return"socket["+SOCKFS.nextname.current+++"]"},websocket_sock_ops:{createPeer:function(sock,addr,port){var ws;if(typeof addr=="object"){ws=addr;addr=null;port=null}if(ws){if(ws._socket){addr=ws._socket.remoteAddress;port=ws._socket.remotePort}else{var result=/ws[s]?:\/\/([^:]+):(\d+)/.exec(ws.url);if(!result){throw new Error("WebSocket URL must be in the format ws(s)://address:port")}addr=result[1];port=parseInt(result[2],10)}}else{try{var runtimeConfig=Module["websocket"]&&"object"===typeof Module["websocket"];var url="ws:#".replace("#","//");if(runtimeConfig){if("string"===typeof Module["websocket"]["url"]){url=Module["websocket"]["url"]}}if(url==="ws://"||url==="wss://"){var parts=addr.split("/");url=url+parts[0]+":"+port+"/"+parts.slice(1).join("/")}var subProtocols="binary";if(runtimeConfig){if("string"===typeof Module["websocket"]["subprotocol"]){subProtocols=Module["websocket"]["subprotocol"]}}var opts=undefined;if(subProtocols!=="null"){subProtocols=subProtocols.replace(/^ +| +$/g,"").split(/ *, */);opts=subProtocols}if(runtimeConfig&&null===Module["websocket"]["subprotocol"]){subProtocols="null";opts=undefined}var WebSocketConstructor;if(ENVIRONMENT_IS_NODE){WebSocketConstructor=require("ws")}else{WebSocketConstructor=WebSocket}ws=new WebSocketConstructor(url,opts);ws.binaryType="arraybuffer"}catch(e){throw new FS.ErrnoError(23)}}var peer={addr:addr,port:port,socket:ws,dgram_send_queue:[]};SOCKFS.websocket_sock_ops.addPeer(sock,peer);SOCKFS.websocket_sock_ops.handlePeerEvents(sock,peer);if(sock.type===2&&typeof sock.sport!="undefined"){peer.dgram_send_queue.push(new Uint8Array([255,255,255,255,"p".charCodeAt(0),"o".charCodeAt(0),"r".charCodeAt(0),"t".charCodeAt(0),(sock.sport&65280)>>8,sock.sport&255]))}return peer},getPeer:function(sock,addr,port){return sock.peers[addr+":"+port]},addPeer:function(sock,peer){sock.peers[peer.addr+":"+peer.port]=peer},removePeer:function(sock,peer){delete sock.peers[peer.addr+":"+peer.port]},handlePeerEvents:function(sock,peer){var first=true;var handleOpen=function(){Module["websocket"].emit("open",sock.stream.fd);try{var queued=peer.dgram_send_queue.shift();while(queued){peer.socket.send(queued);queued=peer.dgram_send_queue.shift()}}catch(e){peer.socket.close()}};function handleMessage(data){if(typeof data=="string"){var encoder=new TextEncoder;data=encoder.encode(data)}else{assert(data.byteLength!==undefined);if(data.byteLength==0){return}data=new Uint8Array(data)}var wasfirst=first;first=false;if(wasfirst&&data.length===10&&data[0]===255&&data[1]===255&&data[2]===255&&data[3]===255&&data[4]==="p".charCodeAt(0)&&data[5]==="o".charCodeAt(0)&&data[6]==="r".charCodeAt(0)&&data[7]==="t".charCodeAt(0)){var newport=data[8]<<8|data[9];SOCKFS.websocket_sock_ops.removePeer(sock,peer);peer.port=newport;SOCKFS.websocket_sock_ops.addPeer(sock,peer);return}sock.recv_queue.push({addr:peer.addr,port:peer.port,data:data});Module["websocket"].emit("message",sock.stream.fd)}if(ENVIRONMENT_IS_NODE){peer.socket.on("open",handleOpen);peer.socket.on("message",function(data,isBinary){if(!isBinary){return}handleMessage(new Uint8Array(data).buffer)});peer.socket.on("close",function(){Module["websocket"].emit("close",sock.stream.fd)});peer.socket.on("error",function(error){sock.error=14;Module["websocket"].emit("error",[sock.stream.fd,sock.error,"ECONNREFUSED: Connection refused"])})}else{peer.socket.onopen=handleOpen;peer.socket.onclose=function(){Module["websocket"].emit("close",sock.stream.fd)};peer.socket.onmessage=function peer_socket_onmessage(event){handleMessage(event.data)};peer.socket.onerror=function(error){sock.error=14;Module["websocket"].emit("error",[sock.stream.fd,sock.error,"ECONNREFUSED: Connection refused"])}}},poll:function(sock){if(sock.type===1&&sock.server){return sock.pending.length?64|1:0}var mask=0;var dest=sock.type===1?SOCKFS.websocket_sock_ops.getPeer(sock,sock.daddr,sock.dport):null;if(sock.recv_queue.length||!dest||dest&&dest.socket.readyState===dest.socket.CLOSING||dest&&dest.socket.readyState===dest.socket.CLOSED){mask|=64|1}if(!dest||dest&&dest.socket.readyState===dest.socket.OPEN){mask|=4}if(dest&&dest.socket.readyState===dest.socket.CLOSING||dest&&dest.socket.readyState===dest.socket.CLOSED){mask|=16}return mask},ioctl:function(sock,request,arg){switch(request){case 21531:var bytes=0;if(sock.recv_queue.length){bytes=sock.recv_queue[0].data.length}GROWABLE_HEAP_I32()[arg>>2]=bytes;return 0;default:return 28}},close:function(sock){if(sock.server){try{sock.server.close()}catch(e){}sock.server=null}var peers=Object.keys(sock.peers);for(var i=0;i<peers.length;i++){var peer=sock.peers[peers[i]];try{peer.socket.close()}catch(e){}SOCKFS.websocket_sock_ops.removePeer(sock,peer)}return 0},bind:function(sock,addr,port){if(typeof sock.saddr!="undefined"||typeof sock.sport!="undefined"){throw new FS.ErrnoError(28)}sock.saddr=addr;sock.sport=port;if(sock.type===2){if(sock.server){sock.server.close();sock.server=null}try{sock.sock_ops.listen(sock,0)}catch(e){if(!(e.name==="ErrnoError"))throw e;if(e.errno!==138)throw e}}},connect:function(sock,addr,port){if(sock.server){throw new FS.ErrnoError(138)}if(typeof sock.daddr!="undefined"&&typeof sock.dport!="undefined"){var dest=SOCKFS.websocket_sock_ops.getPeer(sock,sock.daddr,sock.dport);if(dest){if(dest.socket.readyState===dest.socket.CONNECTING){throw new FS.ErrnoError(7)}else{throw new FS.ErrnoError(30)}}}var peer=SOCKFS.websocket_sock_ops.createPeer(sock,addr,port);sock.daddr=peer.addr;sock.dport=peer.port;throw new FS.ErrnoError(26)},listen:function(sock,backlog){if(!ENVIRONMENT_IS_NODE){throw new FS.ErrnoError(138)}if(sock.server){throw new FS.ErrnoError(28)}var WebSocketServer=require("ws").Server;var host=sock.saddr;sock.server=new WebSocketServer({host:host,port:sock.sport});Module["websocket"].emit("listen",sock.stream.fd);sock.server.on("connection",function(ws){if(sock.type===1){var newsock=SOCKFS.createSocket(sock.family,sock.type,sock.protocol);var peer=SOCKFS.websocket_sock_ops.createPeer(newsock,ws);newsock.daddr=peer.addr;newsock.dport=peer.port;sock.pending.push(newsock);Module["websocket"].emit("connection",newsock.stream.fd)}else{SOCKFS.websocket_sock_ops.createPeer(sock,ws);Module["websocket"].emit("connection",sock.stream.fd)}});sock.server.on("close",function(){Module["websocket"].emit("close",sock.stream.fd);sock.server=null});sock.server.on("error",function(error){sock.error=23;Module["websocket"].emit("error",[sock.stream.fd,sock.error,"EHOSTUNREACH: Host is unreachable"])})},accept:function(listensock){if(!listensock.server||!listensock.pending.length){throw new FS.ErrnoError(28)}var newsock=listensock.pending.shift();newsock.stream.flags=listensock.stream.flags;return newsock},getname:function(sock,peer){var addr,port;if(peer){if(sock.daddr===undefined||sock.dport===undefined){throw new FS.ErrnoError(53)}addr=sock.daddr;port=sock.dport}else{addr=sock.saddr||0;port=sock.sport||0}return{addr:addr,port:port}},sendmsg:function(sock,buffer,offset,length,addr,port){if(sock.type===2){if(addr===undefined||port===undefined){addr=sock.daddr;port=sock.dport}if(addr===undefined||port===undefined){throw new FS.ErrnoError(17)}}else{addr=sock.daddr;port=sock.dport}var dest=SOCKFS.websocket_sock_ops.getPeer(sock,addr,port);if(sock.type===1){if(!dest||dest.socket.readyState===dest.socket.CLOSING||dest.socket.readyState===dest.socket.CLOSED){throw new FS.ErrnoError(53)}else if(dest.socket.readyState===dest.socket.CONNECTING){throw new FS.ErrnoError(6)}}if(ArrayBuffer.isView(buffer)){offset+=buffer.byteOffset;buffer=buffer.buffer}var data;if(buffer instanceof SharedArrayBuffer){data=new Uint8Array(new Uint8Array(buffer.slice(offset,offset+length))).buffer}else{data=buffer.slice(offset,offset+length)}if(sock.type===2){if(!dest||dest.socket.readyState!==dest.socket.OPEN){if(!dest||dest.socket.readyState===dest.socket.CLOSING||dest.socket.readyState===dest.socket.CLOSED){dest=SOCKFS.websocket_sock_ops.createPeer(sock,addr,port)}dest.dgram_send_queue.push(data);return length}}try{dest.socket.send(data);return length}catch(e){throw new FS.ErrnoError(28)}},recvmsg:function(sock,length){if(sock.type===1&&sock.server){throw new FS.ErrnoError(53)}var queued=sock.recv_queue.shift();if(!queued){if(sock.type===1){var dest=SOCKFS.websocket_sock_ops.getPeer(sock,sock.daddr,sock.dport);if(!dest){throw new FS.ErrnoError(53)}if(dest.socket.readyState===dest.socket.CLOSING||dest.socket.readyState===dest.socket.CLOSED){return null}throw new FS.ErrnoError(6)}throw new FS.ErrnoError(6)}var queuedLength=queued.data.byteLength||queued.data.length;var queuedOffset=queued.data.byteOffset||0;var queuedBuffer=queued.data.buffer||queued.data;var bytesRead=Math.min(length,queuedLength);var res={buffer:new Uint8Array(queuedBuffer,queuedOffset,bytesRead),addr:queued.addr,port:queued.port};if(sock.type===1&&bytesRead<queuedLength){var bytesRemaining=queuedLength-bytesRead;queued.data=new Uint8Array(queuedBuffer,queuedOffset+bytesRead,bytesRemaining);sock.recv_queue.unshift(queued)}return res}}};function getSocketFromFD(fd){var socket=SOCKFS.getSocket(fd);if(!socket)throw new FS.ErrnoError(8);return socket}function setErrNo(value){GROWABLE_HEAP_I32()[___errno_location()>>2]=value;return value}function inetPton4(str){var b=str.split(".");for(var i=0;i<4;i++){var tmp=Number(b[i]);if(isNaN(tmp))return null;b[i]=tmp}return(b[0]|b[1]<<8|b[2]<<16|b[3]<<24)>>>0}function jstoi_q(str){return parseInt(str)}function inetPton6(str){var words;var w,offset,z;var valid6regx=/^((?=.*::)(?!.*::.+::)(::)?([\dA-F]{1,4}:(:|\b)|){5}|([\dA-F]{1,4}:){6})((([\dA-F]{1,4}((?!\3)::|:\b|$))|(?!\2\3)){2}|(((2[0-4]|1\d|[1-9])?\d|25[0-5])\.?\b){4})$/i;var parts=[];if(!valid6regx.test(str)){return null}if(str==="::"){return[0,0,0,0,0,0,0,0]}if(str.startsWith("::")){str=str.replace("::","Z:")}else{str=str.replace("::",":Z:")}if(str.indexOf(".")>0){str=str.replace(new RegExp("[.]","g"),":");words=str.split(":");words[words.length-4]=jstoi_q(words[words.length-4])+jstoi_q(words[words.length-3])*256;words[words.length-3]=jstoi_q(words[words.length-2])+jstoi_q(words[words.length-1])*256;words=words.slice(0,words.length-2)}else{words=str.split(":")}offset=0;z=0;for(w=0;w<words.length;w++){if(typeof words[w]=="string"){if(words[w]==="Z"){for(z=0;z<8-words.length+1;z++){parts[w+z]=0}offset=z-1}else{parts[w+offset]=_htons(parseInt(words[w],16))}}else{parts[w+offset]=words[w]}}return[parts[1]<<16|parts[0],parts[3]<<16|parts[2],parts[5]<<16|parts[4],parts[7]<<16|parts[6]]}function writeSockaddr(sa,family,addr,port,addrlen){switch(family){case 2:addr=inetPton4(addr);zeroMemory(sa,16);if(addrlen){GROWABLE_HEAP_I32()[addrlen>>2]=16}GROWABLE_HEAP_I16()[sa>>1]=family;GROWABLE_HEAP_I32()[sa+4>>2]=addr;GROWABLE_HEAP_I16()[sa+2>>1]=_htons(port);break;case 10:addr=inetPton6(addr);zeroMemory(sa,28);if(addrlen){GROWABLE_HEAP_I32()[addrlen>>2]=28}GROWABLE_HEAP_I32()[sa>>2]=family;GROWABLE_HEAP_I32()[sa+8>>2]=addr[0];GROWABLE_HEAP_I32()[sa+12>>2]=addr[1];GROWABLE_HEAP_I32()[sa+16>>2]=addr[2];GROWABLE_HEAP_I32()[sa+20>>2]=addr[3];GROWABLE_HEAP_I16()[sa+2>>1]=_htons(port);break;default:return 5}return 0}var DNS={address_map:{id:1,addrs:{},names:{}},lookup_name:function(name){var res=inetPton4(name);if(res!==null){return name}res=inetPton6(name);if(res!==null){return name}var addr;if(DNS.address_map.addrs[name]){addr=DNS.address_map.addrs[name]}else{var id=DNS.address_map.id++;assert(id<65535,"exceeded max address mappings of 65535");addr="172.29."+(id&255)+"."+(id&65280);DNS.address_map.names[addr]=name;DNS.address_map.addrs[name]=addr}return addr},lookup_addr:function(addr){if(DNS.address_map.names[addr]){return DNS.address_map.names[addr]}return null}};function ___syscall_accept4(fd,addr,addrlen,flags,d1,d2){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(5,1,fd,addr,addrlen,flags,d1,d2);try{var sock=getSocketFromFD(fd);var newsock=sock.sock_ops.accept(sock);if(addr){var errno=writeSockaddr(addr,newsock.family,DNS.lookup_name(newsock.daddr),newsock.dport,addrlen)}return newsock.stream.fd}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}___syscall_accept4.sig="iippiii";function inetNtop4(addr){return(addr&255)+"."+(addr>>8&255)+"."+(addr>>16&255)+"."+(addr>>24&255)}function inetNtop6(ints){var str="";var word=0;var longest=0;var lastzero=0;var zstart=0;var len=0;var i=0;var parts=[ints[0]&65535,ints[0]>>16,ints[1]&65535,ints[1]>>16,ints[2]&65535,ints[2]>>16,ints[3]&65535,ints[3]>>16];var hasipv4=true;var v4part="";for(i=0;i<5;i++){if(parts[i]!==0){hasipv4=false;break}}if(hasipv4){v4part=inetNtop4(parts[6]|parts[7]<<16);if(parts[5]===-1){str="::ffff:";str+=v4part;return str}if(parts[5]===0){str="::";if(v4part==="0.0.0.0")v4part="";if(v4part==="*******")v4part="1";str+=v4part;return str}}for(word=0;word<8;word++){if(parts[word]===0){if(word-lastzero>1){len=0}lastzero=word;len++}if(len>longest){longest=len;zstart=word-longest+1}}for(word=0;word<8;word++){if(longest>1){if(parts[word]===0&&word>=zstart&&word<zstart+longest){if(word===zstart){str+=":";if(zstart===0)str+=":"}continue}}str+=Number(_ntohs(parts[word]&65535)).toString(16);str+=word<7?":":""}return str}function readSockaddr(sa,salen){var family=GROWABLE_HEAP_I16()[sa>>1];var port=_ntohs(GROWABLE_HEAP_U16()[sa+2>>1]);var addr;switch(family){case 2:if(salen!==16){return{errno:28}}addr=GROWABLE_HEAP_I32()[sa+4>>2];addr=inetNtop4(addr);break;case 10:if(salen!==28){return{errno:28}}addr=[GROWABLE_HEAP_I32()[sa+8>>2],GROWABLE_HEAP_I32()[sa+12>>2],GROWABLE_HEAP_I32()[sa+16>>2],GROWABLE_HEAP_I32()[sa+20>>2]];addr=inetNtop6(addr);break;default:return{errno:5}}return{family:family,addr:addr,port:port}}function getSocketAddress(addrp,addrlen,allowNull){if(allowNull&&addrp===0)return null;var info=readSockaddr(addrp,addrlen);if(info.errno)throw new FS.ErrnoError(info.errno);info.addr=DNS.lookup_addr(info.addr)||info.addr;return info}function ___syscall_bind(fd,addr,addrlen,d1,d2,d3){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(6,1,fd,addr,addrlen,d1,d2,d3);try{var sock=getSocketFromFD(fd);var info=getSocketAddress(addr,addrlen);sock.sock_ops.bind(sock,info.addr,info.port);return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}___syscall_bind.sig="iippiii";function ___syscall_chdir(path){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(7,1,path);try{path=SYSCALLS.getStr(path);FS.chdir(path);return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}___syscall_chdir.sig="ip";function ___syscall_chmod(path,mode){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(8,1,path,mode);try{path=SYSCALLS.getStr(path);FS.chmod(path,mode);return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}___syscall_chmod.sig="ipi";function ___syscall_dup(fd){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(9,1,fd);try{var old=SYSCALLS.getStreamFromFD(fd);return FS.createStream(old).fd}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}___syscall_dup.sig="ii";function ___syscall_dup3(fd,newfd,flags){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(10,1,fd,newfd,flags);try{var old=SYSCALLS.getStreamFromFD(fd);if(old.fd===newfd)return-28;var existing=FS.getStream(newfd);if(existing)FS.close(existing);return FS.createStream(old,newfd).fd}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}___syscall_dup3.sig="iiii";function ___syscall_faccessat(dirfd,path,amode,flags){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(11,1,dirfd,path,amode,flags);try{path=SYSCALLS.getStr(path);path=SYSCALLS.calculateAt(dirfd,path);if(amode&~7){return-28}var lookup=FS.lookupPath(path,{follow:true});var node=lookup.node;if(!node){return-44}var perms="";if(amode&4)perms+="r";if(amode&2)perms+="w";if(amode&1)perms+="x";if(perms&&FS.nodePermissions(node,perms)){return-2}return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}___syscall_faccessat.sig="iipii";function ___syscall_fcntl64(fd,cmd,varargs){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(12,1,fd,cmd,varargs);SYSCALLS.varargs=varargs;try{var stream=SYSCALLS.getStreamFromFD(fd);switch(cmd){case 0:{var arg=SYSCALLS.get();if(arg<0){return-28}var newStream;newStream=FS.createStream(stream,arg);return newStream.fd}case 1:case 2:return 0;case 3:return stream.flags;case 4:{var arg=SYSCALLS.get();stream.flags|=arg;return 0}case 5:{var arg=SYSCALLS.get();var offset=0;GROWABLE_HEAP_I16()[arg+offset>>1]=2;return 0}case 6:case 7:return 0;case 16:case 8:return-28;case 9:setErrNo(28);return-1;default:{return-28}}}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}___syscall_fcntl64.sig="iiip";function ___syscall_fstat64(fd,buf){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(13,1,fd,buf);try{var stream=SYSCALLS.getStreamFromFD(fd);return SYSCALLS.doStat(FS.stat,stream.path,buf)}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}___syscall_fstat64.sig="iip";function convertI32PairToI53Checked(lo,hi){return hi+2097152>>>0<4194305-!!lo?(lo>>>0)+hi*4294967296:NaN}function ___syscall_ftruncate64(fd,length_low,length_high){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(14,1,fd,length_low,length_high);try{var length=convertI32PairToI53Checked(length_low,length_high);if(isNaN(length))return-61;FS.ftruncate(fd,length);return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}___syscall_ftruncate64.sig="iiii";function stringToUTF8(str,outPtr,maxBytesToWrite){return stringToUTF8Array(str,GROWABLE_HEAP_U8(),outPtr,maxBytesToWrite)}function ___syscall_getcwd(buf,size){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(15,1,buf,size);try{if(size===0)return-28;var cwd=FS.cwd();var cwdLengthInBytes=lengthBytesUTF8(cwd)+1;if(size<cwdLengthInBytes)return-68;stringToUTF8(cwd,buf,size);return cwdLengthInBytes}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}___syscall_getcwd.sig="ipp";function ___syscall_getdents64(fd,dirp,count){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(16,1,fd,dirp,count);try{var stream=SYSCALLS.getStreamFromFD(fd);if(!stream.getdents){stream.getdents=FS.readdir(stream.path)}var struct_size=280;var pos=0;var off=FS.llseek(stream,0,1);var idx=Math.floor(off/struct_size);while(idx<stream.getdents.length&&pos+struct_size<=count){var id;var type;var name=stream.getdents[idx];if(name==="."){id=stream.node.id;type=4}else if(name===".."){var lookup=FS.lookupPath(stream.path,{parent:true});id=lookup.node.id;type=4}else{var child=FS.lookupNode(stream.node,name);id=child.id;type=FS.isChrdev(child.mode)?2:FS.isDir(child.mode)?4:FS.isLink(child.mode)?10:8}tempI64=[id>>>0,(tempDouble=id,+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],GROWABLE_HEAP_I32()[dirp+pos>>2]=tempI64[0],GROWABLE_HEAP_I32()[dirp+pos+4>>2]=tempI64[1];tempI64=[(idx+1)*struct_size>>>0,(tempDouble=(idx+1)*struct_size,+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],GROWABLE_HEAP_I32()[dirp+pos+8>>2]=tempI64[0],GROWABLE_HEAP_I32()[dirp+pos+12>>2]=tempI64[1];GROWABLE_HEAP_I16()[dirp+pos+16>>1]=280;GROWABLE_HEAP_I8()[dirp+pos+18>>0]=type;stringToUTF8(name,dirp+pos+19,256);pos+=struct_size;idx+=1}FS.llseek(stream,idx*struct_size,0);return pos}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}___syscall_getdents64.sig="iipp";function ___syscall_ioctl(fd,op,varargs){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(17,1,fd,op,varargs);SYSCALLS.varargs=varargs;try{var stream=SYSCALLS.getStreamFromFD(fd);switch(op){case 21509:case 21505:{if(!stream.tty)return-59;return 0}case 21510:case 21511:case 21512:case 21506:case 21507:case 21508:{if(!stream.tty)return-59;return 0}case 21519:{if(!stream.tty)return-59;var argp=SYSCALLS.get();GROWABLE_HEAP_I32()[argp>>2]=0;return 0}case 21520:{if(!stream.tty)return-59;return-28}case 21531:{var argp=SYSCALLS.get();return FS.ioctl(stream,op,argp)}case 21523:{if(!stream.tty)return-59;return 0}case 21524:{if(!stream.tty)return-59;return 0}default:return-28}}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}___syscall_ioctl.sig="iiip";function ___syscall_listen(fd,backlog){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(18,1,fd,backlog);try{var sock=getSocketFromFD(fd);sock.sock_ops.listen(sock,backlog);return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}___syscall_listen.sig="iiiiiii";function ___syscall_lstat64(path,buf){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(19,1,path,buf);try{path=SYSCALLS.getStr(path);return SYSCALLS.doStat(FS.lstat,path,buf)}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}___syscall_lstat64.sig="ipp";function ___syscall_mkdirat(dirfd,path,mode){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(20,1,dirfd,path,mode);try{path=SYSCALLS.getStr(path);path=SYSCALLS.calculateAt(dirfd,path);path=PATH.normalize(path);if(path[path.length-1]==="/")path=path.substr(0,path.length-1);FS.mkdir(path,mode,0);return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}___syscall_mkdirat.sig="iipi";function ___syscall_newfstatat(dirfd,path,buf,flags){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(21,1,dirfd,path,buf,flags);try{path=SYSCALLS.getStr(path);var nofollow=flags&256;var allowEmpty=flags&4096;flags=flags&~6400;path=SYSCALLS.calculateAt(dirfd,path,allowEmpty);return SYSCALLS.doStat(nofollow?FS.lstat:FS.stat,path,buf)}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}___syscall_newfstatat.sig="iippi";function ___syscall_openat(dirfd,path,flags,varargs){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(22,1,dirfd,path,flags,varargs);SYSCALLS.varargs=varargs;try{path=SYSCALLS.getStr(path);path=SYSCALLS.calculateAt(dirfd,path);var mode=varargs?SYSCALLS.get():0;return FS.open(path,flags,mode).fd}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}___syscall_openat.sig="iipip";var PIPEFS={BUCKET_BUFFER_SIZE:8192,mount:function(mount){return FS.createNode(null,"/",16384|511,0)},createPipe:function(){var pipe={buckets:[],refcnt:2};pipe.buckets.push({buffer:new Uint8Array(PIPEFS.BUCKET_BUFFER_SIZE),offset:0,roffset:0});var rName=PIPEFS.nextname();var wName=PIPEFS.nextname();var rNode=FS.createNode(PIPEFS.root,rName,4096,0);var wNode=FS.createNode(PIPEFS.root,wName,4096,0);rNode.pipe=pipe;wNode.pipe=pipe;var readableStream=FS.createStream({path:rName,node:rNode,flags:0,seekable:false,stream_ops:PIPEFS.stream_ops});rNode.stream=readableStream;var writableStream=FS.createStream({path:wName,node:wNode,flags:1,seekable:false,stream_ops:PIPEFS.stream_ops});wNode.stream=writableStream;return{readable_fd:readableStream.fd,writable_fd:writableStream.fd}},stream_ops:{poll:function(stream){var pipe=stream.node.pipe;if((stream.flags&2097155)===1){return 256|4}if(pipe.buckets.length>0){for(var i=0;i<pipe.buckets.length;i++){var bucket=pipe.buckets[i];if(bucket.offset-bucket.roffset>0){return 64|1}}}return 0},ioctl:function(stream,request,varargs){return 28},fsync:function(stream){return 28},read:function(stream,buffer,offset,length,position){var pipe=stream.node.pipe;var currentLength=0;for(var i=0;i<pipe.buckets.length;i++){var bucket=pipe.buckets[i];currentLength+=bucket.offset-bucket.roffset}assert(buffer instanceof ArrayBuffer||buffer instanceof SharedArrayBuffer||ArrayBuffer.isView(buffer));var data=buffer.subarray(offset,offset+length);if(length<=0){return 0}if(currentLength==0){throw new FS.ErrnoError(6)}var toRead=Math.min(currentLength,length);var totalRead=toRead;var toRemove=0;for(var i=0;i<pipe.buckets.length;i++){var currBucket=pipe.buckets[i];var bucketSize=currBucket.offset-currBucket.roffset;if(toRead<=bucketSize){var tmpSlice=currBucket.buffer.subarray(currBucket.roffset,currBucket.offset);if(toRead<bucketSize){tmpSlice=tmpSlice.subarray(0,toRead);currBucket.roffset+=toRead}else{toRemove++}data.set(tmpSlice);break}else{var tmpSlice=currBucket.buffer.subarray(currBucket.roffset,currBucket.offset);data.set(tmpSlice);data=data.subarray(tmpSlice.byteLength);toRead-=tmpSlice.byteLength;toRemove++}}if(toRemove&&toRemove==pipe.buckets.length){toRemove--;pipe.buckets[toRemove].offset=0;pipe.buckets[toRemove].roffset=0}pipe.buckets.splice(0,toRemove);return totalRead},write:function(stream,buffer,offset,length,position){var pipe=stream.node.pipe;assert(buffer instanceof ArrayBuffer||buffer instanceof SharedArrayBuffer||ArrayBuffer.isView(buffer));var data=buffer.subarray(offset,offset+length);var dataLen=data.byteLength;if(dataLen<=0){return 0}var currBucket=null;if(pipe.buckets.length==0){currBucket={buffer:new Uint8Array(PIPEFS.BUCKET_BUFFER_SIZE),offset:0,roffset:0};pipe.buckets.push(currBucket)}else{currBucket=pipe.buckets[pipe.buckets.length-1]}assert(currBucket.offset<=PIPEFS.BUCKET_BUFFER_SIZE);var freeBytesInCurrBuffer=PIPEFS.BUCKET_BUFFER_SIZE-currBucket.offset;if(freeBytesInCurrBuffer>=dataLen){currBucket.buffer.set(data,currBucket.offset);currBucket.offset+=dataLen;return dataLen}else if(freeBytesInCurrBuffer>0){currBucket.buffer.set(data.subarray(0,freeBytesInCurrBuffer),currBucket.offset);currBucket.offset+=freeBytesInCurrBuffer;data=data.subarray(freeBytesInCurrBuffer,data.byteLength)}var numBuckets=data.byteLength/PIPEFS.BUCKET_BUFFER_SIZE|0;var remElements=data.byteLength%PIPEFS.BUCKET_BUFFER_SIZE;for(var i=0;i<numBuckets;i++){var newBucket={buffer:new Uint8Array(PIPEFS.BUCKET_BUFFER_SIZE),offset:PIPEFS.BUCKET_BUFFER_SIZE,roffset:0};pipe.buckets.push(newBucket);newBucket.buffer.set(data.subarray(0,PIPEFS.BUCKET_BUFFER_SIZE));data=data.subarray(PIPEFS.BUCKET_BUFFER_SIZE,data.byteLength)}if(remElements>0){var newBucket={buffer:new Uint8Array(PIPEFS.BUCKET_BUFFER_SIZE),offset:data.byteLength,roffset:0};pipe.buckets.push(newBucket);newBucket.buffer.set(data)}return dataLen},close:function(stream){var pipe=stream.node.pipe;pipe.refcnt--;if(pipe.refcnt===0){pipe.buckets=null}}},nextname:function(){if(!PIPEFS.nextname.current){PIPEFS.nextname.current=0}return"pipe["+PIPEFS.nextname.current+++"]"}};function ___syscall_pipe(fdPtr){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(23,1,fdPtr);try{if(fdPtr==0){throw new FS.ErrnoError(21)}var res=PIPEFS.createPipe();GROWABLE_HEAP_I32()[fdPtr>>2]=res.readable_fd;GROWABLE_HEAP_I32()[fdPtr+4>>2]=res.writable_fd;return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}___syscall_pipe.sig="ip";function ___syscall_readlinkat(dirfd,path,buf,bufsize){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(24,1,dirfd,path,buf,bufsize);try{path=SYSCALLS.getStr(path);path=SYSCALLS.calculateAt(dirfd,path);if(bufsize<=0)return-28;var ret=FS.readlink(path);var len=Math.min(bufsize,lengthBytesUTF8(ret));var endChar=GROWABLE_HEAP_I8()[buf+len];stringToUTF8(ret,buf,bufsize+1);GROWABLE_HEAP_I8()[buf+len]=endChar;return len}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}___syscall_readlinkat.sig="iippp";function ___syscall_recvfrom(fd,buf,len,flags,addr,addrlen){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(25,1,fd,buf,len,flags,addr,addrlen);try{var sock=getSocketFromFD(fd);var msg=sock.sock_ops.recvmsg(sock,len);if(!msg)return 0;if(addr){var errno=writeSockaddr(addr,sock.family,DNS.lookup_name(msg.addr),msg.port,addrlen)}GROWABLE_HEAP_U8().set(msg.buffer,buf);return msg.buffer.byteLength}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}___syscall_recvfrom.sig="iippipp";function ___syscall_renameat(olddirfd,oldpath,newdirfd,newpath){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(26,1,olddirfd,oldpath,newdirfd,newpath);try{oldpath=SYSCALLS.getStr(oldpath);newpath=SYSCALLS.getStr(newpath);oldpath=SYSCALLS.calculateAt(olddirfd,oldpath);newpath=SYSCALLS.calculateAt(newdirfd,newpath);FS.rename(oldpath,newpath);return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}___syscall_renameat.sig="iipip";function ___syscall_rmdir(path){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(27,1,path);try{path=SYSCALLS.getStr(path);FS.rmdir(path);return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}___syscall_rmdir.sig="ip";function ___syscall_sendto(fd,message,length,flags,addr,addr_len){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(28,1,fd,message,length,flags,addr,addr_len);try{var sock=getSocketFromFD(fd);var dest=getSocketAddress(addr,addr_len,true);if(!dest){return FS.write(sock.stream,GROWABLE_HEAP_I8(),message,length)}return sock.sock_ops.sendmsg(sock,GROWABLE_HEAP_I8(),message,length,dest.addr,dest.port)}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}___syscall_sendto.sig="iippipp";function ___syscall_socket(domain,type,protocol){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(29,1,domain,type,protocol);try{var sock=SOCKFS.createSocket(domain,type,protocol);return sock.stream.fd}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}___syscall_socket.sig="iiiiiii";function ___syscall_stat64(path,buf){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(30,1,path,buf);try{path=SYSCALLS.getStr(path);return SYSCALLS.doStat(FS.stat,path,buf)}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}___syscall_stat64.sig="ipp";function ___syscall_symlink(target,linkpath){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(31,1,target,linkpath);try{target=SYSCALLS.getStr(target);linkpath=SYSCALLS.getStr(linkpath);FS.symlink(target,linkpath);return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}___syscall_symlink.sig="ipp";function ___syscall_unlinkat(dirfd,path,flags){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(32,1,dirfd,path,flags);try{path=SYSCALLS.getStr(path);path=SYSCALLS.calculateAt(dirfd,path);if(flags===0){FS.unlink(path)}else if(flags===512){FS.rmdir(path)}else{abort("Invalid flags passed to unlinkat")}return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}___syscall_unlinkat.sig="iipi";var ___table_base=new WebAssembly.Global({"value":"i32","mutable":false},1);var ENV={};function stringToUTF8OnStack(str){var size=lengthBytesUTF8(str)+1;var ret=stackAlloc(size);stringToUTF8(str,ret,size);return ret}function withStackSave(f){var stack=stackSave();var ret=f();stackRestore(stack);return ret}function dlSetError(msg){withStackSave(()=>{var cmsg=stringToUTF8OnStack(msg);___dl_seterr(cmsg,0)})}function dlopenInternal(handle,jsflags){var filename=UTF8ToString(handle+36);var flags=GROWABLE_HEAP_I32()[handle+4>>2];filename=PATH.normalize(filename);var global=Boolean(flags&256);var localScope=global?null:{};var combinedFlags={global:global,nodelete:Boolean(flags&4096),loadAsync:jsflags.loadAsync};if(jsflags.loadAsync){return loadDynamicLibrary(filename,combinedFlags,localScope,handle)}try{return loadDynamicLibrary(filename,combinedFlags,localScope,handle)}catch(e){dlSetError(`Could not load dynamic lib: ${filename}\n${e}`);return 0}}function __dlopen_js(handle){var jsflags={loadAsync:false};return dlopenInternal(handle,jsflags)}__dlopen_js.sig="pp";function __dlsym_catchup_js(handle,symbolIndex){var lib=LDSO.loadedLibsByHandle[handle];var symDict=lib.exports;var symName=Object.keys(symDict)[symbolIndex];var sym=symDict[symName];var result=addFunction(sym,sym.sig);return result}__dlsym_catchup_js.sig="ppi";function __dlsym_js(handle,symbol,symbolIndex){symbol=UTF8ToString(symbol);var result;var newSymIndex;var lib=LDSO.loadedLibsByHandle[handle];if(!lib.exports.hasOwnProperty(symbol)||lib.exports[symbol].stub){dlSetError(`Tried to lookup unknown symbol "${symbol}" in dynamic lib: ${lib.name}`);return 0}newSymIndex=Object.keys(lib.exports).indexOf(symbol);var origSym="orig$"+symbol;result=lib.exports[origSym];if(result){newSymIndex=Object.keys(lib.exports).indexOf(origSym)}else result=lib.exports[symbol];if(typeof result=="function"){var addr=getFunctionAddress(result);if(addr){result=addr}else{result=addFunction(result,result.sig);GROWABLE_HEAP_U32()[symbolIndex>>2]=newSymIndex}}return result}__dlsym_js.sig="pppp";function maybeExit(){if(!keepRuntimeAlive()){try{if(ENVIRONMENT_IS_PTHREAD)__emscripten_thread_exit(EXITSTATUS);else _exit(EXITSTATUS)}catch(e){handleException(e)}}}function callUserCallback(func){if(ABORT){return}try{func();maybeExit()}catch(e){handleException(e)}}function runtimeKeepalivePush(){runtimeKeepaliveCounter+=1}runtimeKeepalivePush.sig="v";function runtimeKeepalivePop(){runtimeKeepaliveCounter-=1}runtimeKeepalivePop.sig="v";function __emscripten_dlopen_js(handle,onsuccess,onerror,user_data){function errorCallback(e){var filename=UTF8ToString(handle+36);dlSetError(`'Could not load dynamic lib: ${filename}\n${e}`);runtimeKeepalivePop();callUserCallback(()=>getWasmTableEntry(onerror)(handle,user_data))}function successCallback(){runtimeKeepalivePop();callUserCallback(()=>getWasmTableEntry(onsuccess)(handle,user_data))}runtimeKeepalivePush();var jsflags={loadAsync:true};var promise=dlopenInternal(handle,jsflags);if(promise){promise.then(successCallback,errorCallback)}else{errorCallback()}}__emscripten_dlopen_js.sig="vpppp";function __emscripten_dlsync_threads(){for(const ptr of Object.keys(PThread.pthreads)){const pthread_ptr=Number(ptr);if(!PThread.finishedThreads.has(pthread_ptr)){__emscripten_proxy_dlsync(pthread_ptr)}}}__emscripten_dlsync_threads.sig="v";function HandleAllocator(){this.allocated=[undefined];this.freelist=[];this.get=function(id){return this.allocated[id]};this.has=function(id){return this.allocated[id]!==undefined};this.allocate=function(handle){var id=this.freelist.pop()||this.allocated.length;this.allocated[id]=handle;return id};this.free=function(id){this.allocated[id]=undefined;this.freelist.push(id)}}var promiseMap=new HandleAllocator;function makePromise(){var promiseInfo={};promiseInfo.promise=new Promise((resolve,reject)=>{promiseInfo.reject=reject;promiseInfo.resolve=resolve});promiseInfo.id=promiseMap.allocate(promiseInfo);return promiseInfo}function _emscripten_promise_create(){return makePromise().id}_emscripten_promise_create.sig="p";function getPromise(id){return promiseMap.get(id).promise}function __emscripten_dlsync_threads_async(caller,callback,ctx){const promises=[];assert(Object.keys(PThread.outstandingPromises).length===0);var info=makePromise();promises.push(info.promise);__emscripten_dlsync_self_async(info.id);for(const ptr of Object.keys(PThread.pthreads)){const pthread_ptr=Number(ptr);if(pthread_ptr!==caller&&!PThread.finishedThreads.has(pthread_ptr)){info=makePromise();__emscripten_proxy_dlsync_async(pthread_ptr,info.id);PThread.outstandingPromises[pthread_ptr]=info;promises.push(info.promise)}}Promise.all(promises).then(()=>{PThread.outstandingPromises={};getWasmTableEntry(callback)(ctx)})}__emscripten_dlsync_threads_async.sig="vppp";var nowIsMonotonic=true;function __emscripten_get_now_is_monotonic(){return nowIsMonotonic}__emscripten_get_now_is_monotonic.sig="i";function __emscripten_thread_mailbox_await(pthread_ptr){if(typeof Atomics.waitAsync==="function"){var wait=Atomics.waitAsync(GROWABLE_HEAP_I32(),pthread_ptr>>2,pthread_ptr);wait.value.then(checkMailbox);var waitingAsync=pthread_ptr+128;Atomics.store(GROWABLE_HEAP_I32(),waitingAsync>>2,1)}}Module["__emscripten_thread_mailbox_await"]=__emscripten_thread_mailbox_await;__emscripten_thread_mailbox_await.sig="vp";function checkMailbox(){var pthread_ptr=_pthread_self();if(pthread_ptr){__emscripten_thread_mailbox_await(pthread_ptr);callUserCallback(()=>__emscripten_check_mailbox())}}Module["checkMailbox"]=checkMailbox;function __emscripten_notify_mailbox_postmessage(targetThreadId,currThreadId,mainThreadId){if(targetThreadId==currThreadId){setTimeout(()=>checkMailbox())}else if(ENVIRONMENT_IS_PTHREAD){postMessage({"targetThread":targetThreadId,"cmd":"checkMailbox"})}else{var worker=PThread.pthreads[targetThreadId];if(!worker){return}worker.postMessage({"cmd":"checkMailbox"})}}__emscripten_notify_mailbox_postmessage.sig="vppp";function __emscripten_set_offscreencanvas_size(target,width,height){return-1}__emscripten_set_offscreencanvas_size.sig="ipii";function __emscripten_thread_exit_joinable(thread){if(!ENVIRONMENT_IS_PTHREAD)markAsFinshed(thread);else postMessage({"cmd":"markAsFinshed","thread":thread})}__emscripten_thread_exit_joinable.sig="vp";function __emscripten_thread_set_strongref(thread){if(ENVIRONMENT_IS_NODE){PThread.pthreads[thread].ref()}}__emscripten_thread_set_strongref.sig="vp";function __emscripten_throw_longjmp(){throw Infinity}__emscripten_throw_longjmp.sig="v";function readI53FromI64(ptr){return GROWABLE_HEAP_U32()[ptr>>2]+GROWABLE_HEAP_I32()[ptr+4>>2]*4294967296}function __gmtime_js(time,tmPtr){var date=new Date(readI53FromI64(time)*1e3);GROWABLE_HEAP_I32()[tmPtr>>2]=date.getUTCSeconds();GROWABLE_HEAP_I32()[tmPtr+4>>2]=date.getUTCMinutes();GROWABLE_HEAP_I32()[tmPtr+8>>2]=date.getUTCHours();GROWABLE_HEAP_I32()[tmPtr+12>>2]=date.getUTCDate();GROWABLE_HEAP_I32()[tmPtr+16>>2]=date.getUTCMonth();GROWABLE_HEAP_I32()[tmPtr+20>>2]=date.getUTCFullYear()-1900;GROWABLE_HEAP_I32()[tmPtr+24>>2]=date.getUTCDay();var start=Date.UTC(date.getUTCFullYear(),0,1,0,0,0,0);var yday=(date.getTime()-start)/(1e3*60*60*24)|0;GROWABLE_HEAP_I32()[tmPtr+28>>2]=yday}__gmtime_js.sig="vpp";function isLeapYear(year){return year%4===0&&(year%100!==0||year%400===0)}var MONTH_DAYS_LEAP_CUMULATIVE=[0,31,60,91,121,152,182,213,244,274,305,335];var MONTH_DAYS_REGULAR_CUMULATIVE=[0,31,59,90,120,151,181,212,243,273,304,334];function ydayFromDate(date){var leap=isLeapYear(date.getFullYear());var monthDaysCumulative=leap?MONTH_DAYS_LEAP_CUMULATIVE:MONTH_DAYS_REGULAR_CUMULATIVE;var yday=monthDaysCumulative[date.getMonth()]+date.getDate()-1;return yday}function __localtime_js(time,tmPtr){var date=new Date(readI53FromI64(time)*1e3);GROWABLE_HEAP_I32()[tmPtr>>2]=date.getSeconds();GROWABLE_HEAP_I32()[tmPtr+4>>2]=date.getMinutes();GROWABLE_HEAP_I32()[tmPtr+8>>2]=date.getHours();GROWABLE_HEAP_I32()[tmPtr+12>>2]=date.getDate();GROWABLE_HEAP_I32()[tmPtr+16>>2]=date.getMonth();GROWABLE_HEAP_I32()[tmPtr+20>>2]=date.getFullYear()-1900;GROWABLE_HEAP_I32()[tmPtr+24>>2]=date.getDay();var yday=ydayFromDate(date)|0;GROWABLE_HEAP_I32()[tmPtr+28>>2]=yday;GROWABLE_HEAP_I32()[tmPtr+36>>2]=-(date.getTimezoneOffset()*60);var start=new Date(date.getFullYear(),0,1);var summerOffset=new Date(date.getFullYear(),6,1).getTimezoneOffset();var winterOffset=start.getTimezoneOffset();var dst=(summerOffset!=winterOffset&&date.getTimezoneOffset()==Math.min(winterOffset,summerOffset))|0;GROWABLE_HEAP_I32()[tmPtr+32>>2]=dst}__localtime_js.sig="vpp";function __mktime_js(tmPtr){var date=new Date(GROWABLE_HEAP_I32()[tmPtr+20>>2]+1900,GROWABLE_HEAP_I32()[tmPtr+16>>2],GROWABLE_HEAP_I32()[tmPtr+12>>2],GROWABLE_HEAP_I32()[tmPtr+8>>2],GROWABLE_HEAP_I32()[tmPtr+4>>2],GROWABLE_HEAP_I32()[tmPtr>>2],0);var dst=GROWABLE_HEAP_I32()[tmPtr+32>>2];var guessedOffset=date.getTimezoneOffset();var start=new Date(date.getFullYear(),0,1);var summerOffset=new Date(date.getFullYear(),6,1).getTimezoneOffset();var winterOffset=start.getTimezoneOffset();var dstOffset=Math.min(winterOffset,summerOffset);if(dst<0){GROWABLE_HEAP_I32()[tmPtr+32>>2]=Number(summerOffset!=winterOffset&&dstOffset==guessedOffset)}else if(dst>0!=(dstOffset==guessedOffset)){var nonDstOffset=Math.max(winterOffset,summerOffset);var trueOffset=dst>0?dstOffset:nonDstOffset;date.setTime(date.getTime()+(trueOffset-guessedOffset)*6e4)}GROWABLE_HEAP_I32()[tmPtr+24>>2]=date.getDay();var yday=ydayFromDate(date)|0;GROWABLE_HEAP_I32()[tmPtr+28>>2]=yday;GROWABLE_HEAP_I32()[tmPtr>>2]=date.getSeconds();GROWABLE_HEAP_I32()[tmPtr+4>>2]=date.getMinutes();GROWABLE_HEAP_I32()[tmPtr+8>>2]=date.getHours();GROWABLE_HEAP_I32()[tmPtr+12>>2]=date.getDate();GROWABLE_HEAP_I32()[tmPtr+16>>2]=date.getMonth();GROWABLE_HEAP_I32()[tmPtr+20>>2]=date.getYear();return date.getTime()/1e3|0}__mktime_js.sig="ip";function stringToNewUTF8(str){var size=lengthBytesUTF8(str)+1;var ret=_malloc(size);if(ret)stringToUTF8(str,ret,size);return ret}function __tzset_js(timezone,daylight,tzname){var currentYear=(new Date).getFullYear();var winter=new Date(currentYear,0,1);var summer=new Date(currentYear,6,1);var winterOffset=winter.getTimezoneOffset();var summerOffset=summer.getTimezoneOffset();var stdTimezoneOffset=Math.max(winterOffset,summerOffset);GROWABLE_HEAP_U32()[timezone>>2]=stdTimezoneOffset*60;GROWABLE_HEAP_I32()[daylight>>2]=Number(winterOffset!=summerOffset);function extractZone(date){var match=date.toTimeString().match(/\(([A-Za-z ]+)\)$/);return match?match[1]:"GMT"}var winterName=extractZone(winter);var summerName=extractZone(summer);var winterNamePtr=stringToNewUTF8(winterName);var summerNamePtr=stringToNewUTF8(summerName);if(summerOffset<winterOffset){GROWABLE_HEAP_U32()[tzname>>2]=winterNamePtr;GROWABLE_HEAP_U32()[tzname+4>>2]=summerNamePtr}else{GROWABLE_HEAP_U32()[tzname>>2]=summerNamePtr;GROWABLE_HEAP_U32()[tzname+4>>2]=winterNamePtr}}__tzset_js.sig="vppp";function _abort(){abort("")}_abort.sig="v";function _emscripten_set_main_loop_timing(mode,value){Browser.mainLoop.timingMode=mode;Browser.mainLoop.timingValue=value;if(!Browser.mainLoop.func){return 1}if(!Browser.mainLoop.running){runtimeKeepalivePush();Browser.mainLoop.running=true}if(mode==0){Browser.mainLoop.scheduler=function Browser_mainLoop_scheduler_setTimeout(){var timeUntilNextTick=Math.max(0,Browser.mainLoop.tickStartTime+value-_emscripten_get_now())|0;setTimeout(Browser.mainLoop.runner,timeUntilNextTick)};Browser.mainLoop.method="timeout"}else if(mode==1){Browser.mainLoop.scheduler=function Browser_mainLoop_scheduler_rAF(){Browser.requestAnimationFrame(Browser.mainLoop.runner)};Browser.mainLoop.method="rAF"}else if(mode==2){if(typeof setImmediate=="undefined"){var setImmediates=[];var emscriptenMainLoopMessageId="setimmediate";var Browser_setImmediate_messageHandler=event=>{if(event.data===emscriptenMainLoopMessageId||event.data.target===emscriptenMainLoopMessageId){event.stopPropagation();setImmediates.shift()()}};addEventListener("message",Browser_setImmediate_messageHandler,true);setImmediate=function Browser_emulated_setImmediate(func){setImmediates.push(func);if(ENVIRONMENT_IS_WORKER){if(Module["setImmediates"]===undefined)Module["setImmediates"]=[];Module["setImmediates"].push(func);postMessage({target:emscriptenMainLoopMessageId})}else postMessage(emscriptenMainLoopMessageId,"*")}}Browser.mainLoop.scheduler=function Browser_mainLoop_scheduler_setImmediate(){setImmediate(Browser.mainLoop.runner)};Browser.mainLoop.method="immediate"}return 0}_emscripten_set_main_loop_timing.sig="iii";var _emscripten_get_now;if(ENVIRONMENT_IS_NODE){global.performance=require("perf_hooks").performance}_emscripten_get_now=()=>performance.timeOrigin+performance.now();_emscripten_get_now.sig="d";function setMainLoop(browserIterationFunc,fps,simulateInfiniteLoop,arg,noSetTiming){assert(!Browser.mainLoop.func,"emscripten_set_main_loop: there can only be one main loop function at once: call emscripten_cancel_main_loop to cancel the previous one before setting a new one with different parameters.");Browser.mainLoop.func=browserIterationFunc;Browser.mainLoop.arg=arg;var thisMainLoopId=Browser.mainLoop.currentlyRunningMainloop;function checkIsRunning(){if(thisMainLoopId<Browser.mainLoop.currentlyRunningMainloop){runtimeKeepalivePop();return false}return true}Browser.mainLoop.running=false;Browser.mainLoop.runner=function Browser_mainLoop_runner(){if(ABORT)return;if(Browser.mainLoop.queue.length>0){var start=Date.now();var blocker=Browser.mainLoop.queue.shift();blocker.func(blocker.arg);if(Browser.mainLoop.remainingBlockers){var remaining=Browser.mainLoop.remainingBlockers;var next=remaining%1==0?remaining-1:Math.floor(remaining);if(blocker.counted){Browser.mainLoop.remainingBlockers=next}else{next=next+.5;Browser.mainLoop.remainingBlockers=(8*remaining+next)/9}}out('main loop blocker "'+blocker.name+'" took '+(Date.now()-start)+" ms");Browser.mainLoop.updateStatus();if(!checkIsRunning())return;setTimeout(Browser.mainLoop.runner,0);return}if(!checkIsRunning())return;Browser.mainLoop.currentFrameNumber=Browser.mainLoop.currentFrameNumber+1|0;if(Browser.mainLoop.timingMode==1&&Browser.mainLoop.timingValue>1&&Browser.mainLoop.currentFrameNumber%Browser.mainLoop.timingValue!=0){Browser.mainLoop.scheduler();return}else if(Browser.mainLoop.timingMode==0){Browser.mainLoop.tickStartTime=_emscripten_get_now()}Browser.mainLoop.runIter(browserIterationFunc);if(!checkIsRunning())return;if(typeof SDL=="object"&&SDL.audio&&SDL.audio.queueNewAudioData)SDL.audio.queueNewAudioData();Browser.mainLoop.scheduler()};if(!noSetTiming){if(fps&&fps>0){_emscripten_set_main_loop_timing(0,1e3/fps)}else{_emscripten_set_main_loop_timing(1,1)}Browser.mainLoop.scheduler()}if(simulateInfiniteLoop){throw"unwind"}}function safeSetTimeout(func,timeout){runtimeKeepalivePush();return setTimeout(()=>{runtimeKeepalivePop();callUserCallback(func)},timeout)}var Browser={mainLoop:{running:false,scheduler:null,method:"",currentlyRunningMainloop:0,func:null,arg:0,timingMode:0,timingValue:0,currentFrameNumber:0,queue:[],pause:function(){Browser.mainLoop.scheduler=null;Browser.mainLoop.currentlyRunningMainloop++},resume:function(){Browser.mainLoop.currentlyRunningMainloop++;var timingMode=Browser.mainLoop.timingMode;var timingValue=Browser.mainLoop.timingValue;var func=Browser.mainLoop.func;Browser.mainLoop.func=null;setMainLoop(func,0,false,Browser.mainLoop.arg,true);_emscripten_set_main_loop_timing(timingMode,timingValue);Browser.mainLoop.scheduler()},updateStatus:function(){if(Module["setStatus"]){var message=Module["statusMessage"]||"Please wait...";var remaining=Browser.mainLoop.remainingBlockers;var expected=Browser.mainLoop.expectedBlockers;if(remaining){if(remaining<expected){Module["setStatus"](message+" ("+(expected-remaining)+"/"+expected+")")}else{Module["setStatus"](message)}}else{Module["setStatus"]("")}}},runIter:function(func){if(ABORT)return;if(Module["preMainLoop"]){var preRet=Module["preMainLoop"]();if(preRet===false){return}}callUserCallback(func);if(Module["postMainLoop"])Module["postMainLoop"]()}},isFullscreen:false,pointerLock:false,moduleContextCreatedCallbacks:[],workers:[],init:function(){if(Browser.initted)return;Browser.initted=true;var imagePlugin={};imagePlugin["canHandle"]=function imagePlugin_canHandle(name){return!Module.noImageDecoding&&/\.(jpg|jpeg|png|bmp)$/i.test(name)};imagePlugin["handle"]=function imagePlugin_handle(byteArray,name,onload,onerror){var b=new Blob([byteArray],{type:Browser.getMimetype(name)});if(b.size!==byteArray.length){b=new Blob([new Uint8Array(byteArray).buffer],{type:Browser.getMimetype(name)})}var url=URL.createObjectURL(b);var img=new Image;img.onload=()=>{assert(img.complete,"Image "+name+" could not be decoded");var canvas=document.createElement("canvas");canvas.width=img.width;canvas.height=img.height;var ctx=canvas.getContext("2d");ctx.drawImage(img,0,0);preloadedImages[name]=canvas;URL.revokeObjectURL(url);if(onload)onload(byteArray)};img.onerror=event=>{out("Image "+url+" could not be decoded");if(onerror)onerror()};img.src=url};preloadPlugins.push(imagePlugin);var audioPlugin={};audioPlugin["canHandle"]=function audioPlugin_canHandle(name){return!Module.noAudioDecoding&&name.substr(-4)in{".ogg":1,".wav":1,".mp3":1}};audioPlugin["handle"]=function audioPlugin_handle(byteArray,name,onload,onerror){var done=false;function finish(audio){if(done)return;done=true;preloadedAudios[name]=audio;if(onload)onload(byteArray)}var b=new Blob([byteArray],{type:Browser.getMimetype(name)});var url=URL.createObjectURL(b);var audio=new Audio;audio.addEventListener("canplaythrough",()=>finish(audio),false);audio.onerror=function audio_onerror(event){if(done)return;err("warning: browser could not fully decode audio "+name+", trying slower base64 approach");function encode64(data){var BASE="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";var PAD="=";var ret="";var leftchar=0;var leftbits=0;for(var i=0;i<data.length;i++){leftchar=leftchar<<8|data[i];leftbits+=8;while(leftbits>=6){var curr=leftchar>>leftbits-6&63;leftbits-=6;ret+=BASE[curr]}}if(leftbits==2){ret+=BASE[(leftchar&3)<<4];ret+=PAD+PAD}else if(leftbits==4){ret+=BASE[(leftchar&15)<<2];ret+=PAD}return ret}audio.src="data:audio/x-"+name.substr(-3)+";base64,"+encode64(byteArray);finish(audio)};audio.src=url;safeSetTimeout(()=>{finish(audio)},1e4)};preloadPlugins.push(audioPlugin);function pointerLockChange(){Browser.pointerLock=document["pointerLockElement"]===Module["canvas"]||document["mozPointerLockElement"]===Module["canvas"]||document["webkitPointerLockElement"]===Module["canvas"]||document["msPointerLockElement"]===Module["canvas"]}var canvas=Module["canvas"];if(canvas){canvas.requestPointerLock=canvas["requestPointerLock"]||canvas["mozRequestPointerLock"]||canvas["webkitRequestPointerLock"]||canvas["msRequestPointerLock"]||(()=>{});canvas.exitPointerLock=document["exitPointerLock"]||document["mozExitPointerLock"]||document["webkitExitPointerLock"]||document["msExitPointerLock"]||(()=>{});canvas.exitPointerLock=canvas.exitPointerLock.bind(document);document.addEventListener("pointerlockchange",pointerLockChange,false);document.addEventListener("mozpointerlockchange",pointerLockChange,false);document.addEventListener("webkitpointerlockchange",pointerLockChange,false);document.addEventListener("mspointerlockchange",pointerLockChange,false);if(Module["elementPointerLock"]){canvas.addEventListener("click",ev=>{if(!Browser.pointerLock&&Module["canvas"].requestPointerLock){Module["canvas"].requestPointerLock();ev.preventDefault()}},false)}}},createContext:function(canvas,useWebGL,setInModule,webGLContextAttributes){if(useWebGL&&Module.ctx&&canvas==Module.canvas)return Module.ctx;var ctx;var contextHandle;if(useWebGL){var contextAttributes={antialias:false,alpha:false,majorVersion:1};if(webGLContextAttributes){for(var attribute in webGLContextAttributes){contextAttributes[attribute]=webGLContextAttributes[attribute]}}if(typeof GL!="undefined"){contextHandle=GL.createContext(canvas,contextAttributes);if(contextHandle){ctx=GL.getContext(contextHandle).GLctx}}}else{ctx=canvas.getContext("2d")}if(!ctx)return null;if(setInModule){if(!useWebGL)assert(typeof GLctx=="undefined","cannot set in module if GLctx is used, but we are a non-GL context that would replace it");Module.ctx=ctx;if(useWebGL)GL.makeContextCurrent(contextHandle);Module.useWebGL=useWebGL;Browser.moduleContextCreatedCallbacks.forEach(callback=>callback());Browser.init()}return ctx},destroyContext:function(canvas,useWebGL,setInModule){},fullscreenHandlersInstalled:false,lockPointer:undefined,resizeCanvas:undefined,requestFullscreen:function(lockPointer,resizeCanvas){Browser.lockPointer=lockPointer;Browser.resizeCanvas=resizeCanvas;if(typeof Browser.lockPointer=="undefined")Browser.lockPointer=true;if(typeof Browser.resizeCanvas=="undefined")Browser.resizeCanvas=false;var canvas=Module["canvas"];function fullscreenChange(){Browser.isFullscreen=false;var canvasContainer=canvas.parentNode;if((document["fullscreenElement"]||document["mozFullScreenElement"]||document["msFullscreenElement"]||document["webkitFullscreenElement"]||document["webkitCurrentFullScreenElement"])===canvasContainer){canvas.exitFullscreen=Browser.exitFullscreen;if(Browser.lockPointer)canvas.requestPointerLock();Browser.isFullscreen=true;if(Browser.resizeCanvas){Browser.setFullscreenCanvasSize()}else{Browser.updateCanvasDimensions(canvas)}}else{canvasContainer.parentNode.insertBefore(canvas,canvasContainer);canvasContainer.parentNode.removeChild(canvasContainer);if(Browser.resizeCanvas){Browser.setWindowedCanvasSize()}else{Browser.updateCanvasDimensions(canvas)}}if(Module["onFullScreen"])Module["onFullScreen"](Browser.isFullscreen);if(Module["onFullscreen"])Module["onFullscreen"](Browser.isFullscreen)}if(!Browser.fullscreenHandlersInstalled){Browser.fullscreenHandlersInstalled=true;document.addEventListener("fullscreenchange",fullscreenChange,false);document.addEventListener("mozfullscreenchange",fullscreenChange,false);document.addEventListener("webkitfullscreenchange",fullscreenChange,false);document.addEventListener("MSFullscreenChange",fullscreenChange,false)}var canvasContainer=document.createElement("div");canvas.parentNode.insertBefore(canvasContainer,canvas);canvasContainer.appendChild(canvas);canvasContainer.requestFullscreen=canvasContainer["requestFullscreen"]||canvasContainer["mozRequestFullScreen"]||canvasContainer["msRequestFullscreen"]||(canvasContainer["webkitRequestFullscreen"]?()=>canvasContainer["webkitRequestFullscreen"](Element["ALLOW_KEYBOARD_INPUT"]):null)||(canvasContainer["webkitRequestFullScreen"]?()=>canvasContainer["webkitRequestFullScreen"](Element["ALLOW_KEYBOARD_INPUT"]):null);canvasContainer.requestFullscreen()},exitFullscreen:function(){if(!Browser.isFullscreen){return false}var CFS=document["exitFullscreen"]||document["cancelFullScreen"]||document["mozCancelFullScreen"]||document["msExitFullscreen"]||document["webkitCancelFullScreen"]||(()=>{});CFS.apply(document,[]);return true},nextRAF:0,fakeRequestAnimationFrame:function(func){var now=Date.now();if(Browser.nextRAF===0){Browser.nextRAF=now+1e3/60}else{while(now+2>=Browser.nextRAF){Browser.nextRAF+=1e3/60}}var delay=Math.max(Browser.nextRAF-now,0);setTimeout(func,delay)},requestAnimationFrame:function(func){if(typeof requestAnimationFrame=="function"){requestAnimationFrame(func);return}var RAF=Browser.fakeRequestAnimationFrame;RAF(func)},safeSetTimeout:function(func,timeout){return safeSetTimeout(func,timeout)},safeRequestAnimationFrame:function(func){runtimeKeepalivePush();return Browser.requestAnimationFrame(()=>{runtimeKeepalivePop();callUserCallback(func)})},getMimetype:function(name){return{"jpg":"image/jpeg","jpeg":"image/jpeg","png":"image/png","bmp":"image/bmp","ogg":"audio/ogg","wav":"audio/wav","mp3":"audio/mpeg"}[name.substr(name.lastIndexOf(".")+1)]},getUserMedia:function(func){if(!window.getUserMedia){window.getUserMedia=navigator["getUserMedia"]||navigator["mozGetUserMedia"]}window.getUserMedia(func)},getMovementX:function(event){return event["movementX"]||event["mozMovementX"]||event["webkitMovementX"]||0},getMovementY:function(event){return event["movementY"]||event["mozMovementY"]||event["webkitMovementY"]||0},getMouseWheelDelta:function(event){var delta=0;switch(event.type){case"DOMMouseScroll":delta=event.detail/3;break;case"mousewheel":delta=event.wheelDelta/120;break;case"wheel":delta=event.deltaY;switch(event.deltaMode){case 0:delta/=100;break;case 1:delta/=3;break;case 2:delta*=80;break;default:throw"unrecognized mouse wheel delta mode: "+event.deltaMode}break;default:throw"unrecognized mouse wheel event: "+event.type}return delta},mouseX:0,mouseY:0,mouseMovementX:0,mouseMovementY:0,touches:{},lastTouches:{},calculateMouseEvent:function(event){if(Browser.pointerLock){if(event.type!="mousemove"&&"mozMovementX"in event){Browser.mouseMovementX=Browser.mouseMovementY=0}else{Browser.mouseMovementX=Browser.getMovementX(event);Browser.mouseMovementY=Browser.getMovementY(event)}if(typeof SDL!="undefined"){Browser.mouseX=SDL.mouseX+Browser.mouseMovementX;Browser.mouseY=SDL.mouseY+Browser.mouseMovementY}else{Browser.mouseX+=Browser.mouseMovementX;Browser.mouseY+=Browser.mouseMovementY}}else{var rect=Module["canvas"].getBoundingClientRect();var cw=Module["canvas"].width;var ch=Module["canvas"].height;var scrollX=typeof window.scrollX!="undefined"?window.scrollX:window.pageXOffset;var scrollY=typeof window.scrollY!="undefined"?window.scrollY:window.pageYOffset;if(event.type==="touchstart"||event.type==="touchend"||event.type==="touchmove"){var touch=event.touch;if(touch===undefined){return}var adjustedX=touch.pageX-(scrollX+rect.left);var adjustedY=touch.pageY-(scrollY+rect.top);adjustedX=adjustedX*(cw/rect.width);adjustedY=adjustedY*(ch/rect.height);var coords={x:adjustedX,y:adjustedY};if(event.type==="touchstart"){Browser.lastTouches[touch.identifier]=coords;Browser.touches[touch.identifier]=coords}else if(event.type==="touchend"||event.type==="touchmove"){var last=Browser.touches[touch.identifier];if(!last)last=coords;Browser.lastTouches[touch.identifier]=last;Browser.touches[touch.identifier]=coords}return}var x=event.pageX-(scrollX+rect.left);var y=event.pageY-(scrollY+rect.top);x=x*(cw/rect.width);y=y*(ch/rect.height);Browser.mouseMovementX=x-Browser.mouseX;Browser.mouseMovementY=y-Browser.mouseY;Browser.mouseX=x;Browser.mouseY=y}},resizeListeners:[],updateResizeListeners:function(){var canvas=Module["canvas"];Browser.resizeListeners.forEach(listener=>listener(canvas.width,canvas.height))},setCanvasSize:function(width,height,noUpdates){var canvas=Module["canvas"];Browser.updateCanvasDimensions(canvas,width,height);if(!noUpdates)Browser.updateResizeListeners()},windowedWidth:0,windowedHeight:0,setFullscreenCanvasSize:function(){if(typeof SDL!="undefined"){var flags=GROWABLE_HEAP_U32()[SDL.screen>>2];flags=flags|8388608;GROWABLE_HEAP_I32()[SDL.screen>>2]=flags}Browser.updateCanvasDimensions(Module["canvas"]);Browser.updateResizeListeners()},setWindowedCanvasSize:function(){if(typeof SDL!="undefined"){var flags=GROWABLE_HEAP_U32()[SDL.screen>>2];flags=flags&~8388608;GROWABLE_HEAP_I32()[SDL.screen>>2]=flags}Browser.updateCanvasDimensions(Module["canvas"]);Browser.updateResizeListeners()},updateCanvasDimensions:function(canvas,wNative,hNative){if(wNative&&hNative){canvas.widthNative=wNative;canvas.heightNative=hNative}else{wNative=canvas.widthNative;hNative=canvas.heightNative}var w=wNative;var h=hNative;if(Module["forcedAspectRatio"]&&Module["forcedAspectRatio"]>0){if(w/h<Module["forcedAspectRatio"]){w=Math.round(h*Module["forcedAspectRatio"])}else{h=Math.round(w/Module["forcedAspectRatio"])}}if((document["fullscreenElement"]||document["mozFullScreenElement"]||document["msFullscreenElement"]||document["webkitFullscreenElement"]||document["webkitCurrentFullScreenElement"])===canvas.parentNode&&typeof screen!="undefined"){var factor=Math.min(screen.width/w,screen.height/h);w=Math.round(w*factor);h=Math.round(h*factor)}if(Browser.resizeCanvas){if(canvas.width!=w)canvas.width=w;if(canvas.height!=h)canvas.height=h;if(typeof canvas.style!="undefined"){canvas.style.removeProperty("width");canvas.style.removeProperty("height")}}else{if(canvas.width!=wNative)canvas.width=wNative;if(canvas.height!=hNative)canvas.height=hNative;if(typeof canvas.style!="undefined"){if(w!=wNative||h!=hNative){canvas.style.setProperty("width",w+"px","important");canvas.style.setProperty("height",h+"px","important")}else{canvas.style.removeProperty("width");canvas.style.removeProperty("height")}}}}};var EGL={errorCode:12288,defaultDisplayInitialized:false,currentContext:0,currentReadSurface:0,currentDrawSurface:0,contextAttributes:{alpha:false,depth:false,stencil:false,antialias:false},stringCache:{},setErrorCode:function(code){EGL.errorCode=code},chooseConfig:function(display,attribList,config,config_size,numConfigs){if(display!=62e3){EGL.setErrorCode(12296);return 0}if(attribList){for(;;){var param=GROWABLE_HEAP_I32()[attribList>>2];if(param==12321){var alphaSize=GROWABLE_HEAP_I32()[attribList+4>>2];EGL.contextAttributes.alpha=alphaSize>0}else if(param==12325){var depthSize=GROWABLE_HEAP_I32()[attribList+4>>2];EGL.contextAttributes.depth=depthSize>0}else if(param==12326){var stencilSize=GROWABLE_HEAP_I32()[attribList+4>>2];EGL.contextAttributes.stencil=stencilSize>0}else if(param==12337){var samples=GROWABLE_HEAP_I32()[attribList+4>>2];EGL.contextAttributes.antialias=samples>0}else if(param==12338){var samples=GROWABLE_HEAP_I32()[attribList+4>>2];EGL.contextAttributes.antialias=samples==1}else if(param==12544){var requestedPriority=GROWABLE_HEAP_I32()[attribList+4>>2];EGL.contextAttributes.lowLatency=requestedPriority!=12547}else if(param==12344){break}attribList+=8}}if((!config||!config_size)&&!numConfigs){EGL.setErrorCode(12300);return 0}if(numConfigs){GROWABLE_HEAP_I32()[numConfigs>>2]=1}if(config&&config_size>0){GROWABLE_HEAP_I32()[config>>2]=62002}EGL.setErrorCode(12288);return 1}};function _eglBindAPI(api){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(33,1,api);if(api==12448){EGL.setErrorCode(12288);return 1}EGL.setErrorCode(12300);return 0}_eglBindAPI.sig="ii";function _eglChooseConfig(display,attrib_list,configs,config_size,numConfigs){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(34,1,display,attrib_list,configs,config_size,numConfigs);return EGL.chooseConfig(display,attrib_list,configs,config_size,numConfigs)}_eglChooseConfig.sig="ipppip";function webgl_enable_ANGLE_instanced_arrays(ctx){var ext=ctx.getExtension("ANGLE_instanced_arrays");if(ext){ctx["vertexAttribDivisor"]=function(index,divisor){ext["vertexAttribDivisorANGLE"](index,divisor)};ctx["drawArraysInstanced"]=function(mode,first,count,primcount){ext["drawArraysInstancedANGLE"](mode,first,count,primcount)};ctx["drawElementsInstanced"]=function(mode,count,type,indices,primcount){ext["drawElementsInstancedANGLE"](mode,count,type,indices,primcount)};return 1}}function webgl_enable_OES_vertex_array_object(ctx){var ext=ctx.getExtension("OES_vertex_array_object");if(ext){ctx["createVertexArray"]=function(){return ext["createVertexArrayOES"]()};ctx["deleteVertexArray"]=function(vao){ext["deleteVertexArrayOES"](vao)};ctx["bindVertexArray"]=function(vao){ext["bindVertexArrayOES"](vao)};ctx["isVertexArray"]=function(vao){return ext["isVertexArrayOES"](vao)};return 1}}function webgl_enable_WEBGL_draw_buffers(ctx){var ext=ctx.getExtension("WEBGL_draw_buffers");if(ext){ctx["drawBuffers"]=function(n,bufs){ext["drawBuffersWEBGL"](n,bufs)};return 1}}function webgl_enable_WEBGL_multi_draw(ctx){return!!(ctx.multiDrawWebgl=ctx.getExtension("WEBGL_multi_draw"))}var GL={counter:1,buffers:[],programs:[],framebuffers:[],renderbuffers:[],textures:[],shaders:[],vaos:[],contexts:{},offscreenCanvases:{},queries:[],stringCache:{},unpackAlignment:4,recordError:function recordError(errorCode){if(!GL.lastError){GL.lastError=errorCode}},getNewId:function(table){var ret=GL.counter++;for(var i=table.length;i<ret;i++){table[i]=null}return ret},getSource:function(shader,count,string,length){var source="";for(var i=0;i<count;++i){var len=length?GROWABLE_HEAP_I32()[length+i*4>>2]:-1;source+=UTF8ToString(GROWABLE_HEAP_I32()[string+i*4>>2],len<0?undefined:len)}return source},createContext:function(canvas,webGLContextAttributes){if(!canvas.getContextSafariWebGL2Fixed){canvas.getContextSafariWebGL2Fixed=canvas.getContext;function fixedGetContext(ver,attrs){var gl=canvas.getContextSafariWebGL2Fixed(ver,attrs);return ver=="webgl"==gl instanceof WebGLRenderingContext?gl:null}canvas.getContext=fixedGetContext}var ctx=canvas.getContext("webgl",webGLContextAttributes);if(!ctx)return 0;var handle=GL.registerContext(ctx,webGLContextAttributes);return handle},registerContext:function(ctx,webGLContextAttributes){var handle=_malloc(8);GROWABLE_HEAP_I32()[handle+4>>2]=_pthread_self();var context={handle:handle,attributes:webGLContextAttributes,version:webGLContextAttributes.majorVersion,GLctx:ctx};if(ctx.canvas)ctx.canvas.GLctxObject=context;GL.contexts[handle]=context;if(typeof webGLContextAttributes.enableExtensionsByDefault=="undefined"||webGLContextAttributes.enableExtensionsByDefault){GL.initExtensions(context)}return handle},makeContextCurrent:function(contextHandle){GL.currentContext=GL.contexts[contextHandle];Module.ctx=GLctx=GL.currentContext&&GL.currentContext.GLctx;return!(contextHandle&&!GLctx)},getContext:function(contextHandle){return GL.contexts[contextHandle]},deleteContext:function(contextHandle){if(GL.currentContext===GL.contexts[contextHandle])GL.currentContext=null;if(typeof JSEvents=="object")JSEvents.removeAllHandlersOnTarget(GL.contexts[contextHandle].GLctx.canvas);if(GL.contexts[contextHandle]&&GL.contexts[contextHandle].GLctx.canvas)GL.contexts[contextHandle].GLctx.canvas.GLctxObject=undefined;_free(GL.contexts[contextHandle].handle);GL.contexts[contextHandle]=null},initExtensions:function(context){if(!context)context=GL.currentContext;if(context.initExtensionsDone)return;context.initExtensionsDone=true;var GLctx=context.GLctx;webgl_enable_ANGLE_instanced_arrays(GLctx);webgl_enable_OES_vertex_array_object(GLctx);webgl_enable_WEBGL_draw_buffers(GLctx);{GLctx.disjointTimerQueryExt=GLctx.getExtension("EXT_disjoint_timer_query")}webgl_enable_WEBGL_multi_draw(GLctx);var exts=GLctx.getSupportedExtensions()||[];exts.forEach(function(ext){if(!ext.includes("lose_context")&&!ext.includes("debug")){GLctx.getExtension(ext)}})}};function _eglCreateContext(display,config,hmm,contextAttribs){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(35,1,display,config,hmm,contextAttribs);if(display!=62e3){EGL.setErrorCode(12296);return 0}var glesContextVersion=1;for(;;){var param=GROWABLE_HEAP_I32()[contextAttribs>>2];if(param==12440){glesContextVersion=GROWABLE_HEAP_I32()[contextAttribs+4>>2]}else if(param==12344){break}else{EGL.setErrorCode(12292);return 0}contextAttribs+=8}if(glesContextVersion!=2){EGL.setErrorCode(12293);return 0}EGL.contextAttributes.majorVersion=glesContextVersion-1;EGL.contextAttributes.minorVersion=0;EGL.context=GL.createContext(Module["canvas"],EGL.contextAttributes);if(EGL.context!=0){EGL.setErrorCode(12288);GL.makeContextCurrent(EGL.context);Module.useWebGL=true;Browser.moduleContextCreatedCallbacks.forEach(function(callback){callback()});GL.makeContextCurrent(null);return 62004}else{EGL.setErrorCode(12297);return 0}}_eglCreateContext.sig="ppppp";function _eglCreateWindowSurface(display,config,win,attrib_list){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(36,1,display,config,win,attrib_list);if(display!=62e3){EGL.setErrorCode(12296);return 0}if(config!=62002){EGL.setErrorCode(12293);return 0}EGL.setErrorCode(12288);return 62006}_eglCreateWindowSurface.sig="pppip";function _eglDestroyContext(display,context){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(37,1,display,context);if(display!=62e3){EGL.setErrorCode(12296);return 0}if(context!=62004){EGL.setErrorCode(12294);return 0}GL.deleteContext(EGL.context);EGL.setErrorCode(12288);if(EGL.currentContext==context){EGL.currentContext=0}return 1}_eglDestroyContext.sig="ipp";function _eglDestroySurface(display,surface){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(38,1,display,surface);if(display!=62e3){EGL.setErrorCode(12296);return 0}if(surface!=62006){EGL.setErrorCode(12301);return 1}if(EGL.currentReadSurface==surface){EGL.currentReadSurface=0}if(EGL.currentDrawSurface==surface){EGL.currentDrawSurface=0}EGL.setErrorCode(12288);return 1}_eglDestroySurface.sig="ipp";function _eglGetConfigAttrib(display,config,attribute,value){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(39,1,display,config,attribute,value);if(display!=62e3){EGL.setErrorCode(12296);return 0}if(config!=62002){EGL.setErrorCode(12293);return 0}if(!value){EGL.setErrorCode(12300);return 0}EGL.setErrorCode(12288);switch(attribute){case 12320:GROWABLE_HEAP_I32()[value>>2]=EGL.contextAttributes.alpha?32:24;return 1;case 12321:GROWABLE_HEAP_I32()[value>>2]=EGL.contextAttributes.alpha?8:0;return 1;case 12322:GROWABLE_HEAP_I32()[value>>2]=8;return 1;case 12323:GROWABLE_HEAP_I32()[value>>2]=8;return 1;case 12324:GROWABLE_HEAP_I32()[value>>2]=8;return 1;case 12325:GROWABLE_HEAP_I32()[value>>2]=EGL.contextAttributes.depth?24:0;return 1;case 12326:GROWABLE_HEAP_I32()[value>>2]=EGL.contextAttributes.stencil?8:0;return 1;case 12327:GROWABLE_HEAP_I32()[value>>2]=12344;return 1;case 12328:GROWABLE_HEAP_I32()[value>>2]=62002;return 1;case 12329:GROWABLE_HEAP_I32()[value>>2]=0;return 1;case 12330:GROWABLE_HEAP_I32()[value>>2]=4096;return 1;case 12331:GROWABLE_HEAP_I32()[value>>2]=16777216;return 1;case 12332:GROWABLE_HEAP_I32()[value>>2]=4096;return 1;case 12333:GROWABLE_HEAP_I32()[value>>2]=0;return 1;case 12334:GROWABLE_HEAP_I32()[value>>2]=0;return 1;case 12335:GROWABLE_HEAP_I32()[value>>2]=12344;return 1;case 12337:GROWABLE_HEAP_I32()[value>>2]=EGL.contextAttributes.antialias?4:0;return 1;case 12338:GROWABLE_HEAP_I32()[value>>2]=EGL.contextAttributes.antialias?1:0;return 1;case 12339:GROWABLE_HEAP_I32()[value>>2]=4;return 1;case 12340:GROWABLE_HEAP_I32()[value>>2]=12344;return 1;case 12341:case 12342:case 12343:GROWABLE_HEAP_I32()[value>>2]=-1;return 1;case 12345:case 12346:GROWABLE_HEAP_I32()[value>>2]=0;return 1;case 12347:GROWABLE_HEAP_I32()[value>>2]=0;return 1;case 12348:GROWABLE_HEAP_I32()[value>>2]=1;return 1;case 12349:case 12350:GROWABLE_HEAP_I32()[value>>2]=0;return 1;case 12351:GROWABLE_HEAP_I32()[value>>2]=12430;return 1;case 12352:GROWABLE_HEAP_I32()[value>>2]=4;return 1;case 12354:GROWABLE_HEAP_I32()[value>>2]=0;return 1;default:EGL.setErrorCode(12292);return 0}}_eglGetConfigAttrib.sig="ippip";function _eglGetDisplay(nativeDisplayType){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(40,1,nativeDisplayType);EGL.setErrorCode(12288);return 62e3}_eglGetDisplay.sig="pi";function _eglGetError(){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(41,1);return EGL.errorCode}_eglGetError.sig="i";function _eglInitialize(display,majorVersion,minorVersion){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(42,1,display,majorVersion,minorVersion);if(display!=62e3){EGL.setErrorCode(12296);return 0}if(majorVersion){GROWABLE_HEAP_I32()[majorVersion>>2]=1}if(minorVersion){GROWABLE_HEAP_I32()[minorVersion>>2]=4}EGL.defaultDisplayInitialized=true;EGL.setErrorCode(12288);return 1}_eglInitialize.sig="ippp";function _eglMakeCurrent(display,draw,read,context){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(43,1,display,draw,read,context);if(display!=62e3){EGL.setErrorCode(12296);return 0}if(context!=0&&context!=62004){EGL.setErrorCode(12294);return 0}if(read!=0&&read!=62006||draw!=0&&draw!=62006){EGL.setErrorCode(12301);return 0}GL.makeContextCurrent(context?EGL.context:null);EGL.currentContext=context;EGL.currentDrawSurface=draw;EGL.currentReadSurface=read;EGL.setErrorCode(12288);return 1}_eglMakeCurrent.sig="ipppp";function _eglQueryString(display,name){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(44,1,display,name);if(display!=62e3){EGL.setErrorCode(12296);return 0}EGL.setErrorCode(12288);if(EGL.stringCache[name])return EGL.stringCache[name];var ret;switch(name){case 12371:ret=stringToNewUTF8("Emscripten");break;case 12372:ret=stringToNewUTF8("1.4 Emscripten EGL");break;case 12373:ret=stringToNewUTF8("");break;case 12429:ret=stringToNewUTF8("OpenGL_ES");break;default:EGL.setErrorCode(12300);return 0}EGL.stringCache[name]=ret;return ret}_eglQueryString.sig="ppi";function _eglSwapBuffers(dpy,surface){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(45,1,dpy,surface);if(!EGL.defaultDisplayInitialized){EGL.setErrorCode(12289)}else if(!Module.ctx){EGL.setErrorCode(12290)}else if(Module.ctx.isContextLost()){EGL.setErrorCode(12302)}else{EGL.setErrorCode(12288);return 1}return 0}_eglSwapBuffers.sig="ipp";function _eglSwapInterval(display,interval){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(46,1,display,interval);if(display!=62e3){EGL.setErrorCode(12296);return 0}if(interval==0)_emscripten_set_main_loop_timing(0,0);else _emscripten_set_main_loop_timing(1,interval);EGL.setErrorCode(12288);return 1}_eglSwapInterval.sig="ipi";function _eglTerminate(display){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(47,1,display);if(display!=62e3){EGL.setErrorCode(12296);return 0}EGL.currentContext=0;EGL.currentReadSurface=0;EGL.currentDrawSurface=0;EGL.defaultDisplayInitialized=false;EGL.setErrorCode(12288);return 1}_eglTerminate.sig="ip";function _eglWaitClient(){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(48,1);EGL.setErrorCode(12288);return 1}_eglWaitClient.sig="i";var _eglWaitGL=_eglWaitClient;_eglWaitGL.sig="i";function _eglWaitNative(nativeEngineId){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(49,1,nativeEngineId);EGL.setErrorCode(12288);return 1}_eglWaitNative.sig="ii";var readEmAsmArgsArray=[];function readEmAsmArgs(sigPtr,buf){readEmAsmArgsArray.length=0;var ch;buf>>=2;while(ch=GROWABLE_HEAP_U8()[sigPtr++]){buf+=ch!=105&buf;readEmAsmArgsArray.push(ch==105?GROWABLE_HEAP_I32()[buf]:GROWABLE_HEAP_F64()[buf++>>1]);++buf}return readEmAsmArgsArray}function runEmAsmFunction(code,sigPtr,argbuf){var args=readEmAsmArgs(sigPtr,argbuf);return ASM_CONSTS[code].apply(null,args)}function _emscripten_asm_const_int(code,sigPtr,argbuf){return runEmAsmFunction(code,sigPtr,argbuf)}_emscripten_asm_const_int.sig="ippp";function proxyToMainThread(index,sync){var numCallArgs=arguments.length-2;var outerArgs=arguments;return withStackSave(()=>{var serializedNumCallArgs=numCallArgs;var args=stackAlloc(serializedNumCallArgs*8);var b=args>>3;for(var i=0;i<numCallArgs;i++){var arg=outerArgs[2+i];GROWABLE_HEAP_F64()[b+i]=arg}return __emscripten_run_in_main_runtime_thread_js(index,serializedNumCallArgs,args,sync)})}function runMainThreadEmAsm(code,sigPtr,argbuf,sync){var args=readEmAsmArgs(sigPtr,argbuf);if(ENVIRONMENT_IS_PTHREAD){return proxyToMainThread.apply(null,[-1-code,sync].concat(args))}return ASM_CONSTS[code].apply(null,args)}function _emscripten_asm_const_int_sync_on_main_thread(code,sigPtr,argbuf){return runMainThreadEmAsm(code,sigPtr,argbuf,1)}_emscripten_asm_const_int_sync_on_main_thread.sig="ippp";function _emscripten_cancel_main_loop(){Browser.mainLoop.pause();Browser.mainLoop.func=null}_emscripten_cancel_main_loop.sig="v";function _emscripten_check_blocking_allowed(){}_emscripten_check_blocking_allowed.sig="v";function _emscripten_date_now(){return Date.now()}_emscripten_date_now.sig="d";function _emscripten_err(str){err(UTF8ToString(str))}_emscripten_err.sig="vp";var JSEvents={inEventHandler:0,removeAllEventListeners:function(){for(var i=JSEvents.eventHandlers.length-1;i>=0;--i){JSEvents._removeHandler(i)}JSEvents.eventHandlers=[];JSEvents.deferredCalls=[]},registerRemoveEventListeners:function(){if(!JSEvents.removeEventListenersRegistered){__ATEXIT__.push(JSEvents.removeAllEventListeners);JSEvents.removeEventListenersRegistered=true}},deferredCalls:[],deferCall:function(targetFunction,precedence,argsList){function arraysHaveEqualContent(arrA,arrB){if(arrA.length!=arrB.length)return false;for(var i in arrA){if(arrA[i]!=arrB[i])return false}return true}for(var i in JSEvents.deferredCalls){var call=JSEvents.deferredCalls[i];if(call.targetFunction==targetFunction&&arraysHaveEqualContent(call.argsList,argsList)){return}}JSEvents.deferredCalls.push({targetFunction:targetFunction,precedence:precedence,argsList:argsList});JSEvents.deferredCalls.sort(function(x,y){return x.precedence<y.precedence})},removeDeferredCalls:function(targetFunction){for(var i=0;i<JSEvents.deferredCalls.length;++i){if(JSEvents.deferredCalls[i].targetFunction==targetFunction){JSEvents.deferredCalls.splice(i,1);--i}}},canPerformEventHandlerRequests:function(){return JSEvents.inEventHandler&&JSEvents.currentEventHandler.allowsDeferredCalls},runDeferredCalls:function(){if(!JSEvents.canPerformEventHandlerRequests()){return}for(var i=0;i<JSEvents.deferredCalls.length;++i){var call=JSEvents.deferredCalls[i];JSEvents.deferredCalls.splice(i,1);--i;call.targetFunction.apply(null,call.argsList)}},eventHandlers:[],removeAllHandlersOnTarget:function(target,eventTypeString){for(var i=0;i<JSEvents.eventHandlers.length;++i){if(JSEvents.eventHandlers[i].target==target&&(!eventTypeString||eventTypeString==JSEvents.eventHandlers[i].eventTypeString)){JSEvents._removeHandler(i--)}}},_removeHandler:function(i){var h=JSEvents.eventHandlers[i];h.target.removeEventListener(h.eventTypeString,h.eventListenerFunc,h.useCapture);JSEvents.eventHandlers.splice(i,1)},registerOrRemoveHandler:function(eventHandler){if(!eventHandler.target){return-4}var jsEventHandler=function jsEventHandler(event){++JSEvents.inEventHandler;JSEvents.currentEventHandler=eventHandler;JSEvents.runDeferredCalls();eventHandler.handlerFunc(event);JSEvents.runDeferredCalls();--JSEvents.inEventHandler};if(eventHandler.callbackfunc){eventHandler.eventListenerFunc=jsEventHandler;eventHandler.target.addEventListener(eventHandler.eventTypeString,jsEventHandler,eventHandler.useCapture);JSEvents.eventHandlers.push(eventHandler);JSEvents.registerRemoveEventListeners()}else{for(var i=0;i<JSEvents.eventHandlers.length;++i){if(JSEvents.eventHandlers[i].target==eventHandler.target&&JSEvents.eventHandlers[i].eventTypeString==eventHandler.eventTypeString){JSEvents._removeHandler(i--)}}}return 0},queueEventHandlerOnThread_iiii:function(targetThread,eventHandlerFunc,eventTypeId,eventData,userData){withStackSave(function(){var varargs=stackAlloc(12);GROWABLE_HEAP_I32()[varargs>>2]=eventTypeId;GROWABLE_HEAP_I32()[varargs+4>>2]=eventData;GROWABLE_HEAP_I32()[varargs+8>>2]=userData;_emscripten_dispatch_to_thread_(targetThread,637534208,eventHandlerFunc,eventData,varargs)})},getTargetThreadForEventCallback:function(targetThread){switch(targetThread){case 1:return 0;case 2:return PThread.currentProxiedOperationCallerThread;default:return targetThread}},getNodeNameForTarget:function(target){if(!target)return"";if(target==window)return"#window";if(target==screen)return"#screen";return target&&target.nodeName?target.nodeName:""},fullscreenEnabled:function(){return document.fullscreenEnabled||document.webkitFullscreenEnabled}};var currentFullscreenStrategy={};function maybeCStringToJsString(cString){return cString>2?UTF8ToString(cString):cString}var specialHTMLTargets=[0,typeof document!="undefined"?document:0,typeof window!="undefined"?window:0];function findEventTarget(target){target=maybeCStringToJsString(target);var domElement=specialHTMLTargets[target]||(typeof document!="undefined"?document.querySelector(target):undefined);return domElement}function findCanvasEventTarget(target){return findEventTarget(target)}function getCanvasSizeCallingThread(target,width,height){var canvas=findCanvasEventTarget(target);if(!canvas)return-4;if(!canvas.controlTransferredOffscreen){GROWABLE_HEAP_I32()[width>>2]=canvas.width;GROWABLE_HEAP_I32()[height>>2]=canvas.height}else{return-4}return 0}function getCanvasSizeMainThread(target,width,height){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(51,1,target,width,height);return getCanvasSizeCallingThread(target,width,height)}function _emscripten_get_canvas_element_size(target,width,height){var canvas=findCanvasEventTarget(target);if(canvas){return getCanvasSizeCallingThread(target,width,height)}return getCanvasSizeMainThread(target,width,height)}_emscripten_get_canvas_element_size.sig="ippp";function getCanvasElementSize(target){return withStackSave(function(){var w=stackAlloc(8);var h=w+4;var targetInt=stringToUTF8OnStack(target.id);var ret=_emscripten_get_canvas_element_size(targetInt,w,h);var size=[GROWABLE_HEAP_I32()[w>>2],GROWABLE_HEAP_I32()[h>>2]];return size})}function setCanvasElementSizeCallingThread(target,width,height){var canvas=findCanvasEventTarget(target);if(!canvas)return-4;if(!canvas.controlTransferredOffscreen){var autoResizeViewport=false;if(canvas.GLctxObject&&canvas.GLctxObject.GLctx){var prevViewport=canvas.GLctxObject.GLctx.getParameter(2978);autoResizeViewport=prevViewport[0]===0&&prevViewport[1]===0&&prevViewport[2]===canvas.width&&prevViewport[3]===canvas.height}canvas.width=width;canvas.height=height;if(autoResizeViewport){canvas.GLctxObject.GLctx.viewport(0,0,width,height)}}else{return-4}return 0}function setCanvasElementSizeMainThread(target,width,height){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(52,1,target,width,height);return setCanvasElementSizeCallingThread(target,width,height)}setCanvasElementSizeMainThread.sig="iiii";function _emscripten_set_canvas_element_size(target,width,height){var canvas=findCanvasEventTarget(target);if(canvas){return setCanvasElementSizeCallingThread(target,width,height)}return setCanvasElementSizeMainThread(target,width,height)}_emscripten_set_canvas_element_size.sig="ipii";function setCanvasElementSize(target,width,height){if(!target.controlTransferredOffscreen){target.width=width;target.height=height}else{withStackSave(function(){var targetInt=stringToUTF8OnStack(target.id);_emscripten_set_canvas_element_size(targetInt,width,height)})}}function registerRestoreOldStyle(canvas){var canvasSize=getCanvasElementSize(canvas);var oldWidth=canvasSize[0];var oldHeight=canvasSize[1];var oldCssWidth=canvas.style.width;var oldCssHeight=canvas.style.height;var oldBackgroundColor=canvas.style.backgroundColor;var oldDocumentBackgroundColor=document.body.style.backgroundColor;var oldPaddingLeft=canvas.style.paddingLeft;var oldPaddingRight=canvas.style.paddingRight;var oldPaddingTop=canvas.style.paddingTop;var oldPaddingBottom=canvas.style.paddingBottom;var oldMarginLeft=canvas.style.marginLeft;var oldMarginRight=canvas.style.marginRight;var oldMarginTop=canvas.style.marginTop;var oldMarginBottom=canvas.style.marginBottom;var oldDocumentBodyMargin=document.body.style.margin;var oldDocumentOverflow=document.documentElement.style.overflow;var oldDocumentScroll=document.body.scroll;var oldImageRendering=canvas.style.imageRendering;function restoreOldStyle(){var fullscreenElement=document.fullscreenElement||document.webkitFullscreenElement;if(!fullscreenElement){document.removeEventListener("fullscreenchange",restoreOldStyle);document.removeEventListener("webkitfullscreenchange",restoreOldStyle);setCanvasElementSize(canvas,oldWidth,oldHeight);canvas.style.width=oldCssWidth;canvas.style.height=oldCssHeight;canvas.style.backgroundColor=oldBackgroundColor;if(!oldDocumentBackgroundColor)document.body.style.backgroundColor="white";document.body.style.backgroundColor=oldDocumentBackgroundColor;canvas.style.paddingLeft=oldPaddingLeft;canvas.style.paddingRight=oldPaddingRight;canvas.style.paddingTop=oldPaddingTop;canvas.style.paddingBottom=oldPaddingBottom;canvas.style.marginLeft=oldMarginLeft;canvas.style.marginRight=oldMarginRight;canvas.style.marginTop=oldMarginTop;canvas.style.marginBottom=oldMarginBottom;document.body.style.margin=oldDocumentBodyMargin;document.documentElement.style.overflow=oldDocumentOverflow;document.body.scroll=oldDocumentScroll;canvas.style.imageRendering=oldImageRendering;if(canvas.GLctxObject)canvas.GLctxObject.GLctx.viewport(0,0,oldWidth,oldHeight);if(currentFullscreenStrategy.canvasResizedCallback){if(currentFullscreenStrategy.canvasResizedCallbackTargetThread)JSEvents.queueEventHandlerOnThread_iiii(currentFullscreenStrategy.canvasResizedCallbackTargetThread,currentFullscreenStrategy.canvasResizedCallback,37,0,currentFullscreenStrategy.canvasResizedCallbackUserData);else getWasmTableEntry(currentFullscreenStrategy.canvasResizedCallback)(37,0,currentFullscreenStrategy.canvasResizedCallbackUserData)}}}document.addEventListener("fullscreenchange",restoreOldStyle);document.addEventListener("webkitfullscreenchange",restoreOldStyle);return restoreOldStyle}function setLetterbox(element,topBottom,leftRight){element.style.paddingLeft=element.style.paddingRight=leftRight+"px";element.style.paddingTop=element.style.paddingBottom=topBottom+"px"}function getBoundingClientRect(e){return specialHTMLTargets.indexOf(e)<0?e.getBoundingClientRect():{"left":0,"top":0}}function JSEvents_resizeCanvasForFullscreen(target,strategy){var restoreOldStyle=registerRestoreOldStyle(target);var cssWidth=strategy.softFullscreen?innerWidth:screen.width;var cssHeight=strategy.softFullscreen?innerHeight:screen.height;var rect=getBoundingClientRect(target);var windowedCssWidth=rect.width;var windowedCssHeight=rect.height;var canvasSize=getCanvasElementSize(target);var windowedRttWidth=canvasSize[0];var windowedRttHeight=canvasSize[1];if(strategy.scaleMode==3){setLetterbox(target,(cssHeight-windowedCssHeight)/2,(cssWidth-windowedCssWidth)/2);cssWidth=windowedCssWidth;cssHeight=windowedCssHeight}else if(strategy.scaleMode==2){if(cssWidth*windowedRttHeight<windowedRttWidth*cssHeight){var desiredCssHeight=windowedRttHeight*cssWidth/windowedRttWidth;setLetterbox(target,(cssHeight-desiredCssHeight)/2,0);cssHeight=desiredCssHeight}else{var desiredCssWidth=windowedRttWidth*cssHeight/windowedRttHeight;setLetterbox(target,0,(cssWidth-desiredCssWidth)/2);cssWidth=desiredCssWidth}}if(!target.style.backgroundColor)target.style.backgroundColor="black";if(!document.body.style.backgroundColor)document.body.style.backgroundColor="black";target.style.width=cssWidth+"px";target.style.height=cssHeight+"px";if(strategy.filteringMode==1){target.style.imageRendering="optimizeSpeed";target.style.imageRendering="-moz-crisp-edges";target.style.imageRendering="-o-crisp-edges";target.style.imageRendering="-webkit-optimize-contrast";target.style.imageRendering="optimize-contrast";target.style.imageRendering="crisp-edges";target.style.imageRendering="pixelated"}var dpiScale=strategy.canvasResolutionScaleMode==2?devicePixelRatio:1;if(strategy.canvasResolutionScaleMode!=0){var newWidth=cssWidth*dpiScale|0;var newHeight=cssHeight*dpiScale|0;setCanvasElementSize(target,newWidth,newHeight);if(target.GLctxObject)target.GLctxObject.GLctx.viewport(0,0,newWidth,newHeight)}return restoreOldStyle}function JSEvents_requestFullscreen(target,strategy){if(strategy.scaleMode!=0||strategy.canvasResolutionScaleMode!=0){JSEvents_resizeCanvasForFullscreen(target,strategy)}if(target.requestFullscreen){target.requestFullscreen()}else if(target.webkitRequestFullscreen){target.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT)}else{return JSEvents.fullscreenEnabled()?-3:-1}currentFullscreenStrategy=strategy;if(strategy.canvasResizedCallback){if(strategy.canvasResizedCallbackTargetThread)JSEvents.queueEventHandlerOnThread_iiii(strategy.canvasResizedCallbackTargetThread,strategy.canvasResizedCallback,37,0,strategy.canvasResizedCallbackUserData);else getWasmTableEntry(strategy.canvasResizedCallback)(37,0,strategy.canvasResizedCallbackUserData)}return 0}function _emscripten_exit_fullscreen(){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(50,1);if(!JSEvents.fullscreenEnabled())return-1;JSEvents.removeDeferredCalls(JSEvents_requestFullscreen);var d=specialHTMLTargets[1];if(d.exitFullscreen){d.fullscreenElement&&d.exitFullscreen()}else if(d.webkitExitFullscreen){d.webkitFullscreenElement&&d.webkitExitFullscreen()}else{return-1}return 0}_emscripten_exit_fullscreen.sig="i";function requestPointerLock(target){if(target.requestPointerLock){target.requestPointerLock()}else{if(document.body.requestPointerLock){return-3}return-1}return 0}function _emscripten_exit_pointerlock(){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(53,1);JSEvents.removeDeferredCalls(requestPointerLock);if(document.exitPointerLock){document.exitPointerLock()}else{return-1}return 0}_emscripten_exit_pointerlock.sig="i";function _emscripten_exit_with_live_runtime(){runtimeKeepalivePush();throw"unwind"}_emscripten_exit_with_live_runtime.sig="v";function _emscripten_force_exit(status){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(54,1,status);noExitRuntime=false;runtimeKeepaliveCounter=0;_exit(status)}_emscripten_force_exit.sig="vi";function _emscripten_get_device_pixel_ratio(){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(55,1);return typeof devicePixelRatio=="number"&&devicePixelRatio||1}_emscripten_get_device_pixel_ratio.sig="d";function _emscripten_get_element_css_size(target,width,height){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(56,1,target,width,height);target=findEventTarget(target);if(!target)return-4;var rect=getBoundingClientRect(target);GROWABLE_HEAP_F64()[width>>3]=rect.width;GROWABLE_HEAP_F64()[height>>3]=rect.height;return 0}_emscripten_get_element_css_size.sig="ippp";function fillGamepadEventData(eventStruct,e){GROWABLE_HEAP_F64()[eventStruct>>3]=e.timestamp;for(var i=0;i<e.axes.length;++i){GROWABLE_HEAP_F64()[eventStruct+i*8+16>>3]=e.axes[i]}for(var i=0;i<e.buttons.length;++i){if(typeof e.buttons[i]=="object"){GROWABLE_HEAP_F64()[eventStruct+i*8+528>>3]=e.buttons[i].value}else{GROWABLE_HEAP_F64()[eventStruct+i*8+528>>3]=e.buttons[i]}}for(var i=0;i<e.buttons.length;++i){if(typeof e.buttons[i]=="object"){GROWABLE_HEAP_I32()[eventStruct+i*4+1040>>2]=e.buttons[i].pressed}else{GROWABLE_HEAP_I32()[eventStruct+i*4+1040>>2]=e.buttons[i]==1}}GROWABLE_HEAP_I32()[eventStruct+1296>>2]=e.connected;GROWABLE_HEAP_I32()[eventStruct+1300>>2]=e.index;GROWABLE_HEAP_I32()[eventStruct+8>>2]=e.axes.length;GROWABLE_HEAP_I32()[eventStruct+12>>2]=e.buttons.length;stringToUTF8(e.id,eventStruct+1304,64);stringToUTF8(e.mapping,eventStruct+1368,64)}function _emscripten_get_gamepad_status(index,gamepadState){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(57,1,index,gamepadState);if(index<0||index>=JSEvents.lastGamepadState.length)return-5;if(!JSEvents.lastGamepadState[index])return-7;fillGamepadEventData(gamepadState,JSEvents.lastGamepadState[index]);return 0}_emscripten_get_gamepad_status.sig="iip";function _emscripten_get_num_gamepads(){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(58,1);return JSEvents.lastGamepadState.length}_emscripten_get_num_gamepads.sig="i";function _emscripten_get_screen_size(width,height){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(59,1,width,height);GROWABLE_HEAP_I32()[width>>2]=screen.width;GROWABLE_HEAP_I32()[height>>2]=screen.height}_emscripten_get_screen_size.sig="vpp";function _glActiveTexture(x0){GLctx.activeTexture(x0)}_glActiveTexture.sig="vi";var _emscripten_glActiveTexture=_glActiveTexture;function _glAttachShader(program,shader){GLctx.attachShader(GL.programs[program],GL.shaders[shader])}_glAttachShader.sig="vii";var _emscripten_glAttachShader=_glAttachShader;function _glBeginQueryEXT(target,id){GLctx.disjointTimerQueryExt["beginQueryEXT"](target,GL.queries[id])}_glBeginQueryEXT.sig="vii";var _emscripten_glBeginQueryEXT=_glBeginQueryEXT;function _glBindAttribLocation(program,index,name){GLctx.bindAttribLocation(GL.programs[program],index,UTF8ToString(name))}_glBindAttribLocation.sig="viip";var _emscripten_glBindAttribLocation=_glBindAttribLocation;function _glBindBuffer(target,buffer){GLctx.bindBuffer(target,GL.buffers[buffer])}_glBindBuffer.sig="vii";var _emscripten_glBindBuffer=_glBindBuffer;function _glBindFramebuffer(target,framebuffer){GLctx.bindFramebuffer(target,GL.framebuffers[framebuffer])}_glBindFramebuffer.sig="vii";var _emscripten_glBindFramebuffer=_glBindFramebuffer;function _glBindRenderbuffer(target,renderbuffer){GLctx.bindRenderbuffer(target,GL.renderbuffers[renderbuffer])}_glBindRenderbuffer.sig="vii";var _emscripten_glBindRenderbuffer=_glBindRenderbuffer;function _glBindTexture(target,texture){GLctx.bindTexture(target,GL.textures[texture])}_glBindTexture.sig="vii";var _emscripten_glBindTexture=_glBindTexture;function _glBindVertexArray(vao){GLctx.bindVertexArray(GL.vaos[vao])}_glBindVertexArray.sig="vi";var _glBindVertexArrayOES=_glBindVertexArray;_glBindVertexArrayOES.sig="vi";var _emscripten_glBindVertexArrayOES=_glBindVertexArrayOES;function _glBlendColor(x0,x1,x2,x3){GLctx.blendColor(x0,x1,x2,x3)}_glBlendColor.sig="vffff";var _emscripten_glBlendColor=_glBlendColor;function _glBlendEquation(x0){GLctx.blendEquation(x0)}_glBlendEquation.sig="vi";var _emscripten_glBlendEquation=_glBlendEquation;function _glBlendEquationSeparate(x0,x1){GLctx.blendEquationSeparate(x0,x1)}_glBlendEquationSeparate.sig="vii";var _emscripten_glBlendEquationSeparate=_glBlendEquationSeparate;function _glBlendFunc(x0,x1){GLctx.blendFunc(x0,x1)}_glBlendFunc.sig="vii";var _emscripten_glBlendFunc=_glBlendFunc;function _glBlendFuncSeparate(x0,x1,x2,x3){GLctx.blendFuncSeparate(x0,x1,x2,x3)}_glBlendFuncSeparate.sig="viiii";var _emscripten_glBlendFuncSeparate=_glBlendFuncSeparate;function _glBufferData(target,size,data,usage){GLctx.bufferData(target,data?GROWABLE_HEAP_U8().subarray(data,data+size):size,usage)}_glBufferData.sig="vippi";var _emscripten_glBufferData=_glBufferData;function _glBufferSubData(target,offset,size,data){GLctx.bufferSubData(target,offset,GROWABLE_HEAP_U8().subarray(data,data+size))}_glBufferSubData.sig="vippp";var _emscripten_glBufferSubData=_glBufferSubData;function _glCheckFramebufferStatus(x0){return GLctx.checkFramebufferStatus(x0)}_glCheckFramebufferStatus.sig="ii";var _emscripten_glCheckFramebufferStatus=_glCheckFramebufferStatus;function _glClear(x0){GLctx.clear(x0)}_glClear.sig="vi";var _emscripten_glClear=_glClear;function _glClearColor(x0,x1,x2,x3){GLctx.clearColor(x0,x1,x2,x3)}_glClearColor.sig="vffff";var _emscripten_glClearColor=_glClearColor;function _glClearDepthf(x0){GLctx.clearDepth(x0)}_glClearDepthf.sig="vf";var _emscripten_glClearDepthf=_glClearDepthf;function _glClearStencil(x0){GLctx.clearStencil(x0)}_glClearStencil.sig="vi";var _emscripten_glClearStencil=_glClearStencil;function _glColorMask(red,green,blue,alpha){GLctx.colorMask(!!red,!!green,!!blue,!!alpha)}_glColorMask.sig="viiii";var _emscripten_glColorMask=_glColorMask;function _glCompileShader(shader){GLctx.compileShader(GL.shaders[shader])}_glCompileShader.sig="vi";var _emscripten_glCompileShader=_glCompileShader;function _glCompressedTexImage2D(target,level,internalFormat,width,height,border,imageSize,data){GLctx.compressedTexImage2D(target,level,internalFormat,width,height,border,data?GROWABLE_HEAP_U8().subarray(data,data+imageSize):null)}_glCompressedTexImage2D.sig="viiiiiiip";var _emscripten_glCompressedTexImage2D=_glCompressedTexImage2D;function _glCompressedTexSubImage2D(target,level,xoffset,yoffset,width,height,format,imageSize,data){GLctx.compressedTexSubImage2D(target,level,xoffset,yoffset,width,height,format,data?GROWABLE_HEAP_U8().subarray(data,data+imageSize):null)}_glCompressedTexSubImage2D.sig="viiiiiiiip";var _emscripten_glCompressedTexSubImage2D=_glCompressedTexSubImage2D;function _glCopyTexImage2D(x0,x1,x2,x3,x4,x5,x6,x7){GLctx.copyTexImage2D(x0,x1,x2,x3,x4,x5,x6,x7)}_glCopyTexImage2D.sig="viiiiiiii";var _emscripten_glCopyTexImage2D=_glCopyTexImage2D;function _glCopyTexSubImage2D(x0,x1,x2,x3,x4,x5,x6,x7){GLctx.copyTexSubImage2D(x0,x1,x2,x3,x4,x5,x6,x7)}_glCopyTexSubImage2D.sig="viiiiiiii";var _emscripten_glCopyTexSubImage2D=_glCopyTexSubImage2D;function _glCreateProgram(){var id=GL.getNewId(GL.programs);var program=GLctx.createProgram();program.name=id;program.maxUniformLength=program.maxAttributeLength=program.maxUniformBlockNameLength=0;program.uniformIdCounter=1;GL.programs[id]=program;return id}_glCreateProgram.sig="i";var _emscripten_glCreateProgram=_glCreateProgram;function _glCreateShader(shaderType){var id=GL.getNewId(GL.shaders);GL.shaders[id]=GLctx.createShader(shaderType);return id}_glCreateShader.sig="ii";var _emscripten_glCreateShader=_glCreateShader;function _glCullFace(x0){GLctx.cullFace(x0)}_glCullFace.sig="vi";var _emscripten_glCullFace=_glCullFace;function _glDeleteBuffers(n,buffers){for(var i=0;i<n;i++){var id=GROWABLE_HEAP_I32()[buffers+i*4>>2];var buffer=GL.buffers[id];if(!buffer)continue;GLctx.deleteBuffer(buffer);buffer.name=0;GL.buffers[id]=null}}_glDeleteBuffers.sig="vip";var _emscripten_glDeleteBuffers=_glDeleteBuffers;function _glDeleteFramebuffers(n,framebuffers){for(var i=0;i<n;++i){var id=GROWABLE_HEAP_I32()[framebuffers+i*4>>2];var framebuffer=GL.framebuffers[id];if(!framebuffer)continue;GLctx.deleteFramebuffer(framebuffer);framebuffer.name=0;GL.framebuffers[id]=null}}_glDeleteFramebuffers.sig="vip";var _emscripten_glDeleteFramebuffers=_glDeleteFramebuffers;function _glDeleteProgram(id){if(!id)return;var program=GL.programs[id];if(!program){GL.recordError(1281);return}GLctx.deleteProgram(program);program.name=0;GL.programs[id]=null}_glDeleteProgram.sig="vi";var _emscripten_glDeleteProgram=_glDeleteProgram;function _glDeleteQueriesEXT(n,ids){for(var i=0;i<n;i++){var id=GROWABLE_HEAP_I32()[ids+i*4>>2];var query=GL.queries[id];if(!query)continue;GLctx.disjointTimerQueryExt["deleteQueryEXT"](query);GL.queries[id]=null}}_glDeleteQueriesEXT.sig="vii";var _emscripten_glDeleteQueriesEXT=_glDeleteQueriesEXT;function _glDeleteRenderbuffers(n,renderbuffers){for(var i=0;i<n;i++){var id=GROWABLE_HEAP_I32()[renderbuffers+i*4>>2];var renderbuffer=GL.renderbuffers[id];if(!renderbuffer)continue;GLctx.deleteRenderbuffer(renderbuffer);renderbuffer.name=0;GL.renderbuffers[id]=null}}_glDeleteRenderbuffers.sig="vip";var _emscripten_glDeleteRenderbuffers=_glDeleteRenderbuffers;function _glDeleteShader(id){if(!id)return;var shader=GL.shaders[id];if(!shader){GL.recordError(1281);return}GLctx.deleteShader(shader);GL.shaders[id]=null}_glDeleteShader.sig="vi";var _emscripten_glDeleteShader=_glDeleteShader;function _glDeleteTextures(n,textures){for(var i=0;i<n;i++){var id=GROWABLE_HEAP_I32()[textures+i*4>>2];var texture=GL.textures[id];if(!texture)continue;GLctx.deleteTexture(texture);texture.name=0;GL.textures[id]=null}}_glDeleteTextures.sig="vip";var _emscripten_glDeleteTextures=_glDeleteTextures;function _glDeleteVertexArrays(n,vaos){for(var i=0;i<n;i++){var id=GROWABLE_HEAP_I32()[vaos+i*4>>2];GLctx.deleteVertexArray(GL.vaos[id]);GL.vaos[id]=null}}_glDeleteVertexArrays.sig="vip";var _glDeleteVertexArraysOES=_glDeleteVertexArrays;_glDeleteVertexArraysOES.sig="vip";var _emscripten_glDeleteVertexArraysOES=_glDeleteVertexArraysOES;function _glDepthFunc(x0){GLctx.depthFunc(x0)}_glDepthFunc.sig="vi";var _emscripten_glDepthFunc=_glDepthFunc;function _glDepthMask(flag){GLctx.depthMask(!!flag)}_glDepthMask.sig="vi";var _emscripten_glDepthMask=_glDepthMask;function _glDepthRangef(x0,x1){GLctx.depthRange(x0,x1)}_glDepthRangef.sig="vff";var _emscripten_glDepthRangef=_glDepthRangef;function _glDetachShader(program,shader){GLctx.detachShader(GL.programs[program],GL.shaders[shader])}_glDetachShader.sig="vii";var _emscripten_glDetachShader=_glDetachShader;function _glDisable(x0){GLctx.disable(x0)}_glDisable.sig="vi";var _emscripten_glDisable=_glDisable;function _glDisableVertexAttribArray(index){GLctx.disableVertexAttribArray(index)}_glDisableVertexAttribArray.sig="vi";var _emscripten_glDisableVertexAttribArray=_glDisableVertexAttribArray;function _glDrawArrays(mode,first,count){GLctx.drawArrays(mode,first,count)}_glDrawArrays.sig="viii";var _emscripten_glDrawArrays=_glDrawArrays;function _glDrawArraysInstanced(mode,first,count,primcount){GLctx.drawArraysInstanced(mode,first,count,primcount)}_glDrawArraysInstanced.sig="viiii";var _glDrawArraysInstancedANGLE=_glDrawArraysInstanced;var _emscripten_glDrawArraysInstancedANGLE=_glDrawArraysInstancedANGLE;var tempFixedLengthArray=[];function _glDrawBuffers(n,bufs){var bufArray=tempFixedLengthArray[n];for(var i=0;i<n;i++){bufArray[i]=GROWABLE_HEAP_I32()[bufs+i*4>>2]}GLctx.drawBuffers(bufArray)}_glDrawBuffers.sig="vip";var _glDrawBuffersWEBGL=_glDrawBuffers;var _emscripten_glDrawBuffersWEBGL=_glDrawBuffersWEBGL;function _glDrawElements(mode,count,type,indices){GLctx.drawElements(mode,count,type,indices)}_glDrawElements.sig="viiip";var _emscripten_glDrawElements=_glDrawElements;function _glDrawElementsInstanced(mode,count,type,indices,primcount){GLctx.drawElementsInstanced(mode,count,type,indices,primcount)}_glDrawElementsInstanced.sig="viiipi";var _glDrawElementsInstancedANGLE=_glDrawElementsInstanced;var _emscripten_glDrawElementsInstancedANGLE=_glDrawElementsInstancedANGLE;function _glEnable(x0){GLctx.enable(x0)}_glEnable.sig="vi";var _emscripten_glEnable=_glEnable;function _glEnableVertexAttribArray(index){GLctx.enableVertexAttribArray(index)}_glEnableVertexAttribArray.sig="vi";var _emscripten_glEnableVertexAttribArray=_glEnableVertexAttribArray;function _glEndQueryEXT(target){GLctx.disjointTimerQueryExt["endQueryEXT"](target)}_glEndQueryEXT.sig="vi";var _emscripten_glEndQueryEXT=_glEndQueryEXT;function _glFinish(){GLctx.finish()}_glFinish.sig="v";var _emscripten_glFinish=_glFinish;function _glFlush(){GLctx.flush()}_glFlush.sig="v";var _emscripten_glFlush=_glFlush;function _glFramebufferRenderbuffer(target,attachment,renderbuffertarget,renderbuffer){GLctx.framebufferRenderbuffer(target,attachment,renderbuffertarget,GL.renderbuffers[renderbuffer])}_glFramebufferRenderbuffer.sig="viiii";var _emscripten_glFramebufferRenderbuffer=_glFramebufferRenderbuffer;function _glFramebufferTexture2D(target,attachment,textarget,texture,level){GLctx.framebufferTexture2D(target,attachment,textarget,GL.textures[texture],level)}_glFramebufferTexture2D.sig="viiiii";var _emscripten_glFramebufferTexture2D=_glFramebufferTexture2D;function _glFrontFace(x0){GLctx.frontFace(x0)}_glFrontFace.sig="vi";var _emscripten_glFrontFace=_glFrontFace;function __glGenObject(n,buffers,createFunction,objectTable){for(var i=0;i<n;i++){var buffer=GLctx[createFunction]();var id=buffer&&GL.getNewId(objectTable);if(buffer){buffer.name=id;objectTable[id]=buffer}else{GL.recordError(1282)}GROWABLE_HEAP_I32()[buffers+i*4>>2]=id}}function _glGenBuffers(n,buffers){__glGenObject(n,buffers,"createBuffer",GL.buffers)}_glGenBuffers.sig="vip";var _emscripten_glGenBuffers=_glGenBuffers;function _glGenFramebuffers(n,ids){__glGenObject(n,ids,"createFramebuffer",GL.framebuffers)}_glGenFramebuffers.sig="vip";var _emscripten_glGenFramebuffers=_glGenFramebuffers;function _glGenQueriesEXT(n,ids){for(var i=0;i<n;i++){var query=GLctx.disjointTimerQueryExt["createQueryEXT"]();if(!query){GL.recordError(1282);while(i<n)GROWABLE_HEAP_I32()[ids+i++*4>>2]=0;return}var id=GL.getNewId(GL.queries);query.name=id;GL.queries[id]=query;GROWABLE_HEAP_I32()[ids+i*4>>2]=id}}_glGenQueriesEXT.sig="vii";var _emscripten_glGenQueriesEXT=_glGenQueriesEXT;function _glGenRenderbuffers(n,renderbuffers){__glGenObject(n,renderbuffers,"createRenderbuffer",GL.renderbuffers)}_glGenRenderbuffers.sig="vip";var _emscripten_glGenRenderbuffers=_glGenRenderbuffers;function _glGenTextures(n,textures){__glGenObject(n,textures,"createTexture",GL.textures)}_glGenTextures.sig="vip";var _emscripten_glGenTextures=_glGenTextures;function _glGenVertexArrays(n,arrays){__glGenObject(n,arrays,"createVertexArray",GL.vaos)}_glGenVertexArrays.sig="vip";var _glGenVertexArraysOES=_glGenVertexArrays;_glGenVertexArraysOES.sig="vip";var _emscripten_glGenVertexArraysOES=_glGenVertexArraysOES;function _glGenerateMipmap(x0){GLctx.generateMipmap(x0)}_glGenerateMipmap.sig="vi";var _emscripten_glGenerateMipmap=_glGenerateMipmap;function __glGetActiveAttribOrUniform(funcName,program,index,bufSize,length,size,type,name){program=GL.programs[program];var info=GLctx[funcName](program,index);if(info){var numBytesWrittenExclNull=name&&stringToUTF8(info.name,name,bufSize);if(length)GROWABLE_HEAP_I32()[length>>2]=numBytesWrittenExclNull;if(size)GROWABLE_HEAP_I32()[size>>2]=info.size;if(type)GROWABLE_HEAP_I32()[type>>2]=info.type}}function _glGetActiveAttrib(program,index,bufSize,length,size,type,name){__glGetActiveAttribOrUniform("getActiveAttrib",program,index,bufSize,length,size,type,name)}_glGetActiveAttrib.sig="viiipppp";var _emscripten_glGetActiveAttrib=_glGetActiveAttrib;function _glGetActiveUniform(program,index,bufSize,length,size,type,name){__glGetActiveAttribOrUniform("getActiveUniform",program,index,bufSize,length,size,type,name)}_glGetActiveUniform.sig="viiipppp";var _emscripten_glGetActiveUniform=_glGetActiveUniform;function _glGetAttachedShaders(program,maxCount,count,shaders){var result=GLctx.getAttachedShaders(GL.programs[program]);var len=result.length;if(len>maxCount){len=maxCount}GROWABLE_HEAP_I32()[count>>2]=len;for(var i=0;i<len;++i){var id=GL.shaders.indexOf(result[i]);GROWABLE_HEAP_I32()[shaders+i*4>>2]=id}}_glGetAttachedShaders.sig="viipp";var _emscripten_glGetAttachedShaders=_glGetAttachedShaders;function _glGetAttribLocation(program,name){return GLctx.getAttribLocation(GL.programs[program],UTF8ToString(name))}_glGetAttribLocation.sig="iip";var _emscripten_glGetAttribLocation=_glGetAttribLocation;function writeI53ToI64(ptr,num){GROWABLE_HEAP_U32()[ptr>>2]=num;GROWABLE_HEAP_U32()[ptr+4>>2]=(num-GROWABLE_HEAP_U32()[ptr>>2])/4294967296}function emscriptenWebGLGet(name_,p,type){if(!p){GL.recordError(1281);return}var ret=undefined;switch(name_){case 36346:ret=1;break;case 36344:if(type!=0&&type!=1){GL.recordError(1280)}return;case 36345:ret=0;break;case 34466:var formats=GLctx.getParameter(34467);ret=formats?formats.length:0;break}if(ret===undefined){var result=GLctx.getParameter(name_);switch(typeof result){case"number":ret=result;break;case"boolean":ret=result?1:0;break;case"string":GL.recordError(1280);return;case"object":if(result===null){switch(name_){case 34964:case 35725:case 34965:case 36006:case 36007:case 32873:case 34229:case 34068:{ret=0;break}default:{GL.recordError(1280);return}}}else if(result instanceof Float32Array||result instanceof Uint32Array||result instanceof Int32Array||result instanceof Array){for(var i=0;i<result.length;++i){switch(type){case 0:GROWABLE_HEAP_I32()[p+i*4>>2]=result[i];break;case 2:GROWABLE_HEAP_F32()[p+i*4>>2]=result[i];break;case 4:GROWABLE_HEAP_I8()[p+i>>0]=result[i]?1:0;break}}return}else{try{ret=result.name|0}catch(e){GL.recordError(1280);err("GL_INVALID_ENUM in glGet"+type+"v: Unknown object returned from WebGL getParameter("+name_+")! (error: "+e+")");return}}break;default:GL.recordError(1280);err("GL_INVALID_ENUM in glGet"+type+"v: Native code calling glGet"+type+"v("+name_+") and it returns "+result+" of type "+typeof result+"!");return}}switch(type){case 1:writeI53ToI64(p,ret);break;case 0:GROWABLE_HEAP_I32()[p>>2]=ret;break;case 2:GROWABLE_HEAP_F32()[p>>2]=ret;break;case 4:GROWABLE_HEAP_I8()[p>>0]=ret?1:0;break}}function _glGetBooleanv(name_,p){emscriptenWebGLGet(name_,p,4)}_glGetBooleanv.sig="vip";var _emscripten_glGetBooleanv=_glGetBooleanv;function _glGetBufferParameteriv(target,value,data){if(!data){GL.recordError(1281);return}GROWABLE_HEAP_I32()[data>>2]=GLctx.getBufferParameter(target,value)}_glGetBufferParameteriv.sig="viip";var _emscripten_glGetBufferParameteriv=_glGetBufferParameteriv;function _glGetError(){var error=GLctx.getError()||GL.lastError;GL.lastError=0;return error}_glGetError.sig="i";var _emscripten_glGetError=_glGetError;function _glGetFloatv(name_,p){emscriptenWebGLGet(name_,p,2)}_glGetFloatv.sig="vip";var _emscripten_glGetFloatv=_glGetFloatv;function _glGetFramebufferAttachmentParameteriv(target,attachment,pname,params){var result=GLctx.getFramebufferAttachmentParameter(target,attachment,pname);if(result instanceof WebGLRenderbuffer||result instanceof WebGLTexture){result=result.name|0}GROWABLE_HEAP_I32()[params>>2]=result}_glGetFramebufferAttachmentParameteriv.sig="viiip";var _emscripten_glGetFramebufferAttachmentParameteriv=_glGetFramebufferAttachmentParameteriv;function _glGetIntegerv(name_,p){emscriptenWebGLGet(name_,p,0)}_glGetIntegerv.sig="vip";var _emscripten_glGetIntegerv=_glGetIntegerv;function _glGetProgramInfoLog(program,maxLength,length,infoLog){var log=GLctx.getProgramInfoLog(GL.programs[program]);if(log===null)log="(unknown error)";var numBytesWrittenExclNull=maxLength>0&&infoLog?stringToUTF8(log,infoLog,maxLength):0;if(length)GROWABLE_HEAP_I32()[length>>2]=numBytesWrittenExclNull}_glGetProgramInfoLog.sig="viipp";var _emscripten_glGetProgramInfoLog=_glGetProgramInfoLog;function _glGetProgramiv(program,pname,p){if(!p){GL.recordError(1281);return}if(program>=GL.counter){GL.recordError(1281);return}program=GL.programs[program];if(pname==35716){var log=GLctx.getProgramInfoLog(program);if(log===null)log="(unknown error)";GROWABLE_HEAP_I32()[p>>2]=log.length+1}else if(pname==35719){if(!program.maxUniformLength){for(var i=0;i<GLctx.getProgramParameter(program,35718);++i){program.maxUniformLength=Math.max(program.maxUniformLength,GLctx.getActiveUniform(program,i).name.length+1)}}GROWABLE_HEAP_I32()[p>>2]=program.maxUniformLength}else if(pname==35722){if(!program.maxAttributeLength){for(var i=0;i<GLctx.getProgramParameter(program,35721);++i){program.maxAttributeLength=Math.max(program.maxAttributeLength,GLctx.getActiveAttrib(program,i).name.length+1)}}GROWABLE_HEAP_I32()[p>>2]=program.maxAttributeLength}else if(pname==35381){if(!program.maxUniformBlockNameLength){for(var i=0;i<GLctx.getProgramParameter(program,35382);++i){program.maxUniformBlockNameLength=Math.max(program.maxUniformBlockNameLength,GLctx.getActiveUniformBlockName(program,i).length+1)}}GROWABLE_HEAP_I32()[p>>2]=program.maxUniformBlockNameLength}else{GROWABLE_HEAP_I32()[p>>2]=GLctx.getProgramParameter(program,pname)}}_glGetProgramiv.sig="viip";var _emscripten_glGetProgramiv=_glGetProgramiv;function _glGetQueryObjecti64vEXT(id,pname,params){if(!params){GL.recordError(1281);return}var query=GL.queries[id];var param;{param=GLctx.disjointTimerQueryExt["getQueryObjectEXT"](query,pname)}var ret;if(typeof param=="boolean"){ret=param?1:0}else{ret=param}writeI53ToI64(params,ret)}_glGetQueryObjecti64vEXT.sig="viii";var _emscripten_glGetQueryObjecti64vEXT=_glGetQueryObjecti64vEXT;function _glGetQueryObjectivEXT(id,pname,params){if(!params){GL.recordError(1281);return}var query=GL.queries[id];var param=GLctx.disjointTimerQueryExt["getQueryObjectEXT"](query,pname);var ret;if(typeof param=="boolean"){ret=param?1:0}else{ret=param}GROWABLE_HEAP_I32()[params>>2]=ret}_glGetQueryObjectivEXT.sig="viii";var _emscripten_glGetQueryObjectivEXT=_glGetQueryObjectivEXT;var _glGetQueryObjectui64vEXT=_glGetQueryObjecti64vEXT;var _emscripten_glGetQueryObjectui64vEXT=_glGetQueryObjectui64vEXT;var _glGetQueryObjectuivEXT=_glGetQueryObjectivEXT;var _emscripten_glGetQueryObjectuivEXT=_glGetQueryObjectuivEXT;function _glGetQueryivEXT(target,pname,params){if(!params){GL.recordError(1281);return}GROWABLE_HEAP_I32()[params>>2]=GLctx.disjointTimerQueryExt["getQueryEXT"](target,pname)}_glGetQueryivEXT.sig="viii";var _emscripten_glGetQueryivEXT=_glGetQueryivEXT;function _glGetRenderbufferParameteriv(target,pname,params){if(!params){GL.recordError(1281);return}GROWABLE_HEAP_I32()[params>>2]=GLctx.getRenderbufferParameter(target,pname)}_glGetRenderbufferParameteriv.sig="viip";var _emscripten_glGetRenderbufferParameteriv=_glGetRenderbufferParameteriv;function _glGetShaderInfoLog(shader,maxLength,length,infoLog){var log=GLctx.getShaderInfoLog(GL.shaders[shader]);if(log===null)log="(unknown error)";var numBytesWrittenExclNull=maxLength>0&&infoLog?stringToUTF8(log,infoLog,maxLength):0;if(length)GROWABLE_HEAP_I32()[length>>2]=numBytesWrittenExclNull}_glGetShaderInfoLog.sig="viipp";var _emscripten_glGetShaderInfoLog=_glGetShaderInfoLog;function _glGetShaderPrecisionFormat(shaderType,precisionType,range,precision){var result=GLctx.getShaderPrecisionFormat(shaderType,precisionType);GROWABLE_HEAP_I32()[range>>2]=result.rangeMin;GROWABLE_HEAP_I32()[range+4>>2]=result.rangeMax;GROWABLE_HEAP_I32()[precision>>2]=result.precision}_glGetShaderPrecisionFormat.sig="viipp";var _emscripten_glGetShaderPrecisionFormat=_glGetShaderPrecisionFormat;function _glGetShaderSource(shader,bufSize,length,source){var result=GLctx.getShaderSource(GL.shaders[shader]);if(!result)return;var numBytesWrittenExclNull=bufSize>0&&source?stringToUTF8(result,source,bufSize):0;if(length)GROWABLE_HEAP_I32()[length>>2]=numBytesWrittenExclNull}_glGetShaderSource.sig="viipp";var _emscripten_glGetShaderSource=_glGetShaderSource;function _glGetShaderiv(shader,pname,p){if(!p){GL.recordError(1281);return}if(pname==35716){var log=GLctx.getShaderInfoLog(GL.shaders[shader]);if(log===null)log="(unknown error)";var logLength=log?log.length+1:0;GROWABLE_HEAP_I32()[p>>2]=logLength}else if(pname==35720){var source=GLctx.getShaderSource(GL.shaders[shader]);var sourceLength=source?source.length+1:0;GROWABLE_HEAP_I32()[p>>2]=sourceLength}else{GROWABLE_HEAP_I32()[p>>2]=GLctx.getShaderParameter(GL.shaders[shader],pname)}}_glGetShaderiv.sig="viip";var _emscripten_glGetShaderiv=_glGetShaderiv;function _glGetString(name_){var ret=GL.stringCache[name_];if(!ret){switch(name_){case 7939:var exts=GLctx.getSupportedExtensions()||[];exts=exts.concat(exts.map(function(e){return"GL_"+e}));ret=stringToNewUTF8(exts.join(" "));break;case 7936:case 7937:case 37445:case 37446:var s=GLctx.getParameter(name_);if(!s){GL.recordError(1280)}ret=s&&stringToNewUTF8(s);break;case 7938:var glVersion=GLctx.getParameter(7938);{glVersion="OpenGL ES 2.0 ("+glVersion+")"}ret=stringToNewUTF8(glVersion);break;case 35724:var glslVersion=GLctx.getParameter(35724);var ver_re=/^WebGL GLSL ES ([0-9]\.[0-9][0-9]?)(?:$| .*)/;var ver_num=glslVersion.match(ver_re);if(ver_num!==null){if(ver_num[1].length==3)ver_num[1]=ver_num[1]+"0";glslVersion="OpenGL ES GLSL ES "+ver_num[1]+" ("+glslVersion+")"}ret=stringToNewUTF8(glslVersion);break;default:GL.recordError(1280)}GL.stringCache[name_]=ret}return ret}_glGetString.sig="pi";var _emscripten_glGetString=_glGetString;function _glGetTexParameterfv(target,pname,params){if(!params){GL.recordError(1281);return}GROWABLE_HEAP_F32()[params>>2]=GLctx.getTexParameter(target,pname)}_glGetTexParameterfv.sig="viip";var _emscripten_glGetTexParameterfv=_glGetTexParameterfv;function _glGetTexParameteriv(target,pname,params){if(!params){GL.recordError(1281);return}GROWABLE_HEAP_I32()[params>>2]=GLctx.getTexParameter(target,pname)}_glGetTexParameteriv.sig="viip";var _emscripten_glGetTexParameteriv=_glGetTexParameteriv;function webglGetLeftBracePos(name){return name.slice(-1)=="]"&&name.lastIndexOf("[")}function webglPrepareUniformLocationsBeforeFirstUse(program){var uniformLocsById=program.uniformLocsById,uniformSizeAndIdsByName=program.uniformSizeAndIdsByName,i,j;if(!uniformLocsById){program.uniformLocsById=uniformLocsById={};program.uniformArrayNamesById={};for(i=0;i<GLctx.getProgramParameter(program,35718);++i){var u=GLctx.getActiveUniform(program,i);var nm=u.name;var sz=u.size;var lb=webglGetLeftBracePos(nm);var arrayName=lb>0?nm.slice(0,lb):nm;var id=program.uniformIdCounter;program.uniformIdCounter+=sz;uniformSizeAndIdsByName[arrayName]=[sz,id];for(j=0;j<sz;++j){uniformLocsById[id]=j;program.uniformArrayNamesById[id++]=arrayName}}}}function _glGetUniformLocation(program,name){name=UTF8ToString(name);if(program=GL.programs[program]){webglPrepareUniformLocationsBeforeFirstUse(program);var uniformLocsById=program.uniformLocsById;var arrayIndex=0;var uniformBaseName=name;var leftBrace=webglGetLeftBracePos(name);if(leftBrace>0){arrayIndex=jstoi_q(name.slice(leftBrace+1))>>>0;uniformBaseName=name.slice(0,leftBrace)}var sizeAndId=program.uniformSizeAndIdsByName[uniformBaseName];if(sizeAndId&&arrayIndex<sizeAndId[0]){arrayIndex+=sizeAndId[1];if(uniformLocsById[arrayIndex]=uniformLocsById[arrayIndex]||GLctx.getUniformLocation(program,name)){return arrayIndex}}}else{GL.recordError(1281)}return-1}_glGetUniformLocation.sig="iip";var _emscripten_glGetUniformLocation=_glGetUniformLocation;function webglGetUniformLocation(location){var p=GLctx.currentProgram;if(p){var webglLoc=p.uniformLocsById[location];if(typeof webglLoc=="number"){p.uniformLocsById[location]=webglLoc=GLctx.getUniformLocation(p,p.uniformArrayNamesById[location]+(webglLoc>0?"["+webglLoc+"]":""))}return webglLoc}else{GL.recordError(1282)}}function emscriptenWebGLGetUniform(program,location,params,type){if(!params){GL.recordError(1281);return}program=GL.programs[program];webglPrepareUniformLocationsBeforeFirstUse(program);var data=GLctx.getUniform(program,webglGetUniformLocation(location));if(typeof data=="number"||typeof data=="boolean"){switch(type){case 0:GROWABLE_HEAP_I32()[params>>2]=data;break;case 2:GROWABLE_HEAP_F32()[params>>2]=data;break}}else{for(var i=0;i<data.length;i++){switch(type){case 0:GROWABLE_HEAP_I32()[params+i*4>>2]=data[i];break;case 2:GROWABLE_HEAP_F32()[params+i*4>>2]=data[i];break}}}}function _glGetUniformfv(program,location,params){emscriptenWebGLGetUniform(program,location,params,2)}_glGetUniformfv.sig="viip";var _emscripten_glGetUniformfv=_glGetUniformfv;function _glGetUniformiv(program,location,params){emscriptenWebGLGetUniform(program,location,params,0)}_glGetUniformiv.sig="viip";var _emscripten_glGetUniformiv=_glGetUniformiv;function _glGetVertexAttribPointerv(index,pname,pointer){if(!pointer){GL.recordError(1281);return}GROWABLE_HEAP_I32()[pointer>>2]=GLctx.getVertexAttribOffset(index,pname)}_glGetVertexAttribPointerv.sig="viip";var _emscripten_glGetVertexAttribPointerv=_glGetVertexAttribPointerv;function emscriptenWebGLGetVertexAttrib(index,pname,params,type){if(!params){GL.recordError(1281);return}var data=GLctx.getVertexAttrib(index,pname);if(pname==34975){GROWABLE_HEAP_I32()[params>>2]=data&&data["name"]}else if(typeof data=="number"||typeof data=="boolean"){switch(type){case 0:GROWABLE_HEAP_I32()[params>>2]=data;break;case 2:GROWABLE_HEAP_F32()[params>>2]=data;break;case 5:GROWABLE_HEAP_I32()[params>>2]=Math.fround(data);break}}else{for(var i=0;i<data.length;i++){switch(type){case 0:GROWABLE_HEAP_I32()[params+i*4>>2]=data[i];break;case 2:GROWABLE_HEAP_F32()[params+i*4>>2]=data[i];break;case 5:GROWABLE_HEAP_I32()[params+i*4>>2]=Math.fround(data[i]);break}}}}function _glGetVertexAttribfv(index,pname,params){emscriptenWebGLGetVertexAttrib(index,pname,params,2)}_glGetVertexAttribfv.sig="viip";var _emscripten_glGetVertexAttribfv=_glGetVertexAttribfv;function _glGetVertexAttribiv(index,pname,params){emscriptenWebGLGetVertexAttrib(index,pname,params,5)}_glGetVertexAttribiv.sig="viip";var _emscripten_glGetVertexAttribiv=_glGetVertexAttribiv;function _glHint(x0,x1){GLctx.hint(x0,x1)}_glHint.sig="vii";var _emscripten_glHint=_glHint;function _glIsBuffer(buffer){var b=GL.buffers[buffer];if(!b)return 0;return GLctx.isBuffer(b)}_glIsBuffer.sig="ii";var _emscripten_glIsBuffer=_glIsBuffer;function _glIsEnabled(x0){return GLctx.isEnabled(x0)}_glIsEnabled.sig="ii";var _emscripten_glIsEnabled=_glIsEnabled;function _glIsFramebuffer(framebuffer){var fb=GL.framebuffers[framebuffer];if(!fb)return 0;return GLctx.isFramebuffer(fb)}_glIsFramebuffer.sig="ii";var _emscripten_glIsFramebuffer=_glIsFramebuffer;function _glIsProgram(program){program=GL.programs[program];if(!program)return 0;return GLctx.isProgram(program)}_glIsProgram.sig="ii";var _emscripten_glIsProgram=_glIsProgram;function _glIsQueryEXT(id){var query=GL.queries[id];if(!query)return 0;return GLctx.disjointTimerQueryExt["isQueryEXT"](query)}_glIsQueryEXT.sig="ii";var _emscripten_glIsQueryEXT=_glIsQueryEXT;function _glIsRenderbuffer(renderbuffer){var rb=GL.renderbuffers[renderbuffer];if(!rb)return 0;return GLctx.isRenderbuffer(rb)}_glIsRenderbuffer.sig="ii";var _emscripten_glIsRenderbuffer=_glIsRenderbuffer;function _glIsShader(shader){var s=GL.shaders[shader];if(!s)return 0;return GLctx.isShader(s)}_glIsShader.sig="ii";var _emscripten_glIsShader=_glIsShader;function _glIsTexture(id){var texture=GL.textures[id];if(!texture)return 0;return GLctx.isTexture(texture)}_glIsTexture.sig="ii";var _emscripten_glIsTexture=_glIsTexture;function _glIsVertexArray(array){var vao=GL.vaos[array];if(!vao)return 0;return GLctx.isVertexArray(vao)}_glIsVertexArray.sig="ii";var _glIsVertexArrayOES=_glIsVertexArray;_glIsVertexArrayOES.sig="ii";var _emscripten_glIsVertexArrayOES=_glIsVertexArrayOES;function _glLineWidth(x0){GLctx.lineWidth(x0)}_glLineWidth.sig="vf";var _emscripten_glLineWidth=_glLineWidth;function _glLinkProgram(program){program=GL.programs[program];GLctx.linkProgram(program);program.uniformLocsById=0;program.uniformSizeAndIdsByName={}}_glLinkProgram.sig="vi";var _emscripten_glLinkProgram=_glLinkProgram;function _glPixelStorei(pname,param){if(pname==3317){GL.unpackAlignment=param}GLctx.pixelStorei(pname,param)}_glPixelStorei.sig="vii";var _emscripten_glPixelStorei=_glPixelStorei;function _glPolygonOffset(x0,x1){GLctx.polygonOffset(x0,x1)}_glPolygonOffset.sig="vff";var _emscripten_glPolygonOffset=_glPolygonOffset;function _glQueryCounterEXT(id,target){GLctx.disjointTimerQueryExt["queryCounterEXT"](GL.queries[id],target)}_glQueryCounterEXT.sig="vii";var _emscripten_glQueryCounterEXT=_glQueryCounterEXT;function computeUnpackAlignedImageSize(width,height,sizePerPixel,alignment){function roundedToNextMultipleOf(x,y){return x+y-1&-y}var plainRowSize=width*sizePerPixel;var alignedRowSize=roundedToNextMultipleOf(plainRowSize,alignment);return height*alignedRowSize}function colorChannelsInGlTextureFormat(format){var colorChannels={5:3,6:4,8:2,29502:3,29504:4};return colorChannels[format-6402]||1}function heapObjectForWebGLType(type){type-=5120;if(type==1)return GROWABLE_HEAP_U8();if(type==4)return GROWABLE_HEAP_I32();if(type==6)return GROWABLE_HEAP_F32();if(type==5||type==28922)return GROWABLE_HEAP_U32();return GROWABLE_HEAP_U16()}function heapAccessShiftForWebGLHeap(heap){return 31-Math.clz32(heap.BYTES_PER_ELEMENT)}function emscriptenWebGLGetTexPixelData(type,format,width,height,pixels,internalFormat){var heap=heapObjectForWebGLType(type);var shift=heapAccessShiftForWebGLHeap(heap);var byteSize=1<<shift;var sizePerPixel=colorChannelsInGlTextureFormat(format)*byteSize;var bytes=computeUnpackAlignedImageSize(width,height,sizePerPixel,GL.unpackAlignment);return heap.subarray(pixels>>shift,pixels+bytes>>shift)}function _glReadPixels(x,y,width,height,format,type,pixels){var pixelData=emscriptenWebGLGetTexPixelData(type,format,width,height,pixels,format);if(!pixelData){GL.recordError(1280);return}GLctx.readPixels(x,y,width,height,format,type,pixelData)}_glReadPixels.sig="viiiiiip";var _emscripten_glReadPixels=_glReadPixels;function _glReleaseShaderCompiler(){}_glReleaseShaderCompiler.sig="v";var _emscripten_glReleaseShaderCompiler=_glReleaseShaderCompiler;function _glRenderbufferStorage(x0,x1,x2,x3){GLctx.renderbufferStorage(x0,x1,x2,x3)}_glRenderbufferStorage.sig="viiii";var _emscripten_glRenderbufferStorage=_glRenderbufferStorage;function _glSampleCoverage(value,invert){GLctx.sampleCoverage(value,!!invert)}_glSampleCoverage.sig="vfi";var _emscripten_glSampleCoverage=_glSampleCoverage;function _glScissor(x0,x1,x2,x3){GLctx.scissor(x0,x1,x2,x3)}_glScissor.sig="viiii";var _emscripten_glScissor=_glScissor;function _glShaderBinary(count,shaders,binaryformat,binary,length){GL.recordError(1280)}_glShaderBinary.sig="vipipi";var _emscripten_glShaderBinary=_glShaderBinary;function _glShaderSource(shader,count,string,length){var source=GL.getSource(shader,count,string,length);GLctx.shaderSource(GL.shaders[shader],source)}_glShaderSource.sig="viipp";var _emscripten_glShaderSource=_glShaderSource;function _glStencilFunc(x0,x1,x2){GLctx.stencilFunc(x0,x1,x2)}_glStencilFunc.sig="viii";var _emscripten_glStencilFunc=_glStencilFunc;function _glStencilFuncSeparate(x0,x1,x2,x3){GLctx.stencilFuncSeparate(x0,x1,x2,x3)}_glStencilFuncSeparate.sig="viiii";var _emscripten_glStencilFuncSeparate=_glStencilFuncSeparate;function _glStencilMask(x0){GLctx.stencilMask(x0)}_glStencilMask.sig="vi";var _emscripten_glStencilMask=_glStencilMask;function _glStencilMaskSeparate(x0,x1){GLctx.stencilMaskSeparate(x0,x1)}_glStencilMaskSeparate.sig="vii";var _emscripten_glStencilMaskSeparate=_glStencilMaskSeparate;function _glStencilOp(x0,x1,x2){GLctx.stencilOp(x0,x1,x2)}_glStencilOp.sig="viii";var _emscripten_glStencilOp=_glStencilOp;function _glStencilOpSeparate(x0,x1,x2,x3){GLctx.stencilOpSeparate(x0,x1,x2,x3)}_glStencilOpSeparate.sig="viiii";var _emscripten_glStencilOpSeparate=_glStencilOpSeparate;function _glTexImage2D(target,level,internalFormat,width,height,border,format,type,pixels){GLctx.texImage2D(target,level,internalFormat,width,height,border,format,type,pixels?emscriptenWebGLGetTexPixelData(type,format,width,height,pixels,internalFormat):null)}_glTexImage2D.sig="viiiiiiiip";var _emscripten_glTexImage2D=_glTexImage2D;function _glTexParameterf(x0,x1,x2){GLctx.texParameterf(x0,x1,x2)}_glTexParameterf.sig="viif";var _emscripten_glTexParameterf=_glTexParameterf;function _glTexParameterfv(target,pname,params){var param=GROWABLE_HEAP_F32()[params>>2];GLctx.texParameterf(target,pname,param)}_glTexParameterfv.sig="viip";var _emscripten_glTexParameterfv=_glTexParameterfv;function _glTexParameteri(x0,x1,x2){GLctx.texParameteri(x0,x1,x2)}_glTexParameteri.sig="viii";var _emscripten_glTexParameteri=_glTexParameteri;function _glTexParameteriv(target,pname,params){var param=GROWABLE_HEAP_I32()[params>>2];GLctx.texParameteri(target,pname,param)}_glTexParameteriv.sig="viip";var _emscripten_glTexParameteriv=_glTexParameteriv;function _glTexSubImage2D(target,level,xoffset,yoffset,width,height,format,type,pixels){var pixelData=null;if(pixels)pixelData=emscriptenWebGLGetTexPixelData(type,format,width,height,pixels,0);GLctx.texSubImage2D(target,level,xoffset,yoffset,width,height,format,type,pixelData)}_glTexSubImage2D.sig="viiiiiiiip";var _emscripten_glTexSubImage2D=_glTexSubImage2D;function _glUniform1f(location,v0){GLctx.uniform1f(webglGetUniformLocation(location),v0)}_glUniform1f.sig="vif";var _emscripten_glUniform1f=_glUniform1f;var miniTempWebGLFloatBuffers=[];function _glUniform1fv(location,count,value){if(count<=288){var view=miniTempWebGLFloatBuffers[count-1];for(var i=0;i<count;++i){view[i]=GROWABLE_HEAP_F32()[value+4*i>>2]}}else{var view=GROWABLE_HEAP_F32().subarray(value>>2,value+count*4>>2)}GLctx.uniform1fv(webglGetUniformLocation(location),view)}_glUniform1fv.sig="viip";var _emscripten_glUniform1fv=_glUniform1fv;function _glUniform1i(location,v0){GLctx.uniform1i(webglGetUniformLocation(location),v0)}_glUniform1i.sig="vii";var _emscripten_glUniform1i=_glUniform1i;var miniTempWebGLIntBuffers=[];function _glUniform1iv(location,count,value){if(count<=288){var view=miniTempWebGLIntBuffers[count-1];for(var i=0;i<count;++i){view[i]=GROWABLE_HEAP_I32()[value+4*i>>2]}}else{var view=GROWABLE_HEAP_I32().subarray(value>>2,value+count*4>>2)}GLctx.uniform1iv(webglGetUniformLocation(location),view)}_glUniform1iv.sig="viip";var _emscripten_glUniform1iv=_glUniform1iv;function _glUniform2f(location,v0,v1){GLctx.uniform2f(webglGetUniformLocation(location),v0,v1)}_glUniform2f.sig="viff";var _emscripten_glUniform2f=_glUniform2f;function _glUniform2fv(location,count,value){if(count<=144){var view=miniTempWebGLFloatBuffers[2*count-1];for(var i=0;i<2*count;i+=2){view[i]=GROWABLE_HEAP_F32()[value+4*i>>2];view[i+1]=GROWABLE_HEAP_F32()[value+(4*i+4)>>2]}}else{var view=GROWABLE_HEAP_F32().subarray(value>>2,value+count*8>>2)}GLctx.uniform2fv(webglGetUniformLocation(location),view)}_glUniform2fv.sig="viip";var _emscripten_glUniform2fv=_glUniform2fv;function _glUniform2i(location,v0,v1){GLctx.uniform2i(webglGetUniformLocation(location),v0,v1)}_glUniform2i.sig="viii";var _emscripten_glUniform2i=_glUniform2i;function _glUniform2iv(location,count,value){if(count<=144){var view=miniTempWebGLIntBuffers[2*count-1];for(var i=0;i<2*count;i+=2){view[i]=GROWABLE_HEAP_I32()[value+4*i>>2];view[i+1]=GROWABLE_HEAP_I32()[value+(4*i+4)>>2]}}else{var view=GROWABLE_HEAP_I32().subarray(value>>2,value+count*8>>2)}GLctx.uniform2iv(webglGetUniformLocation(location),view)}_glUniform2iv.sig="viip";var _emscripten_glUniform2iv=_glUniform2iv;function _glUniform3f(location,v0,v1,v2){GLctx.uniform3f(webglGetUniformLocation(location),v0,v1,v2)}_glUniform3f.sig="vifff";var _emscripten_glUniform3f=_glUniform3f;function _glUniform3fv(location,count,value){if(count<=96){var view=miniTempWebGLFloatBuffers[3*count-1];for(var i=0;i<3*count;i+=3){view[i]=GROWABLE_HEAP_F32()[value+4*i>>2];view[i+1]=GROWABLE_HEAP_F32()[value+(4*i+4)>>2];view[i+2]=GROWABLE_HEAP_F32()[value+(4*i+8)>>2]}}else{var view=GROWABLE_HEAP_F32().subarray(value>>2,value+count*12>>2)}GLctx.uniform3fv(webglGetUniformLocation(location),view)}_glUniform3fv.sig="viip";var _emscripten_glUniform3fv=_glUniform3fv;function _glUniform3i(location,v0,v1,v2){GLctx.uniform3i(webglGetUniformLocation(location),v0,v1,v2)}_glUniform3i.sig="viiii";var _emscripten_glUniform3i=_glUniform3i;function _glUniform3iv(location,count,value){if(count<=96){var view=miniTempWebGLIntBuffers[3*count-1];for(var i=0;i<3*count;i+=3){view[i]=GROWABLE_HEAP_I32()[value+4*i>>2];view[i+1]=GROWABLE_HEAP_I32()[value+(4*i+4)>>2];view[i+2]=GROWABLE_HEAP_I32()[value+(4*i+8)>>2]}}else{var view=GROWABLE_HEAP_I32().subarray(value>>2,value+count*12>>2)}GLctx.uniform3iv(webglGetUniformLocation(location),view)}_glUniform3iv.sig="viip";var _emscripten_glUniform3iv=_glUniform3iv;function _glUniform4f(location,v0,v1,v2,v3){GLctx.uniform4f(webglGetUniformLocation(location),v0,v1,v2,v3)}_glUniform4f.sig="viffff";var _emscripten_glUniform4f=_glUniform4f;function _glUniform4fv(location,count,value){if(count<=72){var view=miniTempWebGLFloatBuffers[4*count-1];var heap=GROWABLE_HEAP_F32();value>>=2;for(var i=0;i<4*count;i+=4){var dst=value+i;view[i]=heap[dst];view[i+1]=heap[dst+1];view[i+2]=heap[dst+2];view[i+3]=heap[dst+3]}}else{var view=GROWABLE_HEAP_F32().subarray(value>>2,value+count*16>>2)}GLctx.uniform4fv(webglGetUniformLocation(location),view)}_glUniform4fv.sig="viip";var _emscripten_glUniform4fv=_glUniform4fv;function _glUniform4i(location,v0,v1,v2,v3){GLctx.uniform4i(webglGetUniformLocation(location),v0,v1,v2,v3)}_glUniform4i.sig="viiiii";var _emscripten_glUniform4i=_glUniform4i;function _glUniform4iv(location,count,value){if(count<=72){var view=miniTempWebGLIntBuffers[4*count-1];for(var i=0;i<4*count;i+=4){view[i]=GROWABLE_HEAP_I32()[value+4*i>>2];view[i+1]=GROWABLE_HEAP_I32()[value+(4*i+4)>>2];view[i+2]=GROWABLE_HEAP_I32()[value+(4*i+8)>>2];view[i+3]=GROWABLE_HEAP_I32()[value+(4*i+12)>>2]}}else{var view=GROWABLE_HEAP_I32().subarray(value>>2,value+count*16>>2)}GLctx.uniform4iv(webglGetUniformLocation(location),view)}_glUniform4iv.sig="viip";var _emscripten_glUniform4iv=_glUniform4iv;function _glUniformMatrix2fv(location,count,transpose,value){if(count<=72){var view=miniTempWebGLFloatBuffers[4*count-1];for(var i=0;i<4*count;i+=4){view[i]=GROWABLE_HEAP_F32()[value+4*i>>2];view[i+1]=GROWABLE_HEAP_F32()[value+(4*i+4)>>2];view[i+2]=GROWABLE_HEAP_F32()[value+(4*i+8)>>2];view[i+3]=GROWABLE_HEAP_F32()[value+(4*i+12)>>2]}}else{var view=GROWABLE_HEAP_F32().subarray(value>>2,value+count*16>>2)}GLctx.uniformMatrix2fv(webglGetUniformLocation(location),!!transpose,view)}_glUniformMatrix2fv.sig="viiip";var _emscripten_glUniformMatrix2fv=_glUniformMatrix2fv;function _glUniformMatrix3fv(location,count,transpose,value){if(count<=32){var view=miniTempWebGLFloatBuffers[9*count-1];for(var i=0;i<9*count;i+=9){view[i]=GROWABLE_HEAP_F32()[value+4*i>>2];view[i+1]=GROWABLE_HEAP_F32()[value+(4*i+4)>>2];view[i+2]=GROWABLE_HEAP_F32()[value+(4*i+8)>>2];view[i+3]=GROWABLE_HEAP_F32()[value+(4*i+12)>>2];view[i+4]=GROWABLE_HEAP_F32()[value+(4*i+16)>>2];view[i+5]=GROWABLE_HEAP_F32()[value+(4*i+20)>>2];view[i+6]=GROWABLE_HEAP_F32()[value+(4*i+24)>>2];view[i+7]=GROWABLE_HEAP_F32()[value+(4*i+28)>>2];view[i+8]=GROWABLE_HEAP_F32()[value+(4*i+32)>>2]}}else{var view=GROWABLE_HEAP_F32().subarray(value>>2,value+count*36>>2)}GLctx.uniformMatrix3fv(webglGetUniformLocation(location),!!transpose,view)}_glUniformMatrix3fv.sig="viiip";var _emscripten_glUniformMatrix3fv=_glUniformMatrix3fv;function _glUniformMatrix4fv(location,count,transpose,value){if(count<=18){var view=miniTempWebGLFloatBuffers[16*count-1];var heap=GROWABLE_HEAP_F32();value>>=2;for(var i=0;i<16*count;i+=16){var dst=value+i;view[i]=heap[dst];view[i+1]=heap[dst+1];view[i+2]=heap[dst+2];view[i+3]=heap[dst+3];view[i+4]=heap[dst+4];view[i+5]=heap[dst+5];view[i+6]=heap[dst+6];view[i+7]=heap[dst+7];view[i+8]=heap[dst+8];view[i+9]=heap[dst+9];view[i+10]=heap[dst+10];view[i+11]=heap[dst+11];view[i+12]=heap[dst+12];view[i+13]=heap[dst+13];view[i+14]=heap[dst+14];view[i+15]=heap[dst+15]}}else{var view=GROWABLE_HEAP_F32().subarray(value>>2,value+count*64>>2)}GLctx.uniformMatrix4fv(webglGetUniformLocation(location),!!transpose,view)}_glUniformMatrix4fv.sig="viiip";var _emscripten_glUniformMatrix4fv=_glUniformMatrix4fv;function _glUseProgram(program){program=GL.programs[program];GLctx.useProgram(program);GLctx.currentProgram=program}_glUseProgram.sig="vi";var _emscripten_glUseProgram=_glUseProgram;function _glValidateProgram(program){GLctx.validateProgram(GL.programs[program])}_glValidateProgram.sig="vi";var _emscripten_glValidateProgram=_glValidateProgram;function _glVertexAttrib1f(x0,x1){GLctx.vertexAttrib1f(x0,x1)}_glVertexAttrib1f.sig="vif";var _emscripten_glVertexAttrib1f=_glVertexAttrib1f;function _glVertexAttrib1fv(index,v){GLctx.vertexAttrib1f(index,GROWABLE_HEAP_F32()[v>>2])}_glVertexAttrib1fv.sig="vip";var _emscripten_glVertexAttrib1fv=_glVertexAttrib1fv;function _glVertexAttrib2f(x0,x1,x2){GLctx.vertexAttrib2f(x0,x1,x2)}_glVertexAttrib2f.sig="viff";var _emscripten_glVertexAttrib2f=_glVertexAttrib2f;function _glVertexAttrib2fv(index,v){GLctx.vertexAttrib2f(index,GROWABLE_HEAP_F32()[v>>2],GROWABLE_HEAP_F32()[v+4>>2])}_glVertexAttrib2fv.sig="vip";var _emscripten_glVertexAttrib2fv=_glVertexAttrib2fv;function _glVertexAttrib3f(x0,x1,x2,x3){GLctx.vertexAttrib3f(x0,x1,x2,x3)}_glVertexAttrib3f.sig="vifff";var _emscripten_glVertexAttrib3f=_glVertexAttrib3f;function _glVertexAttrib3fv(index,v){GLctx.vertexAttrib3f(index,GROWABLE_HEAP_F32()[v>>2],GROWABLE_HEAP_F32()[v+4>>2],GROWABLE_HEAP_F32()[v+8>>2])}_glVertexAttrib3fv.sig="vip";var _emscripten_glVertexAttrib3fv=_glVertexAttrib3fv;function _glVertexAttrib4f(x0,x1,x2,x3,x4){GLctx.vertexAttrib4f(x0,x1,x2,x3,x4)}_glVertexAttrib4f.sig="viffff";var _emscripten_glVertexAttrib4f=_glVertexAttrib4f;function _glVertexAttrib4fv(index,v){GLctx.vertexAttrib4f(index,GROWABLE_HEAP_F32()[v>>2],GROWABLE_HEAP_F32()[v+4>>2],GROWABLE_HEAP_F32()[v+8>>2],GROWABLE_HEAP_F32()[v+12>>2])}_glVertexAttrib4fv.sig="vip";var _emscripten_glVertexAttrib4fv=_glVertexAttrib4fv;function _glVertexAttribDivisor(index,divisor){GLctx.vertexAttribDivisor(index,divisor)}_glVertexAttribDivisor.sig="vii";var _glVertexAttribDivisorANGLE=_glVertexAttribDivisor;var _emscripten_glVertexAttribDivisorANGLE=_glVertexAttribDivisorANGLE;function _glVertexAttribPointer(index,size,type,normalized,stride,ptr){GLctx.vertexAttribPointer(index,size,type,!!normalized,stride,ptr)}_glVertexAttribPointer.sig="viiiiip";var _emscripten_glVertexAttribPointer=_glVertexAttribPointer;function _glViewport(x0,x1,x2,x3){GLctx.viewport(x0,x1,x2,x3)}_glViewport.sig="viiii";var _emscripten_glViewport=_glViewport;function _emscripten_has_asyncify(){return 0}_emscripten_has_asyncify.sig="i";function _emscripten_promise_destroy(id){promiseMap.free(id)}_emscripten_promise_destroy.sig="vp";function _emscripten_promise_resolve(id,result,value){var info=promiseMap.get(id);switch(result){case 0:info.resolve(value);return;case 1:info.resolve(getPromise(value));return;case 2:info.resolve(getPromise(value));_emscripten_promise_destroy(value);return;case 3:info.reject(value);return}}_emscripten_promise_resolve.sig="vpip";var emscripten_receive_on_main_thread_js_callArgs=[];function _emscripten_receive_on_main_thread_js(index,numCallArgs,args){emscripten_receive_on_main_thread_js_callArgs.length=numCallArgs;var b=args>>3;for(var i=0;i<numCallArgs;i++){emscripten_receive_on_main_thread_js_callArgs[i]=GROWABLE_HEAP_F64()[b+i]}var isEmAsmConst=index<0;var func=!isEmAsmConst?proxiedFunctionTable[index]:ASM_CONSTS[-index-1];return func.apply(null,emscripten_receive_on_main_thread_js_callArgs)}_emscripten_receive_on_main_thread_js.sig="diip";function doRequestFullscreen(target,strategy){if(!JSEvents.fullscreenEnabled())return-1;target=findEventTarget(target);if(!target)return-4;if(!target.requestFullscreen&&!target.webkitRequestFullscreen){return-3}var canPerformRequests=JSEvents.canPerformEventHandlerRequests();if(!canPerformRequests){if(strategy.deferUntilInEventHandler){JSEvents.deferCall(JSEvents_requestFullscreen,1,[target,strategy]);return 1}return-2}return JSEvents_requestFullscreen(target,strategy)}function _emscripten_request_fullscreen_strategy(target,deferUntilInEventHandler,fullscreenStrategy){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(60,1,target,deferUntilInEventHandler,fullscreenStrategy);var strategy={scaleMode:GROWABLE_HEAP_I32()[fullscreenStrategy>>2],canvasResolutionScaleMode:GROWABLE_HEAP_I32()[fullscreenStrategy+4>>2],filteringMode:GROWABLE_HEAP_I32()[fullscreenStrategy+8>>2],deferUntilInEventHandler:deferUntilInEventHandler,canvasResizedCallbackTargetThread:GROWABLE_HEAP_I32()[fullscreenStrategy+20>>2],canvasResizedCallback:GROWABLE_HEAP_I32()[fullscreenStrategy+12>>2],canvasResizedCallbackUserData:GROWABLE_HEAP_I32()[fullscreenStrategy+16>>2]};return doRequestFullscreen(target,strategy)}_emscripten_request_fullscreen_strategy.sig="ipip";function _emscripten_request_pointerlock(target,deferUntilInEventHandler){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(61,1,target,deferUntilInEventHandler);target=findEventTarget(target);if(!target)return-4;if(!target.requestPointerLock){return-1}var canPerformRequests=JSEvents.canPerformEventHandlerRequests();if(!canPerformRequests){if(deferUntilInEventHandler){JSEvents.deferCall(requestPointerLock,2,[target]);return 1}return-2}return requestPointerLock(target)}_emscripten_request_pointerlock.sig="ipi";function getHeapMax(){return 1073741824}function emscripten_realloc_buffer(size){var b=wasmMemory.buffer;var pages=size-b.byteLength+65535>>>16;try{wasmMemory.grow(pages);updateMemoryViews();return 1}catch(e){}}function _emscripten_resize_heap(requestedSize){var oldSize=GROWABLE_HEAP_U8().length;requestedSize=requestedSize>>>0;if(requestedSize<=oldSize){return false}var maxHeapSize=getHeapMax();if(requestedSize>maxHeapSize){return false}var alignUp=(x,multiple)=>x+(multiple-x%multiple)%multiple;for(var cutDown=1;cutDown<=4;cutDown*=2){var overGrownHeapSize=oldSize*(1+.2/cutDown);overGrownHeapSize=Math.min(overGrownHeapSize,requestedSize+100663296);var newSize=Math.min(maxHeapSize,alignUp(Math.max(requestedSize,overGrownHeapSize),65536));var replacement=emscripten_realloc_buffer(newSize);if(replacement){return true}}return false}_emscripten_resize_heap.sig="ip";function _emscripten_sample_gamepad_data(){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(62,1);return(JSEvents.lastGamepadState=navigator.getGamepads?navigator.getGamepads():navigator.webkitGetGamepads?navigator.webkitGetGamepads():null)?0:-1}_emscripten_sample_gamepad_data.sig="i";function registerBeforeUnloadEventCallback(target,userData,useCapture,callbackfunc,eventTypeId,eventTypeString){var beforeUnloadEventHandlerFunc=function(e=event){var confirmationMessage=getWasmTableEntry(callbackfunc)(eventTypeId,0,userData);if(confirmationMessage){confirmationMessage=UTF8ToString(confirmationMessage)}if(confirmationMessage){e.preventDefault();e.returnValue=confirmationMessage;return confirmationMessage}};var eventHandler={target:findEventTarget(target),eventTypeString:eventTypeString,callbackfunc:callbackfunc,handlerFunc:beforeUnloadEventHandlerFunc,useCapture:useCapture};return JSEvents.registerOrRemoveHandler(eventHandler)}function _emscripten_set_beforeunload_callback_on_thread(userData,callbackfunc,targetThread){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(63,1,userData,callbackfunc,targetThread);if(typeof onbeforeunload=="undefined")return-1;if(targetThread!==1)return-5;return registerBeforeUnloadEventCallback(2,userData,true,callbackfunc,28,"beforeunload")}_emscripten_set_beforeunload_callback_on_thread.sig="ippp";function registerFocusEventCallback(target,userData,useCapture,callbackfunc,eventTypeId,eventTypeString,targetThread){targetThread=JSEvents.getTargetThreadForEventCallback(targetThread);if(!JSEvents.focusEvent)JSEvents.focusEvent=_malloc(256);var focusEventHandlerFunc=function(e=event){var nodeName=JSEvents.getNodeNameForTarget(e.target);var id=e.target.id?e.target.id:"";var focusEvent=targetThread?_malloc(256):JSEvents.focusEvent;stringToUTF8(nodeName,focusEvent+0,128);stringToUTF8(id,focusEvent+128,128);if(targetThread)JSEvents.queueEventHandlerOnThread_iiii(targetThread,callbackfunc,eventTypeId,focusEvent,userData);else if(getWasmTableEntry(callbackfunc)(eventTypeId,focusEvent,userData))e.preventDefault()};var eventHandler={target:findEventTarget(target),eventTypeString:eventTypeString,callbackfunc:callbackfunc,handlerFunc:focusEventHandlerFunc,useCapture:useCapture};return JSEvents.registerOrRemoveHandler(eventHandler)}function _emscripten_set_blur_callback_on_thread(target,userData,useCapture,callbackfunc,targetThread){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(64,1,target,userData,useCapture,callbackfunc,targetThread);return registerFocusEventCallback(target,userData,useCapture,callbackfunc,12,"blur",targetThread)}_emscripten_set_blur_callback_on_thread.sig="ippipp";function _emscripten_set_element_css_size(target,width,height){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(65,1,target,width,height);target=findEventTarget(target);if(!target)return-4;target.style.width=width+"px";target.style.height=height+"px";return 0}_emscripten_set_element_css_size.sig="ipdd";function _emscripten_set_focus_callback_on_thread(target,userData,useCapture,callbackfunc,targetThread){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(66,1,target,userData,useCapture,callbackfunc,targetThread);return registerFocusEventCallback(target,userData,useCapture,callbackfunc,13,"focus",targetThread)}_emscripten_set_focus_callback_on_thread.sig="ippipp";function fillFullscreenChangeEventData(eventStruct){var fullscreenElement=document.fullscreenElement||document.mozFullScreenElement||document.webkitFullscreenElement||document.msFullscreenElement;var isFullscreen=!!fullscreenElement;GROWABLE_HEAP_I32()[eventStruct>>2]=isFullscreen;GROWABLE_HEAP_I32()[eventStruct+4>>2]=JSEvents.fullscreenEnabled();var reportedElement=isFullscreen?fullscreenElement:JSEvents.previousFullscreenElement;var nodeName=JSEvents.getNodeNameForTarget(reportedElement);var id=reportedElement&&reportedElement.id?reportedElement.id:"";stringToUTF8(nodeName,eventStruct+8,128);stringToUTF8(id,eventStruct+136,128);GROWABLE_HEAP_I32()[eventStruct+264>>2]=reportedElement?reportedElement.clientWidth:0;GROWABLE_HEAP_I32()[eventStruct+268>>2]=reportedElement?reportedElement.clientHeight:0;GROWABLE_HEAP_I32()[eventStruct+272>>2]=screen.width;GROWABLE_HEAP_I32()[eventStruct+276>>2]=screen.height;if(isFullscreen){JSEvents.previousFullscreenElement=fullscreenElement}}function registerFullscreenChangeEventCallback(target,userData,useCapture,callbackfunc,eventTypeId,eventTypeString,targetThread){targetThread=JSEvents.getTargetThreadForEventCallback(targetThread);if(!JSEvents.fullscreenChangeEvent)JSEvents.fullscreenChangeEvent=_malloc(280);var fullscreenChangeEventhandlerFunc=function(e=event){var fullscreenChangeEvent=targetThread?_malloc(280):JSEvents.fullscreenChangeEvent;fillFullscreenChangeEventData(fullscreenChangeEvent);if(targetThread)JSEvents.queueEventHandlerOnThread_iiii(targetThread,callbackfunc,eventTypeId,fullscreenChangeEvent,userData);else if(getWasmTableEntry(callbackfunc)(eventTypeId,fullscreenChangeEvent,userData))e.preventDefault()};var eventHandler={target:target,eventTypeString:eventTypeString,callbackfunc:callbackfunc,handlerFunc:fullscreenChangeEventhandlerFunc,useCapture:useCapture};return JSEvents.registerOrRemoveHandler(eventHandler)}function _emscripten_set_fullscreenchange_callback_on_thread(target,userData,useCapture,callbackfunc,targetThread){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(67,1,target,userData,useCapture,callbackfunc,targetThread);if(!JSEvents.fullscreenEnabled())return-1;target=findEventTarget(target);if(!target)return-4;registerFullscreenChangeEventCallback(target,userData,useCapture,callbackfunc,19,"webkitfullscreenchange",targetThread);return registerFullscreenChangeEventCallback(target,userData,useCapture,callbackfunc,19,"fullscreenchange",targetThread)}_emscripten_set_fullscreenchange_callback_on_thread.sig="ippipp";function registerGamepadEventCallback(target,userData,useCapture,callbackfunc,eventTypeId,eventTypeString,targetThread){targetThread=JSEvents.getTargetThreadForEventCallback(targetThread);if(!JSEvents.gamepadEvent)JSEvents.gamepadEvent=_malloc(1432);var gamepadEventHandlerFunc=function(e=event){var gamepadEvent=targetThread?_malloc(1432):JSEvents.gamepadEvent;fillGamepadEventData(gamepadEvent,e["gamepad"]);if(targetThread)JSEvents.queueEventHandlerOnThread_iiii(targetThread,callbackfunc,eventTypeId,gamepadEvent,userData);else if(getWasmTableEntry(callbackfunc)(eventTypeId,gamepadEvent,userData))e.preventDefault()};var eventHandler={target:findEventTarget(target),allowsDeferredCalls:true,eventTypeString:eventTypeString,callbackfunc:callbackfunc,handlerFunc:gamepadEventHandlerFunc,useCapture:useCapture};return JSEvents.registerOrRemoveHandler(eventHandler)}function _emscripten_set_gamepadconnected_callback_on_thread(userData,useCapture,callbackfunc,targetThread){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(68,1,userData,useCapture,callbackfunc,targetThread);if(!navigator.getGamepads&&!navigator.webkitGetGamepads)return-1;return registerGamepadEventCallback(2,userData,useCapture,callbackfunc,26,"gamepadconnected",targetThread)}_emscripten_set_gamepadconnected_callback_on_thread.sig="ipipp";function _emscripten_set_gamepaddisconnected_callback_on_thread(userData,useCapture,callbackfunc,targetThread){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(69,1,userData,useCapture,callbackfunc,targetThread);if(!navigator.getGamepads&&!navigator.webkitGetGamepads)return-1;return registerGamepadEventCallback(2,userData,useCapture,callbackfunc,27,"gamepaddisconnected",targetThread)}_emscripten_set_gamepaddisconnected_callback_on_thread.sig="ipipp";function registerKeyEventCallback(target,userData,useCapture,callbackfunc,eventTypeId,eventTypeString,targetThread){targetThread=JSEvents.getTargetThreadForEventCallback(targetThread);if(!JSEvents.keyEvent)JSEvents.keyEvent=_malloc(176);var keyEventHandlerFunc=function(e){var keyEventData=targetThread?_malloc(176):JSEvents.keyEvent;GROWABLE_HEAP_F64()[keyEventData>>3]=e.timeStamp;var idx=keyEventData>>2;GROWABLE_HEAP_I32()[idx+2]=e.location;GROWABLE_HEAP_I32()[idx+3]=e.ctrlKey;GROWABLE_HEAP_I32()[idx+4]=e.shiftKey;GROWABLE_HEAP_I32()[idx+5]=e.altKey;GROWABLE_HEAP_I32()[idx+6]=e.metaKey;GROWABLE_HEAP_I32()[idx+7]=e.repeat;GROWABLE_HEAP_I32()[idx+8]=e.charCode;GROWABLE_HEAP_I32()[idx+9]=e.keyCode;GROWABLE_HEAP_I32()[idx+10]=e.which;stringToUTF8(e.key||"",keyEventData+44,32);stringToUTF8(e.code||"",keyEventData+76,32);stringToUTF8(e.char||"",keyEventData+108,32);stringToUTF8(e.locale||"",keyEventData+140,32);if(targetThread)JSEvents.queueEventHandlerOnThread_iiii(targetThread,callbackfunc,eventTypeId,keyEventData,userData);else if(getWasmTableEntry(callbackfunc)(eventTypeId,keyEventData,userData))e.preventDefault()};var eventHandler={target:findEventTarget(target),allowsDeferredCalls:true,eventTypeString:eventTypeString,callbackfunc:callbackfunc,handlerFunc:keyEventHandlerFunc,useCapture:useCapture};return JSEvents.registerOrRemoveHandler(eventHandler)}function _emscripten_set_keydown_callback_on_thread(target,userData,useCapture,callbackfunc,targetThread){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(70,1,target,userData,useCapture,callbackfunc,targetThread);return registerKeyEventCallback(target,userData,useCapture,callbackfunc,2,"keydown",targetThread)}_emscripten_set_keydown_callback_on_thread.sig="ippipp";function _emscripten_set_keypress_callback_on_thread(target,userData,useCapture,callbackfunc,targetThread){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(71,1,target,userData,useCapture,callbackfunc,targetThread);return registerKeyEventCallback(target,userData,useCapture,callbackfunc,1,"keypress",targetThread)}_emscripten_set_keypress_callback_on_thread.sig="ippipp";function _emscripten_set_keyup_callback_on_thread(target,userData,useCapture,callbackfunc,targetThread){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(72,1,target,userData,useCapture,callbackfunc,targetThread);return registerKeyEventCallback(target,userData,useCapture,callbackfunc,3,"keyup",targetThread)}_emscripten_set_keyup_callback_on_thread.sig="ippipp";function _emscripten_set_main_loop(func,fps,simulateInfiniteLoop){var browserIterationFunc=getWasmTableEntry(func);setMainLoop(browserIterationFunc,fps,simulateInfiniteLoop)}_emscripten_set_main_loop.sig="vpii";function fillMouseEventData(eventStruct,e,target){GROWABLE_HEAP_F64()[eventStruct>>3]=e.timeStamp;var idx=eventStruct>>2;GROWABLE_HEAP_I32()[idx+2]=e.screenX;GROWABLE_HEAP_I32()[idx+3]=e.screenY;GROWABLE_HEAP_I32()[idx+4]=e.clientX;GROWABLE_HEAP_I32()[idx+5]=e.clientY;GROWABLE_HEAP_I32()[idx+6]=e.ctrlKey;GROWABLE_HEAP_I32()[idx+7]=e.shiftKey;GROWABLE_HEAP_I32()[idx+8]=e.altKey;GROWABLE_HEAP_I32()[idx+9]=e.metaKey;GROWABLE_HEAP_I16()[idx*2+20]=e.button;GROWABLE_HEAP_I16()[idx*2+21]=e.buttons;GROWABLE_HEAP_I32()[idx+11]=e["movementX"];GROWABLE_HEAP_I32()[idx+12]=e["movementY"];var rect=getBoundingClientRect(target);GROWABLE_HEAP_I32()[idx+13]=e.clientX-rect.left;GROWABLE_HEAP_I32()[idx+14]=e.clientY-rect.top}function registerMouseEventCallback(target,userData,useCapture,callbackfunc,eventTypeId,eventTypeString,targetThread){targetThread=JSEvents.getTargetThreadForEventCallback(targetThread);if(!JSEvents.mouseEvent)JSEvents.mouseEvent=_malloc(72);target=findEventTarget(target);var mouseEventHandlerFunc=function(e=event){fillMouseEventData(JSEvents.mouseEvent,e,target);if(targetThread){var mouseEventData=_malloc(72);fillMouseEventData(mouseEventData,e,target);JSEvents.queueEventHandlerOnThread_iiii(targetThread,callbackfunc,eventTypeId,mouseEventData,userData)}else if(getWasmTableEntry(callbackfunc)(eventTypeId,JSEvents.mouseEvent,userData))e.preventDefault()};var eventHandler={target:target,allowsDeferredCalls:eventTypeString!="mousemove"&&eventTypeString!="mouseenter"&&eventTypeString!="mouseleave",eventTypeString:eventTypeString,callbackfunc:callbackfunc,handlerFunc:mouseEventHandlerFunc,useCapture:useCapture};return JSEvents.registerOrRemoveHandler(eventHandler)}function _emscripten_set_mousedown_callback_on_thread(target,userData,useCapture,callbackfunc,targetThread){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(73,1,target,userData,useCapture,callbackfunc,targetThread);return registerMouseEventCallback(target,userData,useCapture,callbackfunc,5,"mousedown",targetThread)}_emscripten_set_mousedown_callback_on_thread.sig="ippipp";function _emscripten_set_mouseenter_callback_on_thread(target,userData,useCapture,callbackfunc,targetThread){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(74,1,target,userData,useCapture,callbackfunc,targetThread);return registerMouseEventCallback(target,userData,useCapture,callbackfunc,33,"mouseenter",targetThread)}_emscripten_set_mouseenter_callback_on_thread.sig="ippipp";function _emscripten_set_mouseleave_callback_on_thread(target,userData,useCapture,callbackfunc,targetThread){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(75,1,target,userData,useCapture,callbackfunc,targetThread);return registerMouseEventCallback(target,userData,useCapture,callbackfunc,34,"mouseleave",targetThread)}_emscripten_set_mouseleave_callback_on_thread.sig="ippipp";function _emscripten_set_mousemove_callback_on_thread(target,userData,useCapture,callbackfunc,targetThread){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(76,1,target,userData,useCapture,callbackfunc,targetThread);return registerMouseEventCallback(target,userData,useCapture,callbackfunc,8,"mousemove",targetThread)}_emscripten_set_mousemove_callback_on_thread.sig="ippipp";function _emscripten_set_mouseup_callback_on_thread(target,userData,useCapture,callbackfunc,targetThread){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(77,1,target,userData,useCapture,callbackfunc,targetThread);return registerMouseEventCallback(target,userData,useCapture,callbackfunc,6,"mouseup",targetThread)}_emscripten_set_mouseup_callback_on_thread.sig="ippipp";function fillPointerlockChangeEventData(eventStruct){var pointerLockElement=document.pointerLockElement||document.mozPointerLockElement||document.webkitPointerLockElement||document.msPointerLockElement;var isPointerlocked=!!pointerLockElement;GROWABLE_HEAP_I32()[eventStruct>>2]=isPointerlocked;var nodeName=JSEvents.getNodeNameForTarget(pointerLockElement);var id=pointerLockElement&&pointerLockElement.id?pointerLockElement.id:"";stringToUTF8(nodeName,eventStruct+4,128);stringToUTF8(id,eventStruct+132,128)}function registerPointerlockChangeEventCallback(target,userData,useCapture,callbackfunc,eventTypeId,eventTypeString,targetThread){targetThread=JSEvents.getTargetThreadForEventCallback(targetThread);if(!JSEvents.pointerlockChangeEvent)JSEvents.pointerlockChangeEvent=_malloc(260);var pointerlockChangeEventHandlerFunc=function(e=event){var pointerlockChangeEvent=targetThread?_malloc(260):JSEvents.pointerlockChangeEvent;fillPointerlockChangeEventData(pointerlockChangeEvent);if(targetThread)JSEvents.queueEventHandlerOnThread_iiii(targetThread,callbackfunc,eventTypeId,pointerlockChangeEvent,userData);else if(getWasmTableEntry(callbackfunc)(eventTypeId,pointerlockChangeEvent,userData))e.preventDefault()};var eventHandler={target:target,eventTypeString:eventTypeString,callbackfunc:callbackfunc,handlerFunc:pointerlockChangeEventHandlerFunc,useCapture:useCapture};return JSEvents.registerOrRemoveHandler(eventHandler)}function _emscripten_set_pointerlockchange_callback_on_thread(target,userData,useCapture,callbackfunc,targetThread){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(78,1,target,userData,useCapture,callbackfunc,targetThread);if(!document||!document.body||!document.body.requestPointerLock&&!document.body.mozRequestPointerLock&&!document.body.webkitRequestPointerLock&&!document.body.msRequestPointerLock){return-1}target=findEventTarget(target);if(!target)return-4;registerPointerlockChangeEventCallback(target,userData,useCapture,callbackfunc,20,"mozpointerlockchange",targetThread);registerPointerlockChangeEventCallback(target,userData,useCapture,callbackfunc,20,"webkitpointerlockchange",targetThread);registerPointerlockChangeEventCallback(target,userData,useCapture,callbackfunc,20,"mspointerlockchange",targetThread);return registerPointerlockChangeEventCallback(target,userData,useCapture,callbackfunc,20,"pointerlockchange",targetThread)}_emscripten_set_pointerlockchange_callback_on_thread.sig="ippipp";function registerUiEventCallback(target,userData,useCapture,callbackfunc,eventTypeId,eventTypeString,targetThread){targetThread=JSEvents.getTargetThreadForEventCallback(targetThread);if(!JSEvents.uiEvent)JSEvents.uiEvent=_malloc(36);target=findEventTarget(target);var uiEventHandlerFunc=function(e=event){if(e.target!=target){return}var b=document.body;if(!b){return}var uiEvent=targetThread?_malloc(36):JSEvents.uiEvent;GROWABLE_HEAP_I32()[uiEvent>>2]=e.detail;GROWABLE_HEAP_I32()[uiEvent+4>>2]=b.clientWidth;GROWABLE_HEAP_I32()[uiEvent+8>>2]=b.clientHeight;GROWABLE_HEAP_I32()[uiEvent+12>>2]=innerWidth;GROWABLE_HEAP_I32()[uiEvent+16>>2]=innerHeight;GROWABLE_HEAP_I32()[uiEvent+20>>2]=outerWidth;GROWABLE_HEAP_I32()[uiEvent+24>>2]=outerHeight;GROWABLE_HEAP_I32()[uiEvent+28>>2]=pageXOffset;GROWABLE_HEAP_I32()[uiEvent+32>>2]=pageYOffset;if(targetThread)JSEvents.queueEventHandlerOnThread_iiii(targetThread,callbackfunc,eventTypeId,uiEvent,userData);else if(getWasmTableEntry(callbackfunc)(eventTypeId,uiEvent,userData))e.preventDefault()};var eventHandler={target:target,eventTypeString:eventTypeString,callbackfunc:callbackfunc,handlerFunc:uiEventHandlerFunc,useCapture:useCapture};return JSEvents.registerOrRemoveHandler(eventHandler)}function _emscripten_set_resize_callback_on_thread(target,userData,useCapture,callbackfunc,targetThread){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(79,1,target,userData,useCapture,callbackfunc,targetThread);return registerUiEventCallback(target,userData,useCapture,callbackfunc,10,"resize",targetThread)}_emscripten_set_resize_callback_on_thread.sig="ippipp";function registerTouchEventCallback(target,userData,useCapture,callbackfunc,eventTypeId,eventTypeString,targetThread){targetThread=JSEvents.getTargetThreadForEventCallback(targetThread);if(!JSEvents.touchEvent)JSEvents.touchEvent=_malloc(1696);target=findEventTarget(target);var touchEventHandlerFunc=function(e){var t,touches={},et=e.touches;for(var i=0;i<et.length;++i){t=et[i];t.isChanged=t.onTarget=0;touches[t.identifier]=t}for(var i=0;i<e.changedTouches.length;++i){t=e.changedTouches[i];t.isChanged=1;touches[t.identifier]=t}for(var i=0;i<e.targetTouches.length;++i){touches[e.targetTouches[i].identifier].onTarget=1}var touchEvent=targetThread?_malloc(1696):JSEvents.touchEvent;GROWABLE_HEAP_F64()[touchEvent>>3]=e.timeStamp;var idx=touchEvent>>2;GROWABLE_HEAP_I32()[idx+3]=e.ctrlKey;GROWABLE_HEAP_I32()[idx+4]=e.shiftKey;GROWABLE_HEAP_I32()[idx+5]=e.altKey;GROWABLE_HEAP_I32()[idx+6]=e.metaKey;idx+=7;var targetRect=getBoundingClientRect(target);var numTouches=0;for(var i in touches){t=touches[i];GROWABLE_HEAP_I32()[idx+0]=t.identifier;GROWABLE_HEAP_I32()[idx+1]=t.screenX;GROWABLE_HEAP_I32()[idx+2]=t.screenY;GROWABLE_HEAP_I32()[idx+3]=t.clientX;GROWABLE_HEAP_I32()[idx+4]=t.clientY;GROWABLE_HEAP_I32()[idx+5]=t.pageX;GROWABLE_HEAP_I32()[idx+6]=t.pageY;GROWABLE_HEAP_I32()[idx+7]=t.isChanged;GROWABLE_HEAP_I32()[idx+8]=t.onTarget;GROWABLE_HEAP_I32()[idx+9]=t.clientX-targetRect.left;GROWABLE_HEAP_I32()[idx+10]=t.clientY-targetRect.top;idx+=13;if(++numTouches>31){break}}GROWABLE_HEAP_I32()[touchEvent+8>>2]=numTouches;if(targetThread)JSEvents.queueEventHandlerOnThread_iiii(targetThread,callbackfunc,eventTypeId,touchEvent,userData);else if(getWasmTableEntry(callbackfunc)(eventTypeId,touchEvent,userData))e.preventDefault()};var eventHandler={target:target,allowsDeferredCalls:eventTypeString=="touchstart"||eventTypeString=="touchend",eventTypeString:eventTypeString,callbackfunc:callbackfunc,handlerFunc:touchEventHandlerFunc,useCapture:useCapture};return JSEvents.registerOrRemoveHandler(eventHandler)}function _emscripten_set_touchcancel_callback_on_thread(target,userData,useCapture,callbackfunc,targetThread){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(80,1,target,userData,useCapture,callbackfunc,targetThread);return registerTouchEventCallback(target,userData,useCapture,callbackfunc,25,"touchcancel",targetThread)}_emscripten_set_touchcancel_callback_on_thread.sig="ippipp";function _emscripten_set_touchend_callback_on_thread(target,userData,useCapture,callbackfunc,targetThread){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(81,1,target,userData,useCapture,callbackfunc,targetThread);return registerTouchEventCallback(target,userData,useCapture,callbackfunc,23,"touchend",targetThread)}_emscripten_set_touchend_callback_on_thread.sig="ippipp";function _emscripten_set_touchmove_callback_on_thread(target,userData,useCapture,callbackfunc,targetThread){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(82,1,target,userData,useCapture,callbackfunc,targetThread);return registerTouchEventCallback(target,userData,useCapture,callbackfunc,24,"touchmove",targetThread)}_emscripten_set_touchmove_callback_on_thread.sig="ippipp";function _emscripten_set_touchstart_callback_on_thread(target,userData,useCapture,callbackfunc,targetThread){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(83,1,target,userData,useCapture,callbackfunc,targetThread);return registerTouchEventCallback(target,userData,useCapture,callbackfunc,22,"touchstart",targetThread)}_emscripten_set_touchstart_callback_on_thread.sig="ippipp";function fillVisibilityChangeEventData(eventStruct){var visibilityStates=["hidden","visible","prerender","unloaded"];var visibilityState=visibilityStates.indexOf(document.visibilityState);GROWABLE_HEAP_I32()[eventStruct>>2]=document.hidden;GROWABLE_HEAP_I32()[eventStruct+4>>2]=visibilityState}function registerVisibilityChangeEventCallback(target,userData,useCapture,callbackfunc,eventTypeId,eventTypeString,targetThread){targetThread=JSEvents.getTargetThreadForEventCallback(targetThread);if(!JSEvents.visibilityChangeEvent)JSEvents.visibilityChangeEvent=_malloc(8);var visibilityChangeEventHandlerFunc=function(e=event){var visibilityChangeEvent=targetThread?_malloc(8):JSEvents.visibilityChangeEvent;fillVisibilityChangeEventData(visibilityChangeEvent);if(targetThread)JSEvents.queueEventHandlerOnThread_iiii(targetThread,callbackfunc,eventTypeId,visibilityChangeEvent,userData);else if(getWasmTableEntry(callbackfunc)(eventTypeId,visibilityChangeEvent,userData))e.preventDefault()};var eventHandler={target:target,eventTypeString:eventTypeString,callbackfunc:callbackfunc,handlerFunc:visibilityChangeEventHandlerFunc,useCapture:useCapture};return JSEvents.registerOrRemoveHandler(eventHandler)}function _emscripten_set_visibilitychange_callback_on_thread(userData,useCapture,callbackfunc,targetThread){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(84,1,userData,useCapture,callbackfunc,targetThread);if(!specialHTMLTargets[1]){return-4}return registerVisibilityChangeEventCallback(specialHTMLTargets[1],userData,useCapture,callbackfunc,21,"visibilitychange",targetThread)}_emscripten_set_visibilitychange_callback_on_thread.sig="ipipp";function registerWheelEventCallback(target,userData,useCapture,callbackfunc,eventTypeId,eventTypeString,targetThread){targetThread=JSEvents.getTargetThreadForEventCallback(targetThread);if(!JSEvents.wheelEvent)JSEvents.wheelEvent=_malloc(104);var wheelHandlerFunc=function(e=event){var wheelEvent=targetThread?_malloc(104):JSEvents.wheelEvent;fillMouseEventData(wheelEvent,e,target);GROWABLE_HEAP_F64()[wheelEvent+72>>3]=e["deltaX"];GROWABLE_HEAP_F64()[wheelEvent+80>>3]=e["deltaY"];GROWABLE_HEAP_F64()[wheelEvent+88>>3]=e["deltaZ"];GROWABLE_HEAP_I32()[wheelEvent+96>>2]=e["deltaMode"];if(targetThread)JSEvents.queueEventHandlerOnThread_iiii(targetThread,callbackfunc,eventTypeId,wheelEvent,userData);else if(getWasmTableEntry(callbackfunc)(eventTypeId,wheelEvent,userData))e.preventDefault()};var eventHandler={target:target,allowsDeferredCalls:true,eventTypeString:eventTypeString,callbackfunc:callbackfunc,handlerFunc:wheelHandlerFunc,useCapture:useCapture};return JSEvents.registerOrRemoveHandler(eventHandler)}function _emscripten_set_wheel_callback_on_thread(target,userData,useCapture,callbackfunc,targetThread){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(85,1,target,userData,useCapture,callbackfunc,targetThread);target=findEventTarget(target);if(!target)return-4;if(typeof target.onwheel!="undefined"){return registerWheelEventCallback(target,userData,useCapture,callbackfunc,9,"wheel",targetThread)}else{return-1}}_emscripten_set_wheel_callback_on_thread.sig="ippipp";function _emscripten_set_window_title(title){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(86,1,title);setWindowTitle(UTF8ToString(title))}_emscripten_set_window_title.sig="vp";function _emscripten_sleep(){throw"Please compile your program with async support in order to use asynchronous operations like emscripten_sleep"}_emscripten_sleep.sig="vi";function getExecutableName(){return thisProgram||"./this.program"}function getEnvStrings(){if(!getEnvStrings.strings){var lang=(typeof navigator=="object"&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8";var env={"USER":"web_user","LOGNAME":"web_user","PATH":"/","PWD":"/","HOME":"/home/<USER>","LANG":lang,"_":getExecutableName()};for(var x in ENV){if(ENV[x]===undefined)delete env[x];else env[x]=ENV[x]}var strings=[];for(var x in env){strings.push(`${x}=${env[x]}`)}getEnvStrings.strings=strings}return getEnvStrings.strings}function stringToAscii(str,buffer){for(var i=0;i<str.length;++i){GROWABLE_HEAP_I8()[buffer++>>0]=str.charCodeAt(i)}GROWABLE_HEAP_I8()[buffer>>0]=0}function _environ_get(__environ,environ_buf){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(87,1,__environ,environ_buf);var bufSize=0;getEnvStrings().forEach(function(string,i){var ptr=environ_buf+bufSize;GROWABLE_HEAP_U32()[__environ+i*4>>2]=ptr;stringToAscii(string,ptr);bufSize+=string.length+1});return 0}_environ_get.sig="ipp";function _environ_sizes_get(penviron_count,penviron_buf_size){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(88,1,penviron_count,penviron_buf_size);var strings=getEnvStrings();GROWABLE_HEAP_U32()[penviron_count>>2]=strings.length;var bufSize=0;strings.forEach(function(string){bufSize+=string.length+1});GROWABLE_HEAP_U32()[penviron_buf_size>>2]=bufSize;return 0}_environ_sizes_get.sig="ipp";function _fd_close(fd){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(89,1,fd);try{var stream=SYSCALLS.getStreamFromFD(fd);FS.close(stream);return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return e.errno}}_fd_close.sig="ii";function _fd_fdstat_get(fd,pbuf){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(90,1,fd,pbuf);try{var rightsBase=0;var rightsInheriting=0;var flags=0;{var stream=SYSCALLS.getStreamFromFD(fd);var type=stream.tty?2:FS.isDir(stream.mode)?3:FS.isLink(stream.mode)?7:4}GROWABLE_HEAP_I8()[pbuf>>0]=type;GROWABLE_HEAP_I16()[pbuf+2>>1]=flags;tempI64=[rightsBase>>>0,(tempDouble=rightsBase,+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],GROWABLE_HEAP_I32()[pbuf+8>>2]=tempI64[0],GROWABLE_HEAP_I32()[pbuf+12>>2]=tempI64[1];tempI64=[rightsInheriting>>>0,(tempDouble=rightsInheriting,+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],GROWABLE_HEAP_I32()[pbuf+16>>2]=tempI64[0],GROWABLE_HEAP_I32()[pbuf+20>>2]=tempI64[1];return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return e.errno}}_fd_fdstat_get.sig="iip";function doReadv(stream,iov,iovcnt,offset){var ret=0;for(var i=0;i<iovcnt;i++){var ptr=GROWABLE_HEAP_U32()[iov>>2];var len=GROWABLE_HEAP_U32()[iov+4>>2];iov+=8;var curr=FS.read(stream,GROWABLE_HEAP_I8(),ptr,len,offset);if(curr<0)return-1;ret+=curr;if(curr<len)break;if(typeof offset!=="undefined"){offset+=curr}}return ret}function _fd_pread(fd,iov,iovcnt,offset_low,offset_high,pnum){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(91,1,fd,iov,iovcnt,offset_low,offset_high,pnum);try{var offset=convertI32PairToI53Checked(offset_low,offset_high);if(isNaN(offset))return 61;var stream=SYSCALLS.getStreamFromFD(fd);var num=doReadv(stream,iov,iovcnt,offset);GROWABLE_HEAP_U32()[pnum>>2]=num;return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return e.errno}}_fd_pread.sig="iippiip";function doWritev(stream,iov,iovcnt,offset){var ret=0;for(var i=0;i<iovcnt;i++){var ptr=GROWABLE_HEAP_U32()[iov>>2];var len=GROWABLE_HEAP_U32()[iov+4>>2];iov+=8;var curr=FS.write(stream,GROWABLE_HEAP_I8(),ptr,len,offset);if(curr<0)return-1;ret+=curr;if(typeof offset!=="undefined"){offset+=curr}}return ret}function _fd_pwrite(fd,iov,iovcnt,offset_low,offset_high,pnum){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(92,1,fd,iov,iovcnt,offset_low,offset_high,pnum);try{var offset=convertI32PairToI53Checked(offset_low,offset_high);if(isNaN(offset))return 61;var stream=SYSCALLS.getStreamFromFD(fd);var num=doWritev(stream,iov,iovcnt,offset);GROWABLE_HEAP_U32()[pnum>>2]=num;return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return e.errno}}_fd_pwrite.sig="iippiip";function _fd_read(fd,iov,iovcnt,pnum){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(93,1,fd,iov,iovcnt,pnum);try{var stream=SYSCALLS.getStreamFromFD(fd);var num=doReadv(stream,iov,iovcnt);GROWABLE_HEAP_U32()[pnum>>2]=num;return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return e.errno}}_fd_read.sig="iippp";function _fd_seek(fd,offset_low,offset_high,whence,newOffset){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(94,1,fd,offset_low,offset_high,whence,newOffset);try{var offset=convertI32PairToI53Checked(offset_low,offset_high);if(isNaN(offset))return 61;var stream=SYSCALLS.getStreamFromFD(fd);FS.llseek(stream,offset,whence);tempI64=[stream.position>>>0,(tempDouble=stream.position,+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],GROWABLE_HEAP_I32()[newOffset>>2]=tempI64[0],GROWABLE_HEAP_I32()[newOffset+4>>2]=tempI64[1];if(stream.getdents&&offset===0&&whence===0)stream.getdents=null;return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return e.errno}}_fd_seek.sig="iiiiip";function _fd_sync(fd){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(95,1,fd);try{var stream=SYSCALLS.getStreamFromFD(fd);if(stream.stream_ops&&stream.stream_ops.fsync){return stream.stream_ops.fsync(stream)}return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return e.errno}}_fd_sync.sig="ii";function _fd_write(fd,iov,iovcnt,pnum){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(96,1,fd,iov,iovcnt,pnum);try{var stream=SYSCALLS.getStreamFromFD(fd);var num=doWritev(stream,iov,iovcnt);GROWABLE_HEAP_U32()[pnum>>2]=num;return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return e.errno}}_fd_write.sig="iippp";function _getaddrinfo(node,service,hint,out){if(ENVIRONMENT_IS_PTHREAD)return proxyToMainThread(97,1,node,service,hint,out);var addr=0;var port=0;var flags=0;var family=0;var type=0;var proto=0;var ai;function allocaddrinfo(family,type,proto,canon,addr,port){var sa,salen,ai;var errno;salen=family===10?28:16;addr=family===10?inetNtop6(addr):inetNtop4(addr);sa=_malloc(salen);errno=writeSockaddr(sa,family,addr,port);assert(!errno);ai=_malloc(32);GROWABLE_HEAP_I32()[ai+4>>2]=family;GROWABLE_HEAP_I32()[ai+8>>2]=type;GROWABLE_HEAP_I32()[ai+12>>2]=proto;GROWABLE_HEAP_U32()[ai+24>>2]=canon;GROWABLE_HEAP_U32()[ai+20>>2]=sa;if(family===10){GROWABLE_HEAP_I32()[ai+16>>2]=28}else{GROWABLE_HEAP_I32()[ai+16>>2]=16}GROWABLE_HEAP_I32()[ai+28>>2]=0;return ai}if(hint){flags=GROWABLE_HEAP_I32()[hint>>2];family=GROWABLE_HEAP_I32()[hint+4>>2];type=GROWABLE_HEAP_I32()[hint+8>>2];proto=GROWABLE_HEAP_I32()[hint+12>>2]}if(type&&!proto){proto=type===2?17:6}if(!type&&proto){type=proto===17?2:1}if(proto===0){proto=6}if(type===0){type=1}if(!node&&!service){return-2}if(flags&~(1|2|4|1024|8|16|32)){return-1}if(hint!==0&&GROWABLE_HEAP_I32()[hint>>2]&2&&!node){return-1}if(flags&32){return-2}if(type!==0&&type!==1&&type!==2){return-7}if(family!==0&&family!==2&&family!==10){return-6}if(service){service=UTF8ToString(service);port=parseInt(service,10);if(isNaN(port)){if(flags&1024){return-2}return-8}}if(!node){if(family===0){family=2}if((flags&1)===0){if(family===2){addr=_htonl(2130706433)}else{addr=[0,0,0,1]}}ai=allocaddrinfo(family,type,proto,null,addr,port);GROWABLE_HEAP_U32()[out>>2]=ai;return 0}node=UTF8ToString(node);addr=inetPton4(node);if(addr!==null){if(family===0||family===2){family=2}else if(family===10&&flags&8){addr=[0,0,_htonl(65535),addr];family=10}else{return-2}}else{addr=inetPton6(node);if(addr!==null){if(family===0||family===10){family=10}else{return-2}}}if(addr!=null){ai=allocaddrinfo(family,type,proto,node,addr,port);GROWABLE_HEAP_U32()[out>>2]=ai;return 0}if(flags&4){return-2}node=DNS.lookup_name(node);addr=inetPton4(node);if(family===0){family=2}else if(family===10){addr=[0,0,_htonl(65535),addr]}ai=allocaddrinfo(family,type,proto,null,addr,port);GROWABLE_HEAP_U32()[out>>2]=ai;return 0}_getaddrinfo.sig="ipppp";function _getnameinfo(sa,salen,node,nodelen,serv,servlen,flags){var info=readSockaddr(sa,salen);if(info.errno){return-6}var port=info.port;var addr=info.addr;var overflowed=false;if(node&&nodelen){var lookup;if(flags&1||!(lookup=DNS.lookup_addr(addr))){if(flags&8){return-2}}else{addr=lookup}var numBytesWrittenExclNull=stringToUTF8(addr,node,nodelen);if(numBytesWrittenExclNull+1>=nodelen){overflowed=true}}if(serv&&servlen){port=""+port;var numBytesWrittenExclNull=stringToUTF8(port,serv,servlen);if(numBytesWrittenExclNull+1>=servlen){overflowed=true}}if(overflowed){return-12}return 0}_getnameinfo.sig="ipipipii";function arraySum(array,index){var sum=0;for(var i=0;i<=index;sum+=array[i++]){}return sum}var MONTH_DAYS_LEAP=[31,29,31,30,31,30,31,31,30,31,30,31];var MONTH_DAYS_REGULAR=[31,28,31,30,31,30,31,31,30,31,30,31];function addDays(date,days){var newDate=new Date(date.getTime());while(days>0){var leap=isLeapYear(newDate.getFullYear());var currentMonth=newDate.getMonth();var daysInCurrentMonth=(leap?MONTH_DAYS_LEAP:MONTH_DAYS_REGULAR)[currentMonth];if(days>daysInCurrentMonth-newDate.getDate()){days-=daysInCurrentMonth-newDate.getDate()+1;newDate.setDate(1);if(currentMonth<11){newDate.setMonth(currentMonth+1)}else{newDate.setMonth(0);newDate.setFullYear(newDate.getFullYear()+1)}}else{newDate.setDate(newDate.getDate()+days);return newDate}}return newDate}function writeArrayToMemory(array,buffer){GROWABLE_HEAP_I8().set(array,buffer)}function _strftime(s,maxsize,format,tm){var tm_zone=GROWABLE_HEAP_I32()[tm+40>>2];var date={tm_sec:GROWABLE_HEAP_I32()[tm>>2],tm_min:GROWABLE_HEAP_I32()[tm+4>>2],tm_hour:GROWABLE_HEAP_I32()[tm+8>>2],tm_mday:GROWABLE_HEAP_I32()[tm+12>>2],tm_mon:GROWABLE_HEAP_I32()[tm+16>>2],tm_year:GROWABLE_HEAP_I32()[tm+20>>2],tm_wday:GROWABLE_HEAP_I32()[tm+24>>2],tm_yday:GROWABLE_HEAP_I32()[tm+28>>2],tm_isdst:GROWABLE_HEAP_I32()[tm+32>>2],tm_gmtoff:GROWABLE_HEAP_I32()[tm+36>>2],tm_zone:tm_zone?UTF8ToString(tm_zone):""};var pattern=UTF8ToString(format);var EXPANSION_RULES_1={"%c":"%a %b %d %H:%M:%S %Y","%D":"%m/%d/%y","%F":"%Y-%m-%d","%h":"%b","%r":"%I:%M:%S %p","%R":"%H:%M","%T":"%H:%M:%S","%x":"%m/%d/%y","%X":"%H:%M:%S","%Ec":"%c","%EC":"%C","%Ex":"%m/%d/%y","%EX":"%H:%M:%S","%Ey":"%y","%EY":"%Y","%Od":"%d","%Oe":"%e","%OH":"%H","%OI":"%I","%Om":"%m","%OM":"%M","%OS":"%S","%Ou":"%u","%OU":"%U","%OV":"%V","%Ow":"%w","%OW":"%W","%Oy":"%y"};for(var rule in EXPANSION_RULES_1){pattern=pattern.replace(new RegExp(rule,"g"),EXPANSION_RULES_1[rule])}var WEEKDAYS=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"];var MONTHS=["January","February","March","April","May","June","July","August","September","October","November","December"];function leadingSomething(value,digits,character){var str=typeof value=="number"?value.toString():value||"";while(str.length<digits){str=character[0]+str}return str}function leadingNulls(value,digits){return leadingSomething(value,digits,"0")}function compareByDay(date1,date2){function sgn(value){return value<0?-1:value>0?1:0}var compare;if((compare=sgn(date1.getFullYear()-date2.getFullYear()))===0){if((compare=sgn(date1.getMonth()-date2.getMonth()))===0){compare=sgn(date1.getDate()-date2.getDate())}}return compare}function getFirstWeekStartDate(janFourth){switch(janFourth.getDay()){case 0:return new Date(janFourth.getFullYear()-1,11,29);case 1:return janFourth;case 2:return new Date(janFourth.getFullYear(),0,3);case 3:return new Date(janFourth.getFullYear(),0,2);case 4:return new Date(janFourth.getFullYear(),0,1);case 5:return new Date(janFourth.getFullYear()-1,11,31);case 6:return new Date(janFourth.getFullYear()-1,11,30)}}function getWeekBasedYear(date){var thisDate=addDays(new Date(date.tm_year+1900,0,1),date.tm_yday);var janFourthThisYear=new Date(thisDate.getFullYear(),0,4);var janFourthNextYear=new Date(thisDate.getFullYear()+1,0,4);var firstWeekStartThisYear=getFirstWeekStartDate(janFourthThisYear);var firstWeekStartNextYear=getFirstWeekStartDate(janFourthNextYear);if(compareByDay(firstWeekStartThisYear,thisDate)<=0){if(compareByDay(firstWeekStartNextYear,thisDate)<=0){return thisDate.getFullYear()+1}return thisDate.getFullYear()}return thisDate.getFullYear()-1}var EXPANSION_RULES_2={"%a":function(date){return WEEKDAYS[date.tm_wday].substring(0,3)},"%A":function(date){return WEEKDAYS[date.tm_wday]},"%b":function(date){return MONTHS[date.tm_mon].substring(0,3)},"%B":function(date){return MONTHS[date.tm_mon]},"%C":function(date){var year=date.tm_year+1900;return leadingNulls(year/100|0,2)},"%d":function(date){return leadingNulls(date.tm_mday,2)},"%e":function(date){return leadingSomething(date.tm_mday,2," ")},"%g":function(date){return getWeekBasedYear(date).toString().substring(2)},"%G":function(date){return getWeekBasedYear(date)},"%H":function(date){return leadingNulls(date.tm_hour,2)},"%I":function(date){var twelveHour=date.tm_hour;if(twelveHour==0)twelveHour=12;else if(twelveHour>12)twelveHour-=12;return leadingNulls(twelveHour,2)},"%j":function(date){return leadingNulls(date.tm_mday+arraySum(isLeapYear(date.tm_year+1900)?MONTH_DAYS_LEAP:MONTH_DAYS_REGULAR,date.tm_mon-1),3)},"%m":function(date){return leadingNulls(date.tm_mon+1,2)},"%M":function(date){return leadingNulls(date.tm_min,2)},"%n":function(){return"\n"},"%p":function(date){if(date.tm_hour>=0&&date.tm_hour<12){return"AM"}return"PM"},"%S":function(date){return leadingNulls(date.tm_sec,2)},"%t":function(){return"\t"},"%u":function(date){return date.tm_wday||7},"%U":function(date){var days=date.tm_yday+7-date.tm_wday;return leadingNulls(Math.floor(days/7),2)},"%V":function(date){var val=Math.floor((date.tm_yday+7-(date.tm_wday+6)%7)/7);if((date.tm_wday+371-date.tm_yday-2)%7<=2){val++}if(!val){val=52;var dec31=(date.tm_wday+7-date.tm_yday-1)%7;if(dec31==4||dec31==5&&isLeapYear(date.tm_year%400-1)){val++}}else if(val==53){var jan1=(date.tm_wday+371-date.tm_yday)%7;if(jan1!=4&&(jan1!=3||!isLeapYear(date.tm_year)))val=1}return leadingNulls(val,2)},"%w":function(date){return date.tm_wday},"%W":function(date){var days=date.tm_yday+7-(date.tm_wday+6)%7;return leadingNulls(Math.floor(days/7),2)},"%y":function(date){return(date.tm_year+1900).toString().substring(2)},"%Y":function(date){return date.tm_year+1900},"%z":function(date){var off=date.tm_gmtoff;var ahead=off>=0;off=Math.abs(off)/60;off=off/60*100+off%60;return(ahead?"+":"-")+String("0000"+off).slice(-4)},"%Z":function(date){return date.tm_zone},"%%":function(){return"%"}};pattern=pattern.replace(/%%/g,"\0\0");for(var rule in EXPANSION_RULES_2){if(pattern.includes(rule)){pattern=pattern.replace(new RegExp(rule,"g"),EXPANSION_RULES_2[rule](date))}}pattern=pattern.replace(/\0\0/g,"%");var bytes=intArrayFromString(pattern,false);if(bytes.length>maxsize){return 0}writeArrayToMemory(bytes,s);return bytes.length-1}_strftime.sig="ppppp";function getCFunc(ident){var func=Module["_"+ident];return func}function ccall(ident,returnType,argTypes,args,opts){var toC={"string":str=>{var ret=0;if(str!==null&&str!==undefined&&str!==0){ret=stringToUTF8OnStack(str)}return ret},"array":arr=>{var ret=stackAlloc(arr.length);writeArrayToMemory(arr,ret);return ret}};function convertReturnValue(ret){if(returnType==="string"){return UTF8ToString(ret)}if(returnType==="boolean")return Boolean(ret);return ret}var func=getCFunc(ident);var cArgs=[];var stack=0;if(args){for(var i=0;i<args.length;i++){var converter=toC[argTypes[i]];if(converter){if(stack===0)stack=stackSave();cArgs[i]=converter(args[i])}else{cArgs[i]=args[i]}}}var ret=func.apply(null,cArgs);function onDone(ret){if(stack!==0)stackRestore(stack);return convertReturnValue(ret)}ret=onDone(ret);return ret}function cwrap(ident,returnType,argTypes,opts){var numericArgs=!argTypes||argTypes.every(type=>type==="number"||type==="boolean");var numericRet=returnType!=="string";if(numericRet&&numericArgs&&!opts){return getCFunc(ident)}return function(){return ccall(ident,returnType,argTypes,arguments,opts)}}PThread.init();var FSNode=function(parent,name,mode,rdev){if(!parent){parent=this}this.parent=parent;this.mount=parent.mount;this.mounted=null;this.id=FS.nextInode++;this.name=name;this.mode=mode;this.node_ops={};this.stream_ops={};this.rdev=rdev};var readMode=292|73;var writeMode=146;Object.defineProperties(FSNode.prototype,{read:{get:function(){return(this.mode&readMode)===readMode},set:function(val){val?this.mode|=readMode:this.mode&=~readMode}},write:{get:function(){return(this.mode&writeMode)===writeMode},set:function(val){val?this.mode|=writeMode:this.mode&=~writeMode}},isFolder:{get:function(){return FS.isDir(this.mode)}},isDevice:{get:function(){return FS.isChrdev(this.mode)}}});FS.FSNode=FSNode;FS.createPreloadedFile=FS_createPreloadedFile;FS.staticInit();registerWasmPlugin();Module["requestFullscreen"]=function Module_requestFullscreen(lockPointer,resizeCanvas){Browser.requestFullscreen(lockPointer,resizeCanvas)};Module["requestAnimationFrame"]=function Module_requestAnimationFrame(func){Browser.requestAnimationFrame(func)};Module["setCanvasSize"]=function Module_setCanvasSize(width,height,noUpdates){Browser.setCanvasSize(width,height,noUpdates)};Module["pauseMainLoop"]=function Module_pauseMainLoop(){Browser.mainLoop.pause()};Module["resumeMainLoop"]=function Module_resumeMainLoop(){Browser.mainLoop.resume()};Module["getUserMedia"]=function Module_getUserMedia(){Browser.getUserMedia()};Module["createContext"]=function Module_createContext(canvas,useWebGL,setInModule,webGLContextAttributes){return Browser.createContext(canvas,useWebGL,setInModule,webGLContextAttributes)};var preloadedImages={};var preloadedAudios={};var GLctx;for(var i=0;i<32;++i)tempFixedLengthArray.push(new Array(i));var miniTempWebGLFloatBuffersStorage=new Float32Array(288);for(var i=0;i<288;++i){miniTempWebGLFloatBuffers[i]=miniTempWebGLFloatBuffersStorage.subarray(0,i+1)}var miniTempWebGLIntBuffersStorage=new Int32Array(288);for(var i=0;i<288;++i){miniTempWebGLIntBuffers[i]=miniTempWebGLIntBuffersStorage.subarray(0,i+1)}var proxiedFunctionTable=[null,_proc_exit,exitOnMainThread,pthreadCreateProxied,___syscall__newselect,___syscall_accept4,___syscall_bind,___syscall_chdir,___syscall_chmod,___syscall_dup,___syscall_dup3,___syscall_faccessat,___syscall_fcntl64,___syscall_fstat64,___syscall_ftruncate64,___syscall_getcwd,___syscall_getdents64,___syscall_ioctl,___syscall_listen,___syscall_lstat64,___syscall_mkdirat,___syscall_newfstatat,___syscall_openat,___syscall_pipe,___syscall_readlinkat,___syscall_recvfrom,___syscall_renameat,___syscall_rmdir,___syscall_sendto,___syscall_socket,___syscall_stat64,___syscall_symlink,___syscall_unlinkat,_eglBindAPI,_eglChooseConfig,_eglCreateContext,_eglCreateWindowSurface,_eglDestroyContext,_eglDestroySurface,_eglGetConfigAttrib,_eglGetDisplay,_eglGetError,_eglInitialize,_eglMakeCurrent,_eglQueryString,_eglSwapBuffers,_eglSwapInterval,_eglTerminate,_eglWaitClient,_eglWaitNative,_emscripten_exit_fullscreen,getCanvasSizeMainThread,setCanvasElementSizeMainThread,_emscripten_exit_pointerlock,_emscripten_force_exit,_emscripten_get_device_pixel_ratio,_emscripten_get_element_css_size,_emscripten_get_gamepad_status,_emscripten_get_num_gamepads,_emscripten_get_screen_size,_emscripten_request_fullscreen_strategy,_emscripten_request_pointerlock,_emscripten_sample_gamepad_data,_emscripten_set_beforeunload_callback_on_thread,_emscripten_set_blur_callback_on_thread,_emscripten_set_element_css_size,_emscripten_set_focus_callback_on_thread,_emscripten_set_fullscreenchange_callback_on_thread,_emscripten_set_gamepadconnected_callback_on_thread,_emscripten_set_gamepaddisconnected_callback_on_thread,_emscripten_set_keydown_callback_on_thread,_emscripten_set_keypress_callback_on_thread,_emscripten_set_keyup_callback_on_thread,_emscripten_set_mousedown_callback_on_thread,_emscripten_set_mouseenter_callback_on_thread,_emscripten_set_mouseleave_callback_on_thread,_emscripten_set_mousemove_callback_on_thread,_emscripten_set_mouseup_callback_on_thread,_emscripten_set_pointerlockchange_callback_on_thread,_emscripten_set_resize_callback_on_thread,_emscripten_set_touchcancel_callback_on_thread,_emscripten_set_touchend_callback_on_thread,_emscripten_set_touchmove_callback_on_thread,_emscripten_set_touchstart_callback_on_thread,_emscripten_set_visibilitychange_callback_on_thread,_emscripten_set_wheel_callback_on_thread,_emscripten_set_window_title,_environ_get,_environ_sizes_get,_fd_close,_fd_fdstat_get,_fd_pread,_fd_pwrite,_fd_read,_fd_seek,_fd_sync,_fd_write,_getaddrinfo];var wasmImports={"__call_sighandler":___call_sighandler,"__emscripten_init_main_thread_js":___emscripten_init_main_thread_js,"__emscripten_thread_cleanup":___emscripten_thread_cleanup,"__heap_base":___heap_base,"__indirect_function_table":wasmTable,"__memory_base":___memory_base,"__pthread_create_js":___pthread_create_js,"__stack_high":___stack_high,"__stack_low":___stack_low,"__stack_pointer":___stack_pointer,"__syscall__newselect":___syscall__newselect,"__syscall_accept4":___syscall_accept4,"__syscall_chdir":___syscall_chdir,"__syscall_chmod":___syscall_chmod,"__syscall_dup":___syscall_dup,"__syscall_faccessat":___syscall_faccessat,"__syscall_fcntl64":___syscall_fcntl64,"__syscall_fstat64":___syscall_fstat64,"__syscall_ftruncate64":___syscall_ftruncate64,"__syscall_getcwd":___syscall_getcwd,"__syscall_getdents64":___syscall_getdents64,"__syscall_ioctl":___syscall_ioctl,"__syscall_lstat64":___syscall_lstat64,"__syscall_mkdirat":___syscall_mkdirat,"__syscall_newfstatat":___syscall_newfstatat,"__syscall_openat":___syscall_openat,"__syscall_pipe":___syscall_pipe,"__syscall_readlinkat":___syscall_readlinkat,"__syscall_recvfrom":___syscall_recvfrom,"__syscall_renameat":___syscall_renameat,"__syscall_rmdir":___syscall_rmdir,"__syscall_sendto":___syscall_sendto,"__syscall_socket":___syscall_socket,"__syscall_stat64":___syscall_stat64,"__syscall_symlink":___syscall_symlink,"__syscall_unlinkat":___syscall_unlinkat,"__table_base":___table_base,"_dlopen_js":__dlopen_js,"_dlsym_catchup_js":__dlsym_catchup_js,"_dlsym_js":__dlsym_js,"_emscripten_dlopen_js":__emscripten_dlopen_js,"_emscripten_dlsync_threads":__emscripten_dlsync_threads,"_emscripten_dlsync_threads_async":__emscripten_dlsync_threads_async,"_emscripten_get_now_is_monotonic":__emscripten_get_now_is_monotonic,"_emscripten_notify_mailbox_postmessage":__emscripten_notify_mailbox_postmessage,"_emscripten_set_offscreencanvas_size":__emscripten_set_offscreencanvas_size,"_emscripten_thread_exit_joinable":__emscripten_thread_exit_joinable,"_emscripten_thread_mailbox_await":__emscripten_thread_mailbox_await,"_emscripten_thread_set_strongref":__emscripten_thread_set_strongref,"_emscripten_throw_longjmp":__emscripten_throw_longjmp,"_gmtime_js":__gmtime_js,"_localtime_js":__localtime_js,"_mktime_js":__mktime_js,"_tzset_js":__tzset_js,"abort":_abort,"eglBindAPI":_eglBindAPI,"eglChooseConfig":_eglChooseConfig,"eglCreateContext":_eglCreateContext,"eglCreateWindowSurface":_eglCreateWindowSurface,"eglDestroyContext":_eglDestroyContext,"eglDestroySurface":_eglDestroySurface,"eglGetConfigAttrib":_eglGetConfigAttrib,"eglGetDisplay":_eglGetDisplay,"eglGetError":_eglGetError,"eglInitialize":_eglInitialize,"eglMakeCurrent":_eglMakeCurrent,"eglQueryString":_eglQueryString,"eglSwapBuffers":_eglSwapBuffers,"eglSwapInterval":_eglSwapInterval,"eglTerminate":_eglTerminate,"eglWaitGL":_eglWaitGL,"eglWaitNative":_eglWaitNative,"emscripten_asm_const_int":_emscripten_asm_const_int,"emscripten_asm_const_int_sync_on_main_thread":_emscripten_asm_const_int_sync_on_main_thread,"emscripten_cancel_main_loop":_emscripten_cancel_main_loop,"emscripten_check_blocking_allowed":_emscripten_check_blocking_allowed,"emscripten_date_now":_emscripten_date_now,"emscripten_err":_emscripten_err,"emscripten_exit_fullscreen":_emscripten_exit_fullscreen,"emscripten_exit_pointerlock":_emscripten_exit_pointerlock,"emscripten_exit_with_live_runtime":_emscripten_exit_with_live_runtime,"emscripten_force_exit":_emscripten_force_exit,"emscripten_get_device_pixel_ratio":_emscripten_get_device_pixel_ratio,"emscripten_get_element_css_size":_emscripten_get_element_css_size,"emscripten_get_gamepad_status":_emscripten_get_gamepad_status,"emscripten_get_now":_emscripten_get_now,"emscripten_get_num_gamepads":_emscripten_get_num_gamepads,"emscripten_get_screen_size":_emscripten_get_screen_size,"emscripten_glActiveTexture":_emscripten_glActiveTexture,"emscripten_glAttachShader":_emscripten_glAttachShader,"emscripten_glBeginQueryEXT":_emscripten_glBeginQueryEXT,"emscripten_glBindAttribLocation":_emscripten_glBindAttribLocation,"emscripten_glBindBuffer":_emscripten_glBindBuffer,"emscripten_glBindFramebuffer":_emscripten_glBindFramebuffer,"emscripten_glBindRenderbuffer":_emscripten_glBindRenderbuffer,"emscripten_glBindTexture":_emscripten_glBindTexture,"emscripten_glBindVertexArrayOES":_emscripten_glBindVertexArrayOES,"emscripten_glBlendColor":_emscripten_glBlendColor,"emscripten_glBlendEquation":_emscripten_glBlendEquation,"emscripten_glBlendEquationSeparate":_emscripten_glBlendEquationSeparate,"emscripten_glBlendFunc":_emscripten_glBlendFunc,"emscripten_glBlendFuncSeparate":_emscripten_glBlendFuncSeparate,"emscripten_glBufferData":_emscripten_glBufferData,"emscripten_glBufferSubData":_emscripten_glBufferSubData,"emscripten_glCheckFramebufferStatus":_emscripten_glCheckFramebufferStatus,"emscripten_glClear":_emscripten_glClear,"emscripten_glClearColor":_emscripten_glClearColor,"emscripten_glClearDepthf":_emscripten_glClearDepthf,"emscripten_glClearStencil":_emscripten_glClearStencil,"emscripten_glColorMask":_emscripten_glColorMask,"emscripten_glCompileShader":_emscripten_glCompileShader,"emscripten_glCompressedTexImage2D":_emscripten_glCompressedTexImage2D,"emscripten_glCompressedTexSubImage2D":_emscripten_glCompressedTexSubImage2D,"emscripten_glCopyTexImage2D":_emscripten_glCopyTexImage2D,"emscripten_glCopyTexSubImage2D":_emscripten_glCopyTexSubImage2D,"emscripten_glCreateProgram":_emscripten_glCreateProgram,"emscripten_glCreateShader":_emscripten_glCreateShader,"emscripten_glCullFace":_emscripten_glCullFace,"emscripten_glDeleteBuffers":_emscripten_glDeleteBuffers,"emscripten_glDeleteFramebuffers":_emscripten_glDeleteFramebuffers,"emscripten_glDeleteProgram":_emscripten_glDeleteProgram,"emscripten_glDeleteQueriesEXT":_emscripten_glDeleteQueriesEXT,"emscripten_glDeleteRenderbuffers":_emscripten_glDeleteRenderbuffers,"emscripten_glDeleteShader":_emscripten_glDeleteShader,"emscripten_glDeleteTextures":_emscripten_glDeleteTextures,"emscripten_glDeleteVertexArraysOES":_emscripten_glDeleteVertexArraysOES,"emscripten_glDepthFunc":_emscripten_glDepthFunc,"emscripten_glDepthMask":_emscripten_glDepthMask,"emscripten_glDepthRangef":_emscripten_glDepthRangef,"emscripten_glDetachShader":_emscripten_glDetachShader,"emscripten_glDisable":_emscripten_glDisable,"emscripten_glDisableVertexAttribArray":_emscripten_glDisableVertexAttribArray,"emscripten_glDrawArrays":_emscripten_glDrawArrays,"emscripten_glDrawArraysInstancedANGLE":_emscripten_glDrawArraysInstancedANGLE,"emscripten_glDrawBuffersWEBGL":_emscripten_glDrawBuffersWEBGL,"emscripten_glDrawElements":_emscripten_glDrawElements,"emscripten_glDrawElementsInstancedANGLE":_emscripten_glDrawElementsInstancedANGLE,"emscripten_glEnable":_emscripten_glEnable,"emscripten_glEnableVertexAttribArray":_emscripten_glEnableVertexAttribArray,"emscripten_glEndQueryEXT":_emscripten_glEndQueryEXT,"emscripten_glFinish":_emscripten_glFinish,"emscripten_glFlush":_emscripten_glFlush,"emscripten_glFramebufferRenderbuffer":_emscripten_glFramebufferRenderbuffer,"emscripten_glFramebufferTexture2D":_emscripten_glFramebufferTexture2D,"emscripten_glFrontFace":_emscripten_glFrontFace,"emscripten_glGenBuffers":_emscripten_glGenBuffers,"emscripten_glGenFramebuffers":_emscripten_glGenFramebuffers,"emscripten_glGenQueriesEXT":_emscripten_glGenQueriesEXT,"emscripten_glGenRenderbuffers":_emscripten_glGenRenderbuffers,"emscripten_glGenTextures":_emscripten_glGenTextures,"emscripten_glGenVertexArraysOES":_emscripten_glGenVertexArraysOES,"emscripten_glGenerateMipmap":_emscripten_glGenerateMipmap,"emscripten_glGetActiveAttrib":_emscripten_glGetActiveAttrib,"emscripten_glGetActiveUniform":_emscripten_glGetActiveUniform,"emscripten_glGetAttachedShaders":_emscripten_glGetAttachedShaders,"emscripten_glGetAttribLocation":_emscripten_glGetAttribLocation,"emscripten_glGetBooleanv":_emscripten_glGetBooleanv,"emscripten_glGetBufferParameteriv":_emscripten_glGetBufferParameteriv,"emscripten_glGetError":_emscripten_glGetError,"emscripten_glGetFloatv":_emscripten_glGetFloatv,"emscripten_glGetFramebufferAttachmentParameteriv":_emscripten_glGetFramebufferAttachmentParameteriv,"emscripten_glGetIntegerv":_emscripten_glGetIntegerv,"emscripten_glGetProgramInfoLog":_emscripten_glGetProgramInfoLog,"emscripten_glGetProgramiv":_emscripten_glGetProgramiv,"emscripten_glGetQueryObjecti64vEXT":_emscripten_glGetQueryObjecti64vEXT,"emscripten_glGetQueryObjectivEXT":_emscripten_glGetQueryObjectivEXT,"emscripten_glGetQueryObjectui64vEXT":_emscripten_glGetQueryObjectui64vEXT,"emscripten_glGetQueryObjectuivEXT":_emscripten_glGetQueryObjectuivEXT,"emscripten_glGetQueryivEXT":_emscripten_glGetQueryivEXT,"emscripten_glGetRenderbufferParameteriv":_emscripten_glGetRenderbufferParameteriv,"emscripten_glGetShaderInfoLog":_emscripten_glGetShaderInfoLog,"emscripten_glGetShaderPrecisionFormat":_emscripten_glGetShaderPrecisionFormat,"emscripten_glGetShaderSource":_emscripten_glGetShaderSource,"emscripten_glGetShaderiv":_emscripten_glGetShaderiv,"emscripten_glGetString":_emscripten_glGetString,"emscripten_glGetTexParameterfv":_emscripten_glGetTexParameterfv,"emscripten_glGetTexParameteriv":_emscripten_glGetTexParameteriv,"emscripten_glGetUniformLocation":_emscripten_glGetUniformLocation,"emscripten_glGetUniformfv":_emscripten_glGetUniformfv,"emscripten_glGetUniformiv":_emscripten_glGetUniformiv,"emscripten_glGetVertexAttribPointerv":_emscripten_glGetVertexAttribPointerv,"emscripten_glGetVertexAttribfv":_emscripten_glGetVertexAttribfv,"emscripten_glGetVertexAttribiv":_emscripten_glGetVertexAttribiv,"emscripten_glHint":_emscripten_glHint,"emscripten_glIsBuffer":_emscripten_glIsBuffer,"emscripten_glIsEnabled":_emscripten_glIsEnabled,"emscripten_glIsFramebuffer":_emscripten_glIsFramebuffer,"emscripten_glIsProgram":_emscripten_glIsProgram,"emscripten_glIsQueryEXT":_emscripten_glIsQueryEXT,"emscripten_glIsRenderbuffer":_emscripten_glIsRenderbuffer,"emscripten_glIsShader":_emscripten_glIsShader,"emscripten_glIsTexture":_emscripten_glIsTexture,"emscripten_glIsVertexArrayOES":_emscripten_glIsVertexArrayOES,"emscripten_glLineWidth":_emscripten_glLineWidth,"emscripten_glLinkProgram":_emscripten_glLinkProgram,"emscripten_glPixelStorei":_emscripten_glPixelStorei,"emscripten_glPolygonOffset":_emscripten_glPolygonOffset,"emscripten_glQueryCounterEXT":_emscripten_glQueryCounterEXT,"emscripten_glReadPixels":_emscripten_glReadPixels,"emscripten_glReleaseShaderCompiler":_emscripten_glReleaseShaderCompiler,"emscripten_glRenderbufferStorage":_emscripten_glRenderbufferStorage,"emscripten_glSampleCoverage":_emscripten_glSampleCoverage,"emscripten_glScissor":_emscripten_glScissor,"emscripten_glShaderBinary":_emscripten_glShaderBinary,"emscripten_glShaderSource":_emscripten_glShaderSource,"emscripten_glStencilFunc":_emscripten_glStencilFunc,"emscripten_glStencilFuncSeparate":_emscripten_glStencilFuncSeparate,"emscripten_glStencilMask":_emscripten_glStencilMask,"emscripten_glStencilMaskSeparate":_emscripten_glStencilMaskSeparate,"emscripten_glStencilOp":_emscripten_glStencilOp,"emscripten_glStencilOpSeparate":_emscripten_glStencilOpSeparate,"emscripten_glTexImage2D":_emscripten_glTexImage2D,"emscripten_glTexParameterf":_emscripten_glTexParameterf,"emscripten_glTexParameterfv":_emscripten_glTexParameterfv,"emscripten_glTexParameteri":_emscripten_glTexParameteri,"emscripten_glTexParameteriv":_emscripten_glTexParameteriv,"emscripten_glTexSubImage2D":_emscripten_glTexSubImage2D,"emscripten_glUniform1f":_emscripten_glUniform1f,"emscripten_glUniform1fv":_emscripten_glUniform1fv,"emscripten_glUniform1i":_emscripten_glUniform1i,"emscripten_glUniform1iv":_emscripten_glUniform1iv,"emscripten_glUniform2f":_emscripten_glUniform2f,"emscripten_glUniform2fv":_emscripten_glUniform2fv,"emscripten_glUniform2i":_emscripten_glUniform2i,"emscripten_glUniform2iv":_emscripten_glUniform2iv,"emscripten_glUniform3f":_emscripten_glUniform3f,"emscripten_glUniform3fv":_emscripten_glUniform3fv,"emscripten_glUniform3i":_emscripten_glUniform3i,"emscripten_glUniform3iv":_emscripten_glUniform3iv,"emscripten_glUniform4f":_emscripten_glUniform4f,"emscripten_glUniform4fv":_emscripten_glUniform4fv,"emscripten_glUniform4i":_emscripten_glUniform4i,"emscripten_glUniform4iv":_emscripten_glUniform4iv,"emscripten_glUniformMatrix2fv":_emscripten_glUniformMatrix2fv,"emscripten_glUniformMatrix3fv":_emscripten_glUniformMatrix3fv,"emscripten_glUniformMatrix4fv":_emscripten_glUniformMatrix4fv,"emscripten_glUseProgram":_emscripten_glUseProgram,"emscripten_glValidateProgram":_emscripten_glValidateProgram,"emscripten_glVertexAttrib1f":_emscripten_glVertexAttrib1f,"emscripten_glVertexAttrib1fv":_emscripten_glVertexAttrib1fv,"emscripten_glVertexAttrib2f":_emscripten_glVertexAttrib2f,"emscripten_glVertexAttrib2fv":_emscripten_glVertexAttrib2fv,"emscripten_glVertexAttrib3f":_emscripten_glVertexAttrib3f,"emscripten_glVertexAttrib3fv":_emscripten_glVertexAttrib3fv,"emscripten_glVertexAttrib4f":_emscripten_glVertexAttrib4f,"emscripten_glVertexAttrib4fv":_emscripten_glVertexAttrib4fv,"emscripten_glVertexAttribDivisorANGLE":_emscripten_glVertexAttribDivisorANGLE,"emscripten_glVertexAttribPointer":_emscripten_glVertexAttribPointer,"emscripten_glViewport":_emscripten_glViewport,"emscripten_has_asyncify":_emscripten_has_asyncify,"emscripten_promise_destroy":_emscripten_promise_destroy,"emscripten_promise_resolve":_emscripten_promise_resolve,"emscripten_receive_on_main_thread_js":_emscripten_receive_on_main_thread_js,"emscripten_request_fullscreen_strategy":_emscripten_request_fullscreen_strategy,"emscripten_request_pointerlock":_emscripten_request_pointerlock,"emscripten_resize_heap":_emscripten_resize_heap,"emscripten_sample_gamepad_data":_emscripten_sample_gamepad_data,"emscripten_set_beforeunload_callback_on_thread":_emscripten_set_beforeunload_callback_on_thread,"emscripten_set_blur_callback_on_thread":_emscripten_set_blur_callback_on_thread,"emscripten_set_canvas_element_size":_emscripten_set_canvas_element_size,"emscripten_set_element_css_size":_emscripten_set_element_css_size,"emscripten_set_focus_callback_on_thread":_emscripten_set_focus_callback_on_thread,"emscripten_set_fullscreenchange_callback_on_thread":_emscripten_set_fullscreenchange_callback_on_thread,"emscripten_set_gamepadconnected_callback_on_thread":_emscripten_set_gamepadconnected_callback_on_thread,"emscripten_set_gamepaddisconnected_callback_on_thread":_emscripten_set_gamepaddisconnected_callback_on_thread,"emscripten_set_keydown_callback_on_thread":_emscripten_set_keydown_callback_on_thread,"emscripten_set_keypress_callback_on_thread":_emscripten_set_keypress_callback_on_thread,"emscripten_set_keyup_callback_on_thread":_emscripten_set_keyup_callback_on_thread,"emscripten_set_main_loop":_emscripten_set_main_loop,"emscripten_set_mousedown_callback_on_thread":_emscripten_set_mousedown_callback_on_thread,"emscripten_set_mouseenter_callback_on_thread":_emscripten_set_mouseenter_callback_on_thread,"emscripten_set_mouseleave_callback_on_thread":_emscripten_set_mouseleave_callback_on_thread,"emscripten_set_mousemove_callback_on_thread":_emscripten_set_mousemove_callback_on_thread,"emscripten_set_mouseup_callback_on_thread":_emscripten_set_mouseup_callback_on_thread,"emscripten_set_pointerlockchange_callback_on_thread":_emscripten_set_pointerlockchange_callback_on_thread,"emscripten_set_resize_callback_on_thread":_emscripten_set_resize_callback_on_thread,"emscripten_set_touchcancel_callback_on_thread":_emscripten_set_touchcancel_callback_on_thread,"emscripten_set_touchend_callback_on_thread":_emscripten_set_touchend_callback_on_thread,"emscripten_set_touchmove_callback_on_thread":_emscripten_set_touchmove_callback_on_thread,"emscripten_set_touchstart_callback_on_thread":_emscripten_set_touchstart_callback_on_thread,"emscripten_set_visibilitychange_callback_on_thread":_emscripten_set_visibilitychange_callback_on_thread,"emscripten_set_wheel_callback_on_thread":_emscripten_set_wheel_callback_on_thread,"emscripten_set_window_title":_emscripten_set_window_title,"emscripten_sleep":_emscripten_sleep,"environ_get":_environ_get,"environ_sizes_get":_environ_sizes_get,"exit":_exit,"fd_close":_fd_close,"fd_fdstat_get":_fd_fdstat_get,"fd_pread":_fd_pread,"fd_pwrite":_fd_pwrite,"fd_read":_fd_read,"fd_seek":_fd_seek,"fd_sync":_fd_sync,"fd_write":_fd_write,"getaddrinfo":_getaddrinfo,"getnameinfo":_getnameinfo,"invoke_dii":invoke_dii,"invoke_ii":invoke_ii,"invoke_iii":invoke_iii,"invoke_iiii":invoke_iiii,"invoke_iiiii":invoke_iiiii,"invoke_iiiiii":invoke_iiiiii,"invoke_iiiij":invoke_iiiij,"invoke_iiiijii":invoke_iiiijii,"invoke_iiiji":invoke_iiiji,"invoke_iij":invoke_iij,"invoke_iiji":invoke_iiji,"invoke_vi":invoke_vi,"invoke_vii":invoke_vii,"invoke_viii":invoke_viii,"invoke_viiii":invoke_viiii,"invoke_viiji":invoke_viiji,"invoke_vij":invoke_vij,"memory":wasmMemory,"strftime":_strftime};var asm=createWasm();var ___wasm_call_ctors=function(){return(___wasm_call_ctors=Module["asm"]["__wasm_call_ctors"]).apply(null,arguments)};var ___wasm_apply_data_relocs=Module["___wasm_apply_data_relocs"]=function(){return(___wasm_apply_data_relocs=Module["___wasm_apply_data_relocs"]=Module["asm"]["__wasm_apply_data_relocs"]).apply(null,arguments)};var _free=function(){return(_free=Module["asm"]["free"]).apply(null,arguments)};var _drb_trick_clang_for_the_win=Module["_drb_trick_clang_for_the_win"]=function(){return(_drb_trick_clang_for_the_win=Module["_drb_trick_clang_for_the_win"]=Module["asm"]["drb_trick_clang_for_the_win"]).apply(null,arguments)};var _malloc=function(){return(_malloc=Module["asm"]["malloc"]).apply(null,arguments)};var ___errno_location=function(){return(___errno_location=Module["asm"]["__errno_location"]).apply(null,arguments)};var _main=Module["_main"]=function(){return(_main=Module["_main"]=Module["asm"]["__main_argc_argv"]).apply(null,arguments)};var _ffi_js_send=Module["_ffi_js_send"]=function(){return(_ffi_js_send=Module["_ffi_js_send"]=Module["asm"]["ffi_js_send"]).apply(null,arguments)};var _ffi_js_free_string=Module["_ffi_js_free_string"]=function(){return(_ffi_js_free_string=Module["_ffi_js_free_string"]=Module["asm"]["ffi_js_free_string"]).apply(null,arguments)};var _http_update_callback_initial_from_javascript=Module["_http_update_callback_initial_from_javascript"]=function(){return(_http_update_callback_initial_from_javascript=Module["_http_update_callback_initial_from_javascript"]=Module["asm"]["http_update_callback_initial_from_javascript"]).apply(null,arguments)};var _http_mark_callback_complete_from_javascript=Module["_http_mark_callback_complete_from_javascript"]=function(){return(_http_mark_callback_complete_from_javascript=Module["_http_mark_callback_complete_from_javascript"]=Module["asm"]["http_mark_callback_complete_from_javascript"]).apply(null,arguments)};var _http_new_header_from_javascript=Module["_http_new_header_from_javascript"]=function(){return(_http_new_header_from_javascript=Module["_http_new_header_from_javascript"]=Module["asm"]["http_new_header_from_javascript"]).apply(null,arguments)};var _http_new_data_from_javascript=Module["_http_new_data_from_javascript"]=function(){return(_http_new_data_from_javascript=Module["_http_new_data_from_javascript"]=Module["asm"]["http_new_data_from_javascript"]).apply(null,arguments)};var _pthread_self=Module["_pthread_self"]=function(){return(_pthread_self=Module["_pthread_self"]=Module["asm"]["pthread_self"]).apply(null,arguments)};var __emscripten_tls_init=Module["__emscripten_tls_init"]=function(){return(__emscripten_tls_init=Module["__emscripten_tls_init"]=Module["asm"]["_emscripten_tls_init"]).apply(null,arguments)};var ___dl_seterr=function(){return(___dl_seterr=Module["asm"]["__dl_seterr"]).apply(null,arguments)};var __emscripten_dlsync_self_async=function(){return(__emscripten_dlsync_self_async=Module["asm"]["_emscripten_dlsync_self_async"]).apply(null,arguments)};var __emscripten_dlsync_self=function(){return(__emscripten_dlsync_self=Module["asm"]["_emscripten_dlsync_self"]).apply(null,arguments)};var __emscripten_proxy_dlsync_async=function(){return(__emscripten_proxy_dlsync_async=Module["asm"]["_emscripten_proxy_dlsync_async"]).apply(null,arguments)};var __emscripten_proxy_dlsync=function(){return(__emscripten_proxy_dlsync=Module["asm"]["_emscripten_proxy_dlsync"]).apply(null,arguments)};var _emscripten_main_runtime_thread_id=function(){return(_emscripten_main_runtime_thread_id=Module["asm"]["emscripten_main_runtime_thread_id"]).apply(null,arguments)};var __emscripten_thread_init=Module["__emscripten_thread_init"]=function(){return(__emscripten_thread_init=Module["__emscripten_thread_init"]=Module["asm"]["_emscripten_thread_init"]).apply(null,arguments)};var __emscripten_thread_crashed=Module["__emscripten_thread_crashed"]=function(){return(__emscripten_thread_crashed=Module["__emscripten_thread_crashed"]=Module["asm"]["_emscripten_thread_crashed"]).apply(null,arguments)};var _emscripten_main_thread_process_queued_calls=function(){return(_emscripten_main_thread_process_queued_calls=Module["asm"]["emscripten_main_thread_process_queued_calls"]).apply(null,arguments)};var _htonl=function(){return(_htonl=Module["asm"]["htonl"]).apply(null,arguments)};var _htons=function(){return(_htons=Module["asm"]["htons"]).apply(null,arguments)};var __emscripten_run_in_main_runtime_thread_js=function(){return(__emscripten_run_in_main_runtime_thread_js=Module["asm"]["_emscripten_run_in_main_runtime_thread_js"]).apply(null,arguments)};var _emscripten_dispatch_to_thread_=function(){return(_emscripten_dispatch_to_thread_=Module["asm"]["emscripten_dispatch_to_thread_"]).apply(null,arguments)};var _ntohs=function(){return(_ntohs=Module["asm"]["ntohs"]).apply(null,arguments)};var __emscripten_thread_free_data=function(){return(__emscripten_thread_free_data=Module["asm"]["_emscripten_thread_free_data"]).apply(null,arguments)};var __emscripten_thread_exit=Module["__emscripten_thread_exit"]=function(){return(__emscripten_thread_exit=Module["__emscripten_thread_exit"]=Module["asm"]["_emscripten_thread_exit"]).apply(null,arguments)};var __emscripten_check_mailbox=Module["__emscripten_check_mailbox"]=function(){return(__emscripten_check_mailbox=Module["__emscripten_check_mailbox"]=Module["asm"]["_emscripten_check_mailbox"]).apply(null,arguments)};var _setThrew=function(){return(_setThrew=Module["asm"]["setThrew"]).apply(null,arguments)};var _emscripten_stack_set_limits=function(){return(_emscripten_stack_set_limits=Module["asm"]["emscripten_stack_set_limits"]).apply(null,arguments)};var stackSave=function(){return(stackSave=Module["asm"]["stackSave"]).apply(null,arguments)};var stackRestore=function(){return(stackRestore=Module["asm"]["stackRestore"]).apply(null,arguments)};var stackAlloc=function(){return(stackAlloc=Module["asm"]["stackAlloc"]).apply(null,arguments)};var dynCall_jiji=Module["dynCall_jiji"]=function(){return(dynCall_jiji=Module["dynCall_jiji"]=Module["asm"]["dynCall_jiji"]).apply(null,arguments)};var dynCall_ji=Module["dynCall_ji"]=function(){return(dynCall_ji=Module["dynCall_ji"]=Module["asm"]["dynCall_ji"]).apply(null,arguments)};var dynCall_jiij=Module["dynCall_jiij"]=function(){return(dynCall_jiij=Module["dynCall_jiij"]=Module["asm"]["dynCall_jiij"]).apply(null,arguments)};var dynCall_iij=Module["dynCall_iij"]=function(){return(dynCall_iij=Module["dynCall_iij"]=Module["asm"]["dynCall_iij"]).apply(null,arguments)};var dynCall_ijii=Module["dynCall_ijii"]=function(){return(dynCall_ijii=Module["dynCall_ijii"]=Module["asm"]["dynCall_ijii"]).apply(null,arguments)};var dynCall_jiii=Module["dynCall_jiii"]=function(){return(dynCall_jiii=Module["dynCall_jiii"]=Module["asm"]["dynCall_jiii"]).apply(null,arguments)};var dynCall_iiijiii=Module["dynCall_iiijiii"]=function(){return(dynCall_iiijiii=Module["dynCall_iiijiii"]=Module["asm"]["dynCall_iiijiii"]).apply(null,arguments)};var dynCall_iiiji=Module["dynCall_iiiji"]=function(){return(dynCall_iiiji=Module["dynCall_iiiji"]=Module["asm"]["dynCall_iiiji"]).apply(null,arguments)};var dynCall_iiiij=Module["dynCall_iiiij"]=function(){return(dynCall_iiiij=Module["dynCall_iiiij"]=Module["asm"]["dynCall_iiiij"]).apply(null,arguments)};var dynCall_jii=Module["dynCall_jii"]=function(){return(dynCall_jii=Module["dynCall_jii"]=Module["asm"]["dynCall_jii"]).apply(null,arguments)};var dynCall_iiijj=Module["dynCall_iiijj"]=function(){return(dynCall_iiijj=Module["dynCall_iiijj"]=Module["asm"]["dynCall_iiijj"]).apply(null,arguments)};var dynCall_iiij=Module["dynCall_iiij"]=function(){return(dynCall_iiij=Module["dynCall_iiij"]=Module["asm"]["dynCall_iiij"]).apply(null,arguments)};var dynCall_jiiijj=Module["dynCall_jiiijj"]=function(){return(dynCall_jiiijj=Module["dynCall_jiiijj"]=Module["asm"]["dynCall_jiiijj"]).apply(null,arguments)};var dynCall_vij=Module["dynCall_vij"]=function(){return(dynCall_vij=Module["dynCall_vij"]=Module["asm"]["dynCall_vij"]).apply(null,arguments)};var dynCall_iiiiiiji=Module["dynCall_iiiiiiji"]=function(){return(dynCall_iiiiiiji=Module["dynCall_iiiiiiji"]=Module["asm"]["dynCall_iiiiiiji"]).apply(null,arguments)};var dynCall_iiiiiji=Module["dynCall_iiiiiji"]=function(){return(dynCall_iiiiiji=Module["dynCall_iiiiiji"]=Module["asm"]["dynCall_iiiiiji"]).apply(null,arguments)};var dynCall_iiji=Module["dynCall_iiji"]=function(){return(dynCall_iiji=Module["dynCall_iiji"]=Module["asm"]["dynCall_iiji"]).apply(null,arguments)};var dynCall_iiiijii=Module["dynCall_iiiijii"]=function(){return(dynCall_iiiijii=Module["dynCall_iiiijii"]=Module["asm"]["dynCall_iiiijii"]).apply(null,arguments)};var dynCall_iiiiji=Module["dynCall_iiiiji"]=function(){return(dynCall_iiiiji=Module["dynCall_iiiiji"]=Module["asm"]["dynCall_iiiiji"]).apply(null,arguments)};var dynCall_iiijji=Module["dynCall_iiijji"]=function(){return(dynCall_iiijji=Module["dynCall_iiijji"]=Module["asm"]["dynCall_iiijji"]).apply(null,arguments)};var dynCall_viiji=Module["dynCall_viiji"]=function(){return(dynCall_viiji=Module["dynCall_viiji"]=Module["asm"]["dynCall_viiji"]).apply(null,arguments)};var dynCall_vijii=Module["dynCall_vijii"]=function(){return(dynCall_vijii=Module["dynCall_vijii"]=Module["asm"]["dynCall_vijii"]).apply(null,arguments)};var dynCall_ij=Module["dynCall_ij"]=function(){return(dynCall_ij=Module["dynCall_ij"]=Module["asm"]["dynCall_ij"]).apply(null,arguments)};function invoke_iiiii(index,a1,a2,a3,a4){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_vi(index,a1){var sp=stackSave();try{getWasmTableEntry(index)(a1)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iii(index,a1,a2){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiii(index,a1,a2,a3,a4){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iiii(index,a1,a2,a3){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_vii(index,a1,a2){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viii(index,a1,a2,a3){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_ii(index,a1){var sp=stackSave();try{return getWasmTableEntry(index)(a1)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iiiiii(index,a1,a2,a3,a4,a5){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_dii(index,a1,a2){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iiji(index,a1,a2,a3,a4){var sp=stackSave();try{return Module["dynCall_iiji"](index,a1,a2,a3,a4)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_vij(index,a1,a2,a3){var sp=stackSave();try{Module["dynCall_vij"](index,a1,a2,a3)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iiiij(index,a1,a2,a3,a4,a5){var sp=stackSave();try{return Module["dynCall_iiiij"](index,a1,a2,a3,a4,a5)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iiiijii(index,a1,a2,a3,a4,a5,a6,a7){var sp=stackSave();try{return Module["dynCall_iiiijii"](index,a1,a2,a3,a4,a5,a6,a7)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iiiji(index,a1,a2,a3,a4,a5){var sp=stackSave();try{return Module["dynCall_iiiji"](index,a1,a2,a3,a4,a5)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iij(index,a1,a2,a3){var sp=stackSave();try{return Module["dynCall_iij"](index,a1,a2,a3)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiji(index,a1,a2,a3,a4,a5){var sp=stackSave();try{Module["dynCall_viiji"](index,a1,a2,a3,a4,a5)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}Module["addRunDependency"]=addRunDependency;Module["removeRunDependency"]=removeRunDependency;Module["keepRuntimeAlive"]=keepRuntimeAlive;Module["wasmMemory"]=wasmMemory;Module["ccall"]=ccall;Module["cwrap"]=cwrap;Module["ExitStatus"]=ExitStatus;var calledRun;dependenciesFulfilled=function runCaller(){if(!calledRun)run();if(!calledRun)dependenciesFulfilled=runCaller};function callMain(args=[]){var entryFunction=resolveGlobalSymbol("main").sym;if(!entryFunction)return;args.unshift(thisProgram);var argc=args.length;var argv=stackAlloc((argc+1)*4);var argv_ptr=argv>>2;args.forEach(arg=>{GROWABLE_HEAP_I32()[argv_ptr++]=stringToUTF8OnStack(arg)});GROWABLE_HEAP_I32()[argv_ptr]=0;try{var ret=entryFunction(argc,argv);exitJS(ret,true);return ret}catch(e){return handleException(e)}}var sharedModules=Module["sharedModules"]||[];function run(args=arguments_){if(runDependencies>0){return}if(ENVIRONMENT_IS_PTHREAD){initRuntime();startWorker(Module);return}preRun();if(runDependencies>0){return}function doRun(){if(calledRun)return;calledRun=true;Module["calledRun"]=true;if(ABORT)return;initRuntime();preMain();if(Module["onRuntimeInitialized"])Module["onRuntimeInitialized"]();if(shouldRunNow)callMain(args);postRun()}if(Module["setStatus"]){Module["setStatus"]("Running...");setTimeout(function(){setTimeout(function(){Module["setStatus"]("")},1);doRun()},1)}else{doRun()}}if(Module["preInit"]){if(typeof Module["preInit"]=="function")Module["preInit"]=[Module["preInit"]];while(Module["preInit"].length>0){Module["preInit"].pop()()}}var shouldRunNow=true;if(Module["noInitialRun"])shouldRunNow=false;run();
