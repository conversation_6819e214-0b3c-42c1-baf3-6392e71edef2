replay_version 2.0
stopped_at 2166
seed 100
recorded_at Sat Jul 17 09:33:34 2021
[:mouse_button_up, 1, 0, 1, 1, 5]
[:mouse_move, 805, 89, 2, 2, 19]
[:mouse_move, 805, 91, 2, 3, 20]
[:mouse_move, 803, 96, 2, 4, 21]
[:mouse_move, 802, 99, 2, 5, 22]
[:mouse_move, 799, 103, 2, 6, 23]
[:mouse_move, 797, 107, 2, 7, 24]
[:mouse_move, 786, 124, 2, 8, 25]
[:mouse_move, 772, 141, 2, 9, 26]
[:mouse_move, 716, 207, 2, 10, 27]
[:mouse_move, 638, 289, 2, 11, 29]
[:mouse_move, 608, 314, 2, 12, 29]
[:mouse_move, 562, 342, 2, 13, 31]
[:mouse_move, 541, 350, 2, 14, 31]
[:mouse_move, 520, 356, 2, 15, 32]
[:mouse_move, 459, 360, 2, 16, 34]
[:mouse_move, 411, 357, 2, 17, 34]
[:mouse_move, 319, 340, 2, 18, 35]
[:mouse_move, 287, 330, 2, 19, 37]
[:mouse_move, 248, 308, 2, 20, 37]
[:mouse_move, 233, 298, 2, 21, 38]
[:mouse_move, 211, 283, 2, 22, 39]
[:mouse_move, 204, 278, 2, 23, 40]
[:mouse_move, 195, 272, 2, 24, 42]
[:mouse_move, 192, 269, 2, 25, 42]
[:mouse_move, 187, 266, 2, 26, 44]
[:mouse_move, 184, 263, 2, 27, 44]
[:mouse_move, 181, 261, 2, 28, 45]
[:mouse_move, 174, 255, 2, 29, 46]
[:mouse_move, 170, 252, 2, 30, 47]
[:mouse_move, 155, 244, 2, 31, 48]
[:mouse_move, 122, 232, 2, 32, 50]
[:mouse_move, 107, 227, 2, 33, 50]
[:mouse_move, 88, 224, 2, 34, 52]
[:mouse_move, 80, 223, 2, 35, 52]
[:mouse_move, 73, 223, 2, 36, 53]
[:mouse_move, 57, 224, 2, 37, 54]
[:mouse_move, 54, 224, 2, 38, 55]
[:mouse_move, 51, 224, 2, 39, 56]
[:mouse_move, 51, 223, 2, 40, 60]
[:mouse_move, 52, 221, 2, 41, 62]
[:mouse_move, 54, 219, 2, 42, 64]
[:mouse_move, 54, 218, 2, 43, 64]
[:mouse_move, 54, 219, 2, 44, 73]
[:mouse_move, 55, 221, 2, 45, 75]
[:mouse_move, 55, 224, 2, 46, 75]
[:mouse_move, 55, 230, 2, 47, 77]
[:mouse_move, 55, 232, 2, 48, 77]
[:mouse_move, 55, 238, 2, 49, 79]
[:mouse_move, 55, 243, 2, 50, 79]
[:mouse_move, 57, 254, 2, 51, 81]
[:mouse_move, 58, 256, 2, 52, 81]
[:mouse_button_pressed, 1, 0, 1, 53, 91]
[:mouse_move, 60, 256, 2, 54, 95]
[:mouse_move, 63, 256, 2, 55, 97]
[:mouse_move, 73, 256, 2, 56, 97]
[:mouse_move, 83, 256, 2, 57, 99]
[:mouse_move, 111, 257, 2, 58, 99]
[:mouse_move, 128, 257, 2, 59, 101]
[:mouse_move, 163, 257, 2, 60, 101]
[:mouse_move, 179, 257, 2, 61, 103]
[:mouse_move, 203, 257, 2, 62, 103]
[:mouse_move, 214, 257, 2, 63, 105]
[:mouse_move, 236, 257, 2, 64, 105]
[:mouse_move, 247, 257, 2, 65, 107]
[:mouse_move, 267, 257, 2, 66, 107]
[:mouse_move, 276, 257, 2, 67, 109]
[:mouse_move, 288, 257, 2, 68, 109]
[:mouse_move, 293, 257, 2, 69, 111]
[:mouse_move, 302, 257, 2, 70, 111]
[:mouse_move, 308, 257, 2, 71, 113]
[:mouse_move, 322, 257, 2, 72, 113]
[:mouse_move, 330, 257, 2, 73, 115]
[:mouse_move, 334, 257, 2, 74, 115]
[:mouse_move, 340, 257, 2, 75, 117]
[:mouse_move, 342, 257, 2, 76, 117]
[:mouse_move, 348, 257, 2, 77, 119]
[:mouse_move, 350, 257, 2, 78, 119]
[:mouse_move, 354, 257, 2, 79, 121]
[:mouse_button_up, 1, 0, 1, 80, 135]
[:mouse_move, 351, 257, 2, 81, 145]
[:mouse_move, 345, 257, 2, 82, 145]
[:mouse_move, 339, 258, 2, 83, 147]
[:mouse_move, 314, 261, 2, 84, 147]
[:mouse_move, 300, 262, 2, 85, 149]
[:mouse_move, 278, 264, 2, 86, 149]
[:mouse_move, 264, 265, 2, 87, 151]
[:mouse_move, 231, 265, 2, 88, 151]
[:mouse_move, 215, 264, 2, 89, 153]
[:mouse_move, 194, 262, 2, 90, 153]
[:mouse_move, 190, 261, 2, 91, 155]
[:mouse_move, 186, 260, 2, 92, 155]
[:mouse_move, 185, 260, 2, 93, 157]
[:mouse_move, 184, 259, 2, 94, 157]
[:mouse_move, 183, 259, 2, 95, 159]
[:mouse_move, 179, 259, 2, 96, 161]
[:mouse_move, 172, 259, 2, 97, 163]
[:mouse_move, 153, 259, 2, 98, 163]
[:mouse_move, 142, 259, 2, 99, 165]
[:mouse_move, 129, 258, 2, 100, 165]
[:mouse_move, 127, 257, 2, 101, 167]
[:mouse_move, 126, 257, 2, 102, 167]
[:mouse_move, 125, 257, 2, 103, 169]
[:mouse_move, 126, 257, 2, 104, 175]
[:mouse_move, 127, 257, 2, 105, 175]
[:mouse_move, 128, 257, 2, 106, 177]
[:mouse_move, 130, 257, 2, 107, 179]
[:mouse_move, 131, 257, 2, 108, 181]
[:mouse_move, 132, 256, 2, 109, 183]
[:mouse_move, 133, 256, 2, 110, 185]
[:mouse_move, 134, 256, 2, 111, 187]
[:mouse_move, 135, 256, 2, 112, 187]
[:mouse_move, 136, 256, 2, 113, 189]
[:mouse_move, 137, 256, 2, 114, 191]
[:mouse_move, 138, 256, 2, 115, 193]
[:mouse_button_pressed, 1, 0, 1, 116, 199]
[:mouse_move, 138, 255, 2, 117, 201]
[:mouse_button_up, 1, 0, 1, 118, 206]
[:mouse_move, 138, 256, 2, 119, 211]
[:mouse_move, 138, 257, 2, 120, 215]
[:mouse_move, 142, 267, 2, 121, 216]
[:mouse_move, 146, 274, 2, 122, 217]
[:mouse_move, 158, 291, 2, 123, 218]
[:mouse_move, 172, 304, 2, 124, 219]
[:mouse_move, 246, 354, 2, 125, 220]
[:mouse_move, 292, 380, 2, 126, 221]
[:mouse_move, 382, 424, 2, 127, 222]
[:mouse_move, 403, 436, 2, 128, 225]
[:mouse_move, 408, 437, 2, 129, 282]
[:mouse_move, 409, 437, 2, 130, 283]
[:mouse_move, 408, 438, 2, 131, 293]
[:mouse_move, 393, 438, 2, 132, 295]
[:mouse_move, 373, 435, 2, 133, 296]
[:mouse_move, 305, 421, 2, 134, 297]
[:mouse_move, 265, 410, 2, 135, 298]
[:mouse_move, 191, 391, 2, 136, 299]
[:mouse_move, 170, 386, 2, 137, 300]
[:mouse_move, 154, 380, 2, 138, 301]
[:mouse_move, 153, 381, 2, 139, 305]
[:mouse_move, 152, 381, 2, 140, 313]
[:mouse_move, 150, 381, 2, 141, 314]
[:mouse_move, 148, 379, 2, 142, 315]
[:mouse_move, 137, 372, 2, 143, 316]
[:mouse_move, 128, 366, 2, 144, 317]
[:mouse_move, 112, 359, 2, 145, 318]
[:mouse_move, 111, 359, 2, 146, 319]
[:mouse_move, 111, 358, 2, 147, 320]
[:mouse_move, 110, 358, 2, 148, 322]
[:mouse_move, 107, 357, 2, 149, 324]
[:mouse_move, 107, 356, 2, 150, 325]
[:mouse_move, 105, 356, 2, 151, 326]
[:mouse_move, 104, 356, 2, 152, 327]
[:mouse_move, 101, 354, 2, 153, 328]
[:mouse_move, 99, 352, 2, 154, 329]
[:mouse_move, 95, 350, 2, 155, 330]
[:mouse_move, 92, 348, 2, 156, 331]
[:mouse_move, 82, 345, 2, 157, 332]
[:mouse_move, 76, 343, 2, 158, 333]
[:mouse_move, 74, 342, 2, 159, 334]
[:mouse_move, 73, 342, 2, 160, 336]
[:mouse_move, 72, 342, 2, 161, 337]
[:mouse_move, 71, 343, 2, 162, 338]
[:mouse_move, 70, 343, 2, 163, 341]
[:mouse_move, 69, 343, 2, 164, 342]
[:mouse_move, 66, 344, 2, 165, 343]
[:mouse_move, 63, 345, 2, 166, 344]
[:mouse_move, 55, 348, 2, 167, 345]
[:mouse_move, 52, 348, 2, 168, 346]
[:mouse_move, 48, 350, 2, 169, 347]
[:mouse_move, 47, 350, 2, 170, 348]
[:mouse_move, 46, 350, 2, 171, 350]
[:mouse_move, 46, 349, 2, 172, 351]
[:mouse_move, 46, 348, 2, 173, 357]
[:mouse_move, 47, 348, 2, 174, 359]
[:mouse_move, 47, 347, 2, 175, 360]
[:mouse_move, 47, 346, 2, 176, 362]
[:mouse_move, 48, 345, 2, 177, 366]
[:mouse_button_pressed, 1, 0, 1, 178, 370]
[:mouse_move, 49, 346, 2, 179, 376]
[:mouse_move, 53, 346, 2, 180, 377]
[:mouse_move, 64, 348, 2, 181, 378]
[:mouse_move, 71, 348, 2, 182, 379]
[:mouse_move, 85, 350, 2, 183, 380]
[:mouse_move, 95, 351, 2, 184, 381]
[:mouse_move, 116, 351, 2, 185, 382]
[:mouse_move, 128, 351, 2, 186, 383]
[:mouse_move, 153, 351, 2, 187, 384]
[:mouse_move, 166, 352, 2, 188, 385]
[:mouse_move, 189, 354, 2, 189, 386]
[:mouse_move, 198, 354, 2, 190, 387]
[:mouse_move, 217, 354, 2, 191, 388]
[:mouse_move, 226, 354, 2, 192, 389]
[:mouse_move, 249, 355, 2, 193, 390]
[:mouse_move, 261, 355, 2, 194, 391]
[:mouse_move, 282, 355, 2, 195, 392]
[:mouse_move, 291, 355, 2, 196, 393]
[:mouse_move, 299, 354, 2, 197, 394]
[:mouse_move, 309, 353, 2, 198, 395]
[:mouse_move, 310, 353, 2, 199, 396]
[:mouse_move, 311, 353, 2, 200, 398]
[:mouse_move, 317, 352, 2, 201, 399]
[:mouse_move, 320, 352, 2, 202, 400]
[:mouse_move, 327, 352, 2, 203, 401]
[:mouse_move, 330, 352, 2, 204, 402]
[:mouse_move, 333, 351, 2, 205, 403]
[:mouse_move, 335, 351, 2, 206, 404]
[:mouse_move, 337, 351, 2, 207, 405]
[:mouse_move, 338, 351, 2, 208, 412]
[:mouse_move, 339, 351, 2, 209, 417]
[:mouse_move, 340, 351, 2, 210, 420]
[:mouse_move, 341, 351, 2, 211, 421]
[:mouse_move, 343, 351, 2, 212, 422]
[:mouse_move, 349, 351, 2, 213, 424]
[:mouse_move, 351, 351, 2, 214, 425]
[:mouse_move, 353, 351, 2, 215, 426]
[:mouse_move, 354, 351, 2, 216, 427]
[:mouse_move, 355, 351, 2, 217, 428]
[:mouse_move, 356, 351, 2, 218, 428]
[:mouse_move, 357, 351, 2, 219, 429]
[:mouse_move, 358, 351, 2, 220, 430]
[:mouse_move, 359, 351, 2, 221, 431]
[:mouse_move, 360, 351, 2, 222, 432]
[:mouse_move, 361, 351, 2, 223, 433]
[:mouse_move, 362, 351, 2, 224, 434]
[:mouse_move, 365, 351, 2, 225, 435]
[:mouse_move, 366, 351, 2, 226, 436]
[:mouse_move, 367, 351, 2, 227, 439]
[:mouse_move, 368, 351, 2, 228, 446]
[:mouse_move, 369, 351, 2, 229, 456]
[:mouse_move, 370, 351, 2, 230, 460]
[:mouse_button_up, 1, 0, 1, 231, 471]
[:key_down_raw, 1073741905, 0, 2, 232, 514]
[:key_down_raw, 1073741905, 0, 2, 233, 528]
[:key_down_raw, 32, 0, 2, 234, 529]
[:key_up_raw, 32, 0, 2, 235, 535]
[:key_up_raw, 1073741905, 0, 2, 236, 547]
[:key_down_raw, 1073741905, 0, 2, 237, 567]
[:key_down_raw, 32, 0, 2, 238, 577]
[:key_up_raw, 32, 0, 2, 239, 584]
[:key_up_raw, 1073741905, 0, 2, 240, 589]
[:key_down_raw, 32, 0, 2, 241, 608]
[:key_up_raw, 32, 0, 2, 242, 612]
[:key_down_raw, 32, 0, 2, 243, 619]
[:key_up_raw, 32, 0, 2, 244, 623]
[:key_down_raw, 32, 0, 2, 245, 627]
[:key_up_raw, 32, 0, 2, 246, 631]
[:mouse_move, 363, 354, 2, 247, 695]
[:mouse_move, 355, 354, 2, 248, 696]
[:mouse_move, 337, 351, 2, 249, 697]
[:mouse_move, 323, 347, 2, 250, 698]
[:mouse_move, 252, 326, 2, 251, 699]
[:mouse_move, 206, 312, 2, 252, 700]
[:mouse_move, 146, 292, 2, 253, 701]
[:mouse_move, 127, 286, 2, 254, 702]
[:mouse_move, 104, 279, 2, 255, 703]
[:mouse_move, 100, 277, 2, 256, 704]
[:mouse_move, 99, 277, 2, 257, 706]
[:mouse_move, 100, 277, 2, 258, 714]
[:mouse_move, 102, 277, 2, 259, 715]
[:mouse_move, 103, 277, 2, 260, 716]
[:mouse_move, 104, 276, 2, 261, 717]
[:mouse_move, 107, 273, 2, 262, 718]
[:mouse_move, 107, 272, 2, 263, 719]
[:mouse_move, 110, 270, 2, 264, 720]
[:mouse_move, 110, 269, 2, 265, 721]
[:mouse_move, 110, 268, 2, 266, 722]
[:mouse_move, 111, 266, 2, 267, 724]
[:mouse_move, 112, 264, 2, 268, 725]
[:mouse_move, 112, 261, 2, 269, 726]
[:mouse_move, 112, 260, 2, 270, 727]
[:mouse_move, 112, 259, 2, 271, 728]
[:key_down_raw, 120, 0, 2, 272, 739]
[:mouse_move, 111, 259, 2, 273, 745]
[:key_down_raw, 120, 0, 2, 274, 754]
[:key_down_raw, 120, 0, 2, 275, 756]
[:key_down_raw, 120, 0, 2, 276, 758]
[:key_down_raw, 120, 0, 2, 277, 760]
[:key_down_raw, 120, 0, 2, 278, 762]
[:mouse_button_pressed, 1, 0, 1, 279, 763]
[:key_down_raw, 120, 0, 2, 280, 764]
[:key_down_raw, 120, 0, 2, 281, 766]
[:key_down_raw, 120, 0, 2, 282, 768]
[:key_down_raw, 120, 0, 2, 283, 770]
[:mouse_button_up, 1, 0, 1, 284, 770]
[:key_down_raw, 120, 0, 2, 285, 772]
[:key_down_raw, 120, 0, 2, 286, 774]
[:key_down_raw, 120, 0, 2, 287, 776]
[:key_down_raw, 120, 0, 2, 288, 778]
[:mouse_move, 111, 262, 2, 289, 779]
[:mouse_move, 111, 271, 2, 290, 780]
[:key_down_raw, 120, 0, 2, 291, 780]
[:mouse_move, 110, 276, 2, 292, 781]
[:mouse_move, 108, 288, 2, 293, 782]
[:key_down_raw, 120, 0, 2, 294, 782]
[:mouse_move, 107, 295, 2, 295, 783]
[:mouse_move, 104, 312, 2, 296, 784]
[:key_down_raw, 120, 0, 2, 297, 784]
[:mouse_move, 104, 317, 2, 298, 785]
[:mouse_move, 102, 325, 2, 299, 786]
[:key_down_raw, 120, 0, 2, 300, 786]
[:mouse_move, 102, 328, 2, 301, 787]
[:mouse_move, 102, 332, 2, 302, 788]
[:key_down_raw, 120, 0, 2, 303, 788]
[:mouse_move, 102, 335, 2, 304, 789]
[:mouse_move, 102, 341, 2, 305, 790]
[:key_down_raw, 120, 0, 2, 306, 790]
[:mouse_move, 102, 343, 2, 307, 791]
[:mouse_move, 102, 345, 2, 308, 792]
[:key_down_raw, 120, 0, 2, 309, 792]
[:mouse_move, 102, 346, 2, 310, 793]
[:key_down_raw, 120, 0, 2, 311, 794]
[:mouse_move, 102, 347, 2, 312, 795]
[:key_down_raw, 120, 0, 2, 313, 796]
[:key_down_raw, 120, 0, 2, 314, 798]
[:mouse_move, 102, 348, 2, 315, 799]
[:key_down_raw, 120, 0, 2, 316, 800]
[:mouse_move, 103, 348, 2, 317, 801]
[:key_down_raw, 120, 0, 2, 318, 802]
[:key_down_raw, 120, 0, 2, 319, 804]
[:key_down_raw, 120, 0, 2, 320, 806]
[:key_down_raw, 120, 0, 2, 321, 808]
[:key_down_raw, 120, 0, 2, 322, 810]
[:mouse_move, 103, 349, 2, 323, 810]
[:mouse_move, 104, 349, 2, 324, 811]
[:key_down_raw, 120, 0, 2, 325, 812]
[:mouse_move, 104, 350, 2, 326, 813]
[:key_down_raw, 120, 0, 2, 327, 814]
[:key_down_raw, 120, 0, 2, 328, 816]
[:mouse_move, 105, 350, 2, 329, 817]
[:mouse_button_pressed, 1, 0, 1, 330, 817]
[:key_down_raw, 120, 0, 2, 331, 818]
[:key_down_raw, 120, 0, 2, 332, 820]
[:key_down_raw, 120, 0, 2, 333, 822]
[:key_down_raw, 120, 0, 2, 334, 824]
[:mouse_button_up, 1, 0, 1, 335, 826]
[:key_down_raw, 120, 0, 2, 336, 826]
[:key_down_raw, 120, 0, 2, 337, 828]
[:key_down_raw, 120, 0, 2, 338, 830]
[:key_down_raw, 120, 0, 2, 339, 832]
[:key_down_raw, 120, 0, 2, 340, 834]
[:mouse_move, 106, 350, 2, 341, 836]
[:key_down_raw, 120, 0, 2, 342, 836]
[:key_down_raw, 120, 0, 2, 343, 838]
[:key_up_raw, 120, 0, 2, 344, 839]
[:mouse_move, 107, 348, 2, 345, 842]
[:mouse_move, 108, 345, 2, 346, 843]
[:mouse_move, 109, 343, 2, 347, 844]
[:mouse_move, 109, 342, 2, 348, 845]
[:mouse_move, 109, 341, 2, 349, 852]
[:mouse_move, 111, 337, 2, 350, 853]
[:mouse_move, 113, 332, 2, 351, 854]
[:mouse_move, 116, 322, 2, 352, 855]
[:mouse_move, 118, 316, 2, 353, 856]
[:mouse_move, 119, 303, 2, 354, 857]
[:mouse_move, 120, 295, 2, 355, 858]
[:mouse_move, 120, 282, 2, 356, 859]
[:mouse_move, 120, 279, 2, 357, 860]
[:mouse_move, 121, 278, 2, 358, 861]
[:mouse_move, 121, 277, 2, 359, 865]
[:mouse_move, 121, 276, 2, 360, 868]
[:mouse_move, 120, 275, 2, 361, 869]
[:mouse_move, 120, 274, 2, 362, 870]
[:mouse_move, 118, 270, 2, 363, 871]
[:mouse_move, 118, 269, 2, 364, 872]
[:mouse_move, 117, 267, 2, 365, 873]
[:mouse_move, 117, 266, 2, 366, 880]
[:mouse_move, 118, 266, 2, 367, 881]
[:mouse_move, 118, 264, 2, 368, 882]
[:mouse_move, 118, 263, 2, 369, 883]
[:mouse_move, 118, 262, 2, 370, 884]
[:mouse_move, 118, 261, 2, 371, 887]
[:mouse_move, 119, 261, 2, 372, 888]
[:mouse_button_pressed, 1, 0, 1, 373, 909]
[:mouse_button_up, 1, 0, 1, 374, 912]
[:mouse_move, 118, 264, 2, 375, 914]
[:mouse_move, 116, 269, 2, 376, 915]
[:mouse_move, 113, 280, 2, 377, 916]
[:mouse_move, 111, 285, 2, 378, 917]
[:mouse_move, 109, 293, 2, 379, 918]
[:mouse_move, 108, 297, 2, 380, 919]
[:mouse_move, 105, 307, 2, 381, 920]
[:mouse_move, 103, 315, 2, 382, 921]
[:mouse_move, 101, 328, 2, 383, 922]
[:mouse_move, 101, 333, 2, 384, 923]
[:mouse_move, 102, 341, 2, 385, 924]
[:mouse_move, 102, 342, 2, 386, 925]
[:mouse_move, 103, 346, 2, 387, 926]
[:mouse_move, 103, 347, 2, 388, 927]
[:mouse_move, 103, 349, 2, 389, 928]
[:mouse_move, 104, 350, 2, 390, 930]
[:mouse_move, 104, 351, 2, 391, 933]
[:mouse_move, 104, 352, 2, 392, 939]
[:mouse_move, 105, 352, 2, 393, 940]
[:mouse_move, 106, 358, 2, 394, 941]
[:mouse_move, 107, 363, 2, 395, 942]
[:mouse_move, 108, 370, 2, 396, 943]
[:mouse_move, 109, 371, 2, 397, 944]
[:mouse_move, 109, 372, 2, 398, 945]
[:mouse_move, 109, 369, 2, 399, 953]
[:mouse_move, 109, 368, 2, 400, 954]
[:mouse_move, 109, 360, 2, 401, 955]
[:mouse_move, 109, 358, 2, 402, 956]
[:mouse_move, 109, 356, 2, 403, 957]
[:mouse_move, 109, 355, 2, 404, 958]
[:mouse_button_pressed, 1, 0, 1, 405, 970]
[:mouse_move, 109, 354, 2, 406, 970]
[:mouse_button_up, 1, 0, 1, 407, 975]
[:mouse_move, 109, 353, 2, 408, 985]
[:mouse_move, 109, 352, 2, 409, 987]
[:mouse_move, 109, 350, 2, 410, 988]
[:mouse_move, 109, 349, 2, 411, 990]
[:mouse_move, 109, 355, 2, 412, 996]
[:mouse_move, 109, 357, 2, 413, 997]
[:mouse_move, 106, 366, 2, 414, 998]
[:mouse_move, 104, 374, 2, 415, 999]
[:mouse_move, 100, 387, 2, 416, 1000]
[:mouse_move, 100, 391, 2, 417, 1001]
[:mouse_move, 101, 391, 2, 418, 1002]
[:mouse_move, 101, 390, 2, 419, 1008]
[:mouse_move, 102, 389, 2, 420, 1009]
[:mouse_move, 103, 387, 2, 421, 1010]
[:mouse_move, 103, 384, 2, 422, 1011]
[:mouse_move, 104, 382, 2, 423, 1012]
[:mouse_move, 104, 381, 2, 424, 1013]
[:mouse_move, 104, 380, 2, 425, 1014]
[:mouse_move, 105, 380, 2, 426, 1015]
[:mouse_move, 105, 379, 2, 427, 1030]
[:mouse_button_pressed, 1, 0, 1, 428, 1031]
[:mouse_move, 107, 379, 2, 429, 1039]
[:mouse_move, 111, 380, 2, 430, 1040]
[:mouse_move, 113, 380, 2, 431, 1041]
[:mouse_move, 124, 380, 2, 432, 1042]
[:mouse_move, 133, 381, 2, 433, 1043]
[:mouse_move, 145, 381, 2, 434, 1044]
[:mouse_move, 149, 381, 2, 435, 1045]
[:mouse_move, 152, 382, 2, 436, 1045]
[:mouse_move, 153, 382, 2, 437, 1046]
[:mouse_move, 154, 382, 2, 438, 1047]
[:mouse_move, 155, 382, 2, 439, 1048]
[:mouse_move, 158, 382, 2, 440, 1049]
[:mouse_move, 159, 382, 2, 441, 1050]
[:mouse_move, 162, 383, 2, 442, 1051]
[:mouse_move, 164, 383, 2, 443, 1052]
[:mouse_move, 168, 383, 2, 444, 1053]
[:mouse_move, 170, 384, 2, 445, 1053]
[:mouse_move, 172, 384, 2, 446, 1054]
[:mouse_move, 173, 384, 2, 447, 1055]
[:mouse_move, 176, 384, 2, 448, 1056]
[:mouse_move, 178, 384, 2, 449, 1057]
[:mouse_move, 180, 384, 2, 450, 1058]
[:mouse_move, 181, 384, 2, 451, 1059]
[:mouse_move, 183, 384, 2, 452, 1075]
[:mouse_move, 184, 384, 2, 453, 1076]
[:mouse_move, 186, 384, 2, 454, 1077]
[:mouse_move, 187, 384, 2, 455, 1078]
[:mouse_move, 190, 384, 2, 456, 1079]
[:mouse_move, 191, 384, 2, 457, 1080]
[:mouse_move, 193, 385, 2, 458, 1081]
[:mouse_move, 194, 385, 2, 459, 1083]
[:mouse_move, 195, 385, 2, 460, 1087]
[:mouse_move, 196, 385, 2, 461, 1090]
[:mouse_move, 197, 385, 2, 462, 1091]
[:mouse_move, 198, 385, 2, 463, 1093]
[:mouse_button_up, 1, 0, 1, 464, 1108]
[:mouse_move, 199, 385, 2, 465, 1110]
[:key_down_raw, 1073741905, 0, 2, 466, 1148]
[:key_down_raw, 1073741905, 0, 2, 467, 1163]
[:key_down_raw, 1073741905, 0, 2, 468, 1165]
[:key_down_raw, 1073741905, 0, 2, 469, 1167]
[:key_down_raw, 32, 0, 2, 470, 1168]
[:key_up_raw, 32, 0, 2, 471, 1174]
[:key_down_raw, 32, 0, 2, 472, 1210]
[:key_up_raw, 32, 0, 2, 473, 1217]
[:key_down_raw, 32, 0, 2, 474, 1225]
[:key_up_raw, 32, 0, 2, 475, 1231]
[:key_up_raw, 1073741905, 0, 2, 476, 1238]
[:mouse_move, 204, 382, 2, 477, 1263]
[:mouse_move, 213, 375, 2, 478, 1264]
[:mouse_move, 217, 364, 2, 479, 1265]
[:mouse_move, 217, 358, 2, 480, 1266]
[:mouse_move, 212, 345, 2, 481, 1267]
[:mouse_move, 208, 337, 2, 482, 1268]
[:mouse_move, 194, 319, 2, 483, 1269]
[:mouse_move, 183, 305, 2, 484, 1270]
[:mouse_move, 160, 280, 2, 485, 1271]
[:mouse_move, 150, 270, 2, 486, 1272]
[:mouse_move, 137, 258, 2, 487, 1273]
[:mouse_move, 135, 256, 2, 488, 1274]
[:key_down_raw, 32, 0, 2, 489, 1303]
[:key_up_raw, 32, 0, 2, 490, 1307]
[:key_down_raw, 1073741905, 0, 2, 491, 1341]
[:key_down_raw, 1073741905, 0, 2, 492, 1356]
[:key_down_raw, 1073741905, 0, 2, 493, 1358]
[:key_down_raw, 1073741905, 0, 2, 494, 1360]
[:key_down_raw, 32, 0, 2, 495, 1361]
[:key_up_raw, 32, 0, 2, 496, 1367]
[:key_up_raw, 1073741905, 0, 2, 497, 1381]
[:key_down_raw, 1073741904, 0, 2, 498, 1397]
[:key_up_raw, 1073741904, 0, 2, 499, 1403]
[:key_down_raw, 1073741905, 0, 2, 500, 1425]
[:key_down_raw, 1073741905, 0, 2, 501, 1440]
[:key_down_raw, 32, 0, 2, 502, 1441]
[:key_up_raw, 32, 0, 2, 503, 1450]
[:key_up_raw, 1073741905, 0, 2, 504, 1457]
[:key_down_raw, 1073741903, 0, 2, 505, 1470]
[:key_down_raw, 32, 0, 2, 506, 1479]
[:key_up_raw, 32, 0, 2, 507, 1485]
[:key_up_raw, 1073741903, 0, 2, 508, 1499]
[:key_down_raw, 32, 0, 2, 509, 1500]
[:key_up_raw, 32, 0, 2, 510, 1506]
[:key_down_raw, 32, 0, 2, 511, 1511]
[:key_up_raw, 32, 0, 2, 512, 1516]
[:key_down_raw, 32, 0, 2, 513, 1520]
[:key_up_raw, 32, 0, 2, 514, 1524]
[:key_down_raw, 32, 0, 2, 515, 1528]
[:key_up_raw, 32, 0, 2, 516, 1534]
[:key_down_raw, 32, 0, 2, 517, 1538]
[:key_down_raw, 1073741903, 0, 2, 518, 1540]
[:key_up_raw, 32, 0, 2, 519, 1545]
[:key_down_raw, 1073741903, 0, 2, 520, 1555]
[:key_down_raw, 32, 0, 2, 521, 1557]
[:key_up_raw, 1073741903, 0, 2, 522, 1558]
[:key_up_raw, 32, 0, 2, 523, 1563]
[:key_down_raw, 32, 0, 2, 524, 1567]
[:key_up_raw, 32, 0, 2, 525, 1581]
[:key_down_raw, 1073741904, 0, 2, 526, 1592]
[:key_down_raw, 1073741904, 0, 2, 527, 1607]
[:key_down_raw, 1073741904, 0, 2, 528, 1609]
[:key_down_raw, 1073741904, 0, 2, 529, 1611]
[:key_down_raw, 1073741904, 0, 2, 530, 1613]
[:key_down_raw, 1073741904, 0, 2, 531, 1615]
[:key_down_raw, 1073741904, 0, 2, 532, 1617]
[:key_down_raw, 1073741904, 0, 2, 533, 1619]
[:key_down_raw, 1073741904, 0, 2, 534, 1621]
[:key_down_raw, 1073741904, 0, 2, 535, 1623]
[:key_down_raw, 1073741904, 0, 2, 536, 1625]
[:key_down_raw, 1073741904, 0, 2, 537, 1627]
[:key_down_raw, 1073741904, 0, 2, 538, 1629]
[:key_down_raw, 1073741904, 0, 2, 539, 1631]
[:key_down_raw, 1073741904, 0, 2, 540, 1633]
[:key_up_raw, 1073741904, 0, 2, 541, 1633]
[:mouse_move, 137, 254, 2, 542, 1666]
[:mouse_move, 137, 253, 2, 543, 1667]
[:mouse_move, 137, 252, 2, 544, 1668]
[:mouse_move, 138, 248, 2, 545, 1669]
[:mouse_move, 141, 243, 2, 546, 1670]
[:mouse_move, 141, 242, 2, 547, 1675]
[:mouse_move, 142, 240, 2, 548, 1676]
[:mouse_move, 143, 240, 2, 549, 1677]
[:mouse_move, 143, 239, 2, 550, 1678]
[:mouse_move, 143, 238, 2, 551, 1682]
[:mouse_move, 144, 237, 2, 552, 1683]
[:mouse_move, 144, 236, 2, 553, 1684]
[:mouse_move, 144, 235, 2, 554, 1685]
[:mouse_move, 144, 234, 2, 555, 1686]
[:mouse_move, 146, 228, 2, 556, 1687]
[:mouse_move, 147, 226, 2, 557, 1688]
[:mouse_move, 147, 225, 2, 558, 1689]
[:mouse_button_pressed, 1, 0, 1, 559, 1703]
[:mouse_move, 148, 225, 2, 560, 1713]
[:mouse_move, 150, 225, 2, 561, 1714]
[:mouse_move, 157, 225, 2, 562, 1715]
[:mouse_move, 160, 225, 2, 563, 1716]
[:mouse_move, 170, 226, 2, 564, 1717]
[:mouse_move, 174, 226, 2, 565, 1718]
[:mouse_move, 179, 226, 2, 566, 1718]
[:mouse_move, 181, 226, 2, 567, 1719]
[:mouse_move, 185, 226, 2, 568, 1720]
[:mouse_move, 188, 226, 2, 569, 1721]
[:mouse_move, 195, 226, 2, 570, 1722]
[:mouse_move, 199, 226, 2, 571, 1723]
[:mouse_move, 205, 226, 2, 572, 1724]
[:mouse_move, 208, 226, 2, 573, 1724]
[:mouse_move, 216, 226, 2, 574, 1725]
[:mouse_move, 220, 226, 2, 575, 1726]
[:mouse_move, 225, 226, 2, 576, 1727]
[:mouse_move, 229, 226, 2, 577, 1728]
[:mouse_move, 236, 226, 2, 578, 1729]
[:mouse_move, 238, 226, 2, 579, 1729]
[:mouse_move, 240, 226, 2, 580, 1730]
[:mouse_move, 242, 226, 2, 581, 1731]
[:mouse_button_up, 1, 0, 1, 582, 1747]
[:mouse_move, 243, 227, 2, 583, 1749]
[:key_down_raw, 1073741903, 0, 2, 584, 1774]
[:key_up_raw, 1073741903, 0, 2, 585, 1783]
[:key_down_raw, 1073741906, 0, 2, 586, 1785]
[:key_up_raw, 1073741906, 0, 2, 587, 1789]
[:key_down_raw, 32, 0, 2, 588, 1815]
[:key_up_raw, 32, 0, 2, 589, 1819]
[:key_down_raw, 32, 0, 2, 590, 1825]
[:key_up_raw, 32, 0, 2, 591, 1831]
[:key_down_raw, 32, 0, 2, 592, 1845]
[:key_up_raw, 32, 0, 2, 593, 1851]
[:key_down_raw, 32, 0, 2, 594, 1856]
[:key_up_raw, 32, 0, 2, 595, 1863]
[:key_down_raw, 1073741904, 0, 2, 596, 1881]
[:key_down_raw, 1073741904, 0, 2, 597, 1896]
[:key_down_raw, 1073741904, 0, 2, 598, 1898]
[:key_down_raw, 1073741904, 0, 2, 599, 1900]
[:key_down_raw, 1073741904, 0, 2, 600, 1903]
[:key_down_raw, 1073741904, 0, 2, 601, 1904]
[:key_down_raw, 1073741904, 0, 2, 602, 1906]
[:key_up_raw, 1073741904, 0, 2, 603, 1908]
[:key_down_raw, 32, 0, 2, 604, 1920]
[:key_down_raw, 32, 0, 2, 605, 1935]
[:key_down_raw, 1073741903, 0, 2, 606, 1936]
[:key_down_raw, 1073741903, 0, 2, 607, 1951]
[:key_down_raw, 1073741903, 0, 2, 608, 1953]
[:key_up_raw, 32, 0, 2, 609, 1954]
[:key_up_raw, 1073741903, 0, 2, 610, 1954]
[:key_down_raw, 32, 0, 2, 611, 1985]
[:key_down_raw, 1073741903, 0, 2, 612, 1989]
[:key_up_raw, 32, 0, 2, 613, 1999]
[:key_up_raw, 1073741903, 0, 2, 614, 2002]
[:mouse_move, 245, 226, 2, 615, 2037]
[:mouse_move, 308, 213, 2, 616, 2038]
[:mouse_move, 380, 203, 2, 617, 2039]
[:mouse_move, 567, 189, 2, 618, 2040]
[:mouse_move, 663, 187, 2, 619, 2041]
[:mouse_move, 862, 189, 2, 620, 2042]
[:mouse_move, 931, 190, 2, 621, 2043]
[:mouse_move, 1002, 195, 2, 622, 2044]
[:mouse_move, 1016, 195, 2, 623, 2045]
[:mouse_move, 1031, 195, 2, 624, 2046]
[:key_down_raw, 96, 0, 2, 625, 2064]
[:key_up_raw, 96, 0, 2, 626, 2069]
[:mouse_move, 1031, 193, 2, 627, 2074]
[:mouse_move, 1031, 191, 2, 628, 2074]
[:mouse_move, 1031, 189, 2, 629, 2075]
[:mouse_move, 1031, 188, 2, 630, 2077]
[:mouse_move, 1031, 187, 2, 631, 2079]
[:mouse_move, 1029, 185, 2, 632, 2082]
[:mouse_move, 1023, 174, 2, 633, 2082]
[:mouse_move, 1017, 168, 2, 634, 2083]
[:mouse_move, 1005, 155, 2, 635, 2084]
[:mouse_move, 1000, 149, 2, 636, 2085]
[:mouse_move, 986, 140, 2, 637, 2086]
[:mouse_move, 980, 138, 2, 638, 2086]
[:mouse_move, 965, 131, 2, 639, 2087]
[:mouse_move, 953, 126, 2, 640, 2088]
[:mouse_move, 925, 115, 2, 641, 2089]
[:mouse_move, 912, 109, 2, 642, 2089]
[:mouse_move, 902, 105, 2, 643, 2090]
[:mouse_move, 891, 101, 2, 644, 2091]
[:mouse_move, 886, 99, 2, 645, 2092]
[:mouse_move, 877, 98, 2, 646, 2092]
[:mouse_move, 861, 97, 2, 647, 2093]
[:mouse_move, 857, 97, 2, 648, 2094]
[:mouse_move, 850, 98, 2, 649, 2095]
[:mouse_move, 849, 98, 2, 650, 2096]
[:mouse_move, 847, 99, 2, 651, 2096]
[:mouse_move, 846, 99, 2, 652, 2097]
[:mouse_move, 845, 99, 2, 653, 2098]
[:mouse_move, 842, 101, 2, 654, 2099]
[:mouse_move, 841, 101, 2, 655, 2100]
[:mouse_move, 840, 102, 2, 656, 2100]
[:mouse_move, 839, 103, 2, 657, 2104]
[:mouse_move, 840, 103, 2, 658, 2109]
[:mouse_move, 841, 103, 2, 659, 2110]
[:mouse_move, 843, 103, 2, 660, 2112]
[:mouse_move, 845, 103, 2, 661, 2113]
[:mouse_move, 846, 102, 2, 662, 2115]
[:mouse_move, 848, 102, 2, 663, 2116]
[:mouse_move, 855, 101, 2, 664, 2116]
[:mouse_move, 858, 100, 2, 665, 2117]
[:mouse_move, 862, 99, 2, 666, 2118]
[:mouse_move, 865, 98, 2, 667, 2119]
[:mouse_move, 867, 98, 2, 668, 2120]
[:mouse_move, 868, 97, 2, 669, 2121]
[:mouse_move, 869, 97, 2, 670, 2122]
[:mouse_move, 868, 97, 2, 671, 2128]
[:mouse_move, 864, 95, 2, 672, 2129]
[:mouse_move, 846, 92, 2, 673, 2129]
[:mouse_move, 834, 90, 2, 674, 2130]
[:mouse_move, 818, 88, 2, 675, 2131]
[:mouse_move, 813, 87, 2, 676, 2132]
[:mouse_move, 806, 87, 2, 677, 2132]
[:mouse_move, 803, 87, 2, 678, 2133]
[:mouse_move, 798, 87, 2, 679, 2134]
[:mouse_move, 796, 87, 2, 680, 2134]
[:mouse_move, 795, 87, 2, 681, 2135]
[:mouse_move, 794, 87, 2, 682, 2137]
[:key_down_raw, 13, 0, 2, 683, 2166]
