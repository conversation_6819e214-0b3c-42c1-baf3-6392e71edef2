static const uint16_t presym_length_table[] = {
  1,	/* ! */
  1,	/* % */
  1,	/* & */
  1,	/* * */
  1,	/* + */
  1,	/* - */
  1,	/* / */
  1,	/* < */
  1,	/* > */
  1,	/* E */
  1,	/* ^ */
  1,	/* ` */
  1,	/* a */
  1,	/* b */
  1,	/* c */
  1,	/* e */
  1,	/* f */
  1,	/* h */
  1,	/* i */
  1,	/* j */
  1,	/* k */
  1,	/* l */
  1,	/* m */
  1,	/* n */
  1,	/* o */
  1,	/* p */
  1,	/* r */
  1,	/* s */
  1,	/* t */
  1,	/* v */
  1,	/* w */
  1,	/* x */
  1,	/* y */
  1,	/* z */
  1,	/* | */
  1,	/* ~ */
  2,	/* != */
  2,	/* !~ */
  2,	/* $0 */
  2,	/* $? */
  2,	/* && */
  2,	/* ** */
  2,	/* +@ */
  2,	/* -@ */
  2,	/* << */
  2,	/* <= */
  2,	/* == */
  2,	/* =~ */
  2,	/* >= */
  2,	/* >> */
  2,	/* GC */
  2,	/* IO */
  2,	/* PI */
  2,	/* [] */
  2,	/* af */
  2,	/* ai */
  2,	/* ar */
  2,	/* at */
  2,	/* bi */
  2,	/* bs */
  2,	/* e0 */
  2,	/* e2 */
  2,	/* e3 */
  2,	/* ed */
  2,	/* ei */
  2,	/* fd */
  2,	/* gm */
  2,	/* in */
  2,	/* io */
  2,	/* ip */
  2,	/* lz */
  2,	/* nk */
  2,	/* nv */
  2,	/* op */
  2,	/* rs */
  2,	/* sa */
  2,	/* sc */
  2,	/* sv */
  2,	/* tr */
  2,	/* vs */
  2,	/* || */
  3,	/* <=> */
  3,	/* === */
  3,	/* @af */
  3,	/* DIG */
  3,	/* MAX */
  3,	/* MIN */
  3,	/* NAN */
  3,	/* []= */
  3,	/* abs */
  3,	/* arg */
  3,	/* ary */
  3,	/* beg */
  3,	/* blk */
  3,	/* buf */
  3,	/* chr */
  3,	/* cls */
  3,	/* cmd */
  3,	/* cmp */
  3,	/* cos */
  3,	/* day */
  3,	/* dig */
  3,	/* dir */
  3,	/* div */
  3,	/* dup */
  3,	/* end */
  3,	/* eof */
  3,	/* erf */
  3,	/* err */
  3,	/* exp */
  3,	/* fib */
  3,	/* hex */
  3,	/* idx */
  3,	/* int */
  3,	/* ip? */
  3,	/* key */
  3,	/* len */
  3,	/* lhs */
  3,	/* lim */
  3,	/* log */
  3,	/* low */
  3,	/* map */
  3,	/* max */
  3,	/* mid */
  3,	/* min */
  3,	/* mon */
  3,	/* msg */
  3,	/* new */
  3,	/* now */
  3,	/* num */
  3,	/* obj */
  3,	/* oct */
  3,	/* opt */
  3,	/* ord */
  3,	/* out */
  3,	/* pat */
  3,	/* pid */
  3,	/* pop */
  3,	/* pos */
  3,	/* pre */
  3,	/* quo */
  3,	/* req */
  3,	/* res */
  3,	/* ret */
  3,	/* rhs */
  3,	/* row */
  3,	/* sec */
  3,	/* sep */
  3,	/* sin */
  3,	/* str */
  3,	/* sub */
  3,	/* sym */
  3,	/* tan */
  3,	/* tap */
  3,	/* tcp */
  3,	/* tmp */
  3,	/* tr! */
  3,	/* udp */
  3,	/* utc */
  3,	/* val */
  3,	/* zip */
  4,	/* @buf */
  4,	/* @dst */
  4,	/* @fib */
  4,	/* @obj */
  4,	/* ARGV */
  4,	/* EXCL */
  4,	/* FREE */
  4,	/* File */
  4,	/* Hash */
  4,	/* Lazy */
  4,	/* Math */
  4,	/* NONE */
  4,	/* NULL */
  4,	/* Proc */
  4,	/* RDWR */
  4,	/* SYNC */
  4,	/* Time */
  4,	/* _new */
  4,	/* abs2 */
  4,	/* acos */
  4,	/* addr */
  4,	/* all? */
  4,	/* any? */
  4,	/* arg0 */
  4,	/* arg1 */
  4,	/* arg2 */
  4,	/* args */
  4,	/* argv */
  4,	/* asin */
  4,	/* atan */
  4,	/* attr */
  4,	/* bind */
  4,	/* bool */
  4,	/* call */
  4,	/* cbrt */
  4,	/* ceil */
  4,	/* char */
  4,	/* chop */
  4,	/* conj */
  4,	/* cosh */
  4,	/* curr */
  4,	/* data */
  4,	/* drop */
  4,	/* dst? */
  4,	/* dump */
  4,	/* each */
  4,	/* elem */
  4,	/* eof? */
  4,	/* epos */
  4,	/* eql? */
  4,	/* erfc */
  4,	/* eval */
  4,	/* fail */
  4,	/* fdiv */
  4,	/* feed */
  4,	/* file */
  4,	/* fill */
  4,	/* find */
  4,	/* flag */
  4,	/* getc */
  4,	/* gets */
  4,	/* gmt? */
  4,	/* grep */
  4,	/* gsub */
  4,	/* hash */
  4,	/* high */
  4,	/* host */
  4,	/* hour */
  4,	/* idx2 */
  4,	/* imag */
  4,	/* init */
  4,	/* join */
  4,	/* key? */
  4,	/* keys */
  4,	/* lary */
  4,	/* last */
  4,	/* lazy */
  4,	/* left */
  4,	/* lidx */
  4,	/* line */
  4,	/* log2 */
  4,	/* loop */
  4,	/* lval */
  4,	/* map! */
  4,	/* mday */
  4,	/* mesg */
  4,	/* meth */
  4,	/* mode */
  4,	/* name */
  4,	/* nan? */
  4,	/* next */
  4,	/* nil? */
  4,	/* none */
  4,	/* ntop */
  4,	/* obj= */
  4,	/* one? */
  4,	/* open */
  4,	/* opts */
  4,	/* orig */
  4,	/* pack */
  4,	/* pair */
  4,	/* path */
  4,	/* peek */
  4,	/* perm */
  4,	/* pipe */
  4,	/* plen */
  4,	/* port */
  4,	/* pos= */
  4,	/* post */
  4,	/* proc */
  4,	/* pton */
  4,	/* push */
  4,	/* puts */
  4,	/* rand */
  4,	/* read */
  4,	/* real */
  4,	/* rect */
  4,	/* recv */
  4,	/* rest */
  4,	/* ridx */
  4,	/* rval */
  4,	/* sary */
  4,	/* seek */
  4,	/* send */
  4,	/* sinh */
  4,	/* size */
  4,	/* sock */
  4,	/* sort */
  4,	/* sqrt */
  4,	/* step */
  4,	/* str2 */
  4,	/* sub! */
  4,	/* succ */
  4,	/* sync */
  4,	/* take */
  4,	/* tanh */
  4,	/* tell */
  4,	/* then */
  4,	/* this */
  4,	/* to_a */
  4,	/* to_c */
  4,	/* to_f */
  4,	/* to_h */
  4,	/* to_i */
  4,	/* to_r */
  4,	/* to_s */
  4,	/* tr_s */
  4,	/* tty? */
  4,	/* type */
  4,	/* uniq */
  4,	/* unix */
  4,	/* upto */
  4,	/* usec */
  4,	/* user */
  4,	/* utc? */
  4,	/* vals */
  4,	/* warn */
  4,	/* wday */
  4,	/* yday */
  4,	/* year */
  4,	/* zone */
  5,	/* @args */
  5,	/* @data */
  5,	/* @meth */
  5,	/* @name */
  5,	/* @path */
  5,	/* @proc */
  5,	/* Array */
  5,	/* CREAT */
  5,	/* Class */
  5,	/* DSYNC */
  5,	/* Fiber */
  5,	/* Float */
  5,	/* RADIX */
  5,	/* RSYNC */
  5,	/* Range */
  5,	/* STDIN */
  5,	/* TOTAL */
  5,	/* TRUNC */
  5,	/* T_ENV */
  5,	/* _bind */
  5,	/* _name */
  5,	/* _pipe */
  5,	/* _proc */
  5,	/* _recv */
  5,	/* acosh */
  5,	/* angle */
  5,	/* args= */
  5,	/* arity */
  5,	/* array */
  5,	/* ary_F */
  5,	/* ary_T */
  5,	/* asinh */
  5,	/* assoc */
  5,	/* atan2 */
  5,	/* atanh */
  5,	/* begin */
  5,	/* block */
  5,	/* bytes */
  5,	/* chars */
  5,	/* chmod */
  5,	/* chomp */
  5,	/* chop! */
  5,	/* clamp */
  5,	/* class */
  5,	/* clear */
  5,	/* clone */
  5,	/* close */
  5,	/* count */
  5,	/* ctime */
  5,	/* curry */
  5,	/* cycle */
  5,	/* depth */
  5,	/* enums */
  5,	/* fetch */
  5,	/* field */
  5,	/* file? */
  5,	/* first */
  5,	/* flags */
  5,	/* flock */
  5,	/* floor */
  5,	/* flush */
  5,	/* fname */
  5,	/* force */
  5,	/* found */
  5,	/* frexp */
  5,	/* getgm */
  5,	/* gsub! */
  5,	/* hypot */
  5,	/* index */
  5,	/* ipv4? */
  5,	/* ipv6? */
  5,	/* is_a? */
  5,	/* ldexp */
  5,	/* level */
  5,	/* limit */
  5,	/* lines */
  5,	/* ljust */
  5,	/* local */
  5,	/* log10 */
  5,	/* lsize */
  5,	/* merge */
  5,	/* meth= */
  5,	/* month */
  5,	/* mtime */
  5,	/* names */
  5,	/* next! */
  5,	/* none? */
  5,	/* other */
  5,	/* owner */
  5,	/* phase */
  5,	/* pipe? */
  5,	/* polar */
  5,	/* popen */
  5,	/* pproc */
  5,	/* pread */
  5,	/* print */
  5,	/* proto */
  5,	/* raise */
  5,	/* real? */
  5,	/* right */
  5,	/* rjust */
  5,	/* round */
  5,	/* shift */
  5,	/* size? */
  5,	/* slice */
  5,	/* sort! */
  5,	/* split */
  5,	/* srand */
  5,	/* stack */
  5,	/* start */
  5,	/* state */
  5,	/* store */
  5,	/* strip */
  5,	/* succ! */
  5,	/* sync= */
  5,	/* taken */
  5,	/* tally */
  5,	/* times */
  5,	/* tr_s! */
  5,	/* umask */
  5,	/* union */
  5,	/* uniq! */
  5,	/* unix? */
  5,	/* value */
  5,	/* write */
  5,	/* yield */
  5,	/* zero? */
  6,	/* $DEBUG */
  6,	/* $stdin */
  6,	/* @level */
  6,	/* AF_MAX */
  6,	/* APPEND */
  6,	/* BINARY */
  6,	/* DIRECT */
  6,	/* Fixnum */
  6,	/* IP_TOS */
  6,	/* IP_TTL */
  6,	/* Kernel */
  6,	/* Method */
  6,	/* Module */
  6,	/* NOCTTY */
  6,	/* Object */
  6,	/* Option */
  6,	/* RDONLY */
  6,	/* Random */
  6,	/* Regexp */
  6,	/* STDERR */
  6,	/* STDOUT */
  6,	/* Socket */
  6,	/* Status */
  6,	/* String */
  6,	/* Struct */
  6,	/* Symbol */
  6,	/* T_CPTR */
  6,	/* T_DATA */
  6,	/* T_FREE */
  6,	/* T_HASH */
  6,	/* T_PROC */
  6,	/* T_TRUE */
  6,	/* WRONLY */
  6,	/* __id__ */
  6,	/* _getwd */
  6,	/* _klass */
  6,	/* _mtime */
  6,	/* _owner */
  6,	/* _popen */
  6,	/* accept */
  6,	/* alive? */
  6,	/* append */
  6,	/* caller */
  6,	/* chomp! */
  6,	/* concat */
  6,	/* cover? */
  6,	/* delete */
  6,	/* detect */
  6,	/* divmod */
  6,	/* domain */
  6,	/* downto */
  6,	/* empty? */
  6,	/* enable */
  6,	/* equal? */
  6,	/* except */
  6,	/* exist? */
  6,	/* extend */
  6,	/* family */
  6,	/* fileno */
  6,	/* filter */
  6,	/* for_fd */
  6,	/* format */
  6,	/* freeze */
  6,	/* getutc */
  6,	/* gmtime */
  6,	/* ifnone */
  6,	/* inject */
  6,	/* insert */
  6,	/* intern */
  6,	/* invert */
  6,	/* isatty */
  6,	/* itself */
  6,	/* lambda */
  6,	/* length */
  6,	/* linger */
  6,	/* listen */
  6,	/* lstrip */
  6,	/* max_by */
  6,	/* maxlen */
  6,	/* merge! */
  6,	/* method */
  6,	/* min_by */
  6,	/* minmax */
  6,	/* mktime */
  6,	/* object */
  6,	/* offset */
  6,	/* outbuf */
  6,	/* padstr */
  6,	/* printf */
  6,	/* public */
  6,	/* pwrite */
  6,	/* rassoc */
  6,	/* reduce */
  6,	/* rehash */
  6,	/* reject */
  6,	/* rename */
  6,	/* result */
  6,	/* resume */
  6,	/* rewind */
  6,	/* rindex */
  6,	/* rotate */
  6,	/* rstrip */
  6,	/* sample */
  6,	/* select */
  6,	/* slice! */
  6,	/* string */
  6,	/* strip! */
  6,	/* substr */
  6,	/* to_int */
  6,	/* to_str */
  6,	/* to_sym */
  6,	/* unbind */
  6,	/* ungetc */
  6,	/* unlink */
  6,	/* unpack */
  6,	/* upcase */
  6,	/* update */
  6,	/* value? */
  6,	/* values */
  6,	/* whence */
  7,	/* $stderr */
  7,	/* $stdout */
  7,	/* @family */
  7,	/* AF_INET */
  7,	/* AF_LINK */
  7,	/* AF_UNIX */
  7,	/* Complex */
  7,	/* DEFAULT */
  7,	/* EPSILON */
  7,	/* IOError */
  7,	/* Integer */
  7,	/* LOCK_EX */
  7,	/* LOCK_NB */
  7,	/* LOCK_SH */
  7,	/* LOCK_UN */
  7,	/* MAX_EXP */
  7,	/* MIN_EXP */
  7,	/* MSG_EOR */
  7,	/* MSG_OOB */
  7,	/* NOATIME */
  7,	/* Numeric */
  7,	/* PF_INET */
  7,	/* PF_LINK */
  7,	/* PF_UNIX */
  7,	/* Process */
  7,	/* SHUT_RD */
  7,	/* SHUT_WR */
  7,	/* SO_TYPE */
  7,	/* TMPFILE */
  7,	/* T_ARRAY */
  7,	/* T_CLASS */
  7,	/* T_FALSE */
  7,	/* T_FIBER */
  7,	/* T_FLOAT */
  7,	/* T_RANGE */
  7,	/* T_UNDEF */
  7,	/* Yielder */
  7,	/* __div__ */
  7,	/* __lines */
  7,	/* _accept */
  7,	/* _lastai */
  7,	/* _listen */
  7,	/* _socket */
  7,	/* afamily */
  7,	/* asctime */
  7,	/* backlog */
  7,	/* bsearch */
  7,	/* casecmp */
  7,	/* closed? */
  7,	/* collect */
  7,	/* command */
  7,	/* compact */
  7,	/* compile */
  7,	/* connect */
  7,	/* consume */
  7,	/* current */
  7,	/* default */
  7,	/* delete! */
  7,	/* dirname */
  7,	/* disable */
  7,	/* dropped */
  7,	/* entries */
  7,	/* exists? */
  7,	/* extname */
  7,	/* filter! */
  7,	/* finite? */
  7,	/* flatten */
  7,	/* foreach */
  7,	/* friday? */
  7,	/* frozen? */
  7,	/* getbyte */
  7,	/* id2name */
  7,	/* include */
  7,	/* inspect */
  7,	/* integer */
  7,	/* ip_port */
  7,	/* keep_if */
  7,	/* keyrest */
  7,	/* lambda? */
  7,	/* lstrip! */
  7,	/* max_cmp */
  7,	/* member? */
  7,	/* members */
  7,	/* message */
  7,	/* methods */
  7,	/* min_cmp */
  7,	/* modules */
  7,	/* monday? */
  7,	/* nesting */
  7,	/* new_key */
  7,	/* nobits? */
  7,	/* numeric */
  7,	/* optname */
  7,	/* padding */
  7,	/* pattern */
  7,	/* pfamily */
  7,	/* pointer */
  7,	/* prepend */
  7,	/* private */
  7,	/* produce */
  7,	/* reject! */
  7,	/* replace */
  7,	/* result= */
  7,	/* reverse */
  7,	/* rotate! */
  7,	/* rstrip! */
  7,	/* select! */
  7,	/* sep_len */
  7,	/* service */
  7,	/* setbyte */
  7,	/* shuffle */
  7,	/* socket? */
  7,	/* sort_by */
  7,	/* sprintf */
  7,	/* squeeze */
  7,	/* sunday? */
  7,	/* symlink */
  7,	/* sysopen */
  7,	/* sysread */
  7,	/* sysseek */
  7,	/* to_enum */
  7,	/* to_path */
  7,	/* to_proc */
  7,	/* unpack1 */
  7,	/* unshift */
  7,	/* upcase! */
  7,	/* yielder */
  8,	/* @optname */
  8,	/* AF_INET6 */
  8,	/* AF_LOCAL */
  8,	/* AF_ROUTE */
  8,	/* Addrinfo */
  8,	/* BUF_SIZE */
  8,	/* EOFError */
  8,	/* FileTest */
  8,	/* INFINITY */
  8,	/* IPSocket */
  8,	/* KeyError */
  8,	/* MANT_DIG */
  8,	/* MSG_PEEK */
  8,	/* NI_DGRAM */
  8,	/* NOFOLLOW */
  8,	/* NONBLOCK */
  8,	/* NilClass */
  8,	/* PF_INET6 */
  8,	/* PF_LOCAL */
  8,	/* PF_ROUTE */
  8,	/* Rational */
  8,	/* SEEK_CUR */
  8,	/* SEEK_END */
  8,	/* SEEK_SET */
  8,	/* SOCK_RAW */
  8,	/* SO_DEBUG */
  8,	/* SO_ERROR */
  8,	/* T_ICLASS */
  8,	/* T_MODULE */
  8,	/* T_OBJECT */
  8,	/* T_SCLASS */
  8,	/* T_STRING */
  8,	/* T_SYMBOL */
  8,	/* __ary_eq */
  8,	/* __delete */
  8,	/* __send__ */
  8,	/* __svalue */
  8,	/* __to_int */
  8,	/* __to_str */
  8,	/* _accept2 */
  8,	/* _bufread */
  8,	/* _connect */
  8,	/* _gethome */
  8,	/* _inspect */
  8,	/* allbits? */
  8,	/* allocate */
  8,	/* anybits? */
  8,	/* basename */
  8,	/* between? */
  8,	/* bytesize */
  8,	/* casecmp? */
  8,	/* collect! */
  8,	/* compact! */
  8,	/* default= */
  8,	/* downcase */
  8,	/* dropping */
  8,	/* each_key */
  8,	/* enum_for */
  8,	/* extended */
  8,	/* filename */
  8,	/* find_all */
  8,	/* flat_map */
  8,	/* flatten! */
  8,	/* getlocal */
  8,	/* group_by */
  8,	/* has_key? */
  8,	/* home_dir */
  8,	/* include? */
  8,	/* included */
  8,	/* kind_of? */
  8,	/* modified */
  8,	/* new_args */
  8,	/* nodename */
  8,	/* nonzero? */
  8,	/* peeraddr */
  8,	/* protocol */
  8,	/* readchar */
  8,	/* readline */
  8,	/* readlink */
  8,	/* realpath */
  8,	/* receiver */
  8,	/* recvfrom */
  8,	/* reverse! */
  8,	/* self_len */
  8,	/* servname */
  8,	/* shuffle! */
  8,	/* shutdown */
  8,	/* sockaddr */
  8,	/* socktype */
  8,	/* squeeze! */
  8,	/* str_each */
  8,	/* swapcase */
  8,	/* symlink? */
  8,	/* syswrite */
  8,	/* template */
  8,	/* transfer */
  8,	/* truncate */
  8,	/* tuesday? */
  9,	/* @hostname */
  9,	/* @protocol */
  9,	/* @sockaddr */
  9,	/* @socktype */
  9,	/* @stop_exc */
  9,	/* AF_UNSPEC */
  9,	/* Constants */
  9,	/* Exception */
  9,	/* Generator */
  9,	/* MSG_TRUNC */
  9,	/* NI_NOFQDN */
  9,	/* NameError */
  9,	/* PF_UNSPEC */
  9,	/* SEPARATOR */
  9,	/* SHUT_RDWR */
  9,	/* SO_LINGER */
  9,	/* SO_RCVBUF */
  9,	/* SO_SNDBUF */
  9,	/* TCPServer */
  9,	/* TCPSocket */
  9,	/* T_INTEGER */
  9,	/* T_ISTRUCT */
  9,	/* TrueClass */
  9,	/* TypeError */
  9,	/* UDPSocket */
  9,	/* __ary_cmp */
  9,	/* __outer__ */
  9,	/* _allocate */
  9,	/* _gc_root_ */
  9,	/* _read_buf */
  9,	/* _readchar */
  9,	/* _recvfrom */
  9,	/* _sysclose */
  9,	/* _to_array */
  9,	/* ancestors */
  9,	/* backtrace */
  9,	/* base_path */
  9,	/* bind_call */
  9,	/* byteslice */
  9,	/* canonname */
  9,	/* conjugate */
  9,	/* const_get */
  9,	/* const_set */
  9,	/* constants */
  9,	/* delete_at */
  9,	/* delete_if */
  9,	/* downcase! */
  9,	/* each_byte */
  9,	/* each_char */
  9,	/* each_cons */
  9,	/* each_line */
  9,	/* each_pair */
  9,	/* end_with? */
  9,	/* exception */
  9,	/* exclusive */
  9,	/* feedvalue */
  9,	/* imaginary */
  9,	/* infinite? */
  9,	/* inherited */
  9,	/* ip_unpack */
  9,	/* iterator? */
  9,	/* localtime */
  9,	/* magnitude */
  9,	/* minmax_by */
  9,	/* negative? */
  9,	/* numerator */
  9,	/* object_id */
  9,	/* partition */
  9,	/* positive? */
  9,	/* prepended */
  9,	/* protected */
  9,	/* readlines */
  9,	/* satisfied */
  9,	/* saturday? */
  9,	/* separator */
  9,	/* swapcase! */
  9,	/* sysaccept */
  9,	/* thursday? */
  9,	/* transpose */
  9,	/* unix_path */
  9,	/* validated */
  9,	/* values_at */
  10,	/* @canonname */
  10,	/* @feedvalue */
  10,	/* @lookahead */
  10,	/* AI_PASSIVE */
  10,	/* Comparable */
  10,	/* Enumerable */
  10,	/* Enumerator */
  10,	/* FalseClass */
  10,	/* FiberError */
  10,	/* IPPROTO_AH */
  10,	/* IPPROTO_IP */
  10,	/* IP_HDRINCL */
  10,	/* IP_OPTIONS */
  10,	/* IP_PKTINFO */
  10,	/* IP_RECVTOS */
  10,	/* IP_RECVTTL */
  10,	/* IP_RETOPTS */
  10,	/* IndexError */
  10,	/* MAX_10_EXP */
  10,	/* MIN_10_EXP */
  10,	/* MSG_CTRUNC */
  10,	/* NI_MAXHOST */
  10,	/* NI_MAXSERV */
  10,	/* RangeError */
  10,	/* SOCK_DGRAM */
  10,	/* SOL_SOCKET */
  10,	/* TCP_MAXSEG */
  10,	/* UNIXServer */
  10,	/* UNIXSocket */
  10,	/* __case_eqq */
  10,	/* __method__ */
  10,	/* capitalize */
  10,	/* class_eval */
  10,	/* class_exec */
  10,	/* codepoints */
  10,	/* difference */
  10,	/* directory? */
  10,	/* drop_while */
  10,	/* each_index */
  10,	/* each_slice */
  10,	/* each_value */
  10,	/* fd_or_path */
  10,	/* filter_map */
  10,	/* find_index */
  10,	/* getaddress */
  10,	/* getpeereid */
  10,	/* getsockopt */
  10,	/* given_args */
  10,	/* has_value? */
  10,	/* initialize */
  10,	/* ip_address */
  10,	/* local_host */
  10,	/* make_curry */
  10,	/* parameters */
  10,	/* path_token */
  10,	/* recur_list */
  10,	/* rpartition */
  10,	/* self_arity */
  10,	/* setsockopt */
  10,	/* socketpair */
  10,	/* step_ratio */
  10,	/* superclass */
  10,	/* take_while */
  10,	/* wednesday? */
  10,	/* with_index */
  10,	/* yield_self */
  11,	/* BasicObject */
  11,	/* BasicSocket */
  11,	/* DomainError */
  11,	/* FNM_SYSCASE */
  11,	/* FrozenError */
  11,	/* IPPROTO_ESP */
  11,	/* IPPROTO_RAW */
  11,	/* IPPROTO_TCP */
  11,	/* IPPROTO_UDP */
  11,	/* IPV6_V6ONLY */
  11,	/* IP_MSFILTER */
  11,	/* IP_RECVOPTS */
  11,	/* MSG_WAITALL */
  11,	/* NI_NAMEREQD */
  11,	/* ObjectSpace */
  11,	/* RUBY_ENGINE */
  11,	/* RegexpError */
  11,	/* SOCK_STREAM */
  11,	/* SO_RCVLOWAT */
  11,	/* SO_RCVTIMEO */
  11,	/* SO_SNDLOWAT */
  11,	/* SO_SNDTIMEO */
  11,	/* ScriptError */
  11,	/* SocketError */
  11,	/* SyntaxError */
  11,	/* TCP_KEEPCNT */
  11,	/* TCP_NODELAY */
  11,	/* T_EXCEPTION */
  11,	/* __ary_index */
  11,	/* __members__ */
  11,	/* _is_socket= */
  11,	/* _recur_list */
  11,	/* attr_reader */
  11,	/* attr_writer */
  11,	/* capitalize! */
  11,	/* close_write */
  11,	/* combination */
  11,	/* default_dir */
  11,	/* denominator */
  11,	/* each_object */
  11,	/* expand_path */
  11,	/* getaddrinfo */
  11,	/* gethostname */
  11,	/* getnameinfo */
  11,	/* getpeername */
  11,	/* getsockname */
  11,	/* module_eval */
  11,	/* module_exec */
  11,	/* next_values */
  11,	/* peek_values */
  11,	/* permutation */
  11,	/* rectangular */
  11,	/* respond_to? */
  11,	/* sockaddr_in */
  11,	/* sockaddr_un */
  11,	/* start_with? */
  11,	/* step_ratio= */
  11,	/* to_sockaddr */
  11,	/* with_object */
  12,	/* AI_CANONNAME */
  12,	/* FNM_CASEFOLD */
  12,	/* FNM_DOTMATCH */
  12,	/* FNM_NOESCAPE */
  12,	/* FNM_PATHNAME */
  12,	/* IPPROTO_ICMP */
  12,	/* IPPROTO_IPV6 */
  12,	/* IPPROTO_NONE */
  12,	/* MSG_DONTWAIT */
  12,	/* MSG_NOSIGNAL */
  12,	/* RUBY_VERSION */
  12,	/* RuntimeError */
  12,	/* SHARE_DELETE */
  12,	/* SO_BROADCAST */
  12,	/* SO_DONTROUTE */
  12,	/* SO_KEEPALIVE */
  12,	/* SO_NOSIGPIPE */
  12,	/* SO_OOBINLINE */
  12,	/* SO_REUSEADDR */
  12,	/* SO_REUSEPORT */
  12,	/* SO_TIMESTAMP */
  12,	/* __attached__ */
  12,	/* __printstr__ */
  12,	/* _concat_path */
  12,	/* _setnonblock */
  12,	/* _sockaddr_in */
  12,	/* alias_method */
  12,	/* block_given? */
  12,	/* column_count */
  12,	/* column_index */
  12,	/* default_proc */
  12,	/* drive_prefix */
  12,	/* exclude_end? */
  12,	/* fetch_values */
  12,	/* instance_of? */
  12,	/* intersection */
  12,	/* remove_const */
  12,	/* reverse_each */
  12,	/* super_method */
  12,	/* undef_method */
  13,	/* @init_with_fd */
  13,	/* ALT_SEPARATOR */
  13,	/* ArgumentError */
  13,	/* MRUBY_VERSION */
  13,	/* MSG_DONTROUTE */
  13,	/* NoMemoryError */
  13,	/* NoMethodError */
  13,	/* StandardError */
  13,	/* StopIteration */
  13,	/* TCP_KEEPINTVL */
  13,	/* UnboundMethod */
  13,	/* __classname__ */
  13,	/* __sub_replace */
  13,	/* __update_hash */
  13,	/* attr_accessor */
  13,	/* bsearch_index */
  13,	/* const_missing */
  13,	/* count_objects */
  13,	/* default_proc= */
  13,	/* define_method */
  13,	/* delete_prefix */
  13,	/* delete_suffix */
  13,	/* expanded_path */
  13,	/* extend_object */
  13,	/* in_lower_half */
  13,	/* instance_eval */
  13,	/* instance_exec */
  13,	/* local_address */
  13,	/* local_service */
  13,	/* recv_nonblock */
  13,	/* remove_method */
  13,	/* set_backtrace */
  13,	/* splitted_path */
  14,	/* AI_NUMERICHOST */
  14,	/* AI_NUMERICSERV */
  14,	/* IPPROTO_ICMPV6 */
  14,	/* IP_RECVDSTADDR */
  14,	/* IP_RECVRETOPTS */
  14,	/* LocalJumpError */
  14,	/* NI_NUMERICHOST */
  14,	/* NI_NUMERICSERV */
  14,	/* PATH_SEPARATOR */
  14,	/* SOCK_SEQPACKET */
  14,	/* __upto_endless */
  14,	/* close_on_exec= */
  14,	/* close_on_exec? */
  14,	/* collect_concat */
  14,	/* const_defined? */
  14,	/* delete_prefix! */
  14,	/* delete_suffix! */
  14,	/* each_codepoint */
  14,	/* interval_ratio */
  14,	/* method_missing */
  14,	/* method_removed */
  14,	/* paragraph_mode */
  14,	/* public_methods */
  14,	/* remote_address */
  14,	/* transform_keys */
  15,	/* IPPROTO_DSTOPTS */
  15,	/* IPPROTO_ROUTING */
  15,	/* IPV6_JOIN_GROUP */
  15,	/* IP_BLOCK_SOURCE */
  15,	/* IP_IPSEC_POLICY */
  15,	/* IP_MULTICAST_IF */
  15,	/* MRUBY_COPYRIGHT */
  15,	/* _check_readable */
  15,	/* accept_nonblock */
  15,	/* append_features */
  15,	/* class_variables */
  15,	/* each_with_index */
  15,	/* initialize_copy */
  15,	/* instance_method */
  15,	/* interval_ratio= */
  15,	/* local_variables */
  15,	/* method_defined? */
  15,	/* module_function */
  15,	/* pad_repetitions */
  15,	/* private_methods */
  15,	/* singleton_class */
  15,	/* source_location */
  15,	/* transform_keys! */
  16,	/* FloatDomainError */
  16,	/* IPPROTO_FRAGMENT */
  16,	/* IPV6_LEAVE_GROUP */
  16,	/* IP_MULTICAST_TTL */
  16,	/* MCAST_JOIN_GROUP */
  16,	/* MRUBY_RELEASE_NO */
  16,	/* SystemStackError */
  16,	/* _sockaddr_family */
  16,	/* connect_nonblock */
  16,	/* each_with_object */
  16,	/* global_variables */
  16,	/* included_modules */
  16,	/* inspect_sockaddr */
  16,	/* instance_methods */
  16,	/* new_with_prelude */
  16,	/* pack_sockaddr_in */
  16,	/* pack_sockaddr_un */
  16,	/* prepend_features */
  16,	/* singleton_class? */
  16,	/* singleton_method */
  16,	/* transform_values */
  17,	/* IPV6_MULTICAST_IF */
  17,	/* IPV6_UNICAST_HOPS */
  17,	/* IP_ADD_MEMBERSHIP */
  17,	/* IP_MULTICAST_LOOP */
  17,	/* IP_UNBLOCK_SOURCE */
  17,	/* MCAST_LEAVE_GROUP */
  17,	/* MRUBY_DESCRIPTION */
  17,	/* ZeroDivisionError */
  17,	/* expand_path_array */
  17,	/* generational_mode */
  17,	/* protected_methods */
  17,	/* recvfrom_nonblock */
  17,	/* singleton_methods */
  17,	/* transform_values! */
  18,	/* IP_DROP_MEMBERSHIP */
  18,	/* MCAST_BLOCK_SOURCE */
  18,	/* MRUBY_RELEASE_DATE */
  18,	/* class_variable_get */
  18,	/* class_variable_set */
  18,	/* generational_mode= */
  18,	/* instance_variables */
  18,	/* unpack_sockaddr_in */
  18,	/* unpack_sockaddr_un */
  19,	/* IPV6_MULTICAST_HOPS */
  19,	/* IPV6_MULTICAST_LOOP */
  19,	/* NotImplementedError */
  19,	/* RUBY_ENGINE_VERSION */
  19,	/* respond_to_missing? */
  20,	/* MCAST_UNBLOCK_SOURCE */
  21,	/* __coerce_step_counter */
  21,	/* do_not_reverse_lookup */
  21,	/* enumerator_block_call */
  21,	/* instance_variable_get */
  21,	/* instance_variable_set */
  21,	/* remove_class_variable */
  22,	/* @do_not_reverse_lookup */
  22,	/* do_not_reverse_lookup= */
  22,	/* original_operator_name */
  23,	/* @@do_not_reverse_lookup */
  23,	/* MCAST_JOIN_SOURCE_GROUP */
  23,	/* class_variable_defined? */
  23,	/* define_singleton_method */
  24,	/* IP_ADD_SOURCE_MEMBERSHIP */
  24,	/* MCAST_LEAVE_SOURCE_GROUP */
  24,	/* remove_instance_variable */
  25,	/* IP_DROP_SOURCE_MEMBERSHIP */
  26,	/* instance_variable_defined? */
  31,	/* should_yield_subclass_instances */
};

static const char * const presym_name_table[] = {
  "!",
  "%",
  "&",
  "*",
  "+",
  "-",
  "/",
  "<",
  ">",
  "E",
  "^",
  "`",
  "a",
  "b",
  "c",
  "e",
  "f",
  "h",
  "i",
  "j",
  "k",
  "l",
  "m",
  "n",
  "o",
  "p",
  "r",
  "s",
  "t",
  "v",
  "w",
  "x",
  "y",
  "z",
  "|",
  "~",
  "!=",
  "!~",
  "$0",
  "$?",
  "&&",
  "**",
  "+@",
  "-@",
  "<<",
  "<=",
  "==",
  "=~",
  ">=",
  ">>",
  "GC",
  "IO",
  "PI",
  "[]",
  "af",
  "ai",
  "ar",
  "at",
  "bi",
  "bs",
  "e0",
  "e2",
  "e3",
  "ed",
  "ei",
  "fd",
  "gm",
  "in",
  "io",
  "ip",
  "lz",
  "nk",
  "nv",
  "op",
  "rs",
  "sa",
  "sc",
  "sv",
  "tr",
  "vs",
  "||",
  "<=>",
  "===",
  "@af",
  "DIG",
  "MAX",
  "MIN",
  "NAN",
  "[]=",
  "abs",
  "arg",
  "ary",
  "beg",
  "blk",
  "buf",
  "chr",
  "cls",
  "cmd",
  "cmp",
  "cos",
  "day",
  "dig",
  "dir",
  "div",
  "dup",
  "end",
  "eof",
  "erf",
  "err",
  "exp",
  "fib",
  "hex",
  "idx",
  "int",
  "ip?",
  "key",
  "len",
  "lhs",
  "lim",
  "log",
  "low",
  "map",
  "max",
  "mid",
  "min",
  "mon",
  "msg",
  "new",
  "now",
  "num",
  "obj",
  "oct",
  "opt",
  "ord",
  "out",
  "pat",
  "pid",
  "pop",
  "pos",
  "pre",
  "quo",
  "req",
  "res",
  "ret",
  "rhs",
  "row",
  "sec",
  "sep",
  "sin",
  "str",
  "sub",
  "sym",
  "tan",
  "tap",
  "tcp",
  "tmp",
  "tr!",
  "udp",
  "utc",
  "val",
  "zip",
  "@buf",
  "@dst",
  "@fib",
  "@obj",
  "ARGV",
  "EXCL",
  "FREE",
  "File",
  "Hash",
  "Lazy",
  "Math",
  "NONE",
  "NULL",
  "Proc",
  "RDWR",
  "SYNC",
  "Time",
  "_new",
  "abs2",
  "acos",
  "addr",
  "all?",
  "any?",
  "arg0",
  "arg1",
  "arg2",
  "args",
  "argv",
  "asin",
  "atan",
  "attr",
  "bind",
  "bool",
  "call",
  "cbrt",
  "ceil",
  "char",
  "chop",
  "conj",
  "cosh",
  "curr",
  "data",
  "drop",
  "dst?",
  "dump",
  "each",
  "elem",
  "eof?",
  "epos",
  "eql?",
  "erfc",
  "eval",
  "fail",
  "fdiv",
  "feed",
  "file",
  "fill",
  "find",
  "flag",
  "getc",
  "gets",
  "gmt?",
  "grep",
  "gsub",
  "hash",
  "high",
  "host",
  "hour",
  "idx2",
  "imag",
  "init",
  "join",
  "key?",
  "keys",
  "lary",
  "last",
  "lazy",
  "left",
  "lidx",
  "line",
  "log2",
  "loop",
  "lval",
  "map!",
  "mday",
  "mesg",
  "meth",
  "mode",
  "name",
  "nan?",
  "next",
  "nil?",
  "none",
  "ntop",
  "obj=",
  "one?",
  "open",
  "opts",
  "orig",
  "pack",
  "pair",
  "path",
  "peek",
  "perm",
  "pipe",
  "plen",
  "port",
  "pos=",
  "post",
  "proc",
  "pton",
  "push",
  "puts",
  "rand",
  "read",
  "real",
  "rect",
  "recv",
  "rest",
  "ridx",
  "rval",
  "sary",
  "seek",
  "send",
  "sinh",
  "size",
  "sock",
  "sort",
  "sqrt",
  "step",
  "str2",
  "sub!",
  "succ",
  "sync",
  "take",
  "tanh",
  "tell",
  "then",
  "this",
  "to_a",
  "to_c",
  "to_f",
  "to_h",
  "to_i",
  "to_r",
  "to_s",
  "tr_s",
  "tty?",
  "type",
  "uniq",
  "unix",
  "upto",
  "usec",
  "user",
  "utc?",
  "vals",
  "warn",
  "wday",
  "yday",
  "year",
  "zone",
  "@args",
  "@data",
  "@meth",
  "@name",
  "@path",
  "@proc",
  "Array",
  "CREAT",
  "Class",
  "DSYNC",
  "Fiber",
  "Float",
  "RADIX",
  "RSYNC",
  "Range",
  "STDIN",
  "TOTAL",
  "TRUNC",
  "T_ENV",
  "_bind",
  "_name",
  "_pipe",
  "_proc",
  "_recv",
  "acosh",
  "angle",
  "args=",
  "arity",
  "array",
  "ary_F",
  "ary_T",
  "asinh",
  "assoc",
  "atan2",
  "atanh",
  "begin",
  "block",
  "bytes",
  "chars",
  "chmod",
  "chomp",
  "chop!",
  "clamp",
  "class",
  "clear",
  "clone",
  "close",
  "count",
  "ctime",
  "curry",
  "cycle",
  "depth",
  "enums",
  "fetch",
  "field",
  "file?",
  "first",
  "flags",
  "flock",
  "floor",
  "flush",
  "fname",
  "force",
  "found",
  "frexp",
  "getgm",
  "gsub!",
  "hypot",
  "index",
  "ipv4?",
  "ipv6?",
  "is_a?",
  "ldexp",
  "level",
  "limit",
  "lines",
  "ljust",
  "local",
  "log10",
  "lsize",
  "merge",
  "meth=",
  "month",
  "mtime",
  "names",
  "next!",
  "none?",
  "other",
  "owner",
  "phase",
  "pipe?",
  "polar",
  "popen",
  "pproc",
  "pread",
  "print",
  "proto",
  "raise",
  "real?",
  "right",
  "rjust",
  "round",
  "shift",
  "size?",
  "slice",
  "sort!",
  "split",
  "srand",
  "stack",
  "start",
  "state",
  "store",
  "strip",
  "succ!",
  "sync=",
  "taken",
  "tally",
  "times",
  "tr_s!",
  "umask",
  "union",
  "uniq!",
  "unix?",
  "value",
  "write",
  "yield",
  "zero?",
  "$DEBUG",
  "$stdin",
  "@level",
  "AF_MAX",
  "APPEND",
  "BINARY",
  "DIRECT",
  "Fixnum",
  "IP_TOS",
  "IP_TTL",
  "Kernel",
  "Method",
  "Module",
  "NOCTTY",
  "Object",
  "Option",
  "RDONLY",
  "Random",
  "Regexp",
  "STDERR",
  "STDOUT",
  "Socket",
  "Status",
  "String",
  "Struct",
  "Symbol",
  "T_CPTR",
  "T_DATA",
  "T_FREE",
  "T_HASH",
  "T_PROC",
  "T_TRUE",
  "WRONLY",
  "__id__",
  "_getwd",
  "_klass",
  "_mtime",
  "_owner",
  "_popen",
  "accept",
  "alive?",
  "append",
  "caller",
  "chomp!",
  "concat",
  "cover?",
  "delete",
  "detect",
  "divmod",
  "domain",
  "downto",
  "empty?",
  "enable",
  "equal?",
  "except",
  "exist?",
  "extend",
  "family",
  "fileno",
  "filter",
  "for_fd",
  "format",
  "freeze",
  "getutc",
  "gmtime",
  "ifnone",
  "inject",
  "insert",
  "intern",
  "invert",
  "isatty",
  "itself",
  "lambda",
  "length",
  "linger",
  "listen",
  "lstrip",
  "max_by",
  "maxlen",
  "merge!",
  "method",
  "min_by",
  "minmax",
  "mktime",
  "object",
  "offset",
  "outbuf",
  "padstr",
  "printf",
  "public",
  "pwrite",
  "rassoc",
  "reduce",
  "rehash",
  "reject",
  "rename",
  "result",
  "resume",
  "rewind",
  "rindex",
  "rotate",
  "rstrip",
  "sample",
  "select",
  "slice!",
  "string",
  "strip!",
  "substr",
  "to_int",
  "to_str",
  "to_sym",
  "unbind",
  "ungetc",
  "unlink",
  "unpack",
  "upcase",
  "update",
  "value?",
  "values",
  "whence",
  "$stderr",
  "$stdout",
  "@family",
  "AF_INET",
  "AF_LINK",
  "AF_UNIX",
  "Complex",
  "DEFAULT",
  "EPSILON",
  "IOError",
  "Integer",
  "LOCK_EX",
  "LOCK_NB",
  "LOCK_SH",
  "LOCK_UN",
  "MAX_EXP",
  "MIN_EXP",
  "MSG_EOR",
  "MSG_OOB",
  "NOATIME",
  "Numeric",
  "PF_INET",
  "PF_LINK",
  "PF_UNIX",
  "Process",
  "SHUT_RD",
  "SHUT_WR",
  "SO_TYPE",
  "TMPFILE",
  "T_ARRAY",
  "T_CLASS",
  "T_FALSE",
  "T_FIBER",
  "T_FLOAT",
  "T_RANGE",
  "T_UNDEF",
  "Yielder",
  "__div__",
  "__lines",
  "_accept",
  "_lastai",
  "_listen",
  "_socket",
  "afamily",
  "asctime",
  "backlog",
  "bsearch",
  "casecmp",
  "closed?",
  "collect",
  "command",
  "compact",
  "compile",
  "connect",
  "consume",
  "current",
  "default",
  "delete!",
  "dirname",
  "disable",
  "dropped",
  "entries",
  "exists?",
  "extname",
  "filter!",
  "finite?",
  "flatten",
  "foreach",
  "friday?",
  "frozen?",
  "getbyte",
  "id2name",
  "include",
  "inspect",
  "integer",
  "ip_port",
  "keep_if",
  "keyrest",
  "lambda?",
  "lstrip!",
  "max_cmp",
  "member?",
  "members",
  "message",
  "methods",
  "min_cmp",
  "modules",
  "monday?",
  "nesting",
  "new_key",
  "nobits?",
  "numeric",
  "optname",
  "padding",
  "pattern",
  "pfamily",
  "pointer",
  "prepend",
  "private",
  "produce",
  "reject!",
  "replace",
  "result=",
  "reverse",
  "rotate!",
  "rstrip!",
  "select!",
  "sep_len",
  "service",
  "setbyte",
  "shuffle",
  "socket?",
  "sort_by",
  "sprintf",
  "squeeze",
  "sunday?",
  "symlink",
  "sysopen",
  "sysread",
  "sysseek",
  "to_enum",
  "to_path",
  "to_proc",
  "unpack1",
  "unshift",
  "upcase!",
  "yielder",
  "@optname",
  "AF_INET6",
  "AF_LOCAL",
  "AF_ROUTE",
  "Addrinfo",
  "BUF_SIZE",
  "EOFError",
  "FileTest",
  "INFINITY",
  "IPSocket",
  "KeyError",
  "MANT_DIG",
  "MSG_PEEK",
  "NI_DGRAM",
  "NOFOLLOW",
  "NONBLOCK",
  "NilClass",
  "PF_INET6",
  "PF_LOCAL",
  "PF_ROUTE",
  "Rational",
  "SEEK_CUR",
  "SEEK_END",
  "SEEK_SET",
  "SOCK_RAW",
  "SO_DEBUG",
  "SO_ERROR",
  "T_ICLASS",
  "T_MODULE",
  "T_OBJECT",
  "T_SCLASS",
  "T_STRING",
  "T_SYMBOL",
  "__ary_eq",
  "__delete",
  "__send__",
  "__svalue",
  "__to_int",
  "__to_str",
  "_accept2",
  "_bufread",
  "_connect",
  "_gethome",
  "_inspect",
  "allbits?",
  "allocate",
  "anybits?",
  "basename",
  "between?",
  "bytesize",
  "casecmp?",
  "collect!",
  "compact!",
  "default=",
  "downcase",
  "dropping",
  "each_key",
  "enum_for",
  "extended",
  "filename",
  "find_all",
  "flat_map",
  "flatten!",
  "getlocal",
  "group_by",
  "has_key?",
  "home_dir",
  "include?",
  "included",
  "kind_of?",
  "modified",
  "new_args",
  "nodename",
  "nonzero?",
  "peeraddr",
  "protocol",
  "readchar",
  "readline",
  "readlink",
  "realpath",
  "receiver",
  "recvfrom",
  "reverse!",
  "self_len",
  "servname",
  "shuffle!",
  "shutdown",
  "sockaddr",
  "socktype",
  "squeeze!",
  "str_each",
  "swapcase",
  "symlink?",
  "syswrite",
  "template",
  "transfer",
  "truncate",
  "tuesday?",
  "@hostname",
  "@protocol",
  "@sockaddr",
  "@socktype",
  "@stop_exc",
  "AF_UNSPEC",
  "Constants",
  "Exception",
  "Generator",
  "MSG_TRUNC",
  "NI_NOFQDN",
  "NameError",
  "PF_UNSPEC",
  "SEPARATOR",
  "SHUT_RDWR",
  "SO_LINGER",
  "SO_RCVBUF",
  "SO_SNDBUF",
  "TCPServer",
  "TCPSocket",
  "T_INTEGER",
  "T_ISTRUCT",
  "TrueClass",
  "TypeError",
  "UDPSocket",
  "__ary_cmp",
  "__outer__",
  "_allocate",
  "_gc_root_",
  "_read_buf",
  "_readchar",
  "_recvfrom",
  "_sysclose",
  "_to_array",
  "ancestors",
  "backtrace",
  "base_path",
  "bind_call",
  "byteslice",
  "canonname",
  "conjugate",
  "const_get",
  "const_set",
  "constants",
  "delete_at",
  "delete_if",
  "downcase!",
  "each_byte",
  "each_char",
  "each_cons",
  "each_line",
  "each_pair",
  "end_with?",
  "exception",
  "exclusive",
  "feedvalue",
  "imaginary",
  "infinite?",
  "inherited",
  "ip_unpack",
  "iterator?",
  "localtime",
  "magnitude",
  "minmax_by",
  "negative?",
  "numerator",
  "object_id",
  "partition",
  "positive?",
  "prepended",
  "protected",
  "readlines",
  "satisfied",
  "saturday?",
  "separator",
  "swapcase!",
  "sysaccept",
  "thursday?",
  "transpose",
  "unix_path",
  "validated",
  "values_at",
  "@canonname",
  "@feedvalue",
  "@lookahead",
  "AI_PASSIVE",
  "Comparable",
  "Enumerable",
  "Enumerator",
  "FalseClass",
  "FiberError",
  "IPPROTO_AH",
  "IPPROTO_IP",
  "IP_HDRINCL",
  "IP_OPTIONS",
  "IP_PKTINFO",
  "IP_RECVTOS",
  "IP_RECVTTL",
  "IP_RETOPTS",
  "IndexError",
  "MAX_10_EXP",
  "MIN_10_EXP",
  "MSG_CTRUNC",
  "NI_MAXHOST",
  "NI_MAXSERV",
  "RangeError",
  "SOCK_DGRAM",
  "SOL_SOCKET",
  "TCP_MAXSEG",
  "UNIXServer",
  "UNIXSocket",
  "__case_eqq",
  "__method__",
  "capitalize",
  "class_eval",
  "class_exec",
  "codepoints",
  "difference",
  "directory?",
  "drop_while",
  "each_index",
  "each_slice",
  "each_value",
  "fd_or_path",
  "filter_map",
  "find_index",
  "getaddress",
  "getpeereid",
  "getsockopt",
  "given_args",
  "has_value?",
  "initialize",
  "ip_address",
  "local_host",
  "make_curry",
  "parameters",
  "path_token",
  "recur_list",
  "rpartition",
  "self_arity",
  "setsockopt",
  "socketpair",
  "step_ratio",
  "superclass",
  "take_while",
  "wednesday?",
  "with_index",
  "yield_self",
  "BasicObject",
  "BasicSocket",
  "DomainError",
  "FNM_SYSCASE",
  "FrozenError",
  "IPPROTO_ESP",
  "IPPROTO_RAW",
  "IPPROTO_TCP",
  "IPPROTO_UDP",
  "IPV6_V6ONLY",
  "IP_MSFILTER",
  "IP_RECVOPTS",
  "MSG_WAITALL",
  "NI_NAMEREQD",
  "ObjectSpace",
  "RUBY_ENGINE",
  "RegexpError",
  "SOCK_STREAM",
  "SO_RCVLOWAT",
  "SO_RCVTIMEO",
  "SO_SNDLOWAT",
  "SO_SNDTIMEO",
  "ScriptError",
  "SocketError",
  "SyntaxError",
  "TCP_KEEPCNT",
  "TCP_NODELAY",
  "T_EXCEPTION",
  "__ary_index",
  "__members__",
  "_is_socket=",
  "_recur_list",
  "attr_reader",
  "attr_writer",
  "capitalize!",
  "close_write",
  "combination",
  "default_dir",
  "denominator",
  "each_object",
  "expand_path",
  "getaddrinfo",
  "gethostname",
  "getnameinfo",
  "getpeername",
  "getsockname",
  "module_eval",
  "module_exec",
  "next_values",
  "peek_values",
  "permutation",
  "rectangular",
  "respond_to?",
  "sockaddr_in",
  "sockaddr_un",
  "start_with?",
  "step_ratio=",
  "to_sockaddr",
  "with_object",
  "AI_CANONNAME",
  "FNM_CASEFOLD",
  "FNM_DOTMATCH",
  "FNM_NOESCAPE",
  "FNM_PATHNAME",
  "IPPROTO_ICMP",
  "IPPROTO_IPV6",
  "IPPROTO_NONE",
  "MSG_DONTWAIT",
  "MSG_NOSIGNAL",
  "RUBY_VERSION",
  "RuntimeError",
  "SHARE_DELETE",
  "SO_BROADCAST",
  "SO_DONTROUTE",
  "SO_KEEPALIVE",
  "SO_NOSIGPIPE",
  "SO_OOBINLINE",
  "SO_REUSEADDR",
  "SO_REUSEPORT",
  "SO_TIMESTAMP",
  "__attached__",
  "__printstr__",
  "_concat_path",
  "_setnonblock",
  "_sockaddr_in",
  "alias_method",
  "block_given?",
  "column_count",
  "column_index",
  "default_proc",
  "drive_prefix",
  "exclude_end?",
  "fetch_values",
  "instance_of?",
  "intersection",
  "remove_const",
  "reverse_each",
  "super_method",
  "undef_method",
  "@init_with_fd",
  "ALT_SEPARATOR",
  "ArgumentError",
  "MRUBY_VERSION",
  "MSG_DONTROUTE",
  "NoMemoryError",
  "NoMethodError",
  "StandardError",
  "StopIteration",
  "TCP_KEEPINTVL",
  "UnboundMethod",
  "__classname__",
  "__sub_replace",
  "__update_hash",
  "attr_accessor",
  "bsearch_index",
  "const_missing",
  "count_objects",
  "default_proc=",
  "define_method",
  "delete_prefix",
  "delete_suffix",
  "expanded_path",
  "extend_object",
  "in_lower_half",
  "instance_eval",
  "instance_exec",
  "local_address",
  "local_service",
  "recv_nonblock",
  "remove_method",
  "set_backtrace",
  "splitted_path",
  "AI_NUMERICHOST",
  "AI_NUMERICSERV",
  "IPPROTO_ICMPV6",
  "IP_RECVDSTADDR",
  "IP_RECVRETOPTS",
  "LocalJumpError",
  "NI_NUMERICHOST",
  "NI_NUMERICSERV",
  "PATH_SEPARATOR",
  "SOCK_SEQPACKET",
  "__upto_endless",
  "close_on_exec=",
  "close_on_exec?",
  "collect_concat",
  "const_defined?",
  "delete_prefix!",
  "delete_suffix!",
  "each_codepoint",
  "interval_ratio",
  "method_missing",
  "method_removed",
  "paragraph_mode",
  "public_methods",
  "remote_address",
  "transform_keys",
  "IPPROTO_DSTOPTS",
  "IPPROTO_ROUTING",
  "IPV6_JOIN_GROUP",
  "IP_BLOCK_SOURCE",
  "IP_IPSEC_POLICY",
  "IP_MULTICAST_IF",
  "MRUBY_COPYRIGHT",
  "_check_readable",
  "accept_nonblock",
  "append_features",
  "class_variables",
  "each_with_index",
  "initialize_copy",
  "instance_method",
  "interval_ratio=",
  "local_variables",
  "method_defined?",
  "module_function",
  "pad_repetitions",
  "private_methods",
  "singleton_class",
  "source_location",
  "transform_keys!",
  "FloatDomainError",
  "IPPROTO_FRAGMENT",
  "IPV6_LEAVE_GROUP",
  "IP_MULTICAST_TTL",
  "MCAST_JOIN_GROUP",
  "MRUBY_RELEASE_NO",
  "SystemStackError",
  "_sockaddr_family",
  "connect_nonblock",
  "each_with_object",
  "global_variables",
  "included_modules",
  "inspect_sockaddr",
  "instance_methods",
  "new_with_prelude",
  "pack_sockaddr_in",
  "pack_sockaddr_un",
  "prepend_features",
  "singleton_class?",
  "singleton_method",
  "transform_values",
  "IPV6_MULTICAST_IF",
  "IPV6_UNICAST_HOPS",
  "IP_ADD_MEMBERSHIP",
  "IP_MULTICAST_LOOP",
  "IP_UNBLOCK_SOURCE",
  "MCAST_LEAVE_GROUP",
  "MRUBY_DESCRIPTION",
  "ZeroDivisionError",
  "expand_path_array",
  "generational_mode",
  "protected_methods",
  "recvfrom_nonblock",
  "singleton_methods",
  "transform_values!",
  "IP_DROP_MEMBERSHIP",
  "MCAST_BLOCK_SOURCE",
  "MRUBY_RELEASE_DATE",
  "class_variable_get",
  "class_variable_set",
  "generational_mode=",
  "instance_variables",
  "unpack_sockaddr_in",
  "unpack_sockaddr_un",
  "IPV6_MULTICAST_HOPS",
  "IPV6_MULTICAST_LOOP",
  "NotImplementedError",
  "RUBY_ENGINE_VERSION",
  "respond_to_missing?",
  "MCAST_UNBLOCK_SOURCE",
  "__coerce_step_counter",
  "do_not_reverse_lookup",
  "enumerator_block_call",
  "instance_variable_get",
  "instance_variable_set",
  "remove_class_variable",
  "@do_not_reverse_lookup",
  "do_not_reverse_lookup=",
  "original_operator_name",
  "@@do_not_reverse_lookup",
  "MCAST_JOIN_SOURCE_GROUP",
  "class_variable_defined?",
  "define_singleton_method",
  "IP_ADD_SOURCE_MEMBERSHIP",
  "MCAST_LEAVE_SOURCE_GROUP",
  "remove_instance_variable",
  "IP_DROP_SOURCE_MEMBERSHIP",
  "instance_variable_defined?",
  "should_yield_subclass_instances",
};
