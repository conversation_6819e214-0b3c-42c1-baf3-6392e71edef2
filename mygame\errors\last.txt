* EXCEPTION: stack level too deep
** Backtrace:
*** app/entities.rb:17:in render
*** app/main.rb:50:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick