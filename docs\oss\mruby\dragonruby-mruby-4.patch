commit 02bf631b5b152c75caf4a70b4efe520bd7a862d5
Author: <PERSON> <<EMAIL>>
Date:   Thu Jun 20 15:26:09 2024 -0500

    [mRuby] compatible changes made to gc.c in 3.3 applied.

    - <https://github.com/mruby/mruby/commit/520df805a97989018d17e02c78ed8cb1c6f27fa1>
    - <https://github.com/mruby/mruby/commit/87c9bd0754589042e21d89b34f6af351aebb8f97>
    - <https://github.com/mruby/mruby/commit/2eefee0d6a1b25963b5550ce920de9e832675aa1>
    - <https://github.com/mruby/mruby/commit/8819183088b391185723582ae93cbe534437e04e>

diff --git a/mruby/src/gc.c b/mruby/src/gc.c
index 98a2237d5..00b120570 100644
--- a/mruby/src/gc.c
+++ b/mruby/src/gc.c
@@ -308,7 +308,7 @@ heap_p(mrb_gc *gc, struct RBasic *object)
     RVALUE *p;

     p = objects(page);
-    if (&p[0].as.basic <= object && object <= &p[MRB_HEAP_PAGE_SIZE].as.basic) {
+    if (&p[0].as.basic <= object && object <= &p[MRB_HEAP_PAGE_SIZE - 1].as.basic) {
       return TRUE;
     }
     page = page->next;
@@ -471,7 +471,9 @@ MRB_API void
 mrb_gc_protect(mrb_state *mrb, mrb_value obj)
 {
   if (mrb_immediate_p(obj)) return;
-  gc_protect(mrb, &mrb->gc, mrb_basic_ptr(obj));
+  struct RBasic *p = mrb_basic_ptr(obj);
+  if (is_red(p)) return;
+  gc_protect(mrb, &mrb->gc, p);
 }

 #define GC_ROOT_SYM MRB_SYM(_gc_root_)
@@ -1346,8 +1348,10 @@ mrb_field_write_barrier(mrb_state *mrb, struct RBasic *obj, struct RBasic *value
 {
   mrb_gc *gc = &mrb->gc;

+  if (!value) return;
   if (!is_black(obj)) return;
   if (!is_white(value)) return;
+  if (is_red(value)) return;

   mrb_assert(gc->state == MRB_GC_STATE_MARK || (!is_dead(gc, value) && !is_dead(gc, obj)));
   mrb_assert(is_generational(gc) || gc->state != MRB_GC_STATE_ROOT);
