<?xml version="1.0" encoding="utf-8"?>
<!--
Do not edit this file!

If you want to add stuff to your app's Android manifest, if the file
 metadata/oculusquest-manifest-additions.xml exists in your game, its contents
 will be appended to this file to replace the $DRGTK_MANIFEST_ADDITIONS
 string. The settings in here are common to every game, and are mostly things
 that the engine itself needs to be set in order to properly function.

If you want to completely replace this file: if the file
 metadata/oculusquest-manifest.xml exists in your game it will be used in place
 of this file.

In either case, don't do it if you don't know what you're doing, and do so
 AT YOUR OWN RISK!
-->
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    android:versionCode="$DRGTK_VERSION_CODE"
    android:versionName="$DRGTK_VERSION"
    android:installLocation="auto"
    package="$DRGTK_PACKAGEID">

    <!-- This will increase later as necessary. -->
    <uses-sdk android:minSdkVersion="26" android:targetSdkVersion="29" />

    <!-- OpenGL ES 3.1 (this is more than generic Android DRGTK needs, which manages with GLES 2.0!) -->
    <uses-feature android:glEsVersion="0x00030001" android:required="true"/>

    <!-- obviously you want VR features.  :) -->
    <uses-feature android:name="android.hardware.vr.headtracking" android:required="true" />

    <!-- !!! FIXME: can we actually use bluetooth controllers through hidapi on the Quest?!?! -->
    <!-- Game controller support -->
    <uses-feature android:name="android.hardware.bluetooth" android:required="false" />
    <uses-feature android:name="android.hardware.gamepad" android:required="false" />
    <uses-feature android:name="android.hardware.usb.host" android:required="false" />

    <!-- !!! FIXME: Surely _this_ doesn't work, right? -->
    <!-- External mouse input events -->
    <uses-feature android:name="android.hardware.type.pc" android:required="false" />

    <!-- Audio recording support -->
    <!-- if you want to capture audio, uncomment this. -->
    <!-- <uses-feature android:name="android.hardware.microphone" android:required="false" /> -->

    <!-- Allow writing to external storage -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <!-- Allow access to Bluetooth devices -->
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <!-- Allow access to sockets (you need this on the Quest for OVRMonitor even if your app doesn't otherwise use networking!) -->
    <uses-permission android:name="android.permission.INTERNET" />

    <!-- Volume Control -->
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <!-- if you want to capture audio, uncomment this. -->
    <!-- <uses-permission android:name="android.permission.RECORD_AUDIO" /> -->

    <application
        android:label="@string/app_name"
        android:icon="@mipmap/ic_launcher"
        android:allowBackup="true"
        android:hardwareAccelerated="true">

        <meta-data android:name="com.samsung.android.vr.application.mode" android:value="vr_only"/>
        <meta-data android:name="com.oculus.supportedDevices" android:value="all" />

        <!-- Example of setting SDL hints from AndroidManifest.xml:
        <meta-data android:name="SDL_ENV.SDL_ACCELEROMETER_AS_JOYSTICK" android:value="0"/>
         -->
     
        <activity
            android:theme="@android:style/Theme.Black.NoTitleBar.Fullscreen"
            android:label="@string/app_name"
            android:name="$DRGTK_PACKAGEID.DragonRubyActivity"
            android:launchMode="singleTask"
            android:screenOrientation="landscape"
            android:excludeFromRecents="false"
            android:configChanges="screenSize|screenLayout|orientation|keyboardHidden|keyboard|navigation|uiMode">

            <!-- Indicate the activity is aware of VrApi focus states required for system overlays  -->
            <meta-data android:name="com.oculus.vr.focusaware" android:value="true"/>

            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
    </application>

    <!-- dragonruby-publish will replace this string with the contents of your
         game's metadata/oculusquest-manifest-additions.xml file, if it exists. -->

$DRGTK_MANIFEST_ADDITIONS

</manifest>

<!-- end of DragonRubyManifest.xml ... -->

