# * EXCEPTION:
# * ERROR - :alive? method missing on ~NilClass~.
# 
# #+begin_src ~NilClass#inspect~
# nil
# #+end_src ~NilClass#inspect~
# 
# 
# 
# The method named
# :alive?
# doesn't exist on [NilClass, NilClassFalseClass].
# 
# (suggestions above)
# ** Backtrace:
# *** app/main.rb:10:in update
# *** app/main.rb:57:in tick

Game State:
{:state=>{:tick_count=>38393, :anim=>#<Animation:0x21ecc081a30 @index=6, @commands=[[:frame, "sprites/square/blue.png"], [:wait, 60], [:frame, "sprites/square/red.png"], [:wait, 60], [:frame, "sprites/square/green.png"], [:wait, 60]], @wait=0>, :primitive_marker=>:sprite, :path=>"sprites/square/green.png"}, :temp_state=>{}, :inputs=>{:controller_one=>{:left_analog_x_raw=>0, :left_analog_y_raw=>0, :left_analog_x_perc=>0.0, :left_analog_y_perc=>0.0, :right_analog_x_raw=>0, :right_analog_y_raw=>0, :right_analog_x_perc=>0.0, :right_analog_y_perc=>0.0, :active=>false, :key_down=>{}, :key_held=>{}, :key_up=>{}, :left_analog_angle=>nil, :right_analog_angle=>nil, :left_analog_active=>false, :right_analog_active=>false}, :controller_two=>{:left_analog_x_raw=>0, :left_analog_y_raw=>0, :left_analog_x_perc=>0.0, :left_analog_y_perc=>0.0, :right_analog_x_raw=>0, :right_analog_y_raw=>0, :right_analog_x_perc=>0.0, :right_analog_y_perc=>0.0, :active=>false, :key_down=>{}, :key_held=>{}, :key_up=>{}, :left_analog_angle=>nil, :right_analog_angle=>nil, :left_analog_active=>false, :right_analog_active=>false}, :controller_three=>{:left_analog_x_raw=>0, :left_analog_y_raw=>0, :left_analog_x_perc=>0.0, :left_analog_y_perc=>0.0, :right_analog_x_raw=>0, :right_analog_y_raw=>0, :right_analog_x_perc=>0.0, :right_analog_y_perc=>0.0, :active=>false, :key_down=>{}, :key_held=>{}, :key_up=>{}, :left_analog_angle=>nil, :right_analog_angle=>nil, :left_analog_active=>false, :right_analog_active=>false}, :controller_four=>{:left_analog_x_raw=>0, :left_analog_y_raw=>0, :left_analog_x_perc=>0.0, :left_analog_y_perc=>0.0, :right_analog_x_raw=>0, :right_analog_y_raw=>0, :right_analog_x_perc=>0.0, :right_analog_y_perc=>0.0, :active=>false, :key_down=>{}, :key_held=>{}, :key_up=>{}, :left_analog_angle=>nil, :right_analog_angle=>nil, :left_analog_active=>false, :right_analog_active=>false}, :keyboard=>{:key_up=>{:keycodes=>[], :truthy_keys=>[]}, :key_held=>{:keycodes=>[], :truthy_keys=>[]}, :key_down=>{:keycodes=>[], :truthy_keys=>[]}, :has_focus=>false}, :mouse=>{:x=>448.0, :y=>719.0, :moved=>nil, :moved_at=>36341, :has_focus=>false}, :text=>[], :application_control=>"nil"}, :passes=>[], :outputs=>{:solids=>[], :sprites=>[], :lines=>[], :labels=>[], :sounds=>[], :borders=>[], :primitives=>[], :static_solids=>[], :static_borders=>[], :static_sprites=>[], :static_lines=>[], :static_labels=>[], :static_primitives=>[]}, :grid=>{:right_px=>1280, :w_px=>1280, :ffi_draw=>"", :runtime=>{:argv=>"C:\\Users\\<USER>\\work\\the_necromancers_cube\\dragonruby.exe", :platform=>"Windows", :required_files=>["app/main.rb"], :reload_list_history=>{"app/main.rb"=>{:current=>{:path=>"app/main.rb", :global_at=>55848, :event=>:reload_completed}, :history=>[{:path=>"app/main.rb", :global_at=>-1, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>-1, :event=>:processing}, {:path=>"app/main.rb", :global_at=>-1, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>6300, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>6300, :event=>:processing}, {:path=>"app/main.rb", :global_at=>6300, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>11038, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>11038, :event=>:processing}, {:path=>"app/main.rb", :global_at=>11038, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>14035, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>14035, :event=>:processing}, {:path=>"app/main.rb", :global_at=>14035, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>14454, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>14454, :event=>:processing}, {:path=>"app/main.rb", :global_at=>14454, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>16314, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>16314, :event=>:processing}, {:path=>"app/main.rb", :global_at=>16314, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>16494, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>16494, :event=>:processing}, {:path=>"app/main.rb", :global_at=>16494, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>16674, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>16674, :event=>:processing}, {:path=>"app/main.rb", :global_at=>16674, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>18234, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>18234, :event=>:processing}, {:path=>"app/main.rb", :global_at=>18234, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>18714, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>18714, :event=>:processing}, {:path=>"app/main.rb", :global_at=>18714, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>19974, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>19974, :event=>:processing}, {:path=>"app/main.rb", :global_at=>19974, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>35274, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>35274, :event=>:processing}, {:path=>"app/main.rb", :global_at=>35274, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>43194, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>43194, :event=>:processing}, {:path=>"app/main.rb", :global_at=>43194, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>50694, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>50694, :event=>:processing}, {:path=>"app/main.rb", :global_at=>50694, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>51954, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>51954, :event=>:processing}, {:path=>"app/main.rb", :global_at=>51954, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>53334, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>53334, :event=>:processing}, {:path=>"app/main.rb", :global_at=>53334, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>53930, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>53930, :event=>:processing}, {:path=>"app/main.rb", :global_at=>53930, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>54410, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>54410, :event=>:processing}, {:path=>"app/main.rb", :global_at=>54410, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>55068, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>55068, :event=>:processing}, {:path=>"app/main.rb", :global_at=>55068, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>55848, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>55848, :event=>:processing}, {:path=>"app/main.rb", :global_at=>55848, :event=>:reload_completed}]}}}, :h_px=>720, :allscreen_w=>1280, :right=>1280, :render_origin_x=>0.0, :allscreen_h=>720, :render_origin_y=>720, :allscreen_top=>720, :high_dpi_scale=>1.0, :allscreen_h_px=>720, :h=>720, :texture_scale_enum=>100, :allscreen_right_px=>1280, :y=>0.0, :allscreen_w_px=>1280, :left=>0, :texture_scale=>1.0, :allscreen_right=>1280, :letterbox=>true, :render_scale=>1.0, :center=>[[:x, 640.0], [:y, 360.0]], :allscreen_offset_x_px=>0, :x=>0.0, :origin_x=>0.0, :allscreen_offset_y=>0, :w=>1280, :left_px=>0, :origin_y=>0.0, :bottom_px=>0, :center_y=>360.0, :allscreen_offset_y_px=>0, :rect=>[[:x, 0.0], [:y, 0.0], [:w, 1280], [:h, 720]], :native_scale=>1.0, :top_px=>720, :center_x=>640.0, :allscreen_w_pt=>1280, :allscreen_bottom_px=>0, :bottom=>0, :allscreen_top_px=>720, :allscreen_bottom=>0, :allscreen_left_px=>0, :top=>720, :allscreen_offset_x=>0, :allscreen_left=>0, :origin_name=>:bottom_left, :allscreen_h_pt=>720}}