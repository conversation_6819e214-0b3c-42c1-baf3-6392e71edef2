# * EXCEPTION:
# * ERROR - :alive? method missing on ~NilClass~.
# 
# #+begin_src ~NilClass#inspect~
# nil
# #+end_src ~NilClass#inspect~
# 
# 
# 
# The method named
# :alive?
# doesn't exist on [NilClass, NilClassFalseClass].
# 
# (suggestions above)
# ** Backtrace:
# *** app/main.rb:23:in done?
# *** app/main.rb:10:in update
# *** app/main.rb:55:in tick

Game State:
{:state=>{:tick_count=>28556, :anim=>#<Animation:0x1e44edf13b0 @index=6, @commands=[[:frame, "sprites/square/blue.png"], [:wait, 60], [:frame, "sprites/square/red.png"], [:wait, 60], [:frame, "sprites/square/green.png"], [:wait, 60]], @wait=0>, :primitive_marker=>:sprite, :path=>"sprites/square/green.png"}, :temp_state=>{}, :inputs=>{:controller_one=>{:left_analog_x_raw=>0, :left_analog_y_raw=>0, :left_analog_x_perc=>0.0, :left_analog_y_perc=>0.0, :right_analog_x_raw=>0, :right_analog_y_raw=>0, :right_analog_x_perc=>0.0, :right_analog_y_perc=>0.0, :active=>false, :key_down=>{}, :key_held=>{}, :key_up=>{}, :left_analog_angle=>nil, :right_analog_angle=>nil, :left_analog_active=>false, :right_analog_active=>false}, :controller_two=>{:left_analog_x_raw=>0, :left_analog_y_raw=>0, :left_analog_x_perc=>0.0, :left_analog_y_perc=>0.0, :right_analog_x_raw=>0, :right_analog_y_raw=>0, :right_analog_x_perc=>0.0, :right_analog_y_perc=>0.0, :active=>false, :key_down=>{}, :key_held=>{}, :key_up=>{}, :left_analog_angle=>nil, :right_analog_angle=>nil, :left_analog_active=>false, :right_analog_active=>false}, :controller_three=>{:left_analog_x_raw=>0, :left_analog_y_raw=>0, :left_analog_x_perc=>0.0, :left_analog_y_perc=>0.0, :right_analog_x_raw=>0, :right_analog_y_raw=>0, :right_analog_x_perc=>0.0, :right_analog_y_perc=>0.0, :active=>false, :key_down=>{}, :key_held=>{}, :key_up=>{}, :left_analog_angle=>nil, :right_analog_angle=>nil, :left_analog_active=>false, :right_analog_active=>false}, :controller_four=>{:left_analog_x_raw=>0, :left_analog_y_raw=>0, :left_analog_x_perc=>0.0, :left_analog_y_perc=>0.0, :right_analog_x_raw=>0, :right_analog_y_raw=>0, :right_analog_x_perc=>0.0, :right_analog_y_perc=>0.0, :active=>false, :key_down=>{}, :key_held=>{}, :key_up=>{}, :left_analog_angle=>nil, :right_analog_angle=>nil, :left_analog_active=>false, :right_analog_active=>false}, :keyboard=>{:key_up=>{:keycodes=>[], :truthy_keys=>[]}, :key_held=>{:keycodes=>[], :truthy_keys=>[]}, :key_down=>{:keycodes=>[], :truthy_keys=>[]}, :has_focus=>false}, :mouse=>{:x=>596.0, :y=>689.0, :moved=>nil, :moved_at=>13694, :has_focus=>false}, :text=>[], :application_control=>"nil"}, :passes=>[], :outputs=>{:solids=>[], :sprites=>[], :lines=>[], :labels=>[], :sounds=>[], :borders=>[], :primitives=>[], :static_solids=>[], :static_borders=>[], :static_sprites=>[], :static_lines=>[], :static_labels=>[], :static_primitives=>[]}, :grid=>{:right_px=>1280, :w_px=>1280, :ffi_draw=>"", :runtime=>{:argv=>"C:\\Users\\<USER>\\work\\the_necromancers_cube\\dragonruby.exe", :platform=>"Windows", :required_files=>["app/main.rb"], :reload_list_history=>{"app/main.rb"=>{:current=>{:path=>"app/main.rb", :global_at=>28555, :event=>:reload_completed}, :history=>[{:path=>"app/main.rb", :global_at=>-1, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>-1, :event=>:processing}, {:path=>"app/main.rb", :global_at=>-1, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>4320, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>4320, :event=>:processing}, {:path=>"app/main.rb", :global_at=>4320, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>7319, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>7319, :event=>:processing}, {:path=>"app/main.rb", :global_at=>7319, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>7739, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>7739, :event=>:processing}, {:path=>"app/main.rb", :global_at=>7739, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>9599, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>9599, :event=>:processing}, {:path=>"app/main.rb", :global_at=>9599, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>9779, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>9779, :event=>:processing}, {:path=>"app/main.rb", :global_at=>9779, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>9958, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>9958, :event=>:processing}, {:path=>"app/main.rb", :global_at=>9958, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>11517, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>11517, :event=>:processing}, {:path=>"app/main.rb", :global_at=>11517, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>11996, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>11996, :event=>:processing}, {:path=>"app/main.rb", :global_at=>11996, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>13256, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>13256, :event=>:processing}, {:path=>"app/main.rb", :global_at=>13256, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>28555, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>28555, :event=>:processing}, {:path=>"app/main.rb", :global_at=>28555, :event=>:reload_completed}]}}}, :h_px=>720, :allscreen_w=>1280, :right=>1280, :render_origin_x=>0.0, :allscreen_h=>720, :render_origin_y=>720, :allscreen_top=>720, :high_dpi_scale=>1.0, :allscreen_h_px=>720, :h=>720, :texture_scale_enum=>100, :allscreen_right_px=>1280, :y=>0.0, :allscreen_w_px=>1280, :left=>0, :texture_scale=>1.0, :allscreen_right=>1280, :letterbox=>true, :render_scale=>1.0, :center=>[[:x, 640.0], [:y, 360.0]], :allscreen_offset_x_px=>0, :x=>0.0, :origin_x=>0.0, :allscreen_offset_y=>0, :w=>1280, :left_px=>0, :origin_y=>0.0, :bottom_px=>0, :center_y=>360.0, :allscreen_offset_y_px=>0, :rect=>[[:x, 0.0], [:y, 0.0], [:w, 1280], [:h, 720]], :native_scale=>1.0, :top_px=>720, :center_x=>640.0, :allscreen_w_pt=>1280, :allscreen_bottom_px=>0, :bottom=>0, :allscreen_top_px=>720, :allscreen_bottom=>0, :allscreen_left_px=>0, :top=>720, :allscreen_offset_x=>0, :allscreen_left=>0, :origin_name=>:bottom_left, :allscreen_h_pt=>720}}