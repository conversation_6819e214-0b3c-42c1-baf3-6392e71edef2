class Entity
  attr_accessor :x, :y, :frame, :anim, :color

  def initialize(x:, y:, color: [255,255,255])
    @x = x
    @y = y
    @frame = 0
    @anim = nil
    @color = color
  end

  def tick(args)
    @anim&.tick(args)
  end

  def render(args)
    # Ensure color is a valid array with at least 3 values (RGB)
    color = @color || [255, 255, 255]
    color = [255, 255, 255] unless color.is_a?(Array) && color.length >= 3

    args.outputs.solids << [@x.round, @y.round, 64, 64, *color]
    args.outputs.labels << [@x.round, @y.round + 80, "F#{@frame}"]
  end
end
