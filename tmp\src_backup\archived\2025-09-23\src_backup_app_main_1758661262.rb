include "animation.rb"

def boot args
  args.state = {}
end

def tick args
  args.state.player ||= { x: 100, y: 100, w: 64, h: 64, path: "sprites/square/blue.png" }

  #args.state.anim ||= Animation.new do
  #  set_frame(args.state.player, "sprites/square/blue.png")
  #  wait 120
  #  set_frame(args.state.player, "sprites/square/red.png")
  #  wait 120
  #  set_frame(args.state.player, "sprites/square/green.png")
  #  wait 120
  #end

  #if !args.state.anim.done? 
  #  args.state.anim.update args
  #end
  args.outputs.sprites << args.state.player
end

GTK.reset