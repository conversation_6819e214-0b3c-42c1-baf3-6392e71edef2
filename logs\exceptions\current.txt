# * EXCEPTION: 'tick': wrong number of arguments (1 for 0)
# ** Backtrace:
# *** app/animations.rb:50:in tick
# *** app/animations.rb:50:in tick
# *** app/cutscenes.rb:17:in tick
# *** app/main.rb:92:in tick
# *** app/scenes.rb:44:in tick
# *** app/main.rb:114:in tick

Game State:
{:state=>{:entity_id=>3, :tick_count=>148, :__thrash_count__=>{}, :scene_manager=>#<SceneManager:0x1c1909b13c0 @scenes={:menu=>#<MenuScene:0x1c1909b11e0>, :game=>#<GameScene:0x1c1909b1180>}, @stack=[#<GameScene:0x1c1909b1180>], @transition=nil>, :player=>#<Entity:0x1c19126da70 @color=[100, 200, 255], @x=0, @frame=0, @y=300, @anim=nil>, :enemy=>#<Entity:0x1c19126ce40 @color=[255, 100, 100], @x=800, @frame=0, @y=500, @anim=nil>, :cutscene_manager=>#<CutsceneManager:0x1c19126c270 @queue=[#<Animation:0x1c19126b4f0 @loop=false, @steps=[#<Proc:0x1c190962060@app/animations.rb:96 (lambda)>, {:wait=>60}], @repeat=1, @pc=0, @dsl=#<AnimationDSL:0x1c19126b400>, @wait=0, @finished_repeats=0, @done=false, @on_finish=[]>], @active=nil>, :effects=>[], :messages=>[{:text=>"A wild enemy appears!", :duration=>87}]}, :temp_state=>{}, :inputs=>{:controller_one=>{:left_analog_x_raw=>0, :left_analog_y_raw=>0, :left_analog_x_perc=>0.0, :left_analog_y_perc=>0.0, :right_analog_x_raw=>0, :right_analog_y_raw=>0, :right_analog_x_perc=>0.0, :right_analog_y_perc=>0.0, :active=>false, :key_down=>{}, :key_held=>{}, :key_up=>{}, :left_analog_angle=>nil, :right_analog_angle=>nil, :left_analog_active=>false, :right_analog_active=>false}, :controller_two=>{:left_analog_x_raw=>0, :left_analog_y_raw=>0, :left_analog_x_perc=>0.0, :left_analog_y_perc=>0.0, :right_analog_x_raw=>0, :right_analog_y_raw=>0, :right_analog_x_perc=>0.0, :right_analog_y_perc=>0.0, :active=>false, :key_down=>{}, :key_held=>{}, :key_up=>{}, :left_analog_angle=>nil, :right_analog_angle=>nil, :left_analog_active=>false, :right_analog_active=>false}, :controller_three=>{:left_analog_x_raw=>0, :left_analog_y_raw=>0, :left_analog_x_perc=>0.0, :left_analog_y_perc=>0.0, :right_analog_x_raw=>0, :right_analog_y_raw=>0, :right_analog_x_perc=>0.0, :right_analog_y_perc=>0.0, :active=>false, :key_down=>{}, :key_held=>{}, :key_up=>{}, :left_analog_angle=>nil, :right_analog_angle=>nil, :left_analog_active=>false, :right_analog_active=>false}, :controller_four=>{:left_analog_x_raw=>0, :left_analog_y_raw=>0, :left_analog_x_perc=>0.0, :left_analog_y_perc=>0.0, :right_analog_x_raw=>0, :right_analog_y_raw=>0, :right_analog_x_perc=>0.0, :right_analog_y_perc=>0.0, :active=>false, :key_down=>{}, :key_held=>{}, :key_up=>{}, :left_analog_angle=>nil, :right_analog_angle=>nil, :left_analog_active=>false, :right_analog_active=>false}, :keyboard=>{:key_up=>{:keycodes=>[], :truthy_keys=>[]}, :key_held=>{:keycodes=>[], :truthy_keys=>[]}, :key_down=>{:keycodes=>[], :truthy_keys=>[]}, :has_focus=>true}, :mouse=>{:x=>479.0, :y=>456.0, :moved=>{:x=>479.0, :y=>456.0, :created_at=>147, :global_created_at=>147}, :moved_at=>147, :has_focus=>true}, :text=>[], :application_control=>"nil"}, :passes=>[], :outputs=>{:solids=>[[0, 300, 64, 64, 100, 200, 255], [800, 500, 64, 64, 255, 100, 100], [0, 300, 64, 64, 100, 200, 255], [800, 500, 64, 64, 255, 100, 100]], :sprites=>[], :lines=>[], :labels=>[[0, 380, "F0"], [800, 580, "F0"], [0, 380, "F0"], [800, 580, "F0"], [400, 600, "A wild enemy appears!"]], :sounds=>[], :borders=>[], :primitives=>[], :static_solids=>[], :static_borders=>[], :static_sprites=>[], :static_lines=>[], :static_labels=>[], :static_primitives=>[]}, :grid=>{:right_px=>1280, :w_px=>1280, :ffi_draw=>"", :runtime=>{:argv=>"C:\\Users\\<USER>\\work\\the_necromancers_cube\\dragonruby.exe", :platform=>"Windows", :required_files=>["app/main.rb", "app/animations.rb", "app/entities.rb", "app/scenes.rb", "app/cutscenes.rb"], :reload_list_history=>{"app/main.rb"=>{:current=>{:path=>"app/main.rb", :global_at=>-1, :event=>:reload_completed}, :history=>[{:path=>"app/main.rb", :global_at=>-1, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>-1, :event=>:processing}, {:path=>"app/main.rb", :global_at=>-1, :event=>:reload_completed}]}, "app/animations.rb"=>{:current=>{:path=>"app/animations.rb", :global_at=>-1, :event=>:reload_completed}, :history=>[{:path=>"app/animations.rb", :global_at=>-1, :event=>:reload_queued}, {:path=>"app/animations.rb", :global_at=>-1, :event=>:processing}, {:path=>"app/animations.rb", :global_at=>-1, :event=>:reload_completed}]}, "app/entities.rb"=>{:current=>{:path=>"app/entities.rb", :global_at=>-1, :event=>:reload_completed}, :history=>[{:path=>"app/entities.rb", :global_at=>-1, :event=>:reload_queued}, {:path=>"app/entities.rb", :global_at=>-1, :event=>:processing}, {:path=>"app/entities.rb", :global_at=>-1, :event=>:reload_completed}]}, "app/scenes.rb"=>{:current=>{:path=>"app/scenes.rb", :global_at=>-1, :event=>:reload_completed}, :history=>[{:path=>"app/scenes.rb", :global_at=>-1, :event=>:reload_queued}, {:path=>"app/scenes.rb", :global_at=>-1, :event=>:processing}, {:path=>"app/scenes.rb", :global_at=>-1, :event=>:reload_completed}]}, "app/cutscenes.rb"=>{:current=>{:path=>"app/cutscenes.rb", :global_at=>-1, :event=>:reload_completed}, :history=>[{:path=>"app/cutscenes.rb", :global_at=>-1, :event=>:reload_queued}, {:path=>"app/cutscenes.rb", :global_at=>-1, :event=>:processing}, {:path=>"app/cutscenes.rb", :global_at=>-1, :event=>:reload_completed}]}}}, :h_px=>720, :allscreen_w=>1280, :right=>1280, :render_origin_x=>0.0, :allscreen_h=>720, :render_origin_y=>720, :allscreen_top=>720, :high_dpi_scale=>1.0, :allscreen_h_px=>720, :h=>720, :texture_scale_enum=>100, :allscreen_right_px=>1280, :y=>0.0, :allscreen_w_px=>1280, :left=>0, :texture_scale=>1.0, :allscreen_right=>1280, :letterbox=>true, :render_scale=>1.0, :center=>[[:x, 640.0], [:y, 360.0]], :allscreen_offset_x_px=>0, :x=>0.0, :origin_x=>0.0, :allscreen_offset_y=>0, :w=>1280, :left_px=>0, :origin_y=>0.0, :bottom_px=>0, :center_y=>360.0, :allscreen_offset_y_px=>0, :rect=>[[:x, 0.0], [:y, 0.0], [:w, 1280], [:h, 720]], :native_scale=>1.0, :top_px=>720, :center_x=>640.0, :allscreen_w_pt=>1280, :allscreen_bottom_px=>0, :bottom=>0, :allscreen_top_px=>720, :allscreen_bottom=>0, :allscreen_left_px=>0, :top=>720, :allscreen_offset_x=>0, :allscreen_left=>0, :origin_name=>:bottom_left, :allscreen_h_pt=>720}}