replay_version 2.0
stopped_at 1283
seed 100
recorded_at Sun Sep 29 21:23:38 2019
[:mouse_move, 39, 552, 2, 1, 54]
[:mouse_move, 87, 534, 2, 2, 55]
[:mouse_move, 96, 531, 2, 3, 56]
[:mouse_move, 115, 523, 2, 4, 57]
[:mouse_move, 122, 520, 2, 5, 58]
[:mouse_move, 136, 513, 2, 6, 59]
[:mouse_move, 143, 510, 2, 7, 60]
[:mouse_move, 147, 507, 2, 8, 61]
[:mouse_move, 168, 492, 2, 9, 62]
[:mouse_move, 181, 479, 2, 10, 63]
[:mouse_move, 233, 424, 2, 11, 64]
[:mouse_move, 259, 390, 2, 12, 65]
[:mouse_move, 286, 353, 2, 13, 66]
[:mouse_move, 326, 301, 2, 14, 67]
[:mouse_move, 335, 287, 2, 15, 68]
[:mouse_move, 368, 240, 2, 16, 69]
[:mouse_move, 377, 221, 2, 17, 70]
[:mouse_move, 395, 169, 2, 18, 71]
[:mouse_move, 401, 127, 2, 19, 72]
[:mouse_move, 403, 122, 2, 20, 86]
[:mouse_move, 422, 96, 2, 21, 87]
[:mouse_move, 431, 84, 2, 22, 88]
[:mouse_move, 439, 73, 2, 23, 89]
[:mouse_move, 450, 57, 2, 24, 90]
[:mouse_move, 454, 53, 2, 25, 91]
[:mouse_move, 462, 43, 2, 26, 92]
[:mouse_move, 465, 39, 2, 27, 93]
[:mouse_move, 468, 35, 2, 28, 94]
[:mouse_move, 469, 35, 2, 29, 95]
[:mouse_move, 470, 34, 2, 30, 96]
[:mouse_move, 469, 34, 2, 31, 100]
[:mouse_move, 467, 34, 2, 32, 101]
[:mouse_move, 459, 37, 2, 33, 102]
[:mouse_move, 455, 37, 2, 34, 103]
[:mouse_move, 449, 38, 2, 35, 104]
[:mouse_move, 444, 38, 2, 36, 105]
[:mouse_move, 440, 37, 2, 37, 106]
[:mouse_move, 440, 35, 2, 38, 107]
[:mouse_move, 439, 31, 2, 39, 108]
[:mouse_move, 439, 29, 2, 40, 109]
[:mouse_move, 440, 24, 2, 41, 110]
[:mouse_move, 441, 23, 2, 42, 111]
[:mouse_move, 445, 20, 2, 43, 112]
[:mouse_move, 446, 19, 2, 44, 113]
[:mouse_move, 451, 18, 2, 45, 114]
[:mouse_move, 453, 18, 2, 46, 115]
[:mouse_move, 456, 18, 2, 47, 116]
[:mouse_move, 463, 18, 2, 48, 117]
[:mouse_move, 466, 18, 2, 49, 118]
[:mouse_move, 474, 18, 2, 50, 119]
[:mouse_move, 478, 18, 2, 51, 120]
[:mouse_move, 489, 18, 2, 52, 121]
[:mouse_move, 495, 20, 2, 53, 122]
[:mouse_move, 508, 22, 2, 54, 123]
[:mouse_move, 513, 23, 2, 55, 124]
[:mouse_move, 525, 24, 2, 56, 125]
[:mouse_move, 528, 24, 2, 57, 126]
[:mouse_move, 539, 25, 2, 58, 127]
[:mouse_move, 541, 25, 2, 59, 128]
[:mouse_move, 547, 25, 2, 60, 129]
[:mouse_move, 550, 25, 2, 61, 130]
[:mouse_move, 555, 25, 2, 62, 131]
[:mouse_move, 557, 25, 2, 63, 132]
[:mouse_move, 564, 25, 2, 64, 133]
[:mouse_move, 567, 25, 2, 65, 134]
[:mouse_move, 571, 25, 2, 66, 135]
[:mouse_move, 574, 25, 2, 67, 136]
[:mouse_move, 581, 26, 2, 68, 137]
[:mouse_move, 585, 27, 2, 69, 138]
[:mouse_move, 592, 28, 2, 70, 139]
[:mouse_move, 596, 28, 2, 71, 140]
[:mouse_move, 602, 29, 2, 72, 141]
[:mouse_move, 605, 30, 2, 73, 142]
[:mouse_move, 610, 30, 2, 74, 143]
[:mouse_move, 617, 30, 2, 75, 144]
[:mouse_move, 619, 30, 2, 76, 145]
[:mouse_move, 628, 30, 2, 77, 146]
[:mouse_move, 629, 30, 2, 78, 147]
[:mouse_move, 634, 30, 2, 79, 148]
[:mouse_move, 636, 29, 2, 80, 149]
[:mouse_move, 639, 29, 2, 81, 150]
[:mouse_move, 640, 28, 2, 82, 151]
[:mouse_move, 642, 28, 2, 83, 152]
[:mouse_move, 643, 28, 2, 84, 153]
[:mouse_move, 645, 27, 2, 85, 154]
[:mouse_move, 646, 27, 2, 86, 155]
[:mouse_move, 647, 26, 2, 87, 156]
[:mouse_move, 648, 26, 2, 88, 158]
[:mouse_move, 649, 26, 2, 89, 160]
[:mouse_move, 648, 26, 2, 90, 192]
[:mouse_move, 642, 26, 2, 91, 193]
[:mouse_move, 638, 26, 2, 92, 194]
[:mouse_move, 628, 27, 2, 93, 195]
[:mouse_move, 623, 28, 2, 94, 196]
[:mouse_move, 618, 29, 2, 95, 197]
[:mouse_move, 607, 31, 2, 96, 198]
[:mouse_move, 600, 34, 2, 97, 199]
[:mouse_move, 591, 35, 2, 98, 200]
[:mouse_move, 586, 37, 2, 99, 201]
[:mouse_move, 577, 39, 2, 100, 202]
[:mouse_move, 574, 39, 2, 101, 203]
[:mouse_move, 564, 41, 2, 102, 204]
[:mouse_move, 560, 42, 2, 103, 205]
[:mouse_move, 552, 43, 2, 104, 206]
[:mouse_move, 548, 43, 2, 105, 207]
[:mouse_move, 538, 44, 2, 106, 208]
[:mouse_move, 533, 44, 2, 107, 209]
[:mouse_move, 525, 44, 2, 108, 210]
[:mouse_move, 520, 44, 2, 109, 211]
[:mouse_move, 505, 44, 2, 110, 212]
[:mouse_move, 501, 45, 2, 111, 213]
[:mouse_move, 489, 46, 2, 112, 214]
[:mouse_move, 487, 46, 2, 113, 215]
[:mouse_move, 480, 48, 2, 114, 216]
[:mouse_move, 477, 48, 2, 115, 217]
[:mouse_move, 471, 51, 2, 116, 218]
[:mouse_move, 467, 53, 2, 117, 219]
[:mouse_move, 462, 55, 2, 118, 220]
[:mouse_move, 459, 57, 2, 119, 221]
[:mouse_move, 456, 59, 2, 120, 222]
[:mouse_move, 455, 60, 2, 121, 223]
[:mouse_move, 454, 61, 2, 122, 224]
[:mouse_move, 453, 61, 2, 123, 225]
[:mouse_move, 457, 61, 2, 124, 242]
[:mouse_move, 465, 63, 2, 125, 243]
[:mouse_move, 471, 64, 2, 126, 244]
[:mouse_move, 486, 67, 2, 127, 245]
[:mouse_move, 498, 68, 2, 128, 246]
[:mouse_move, 523, 70, 2, 129, 247]
[:mouse_move, 535, 70, 2, 130, 248]
[:mouse_move, 551, 70, 2, 131, 249]
[:mouse_move, 560, 70, 2, 132, 250]
[:mouse_move, 571, 70, 2, 133, 251]
[:mouse_move, 578, 70, 2, 134, 252]
[:mouse_move, 583, 70, 2, 135, 253]
[:mouse_move, 588, 70, 2, 136, 254]
[:mouse_move, 590, 70, 2, 137, 255]
[:mouse_move, 592, 70, 2, 138, 256]
[:mouse_move, 593, 70, 2, 139, 257]
[:mouse_move, 595, 70, 2, 140, 258]
[:mouse_move, 596, 70, 2, 141, 259]
[:mouse_move, 597, 70, 2, 142, 260]
[:mouse_move, 598, 70, 2, 143, 261]
[:mouse_move, 600, 70, 2, 144, 262]
[:mouse_move, 601, 70, 2, 145, 263]
[:mouse_move, 603, 70, 2, 146, 264]
[:mouse_move, 605, 70, 2, 147, 265]
[:mouse_move, 608, 70, 2, 148, 266]
[:mouse_move, 609, 70, 2, 149, 267]
[:mouse_move, 613, 70, 2, 150, 268]
[:mouse_move, 614, 70, 2, 151, 269]
[:mouse_move, 616, 69, 2, 152, 270]
[:mouse_move, 617, 69, 2, 153, 271]
[:mouse_move, 619, 68, 2, 154, 272]
[:mouse_move, 620, 68, 2, 155, 273]
[:mouse_move, 621, 68, 2, 156, 274]
[:mouse_move, 622, 67, 2, 157, 275]
[:mouse_move, 623, 67, 2, 158, 276]
[:mouse_move, 624, 67, 2, 159, 278]
[:mouse_move, 625, 67, 2, 160, 279]
[:mouse_move, 626, 66, 2, 161, 280]
[:mouse_move, 628, 66, 2, 162, 281]
[:mouse_move, 629, 66, 2, 163, 282]
[:mouse_move, 631, 66, 2, 164, 283]
[:mouse_move, 633, 66, 2, 165, 284]
[:mouse_move, 638, 65, 2, 166, 285]
[:mouse_move, 640, 65, 2, 167, 286]
[:mouse_move, 646, 65, 2, 168, 287]
[:mouse_move, 648, 65, 2, 169, 288]
[:mouse_move, 656, 65, 2, 170, 289]
[:mouse_move, 659, 65, 2, 171, 290]
[:mouse_move, 666, 64, 2, 172, 291]
[:mouse_move, 668, 64, 2, 173, 292]
[:mouse_move, 675, 63, 2, 174, 293]
[:mouse_move, 677, 63, 2, 175, 294]
[:mouse_move, 682, 63, 2, 176, 295]
[:mouse_move, 684, 62, 2, 177, 296]
[:mouse_move, 688, 62, 2, 178, 297]
[:mouse_move, 690, 61, 2, 179, 298]
[:mouse_move, 693, 61, 2, 180, 299]
[:mouse_move, 696, 60, 2, 181, 300]
[:mouse_move, 699, 59, 2, 182, 301]
[:mouse_move, 701, 58, 2, 183, 302]
[:mouse_move, 706, 58, 2, 184, 303]
[:mouse_move, 708, 57, 2, 185, 304]
[:mouse_move, 715, 56, 2, 186, 305]
[:mouse_move, 718, 56, 2, 187, 306]
[:mouse_move, 721, 56, 2, 188, 307]
[:mouse_move, 726, 54, 2, 189, 308]
[:mouse_move, 731, 53, 2, 190, 309]
[:mouse_move, 732, 53, 2, 191, 325]
[:mouse_move, 736, 54, 2, 192, 326]
[:mouse_move, 739, 55, 2, 193, 327]
[:mouse_move, 745, 57, 2, 194, 328]
[:mouse_move, 747, 58, 2, 195, 329]
[:mouse_move, 752, 59, 2, 196, 330]
[:mouse_move, 757, 61, 2, 197, 331]
[:mouse_move, 760, 61, 2, 198, 332]
[:mouse_move, 767, 63, 2, 199, 333]
[:mouse_move, 771, 63, 2, 200, 334]
[:mouse_move, 775, 63, 2, 201, 335]
[:mouse_move, 778, 63, 2, 202, 336]
[:mouse_move, 781, 64, 2, 203, 337]
[:mouse_move, 782, 64, 2, 204, 338]
[:mouse_move, 783, 64, 2, 205, 339]
[:mouse_move, 784, 64, 2, 206, 340]
[:mouse_move, 784, 65, 2, 207, 341]
[:mouse_move, 785, 65, 2, 208, 342]
[:mouse_move, 787, 65, 2, 209, 343]
[:mouse_move, 789, 66, 2, 210, 344]
[:mouse_move, 795, 67, 2, 211, 345]
[:mouse_move, 797, 67, 2, 212, 346]
[:mouse_move, 803, 67, 2, 213, 347]
[:mouse_move, 805, 67, 2, 214, 348]
[:mouse_move, 810, 67, 2, 215, 349]
[:mouse_move, 811, 66, 2, 216, 350]
[:mouse_move, 814, 64, 2, 217, 351]
[:mouse_move, 814, 63, 2, 218, 352]
[:mouse_move, 815, 61, 2, 219, 353]
[:mouse_move, 816, 58, 2, 220, 354]
[:mouse_move, 817, 55, 2, 221, 355]
[:mouse_move, 817, 53, 2, 222, 356]
[:mouse_move, 818, 49, 2, 223, 357]
[:mouse_move, 818, 47, 2, 224, 358]
[:mouse_move, 818, 46, 2, 225, 359]
[:mouse_move, 817, 42, 2, 226, 360]
[:mouse_move, 815, 40, 2, 227, 361]
[:mouse_move, 811, 38, 2, 228, 362]
[:mouse_move, 805, 35, 2, 229, 363]
[:mouse_move, 803, 35, 2, 230, 364]
[:mouse_move, 798, 34, 2, 231, 365]
[:mouse_move, 796, 34, 2, 232, 366]
[:mouse_move, 794, 34, 2, 233, 367]
[:mouse_move, 788, 34, 2, 234, 368]
[:mouse_move, 785, 34, 2, 235, 369]
[:mouse_move, 779, 34, 2, 236, 370]
[:mouse_move, 778, 35, 2, 237, 371]
[:mouse_move, 772, 37, 2, 238, 372]
[:mouse_move, 771, 38, 2, 239, 373]
[:mouse_move, 768, 41, 2, 240, 374]
[:mouse_move, 767, 42, 2, 241, 375]
[:mouse_move, 764, 46, 2, 242, 376]
[:mouse_move, 764, 48, 2, 243, 377]
[:mouse_move, 762, 53, 2, 244, 378]
[:mouse_move, 762, 55, 2, 245, 379]
[:mouse_move, 762, 59, 2, 246, 380]
[:mouse_move, 762, 62, 2, 247, 381]
[:mouse_move, 762, 64, 2, 248, 382]
[:mouse_move, 762, 66, 2, 249, 383]
[:mouse_move, 762, 68, 2, 250, 384]
[:mouse_move, 764, 69, 2, 251, 385]
[:mouse_move, 766, 71, 2, 252, 386]
[:mouse_move, 773, 73, 2, 253, 387]
[:mouse_move, 776, 74, 2, 254, 388]
[:mouse_move, 784, 76, 2, 255, 389]
[:mouse_move, 793, 76, 2, 256, 390]
[:mouse_move, 797, 76, 2, 257, 391]
[:mouse_move, 800, 75, 2, 258, 392]
[:mouse_move, 806, 71, 2, 259, 393]
[:mouse_move, 809, 68, 2, 260, 394]
[:mouse_move, 811, 66, 2, 261, 395]
[:mouse_move, 814, 63, 2, 262, 396]
[:mouse_move, 816, 60, 2, 263, 397]
[:mouse_move, 817, 57, 2, 264, 398]
[:mouse_move, 819, 51, 2, 265, 399]
[:mouse_move, 819, 48, 2, 266, 400]
[:mouse_move, 818, 42, 2, 267, 401]
[:mouse_move, 817, 39, 2, 268, 402]
[:mouse_move, 812, 35, 2, 269, 403]
[:mouse_move, 808, 33, 2, 270, 404]
[:mouse_move, 800, 30, 2, 271, 405]
[:mouse_move, 795, 29, 2, 272, 406]
[:mouse_move, 784, 29, 2, 273, 407]
[:mouse_move, 778, 29, 2, 274, 408]
[:mouse_move, 767, 29, 2, 275, 409]
[:mouse_move, 761, 29, 2, 276, 410]
[:mouse_move, 752, 34, 2, 277, 411]
[:mouse_move, 747, 36, 2, 278, 412]
[:mouse_move, 743, 40, 2, 279, 413]
[:mouse_move, 741, 43, 2, 280, 414]
[:mouse_move, 740, 45, 2, 281, 415]
[:mouse_move, 738, 49, 2, 282, 416]
[:mouse_move, 738, 53, 2, 283, 417]
[:mouse_move, 743, 58, 2, 284, 418]
[:mouse_move, 747, 60, 2, 285, 419]
[:mouse_move, 756, 62, 2, 286, 420]
[:mouse_move, 767, 64, 2, 287, 421]
[:mouse_move, 773, 64, 2, 288, 422]
[:mouse_move, 785, 64, 2, 289, 423]
[:mouse_move, 790, 64, 2, 290, 424]
[:mouse_move, 792, 63, 2, 291, 425]
[:mouse_move, 798, 61, 2, 292, 426]
[:mouse_move, 800, 61, 2, 293, 427]
[:mouse_move, 802, 61, 2, 294, 428]
[:mouse_move, 802, 60, 2, 295, 429]
[:mouse_move, 803, 60, 2, 296, 440]
[:mouse_move, 808, 60, 2, 297, 441]
[:mouse_move, 814, 60, 2, 298, 442]
[:mouse_move, 823, 60, 2, 299, 443]
[:mouse_move, 832, 59, 2, 300, 444]
[:mouse_move, 836, 59, 2, 301, 445]
[:mouse_move, 839, 59, 2, 302, 446]
[:mouse_move, 844, 58, 2, 303, 447]
[:mouse_move, 845, 58, 2, 304, 449]
[:mouse_move, 846, 58, 2, 305, 450]
[:mouse_move, 846, 61, 2, 306, 470]
[:mouse_move, 846, 64, 2, 307, 471]
[:mouse_move, 846, 70, 2, 308, 472]
[:mouse_move, 844, 73, 2, 309, 473]
[:mouse_move, 835, 83, 2, 310, 474]
[:mouse_move, 827, 89, 2, 311, 475]
[:mouse_move, 796, 103, 2, 312, 476]
[:mouse_move, 778, 107, 2, 313, 477]
[:mouse_move, 733, 113, 2, 314, 478]
[:mouse_move, 710, 113, 2, 315, 479]
[:mouse_move, 675, 114, 2, 316, 480]
[:mouse_move, 655, 114, 2, 317, 481]
[:mouse_move, 619, 108, 2, 318, 482]
[:mouse_move, 613, 107, 2, 319, 483]
[:mouse_move, 591, 100, 2, 320, 484]
[:mouse_move, 582, 97, 2, 321, 485]
[:mouse_move, 567, 93, 2, 322, 486]
[:mouse_move, 560, 92, 2, 323, 487]
[:mouse_move, 546, 89, 2, 324, 488]
[:mouse_move, 538, 88, 2, 325, 489]
[:mouse_move, 525, 86, 2, 326, 490]
[:mouse_move, 519, 86, 2, 327, 491]
[:mouse_move, 510, 85, 2, 328, 492]
[:mouse_move, 503, 85, 2, 329, 493]
[:mouse_move, 495, 85, 2, 330, 494]
[:mouse_move, 492, 85, 2, 331, 495]
[:mouse_move, 488, 85, 2, 332, 496]
[:mouse_move, 479, 85, 2, 333, 497]
[:mouse_move, 473, 85, 2, 334, 498]
[:mouse_move, 466, 85, 2, 335, 499]
[:mouse_move, 462, 85, 2, 336, 500]
[:mouse_move, 457, 85, 2, 337, 501]
[:mouse_move, 455, 85, 2, 338, 502]
[:mouse_move, 453, 85, 2, 339, 503]
[:mouse_move, 452, 85, 2, 340, 504]
[:mouse_move, 451, 85, 2, 341, 506]
[:mouse_move, 454, 85, 2, 342, 513]
[:mouse_move, 457, 85, 2, 343, 514]
[:mouse_move, 461, 85, 2, 344, 515]
[:mouse_move, 465, 85, 2, 345, 516]
[:mouse_move, 471, 85, 2, 346, 517]
[:mouse_move, 473, 85, 2, 347, 518]
[:mouse_move, 479, 85, 2, 348, 519]
[:mouse_move, 484, 85, 2, 349, 520]
[:mouse_move, 488, 85, 2, 350, 521]
[:mouse_move, 490, 85, 2, 351, 522]
[:mouse_move, 492, 85, 2, 352, 523]
[:mouse_move, 497, 85, 2, 353, 524]
[:mouse_move, 500, 84, 2, 354, 525]
[:mouse_move, 505, 84, 2, 355, 526]
[:mouse_move, 509, 84, 2, 356, 527]
[:mouse_move, 515, 84, 2, 357, 528]
[:mouse_move, 518, 84, 2, 358, 529]
[:mouse_move, 525, 83, 2, 359, 530]
[:mouse_move, 529, 83, 2, 360, 531]
[:mouse_move, 537, 82, 2, 361, 532]
[:mouse_move, 541, 82, 2, 362, 533]
[:mouse_move, 550, 81, 2, 363, 534]
[:mouse_move, 554, 80, 2, 364, 535]
[:mouse_move, 562, 79, 2, 365, 536]
[:mouse_move, 566, 79, 2, 366, 537]
[:mouse_move, 574, 78, 2, 367, 538]
[:mouse_move, 577, 78, 2, 368, 539]
[:mouse_move, 585, 77, 2, 369, 540]
[:mouse_move, 587, 77, 2, 370, 541]
[:mouse_move, 593, 77, 2, 371, 542]
[:mouse_move, 596, 77, 2, 372, 543]
[:mouse_move, 600, 77, 2, 373, 544]
[:mouse_move, 604, 77, 2, 374, 545]
[:mouse_move, 608, 77, 2, 375, 546]
[:mouse_move, 610, 77, 2, 376, 547]
[:mouse_move, 616, 77, 2, 377, 548]
[:mouse_move, 619, 77, 2, 378, 549]
[:mouse_move, 621, 78, 2, 379, 550]
[:mouse_move, 628, 80, 2, 380, 551]
[:mouse_move, 631, 80, 2, 381, 552]
[:mouse_move, 637, 82, 2, 382, 553]
[:mouse_move, 641, 83, 2, 383, 554]
[:mouse_move, 648, 84, 2, 384, 555]
[:mouse_move, 651, 84, 2, 385, 556]
[:mouse_move, 657, 84, 2, 386, 557]
[:mouse_move, 660, 84, 2, 387, 558]
[:mouse_move, 665, 84, 2, 388, 559]
[:mouse_move, 668, 84, 2, 389, 560]
[:mouse_move, 671, 83, 2, 390, 561]
[:mouse_move, 673, 83, 2, 391, 562]
[:mouse_move, 674, 83, 2, 392, 563]
[:mouse_move, 675, 83, 2, 393, 564]
[:mouse_move, 676, 83, 2, 394, 566]
[:mouse_move, 674, 83, 2, 395, 587]
[:mouse_move, 667, 83, 2, 396, 588]
[:mouse_move, 661, 84, 2, 397, 589]
[:mouse_move, 642, 86, 2, 398, 590]
[:mouse_move, 629, 87, 2, 399, 591]
[:mouse_move, 598, 89, 2, 400, 592]
[:mouse_move, 582, 90, 2, 401, 593]
[:mouse_move, 553, 90, 2, 402, 594]
[:mouse_move, 547, 90, 2, 403, 595]
[:mouse_move, 529, 91, 2, 404, 596]
[:mouse_move, 521, 91, 2, 405, 597]
[:mouse_move, 514, 91, 2, 406, 598]
[:mouse_move, 509, 92, 2, 407, 599]
[:mouse_move, 501, 92, 2, 408, 600]
[:mouse_move, 499, 93, 2, 409, 601]
[:mouse_move, 492, 93, 2, 410, 602]
[:mouse_move, 489, 94, 2, 411, 603]
[:mouse_move, 485, 94, 2, 412, 604]
[:mouse_move, 474, 95, 2, 413, 605]
[:mouse_move, 469, 96, 2, 414, 606]
[:mouse_move, 462, 96, 2, 415, 607]
[:mouse_move, 459, 96, 2, 416, 608]
[:mouse_move, 456, 96, 2, 417, 609]
[:mouse_move, 454, 96, 2, 418, 610]
[:mouse_move, 453, 96, 2, 419, 611]
[:mouse_move, 453, 97, 2, 420, 615]
[:mouse_move, 455, 98, 2, 421, 617]
[:mouse_move, 457, 99, 2, 422, 618]
[:mouse_move, 465, 102, 2, 423, 619]
[:mouse_move, 470, 104, 2, 424, 620]
[:mouse_move, 481, 106, 2, 425, 621]
[:mouse_move, 487, 107, 2, 426, 622]
[:mouse_move, 499, 109, 2, 427, 623]
[:mouse_move, 504, 109, 2, 428, 624]
[:mouse_move, 513, 110, 2, 429, 625]
[:mouse_move, 518, 110, 2, 430, 626]
[:mouse_move, 527, 110, 2, 431, 627]
[:mouse_move, 531, 110, 2, 432, 628]
[:mouse_move, 538, 108, 2, 433, 629]
[:mouse_move, 542, 108, 2, 434, 630]
[:mouse_move, 548, 106, 2, 435, 631]
[:mouse_move, 549, 106, 2, 436, 632]
[:mouse_move, 554, 105, 2, 437, 633]
[:mouse_move, 557, 104, 2, 438, 634]
[:mouse_move, 560, 104, 2, 439, 635]
[:mouse_move, 565, 103, 2, 440, 636]
[:mouse_move, 568, 103, 2, 441, 637]
[:mouse_move, 573, 102, 2, 442, 638]
[:mouse_move, 575, 102, 2, 443, 639]
[:mouse_move, 581, 102, 2, 444, 640]
[:mouse_move, 585, 102, 2, 445, 641]
[:mouse_move, 592, 102, 2, 446, 642]
[:mouse_move, 596, 102, 2, 447, 643]
[:mouse_move, 604, 102, 2, 448, 644]
[:mouse_move, 608, 102, 2, 449, 645]
[:mouse_move, 617, 102, 2, 450, 646]
[:mouse_move, 622, 102, 2, 451, 647]
[:mouse_move, 634, 102, 2, 452, 648]
[:mouse_move, 641, 102, 2, 453, 649]
[:mouse_move, 655, 102, 2, 454, 650]
[:mouse_move, 668, 102, 2, 455, 651]
[:mouse_move, 680, 102, 2, 456, 652]
[:mouse_move, 687, 102, 2, 457, 653]
[:mouse_move, 699, 102, 2, 458, 654]
[:mouse_move, 705, 102, 2, 459, 655]
[:mouse_move, 712, 102, 2, 460, 656]
[:mouse_move, 715, 102, 2, 461, 657]
[:mouse_move, 718, 102, 2, 462, 658]
[:mouse_move, 719, 102, 2, 463, 660]
[:mouse_move, 718, 102, 2, 464, 663]
[:mouse_move, 717, 102, 2, 465, 664]
[:mouse_move, 713, 102, 2, 466, 665]
[:mouse_move, 709, 101, 2, 467, 666]
[:mouse_move, 693, 100, 2, 468, 667]
[:mouse_move, 673, 99, 2, 469, 668]
[:mouse_move, 640, 99, 2, 470, 669]
[:mouse_move, 622, 99, 2, 471, 670]
[:mouse_move, 585, 99, 2, 472, 671]
[:mouse_move, 578, 99, 2, 473, 672]
[:mouse_move, 554, 100, 2, 474, 673]
[:mouse_move, 550, 100, 2, 475, 674]
[:mouse_move, 538, 100, 2, 476, 675]
[:mouse_move, 535, 100, 2, 477, 676]
[:mouse_move, 532, 100, 2, 478, 677]
[:mouse_move, 531, 100, 2, 479, 678]
[:mouse_move, 530, 100, 2, 480, 679]
[:mouse_move, 529, 100, 2, 481, 681]
[:mouse_move, 530, 100, 2, 482, 686]
[:mouse_move, 532, 100, 2, 483, 687]
[:mouse_move, 542, 104, 2, 484, 688]
[:mouse_move, 546, 104, 2, 485, 689]
[:mouse_move, 560, 107, 2, 486, 690]
[:mouse_move, 567, 107, 2, 487, 691]
[:mouse_move, 581, 107, 2, 488, 692]
[:mouse_move, 587, 107, 2, 489, 693]
[:mouse_move, 593, 103, 2, 490, 694]
[:mouse_move, 595, 101, 2, 491, 695]
[:mouse_move, 598, 96, 2, 492, 696]
[:mouse_move, 599, 93, 2, 493, 697]
[:mouse_move, 599, 88, 2, 494, 698]
[:mouse_move, 598, 86, 2, 495, 699]
[:mouse_move, 589, 78, 2, 496, 700]
[:mouse_move, 583, 75, 2, 497, 701]
[:mouse_move, 570, 70, 2, 498, 702]
[:mouse_move, 563, 68, 2, 499, 703]
[:mouse_move, 546, 66, 2, 500, 704]
[:mouse_move, 540, 65, 2, 501, 705]
[:mouse_move, 526, 65, 2, 502, 706]
[:mouse_move, 521, 65, 2, 503, 707]
[:mouse_move, 513, 66, 2, 504, 708]
[:mouse_move, 510, 68, 2, 505, 709]
[:mouse_move, 504, 71, 2, 506, 710]
[:mouse_move, 502, 72, 2, 507, 711]
[:mouse_move, 499, 76, 2, 508, 712]
[:mouse_move, 498, 78, 2, 509, 713]
[:mouse_move, 498, 81, 2, 510, 714]
[:mouse_move, 498, 87, 2, 511, 715]
[:mouse_move, 498, 90, 2, 512, 716]
[:mouse_move, 500, 97, 2, 513, 717]
[:mouse_move, 500, 98, 2, 514, 718]
[:mouse_move, 506, 105, 2, 515, 719]
[:mouse_move, 512, 110, 2, 516, 720]
[:mouse_move, 523, 115, 2, 517, 721]
[:mouse_move, 531, 118, 2, 518, 722]
[:mouse_move, 550, 121, 2, 519, 723]
[:mouse_move, 555, 121, 2, 520, 724]
[:mouse_move, 571, 121, 2, 521, 725]
[:mouse_move, 577, 119, 2, 522, 726]
[:mouse_move, 586, 113, 2, 523, 727]
[:mouse_move, 590, 110, 2, 524, 728]
[:mouse_move, 595, 101, 2, 525, 729]
[:mouse_move, 598, 94, 2, 526, 730]
[:mouse_move, 599, 83, 2, 527, 731]
[:mouse_move, 599, 80, 2, 528, 732]
[:mouse_move, 599, 69, 2, 529, 733]
[:mouse_move, 597, 66, 2, 530, 734]
[:mouse_move, 593, 62, 2, 531, 735]
[:mouse_move, 581, 60, 2, 532, 736]
[:mouse_move, 561, 59, 2, 533, 737]
[:mouse_move, 551, 59, 2, 534, 738]
[:mouse_move, 535, 59, 2, 535, 739]
[:mouse_move, 526, 60, 2, 536, 740]
[:mouse_move, 518, 62, 2, 537, 741]
[:mouse_move, 506, 65, 2, 538, 742]
[:mouse_move, 504, 67, 2, 539, 743]
[:mouse_move, 498, 70, 2, 540, 744]
[:mouse_move, 496, 72, 2, 541, 745]
[:mouse_move, 493, 77, 2, 542, 746]
[:mouse_move, 493, 79, 2, 543, 747]
[:mouse_move, 493, 86, 2, 544, 748]
[:mouse_move, 493, 88, 2, 545, 749]
[:mouse_move, 495, 96, 2, 546, 750]
[:mouse_move, 497, 98, 2, 547, 751]
[:mouse_move, 502, 104, 2, 548, 752]
[:mouse_move, 504, 106, 2, 549, 753]
[:mouse_move, 509, 110, 2, 550, 754]
[:mouse_move, 513, 112, 2, 551, 755]
[:mouse_move, 521, 116, 2, 552, 756]
[:mouse_move, 525, 118, 2, 553, 757]
[:mouse_move, 535, 120, 2, 554, 758]
[:mouse_move, 540, 121, 2, 555, 759]
[:mouse_move, 549, 122, 2, 556, 760]
[:mouse_move, 553, 122, 2, 557, 761]
[:mouse_move, 560, 121, 2, 558, 762]
[:mouse_move, 561, 120, 2, 559, 763]
[:mouse_move, 564, 119, 2, 560, 764]
[:mouse_move, 565, 118, 2, 561, 765]
[:mouse_move, 566, 118, 2, 562, 766]
[:key_down_raw, 104, 0, 2, 563, 817]
[:key_up_raw, 104, 0, 2, 564, 820]
[:mouse_move, 566, 115, 2, 565, 843]
[:mouse_move, 564, 113, 2, 566, 844]
[:mouse_move, 562, 110, 2, 567, 845]
[:mouse_move, 560, 107, 2, 568, 846]
[:mouse_move, 557, 104, 2, 569, 847]
[:mouse_move, 556, 103, 2, 570, 848]
[:mouse_move, 555, 102, 2, 571, 849]
[:mouse_move, 554, 101, 2, 572, 850]
[:mouse_move, 553, 101, 2, 573, 851]
[:mouse_move, 552, 100, 2, 574, 853]
[:mouse_move, 549, 99, 2, 575, 854]
[:mouse_move, 548, 99, 2, 576, 855]
[:mouse_move, 543, 98, 2, 577, 856]
[:mouse_move, 541, 98, 2, 578, 857]
[:mouse_move, 538, 98, 2, 579, 858]
[:mouse_move, 536, 98, 2, 580, 859]
[:mouse_move, 535, 98, 2, 581, 860]
[:mouse_move, 537, 99, 2, 582, 862]
[:mouse_move, 544, 101, 2, 583, 863]
[:mouse_move, 555, 105, 2, 584, 864]
[:mouse_move, 569, 108, 2, 585, 865]
[:mouse_move, 590, 112, 2, 586, 866]
[:mouse_move, 595, 112, 2, 587, 867]
[:mouse_move, 612, 114, 2, 588, 868]
[:mouse_move, 619, 115, 2, 589, 869]
[:mouse_move, 631, 115, 2, 590, 870]
[:mouse_move, 636, 115, 2, 591, 871]
[:mouse_move, 640, 115, 2, 592, 872]
[:mouse_move, 643, 115, 2, 593, 873]
[:mouse_move, 646, 115, 2, 594, 874]
[:mouse_move, 646, 114, 2, 595, 875]
[:mouse_move, 646, 113, 2, 596, 877]
[:mouse_move, 645, 113, 2, 597, 879]
[:mouse_move, 644, 112, 2, 598, 880]
[:mouse_move, 644, 111, 2, 599, 882]
[:mouse_move, 644, 110, 2, 600, 883]
[:mouse_move, 647, 110, 2, 601, 884]
[:mouse_move, 656, 110, 2, 602, 885]
[:mouse_move, 665, 110, 2, 603, 886]
[:mouse_move, 676, 109, 2, 604, 887]
[:mouse_move, 683, 108, 2, 605, 888]
[:mouse_move, 690, 106, 2, 606, 889]
[:mouse_move, 693, 105, 2, 607, 890]
[:mouse_move, 695, 104, 2, 608, 891]
[:mouse_move, 695, 103, 2, 609, 892]
[:mouse_move, 690, 103, 2, 610, 893]
[:mouse_move, 688, 103, 2, 611, 894]
[:mouse_move, 683, 103, 2, 612, 895]
[:mouse_move, 682, 103, 2, 613, 896]
[:mouse_move, 680, 103, 2, 614, 897]
[:mouse_move, 682, 103, 2, 615, 901]
[:mouse_move, 683, 103, 2, 616, 902]
[:mouse_move, 685, 103, 2, 617, 904]
[:mouse_move, 685, 102, 2, 618, 905]
[:mouse_move, 676, 102, 2, 619, 908]
[:mouse_move, 673, 102, 2, 620, 909]
[:mouse_move, 667, 102, 2, 621, 910]
[:mouse_move, 664, 102, 2, 622, 911]
[:mouse_move, 661, 102, 2, 623, 912]
[:mouse_move, 660, 102, 2, 624, 913]
[:mouse_move, 662, 102, 2, 625, 915]
[:mouse_move, 668, 102, 2, 626, 916]
[:mouse_move, 670, 102, 2, 627, 917]
[:mouse_move, 675, 102, 2, 628, 918]
[:mouse_move, 676, 102, 2, 629, 919]
[:mouse_move, 677, 102, 2, 630, 920]
[:mouse_move, 674, 102, 2, 631, 922]
[:mouse_move, 671, 102, 2, 632, 923]
[:mouse_move, 665, 102, 2, 633, 924]
[:mouse_move, 663, 103, 2, 634, 925]
[:mouse_move, 660, 103, 2, 635, 926]
[:mouse_move, 661, 103, 2, 636, 928]
[:mouse_move, 663, 103, 2, 637, 929]
[:mouse_move, 667, 103, 2, 638, 930]
[:mouse_move, 669, 103, 2, 639, 931]
[:mouse_move, 671, 103, 2, 640, 932]
[:mouse_move, 673, 103, 2, 641, 933]
[:key_down_raw, 104, 0, 2, 642, 949]
[:key_up_raw, 104, 0, 2, 643, 955]
[:mouse_move, 665, 102, 2, 644, 985]
[:mouse_move, 657, 101, 2, 645, 986]
[:mouse_move, 647, 101, 2, 646, 987]
[:mouse_move, 646, 101, 2, 647, 988]
[:mouse_move, 641, 101, 2, 648, 989]
[:mouse_move, 640, 101, 2, 649, 990]
[:mouse_move, 640, 102, 2, 650, 991]
[:mouse_move, 642, 102, 2, 651, 992]
[:mouse_move, 655, 103, 2, 652, 993]
[:mouse_move, 663, 104, 2, 653, 994]
[:mouse_move, 676, 104, 2, 654, 995]
[:mouse_move, 679, 104, 2, 655, 996]
[:mouse_move, 689, 104, 2, 656, 997]
[:mouse_move, 691, 104, 2, 657, 998]
[:mouse_move, 695, 104, 2, 658, 999]
[:mouse_move, 695, 103, 2, 659, 1000]
[:mouse_move, 692, 102, 2, 660, 1002]
[:mouse_move, 681, 102, 2, 661, 1003]
[:mouse_move, 677, 102, 2, 662, 1004]
[:mouse_move, 670, 103, 2, 663, 1005]
[:mouse_move, 667, 103, 2, 664, 1006]
[:mouse_move, 663, 104, 2, 665, 1007]
[:mouse_move, 662, 104, 2, 666, 1008]
[:mouse_move, 663, 104, 2, 667, 1009]
[:mouse_move, 666, 104, 2, 668, 1010]
[:mouse_move, 674, 103, 2, 669, 1011]
[:mouse_move, 678, 102, 2, 670, 1012]
[:mouse_move, 680, 102, 2, 671, 1013]
[:mouse_move, 687, 101, 2, 672, 1014]
[:mouse_move, 689, 101, 2, 673, 1015]
[:mouse_move, 691, 101, 2, 674, 1016]
[:mouse_move, 686, 101, 2, 675, 1018]
[:mouse_move, 682, 101, 2, 676, 1019]
[:mouse_move, 676, 102, 2, 677, 1020]
[:mouse_move, 673, 103, 2, 678, 1021]
[:mouse_move, 670, 104, 2, 679, 1022]
[:mouse_move, 669, 104, 2, 680, 1023]
[:mouse_move, 672, 104, 2, 681, 1025]
[:mouse_move, 677, 104, 2, 682, 1026]
[:mouse_move, 680, 104, 2, 683, 1027]
[:mouse_move, 685, 103, 2, 684, 1028]
[:mouse_move, 686, 103, 2, 685, 1029]
[:mouse_move, 687, 103, 2, 686, 1030]
[:mouse_move, 676, 103, 2, 687, 1032]
[:mouse_move, 672, 103, 2, 688, 1033]
[:mouse_move, 663, 104, 2, 689, 1034]
[:mouse_move, 660, 104, 2, 690, 1035]
[:mouse_move, 655, 105, 2, 691, 1036]
[:mouse_move, 657, 105, 2, 692, 1038]
[:mouse_move, 659, 105, 2, 693, 1039]
[:mouse_move, 660, 105, 2, 694, 1040]
[:mouse_move, 663, 105, 2, 695, 1041]
[:mouse_move, 664, 105, 2, 696, 1042]
[:mouse_move, 665, 105, 2, 697, 1043]
[:key_down_raw, 104, 0, 2, 698, 1052]
[:key_up_raw, 104, 0, 2, 699, 1056]
[:mouse_move, 664, 105, 2, 700, 1074]
[:mouse_move, 666, 105, 2, 701, 1079]
[:mouse_move, 673, 105, 2, 702, 1080]
[:mouse_move, 677, 104, 2, 703, 1081]
[:mouse_move, 689, 103, 2, 704, 1082]
[:mouse_move, 695, 102, 2, 705, 1083]
[:mouse_move, 702, 101, 2, 706, 1084]
[:mouse_move, 705, 99, 2, 707, 1085]
[:mouse_move, 710, 97, 2, 708, 1086]
[:mouse_move, 711, 96, 2, 709, 1087]
[:mouse_move, 711, 93, 2, 710, 1088]
[:mouse_move, 711, 91, 2, 711, 1089]
[:mouse_move, 711, 86, 2, 712, 1090]
[:mouse_move, 710, 83, 2, 713, 1091]
[:mouse_move, 706, 78, 2, 714, 1092]
[:mouse_move, 703, 75, 2, 715, 1093]
[:mouse_move, 699, 74, 2, 716, 1094]
[:mouse_move, 690, 73, 2, 717, 1095]
[:mouse_move, 683, 73, 2, 718, 1096]
[:mouse_move, 675, 73, 2, 719, 1097]
[:mouse_move, 670, 73, 2, 720, 1098]
[:mouse_move, 662, 73, 2, 721, 1099]
[:mouse_move, 660, 73, 2, 722, 1100]
[:mouse_move, 655, 75, 2, 723, 1101]
[:mouse_move, 652, 77, 2, 724, 1102]
[:mouse_move, 649, 81, 2, 725, 1103]
[:mouse_move, 648, 83, 2, 726, 1104]
[:mouse_move, 647, 87, 2, 727, 1105]
[:mouse_move, 647, 89, 2, 728, 1106]
[:mouse_move, 646, 92, 2, 729, 1107]
[:mouse_move, 646, 95, 2, 730, 1108]
[:mouse_move, 648, 99, 2, 731, 1109]
[:mouse_move, 652, 102, 2, 732, 1110]
[:mouse_move, 664, 107, 2, 733, 1111]
[:mouse_move, 668, 108, 2, 734, 1112]
[:mouse_move, 682, 111, 2, 735, 1113]
[:mouse_move, 688, 111, 2, 736, 1114]
[:mouse_move, 696, 111, 2, 737, 1115]
[:mouse_move, 703, 110, 2, 738, 1116]
[:mouse_move, 707, 107, 2, 739, 1117]
[:mouse_move, 709, 105, 2, 740, 1118]
[:mouse_move, 712, 102, 2, 741, 1119]
[:mouse_move, 712, 100, 2, 742, 1120]
[:mouse_move, 712, 96, 2, 743, 1121]
[:mouse_move, 710, 94, 2, 744, 1122]
[:mouse_move, 709, 94, 2, 745, 1123]
[:mouse_move, 704, 92, 2, 746, 1124]
[:mouse_move, 701, 89, 2, 747, 1125]
[:mouse_move, 696, 84, 2, 748, 1126]
[:mouse_move, 691, 78, 2, 749, 1127]
[:mouse_move, 682, 63, 2, 750, 1128]
[:mouse_move, 679, 59, 2, 751, 1129]
[:mouse_move, 668, 46, 2, 752, 1130]
[:mouse_move, 666, 44, 2, 753, 1131]
[:mouse_move, 656, 33, 2, 754, 1132]
[:mouse_move, 652, 30, 2, 755, 1133]
[:mouse_move, 647, 25, 2, 756, 1134]
[:mouse_move, 643, 23, 2, 757, 1135]
[:mouse_move, 638, 20, 2, 758, 1136]
[:mouse_move, 636, 19, 2, 759, 1137]
[:mouse_move, 631, 18, 2, 760, 1138]
[:mouse_move, 629, 17, 2, 761, 1139]
[:mouse_move, 625, 17, 2, 762, 1140]
[:mouse_move, 624, 17, 2, 763, 1141]
[:mouse_move, 620, 17, 2, 764, 1142]
[:mouse_move, 618, 17, 2, 765, 1143]
[:mouse_move, 615, 17, 2, 766, 1144]
[:mouse_move, 614, 17, 2, 767, 1145]
[:mouse_move, 612, 18, 2, 768, 1146]
[:mouse_move, 611, 18, 2, 769, 1147]
[:mouse_move, 611, 19, 2, 770, 1150]
[:mouse_move, 612, 19, 2, 771, 1151]
[:mouse_move, 615, 20, 2, 772, 1152]
[:mouse_move, 624, 22, 2, 773, 1153]
[:mouse_move, 629, 23, 2, 774, 1154]
[:mouse_move, 640, 24, 2, 775, 1155]
[:mouse_move, 646, 24, 2, 776, 1156]
[:mouse_move, 652, 25, 2, 777, 1157]
[:mouse_move, 656, 25, 2, 778, 1158]
[:mouse_move, 661, 25, 2, 779, 1159]
[:mouse_move, 663, 25, 2, 780, 1160]
[:mouse_move, 665, 25, 2, 781, 1161]
[:mouse_move, 666, 25, 2, 782, 1162]
[:mouse_move, 667, 25, 2, 783, 1163]
[:mouse_move, 665, 25, 2, 784, 1166]
[:mouse_move, 659, 25, 2, 785, 1167]
[:mouse_move, 650, 26, 2, 786, 1168]
[:mouse_move, 644, 26, 2, 787, 1169]
[:mouse_move, 635, 26, 2, 788, 1170]
[:mouse_move, 629, 26, 2, 789, 1171]
[:mouse_move, 627, 26, 2, 790, 1172]
[:mouse_move, 624, 26, 2, 791, 1173]
[:mouse_move, 623, 26, 2, 792, 1174]
[:mouse_move, 624, 26, 2, 793, 1178]
[:mouse_move, 626, 26, 2, 794, 1179]
[:mouse_move, 634, 26, 2, 795, 1180]
[:mouse_move, 636, 26, 2, 796, 1181]
[:mouse_move, 641, 26, 2, 797, 1182]
[:mouse_move, 643, 26, 2, 798, 1183]
[:mouse_move, 646, 26, 2, 799, 1184]
[:mouse_move, 645, 26, 2, 800, 1187]
[:mouse_move, 634, 26, 2, 801, 1188]
[:mouse_move, 631, 26, 2, 802, 1189]
[:mouse_move, 623, 26, 2, 803, 1190]
[:mouse_move, 620, 26, 2, 804, 1191]
[:mouse_move, 615, 26, 2, 805, 1192]
[:mouse_move, 614, 26, 2, 806, 1193]
[:mouse_move, 613, 26, 2, 807, 1195]
[:mouse_move, 613, 27, 2, 808, 1197]
[:key_down_raw, 1073742051, 1024, 2, 809, 1282]
[:key_down_raw, 113, 1024, 2, 810, 1282]
