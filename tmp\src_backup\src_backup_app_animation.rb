class AnimationScript

  def initialize &block
    @waiting = 0
    @fiber = Fiber.new do
      instance_eval &block
      :done
    end
  end

  def update(args, sprite)
    return if done?

    if @waiting > 0
      @waiting -= 1
      return
    end

    result = @fiber.resume(args, sprite)
    if result.is_a? Integer
      @waiting = result
    end
  end

  def done?
    !@fiber.alive? && @waiting <= 0
  end

  def running?
    !done?
  end

  # DSL methods
  def wait ticks
    Fiber.yield ticks 
  end

  def frame(sprite, path)
    sprite.path = path
  end

  def sound(args, path)
    args.outputs.sounds << path
  end
end

def anim &block 
  AnimationScript.new &block
end
