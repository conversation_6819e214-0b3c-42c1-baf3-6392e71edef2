* [Home](/index.md)
* [Quick Start](/guides/getting-started.md)
* [DragonRuby Philosophy](/misc/philosophy.md)
* [FAQ](/misc/faq.md)
* Guides
  * [Starting A New Project](/guides/starting-a-new-project.md)
  * [Updating DragonRuby](/guides/updating-dragonruby.md)
  * [Deploying To Itch.io](/guides/deploying-to-itch.md)
  * [Deploying Mobile](/guides/deploying-to-mobile.md)
  * [Deploying To Steam](/guides/deploying-to-steam.md)
  * [Troubleshoot Performance](/guides/troubleshoot-performance.md)
* API
  * [Outputs (`args.outputs`)](/api/outputs.md)
  * [Inputs (`args.inputs`)](/api/inputs.md)
  * [Events (`args.events`)](/api/events.md)
  * [State (`args.state`)](/api/state.md)
  * [Runtime](/api/runtime.md)
  * [Geometry](/api/geometry.md)
  * [Audio (`args.audio`)](/api/audio.md)
  * [Easing](/api/easing.md)
  * [Layout](/api/layout.md)
  * [Grid](/api/grid.md)
  * [CVars / Configuration (`args.cvars`)](/api/cvars.md)
  * [Array](/api/array.md)
  * [Numeric](/api/numeric.md)
  * [Zlib](/api/zlib.md)
* Samples
  * <a href="https://samples.dragonruby.org">Live Demos</a>
  * [Rendering Basics](/samples/rendering-basics.md)
  * [Input Basics](/samples/input-basics.md)
  * [Rendering Sprites](/samples/rendering-sprites.md)
  * [Physics And Collisions](/samples/physics-and-collisions.md)
  * [Mouse](/samples/mouse.md)
  * [Save Load](/samples/save-load.md)
  * [Advanced Audio](/samples/advanced-audio.md)
  * [Advanced Rendering](/samples/advanced-rendering.md)
  * [Advanced Rendering Hd](/samples/advanced-rendering-hd.md)
  * [Tweening Lerping Easing Functions](/samples/tweening-lerping-easing-functions.md)
  * [Performance](/samples/performance.md)
  * [Ui Controls](/samples/ui-controls.md)
  * [Advanced Debugging](/samples/advanced-debugging.md)
  * [Http](/samples/http.md)
  * [C Extensions](/samples/c-extensions.md)
  * [Path Finding Algorithms](/samples/path-finding-algorithms.md)
  * [Vr](/samples/vr.md)
  * [Genre 3d](/samples/genre-3d.md)
  * [Genre Arcade](/samples/genre-arcade.md)
  * [Genre Board Game](/samples/genre-board-game.md)
  * [Genre Boss Battle](/samples/genre-boss-battle.md)
  * [Genre Card Game](/samples/genre-card-game.md)
  * [Genre Crafting](/samples/genre-crafting.md)
  * [Genre Dev Tools](/samples/genre-dev-tools.md)
  * [Genre Dungeon Crawl](/samples/genre-dungeon-crawl.md)
  * [Genre Fighting](/samples/genre-fighting.md)
  * [Genre Jrpg](/samples/genre-jrpg.md)
  * [Genre Lowrez](/samples/genre-lowrez.md)
  * [Genre Mario](/samples/genre-mario.md)
  * [Genre Platformer](/samples/genre-platformer.md)
  * [Genre Rpg Narrative](/samples/genre-rpg-narrative.md)
  * [Genre Rpg Roguelike](/samples/genre-rpg-roguelike.md)
  * [Genre Rpg Tactical](/samples/genre-rpg-tactical.md)
  * [Genre Rpg Topdown](/samples/genre-rpg-topdown.md)
  * [Genre Simulation](/samples/genre-simulation.md)
  * [Genre Twenty Second Games](/samples/genre-twenty-second-games.md)