replay_version 2.0
stopped_at 2551
seed 100
recorded_at Wed Jul 14 23:43:22 2021
[:mouse_button_up, 1, 0, 1, 1, 3]
[:mouse_move, 797, 90, 2, 2, 111]
[:mouse_move, 796, 93, 2, 3, 112]
[:mouse_move, 794, 97, 2, 4, 113]
[:mouse_move, 794, 98, 2, 5, 114]
[:mouse_move, 794, 104, 2, 6, 115]
[:mouse_move, 793, 106, 2, 7, 116]
[:mouse_move, 794, 105, 2, 8, 117]
[:mouse_move, 796, 104, 2, 9, 128]
[:mouse_move, 798, 103, 2, 10, 129]
[:mouse_move, 802, 100, 2, 11, 130]
[:mouse_move, 805, 100, 2, 12, 131]
[:mouse_move, 809, 100, 2, 13, 132]
[:mouse_move, 810, 100, 2, 14, 133]
[:mouse_move, 811, 100, 2, 15, 134]
[:mouse_move, 812, 100, 2, 16, 135]
[:mouse_move, 814, 102, 2, 17, 138]
[:mouse_move, 817, 105, 2, 18, 139]
[:mouse_move, 823, 118, 2, 19, 140]
[:mouse_move, 826, 130, 2, 20, 141]
[:mouse_move, 832, 169, 2, 21, 142]
[:mouse_move, 834, 196, 2, 22, 143]
[:mouse_move, 834, 262, 2, 23, 144]
[:mouse_move, 833, 295, 2, 24, 145]
[:mouse_move, 829, 327, 2, 25, 146]
[:mouse_move, 825, 352, 2, 26, 147]
[:mouse_move, 815, 390, 2, 27, 148]
[:mouse_move, 805, 414, 2, 28, 149]
[:mouse_move, 798, 424, 2, 29, 150]
[:mouse_move, 786, 439, 2, 30, 151]
[:mouse_move, 783, 443, 2, 31, 152]
[:mouse_move, 779, 450, 2, 32, 153]
[:mouse_move, 777, 454, 2, 33, 154]
[:mouse_move, 775, 461, 2, 34, 155]
[:mouse_move, 775, 465, 2, 35, 157]
[:mouse_move, 777, 470, 2, 36, 158]
[:mouse_move, 783, 490, 2, 37, 159]
[:mouse_move, 787, 502, 2, 38, 160]
[:mouse_move, 791, 528, 2, 39, 161]
[:mouse_move, 794, 548, 2, 40, 162]
[:mouse_move, 802, 623, 2, 41, 163]
[:mouse_move, 809, 662, 2, 42, 164]
[:mouse_move, 819, 691, 2, 43, 165]
[:mouse_move, 820, 692, 2, 44, 166]
[:mouse_move, 820, 690, 2, 45, 169]
[:mouse_move, 821, 685, 2, 46, 170]
[:mouse_move, 823, 681, 2, 47, 171]
[:mouse_move, 823, 680, 2, 48, 172]
[:mouse_move, 823, 679, 2, 49, 173]
[:mouse_move, 824, 678, 2, 50, 174]
[:mouse_move, 825, 677, 2, 51, 175]
[:mouse_move, 826, 675, 2, 52, 176]
[:mouse_move, 827, 674, 2, 53, 177]
[:mouse_move, 828, 673, 2, 54, 178]
[:mouse_move, 829, 673, 2, 55, 179]
[:mouse_move, 831, 672, 2, 56, 180]
[:mouse_move, 832, 671, 2, 57, 181]
[:mouse_move, 835, 669, 2, 58, 182]
[:mouse_move, 837, 667, 2, 59, 183]
[:mouse_move, 839, 663, 2, 60, 184]
[:mouse_move, 839, 662, 2, 61, 185]
[:mouse_move, 840, 661, 2, 62, 189]
[:mouse_move, 841, 659, 2, 63, 190]
[:mouse_move, 841, 658, 2, 64, 191]
[:mouse_move, 842, 654, 2, 65, 192]
[:mouse_move, 843, 652, 2, 66, 193]
[:mouse_move, 845, 644, 2, 67, 194]
[:mouse_move, 846, 641, 2, 68, 195]
[:mouse_move, 847, 640, 2, 69, 196]
[:mouse_button_pressed, 1, 0, 1, 70, 221]
[:mouse_button_up, 1, 0, 1, 71, 225]
[:mouse_move, 846, 640, 2, 72, 227]
[:mouse_move, 839, 634, 2, 73, 254]
[:mouse_move, 830, 624, 2, 74, 255]
[:mouse_move, 782, 580, 2, 75, 256]
[:mouse_move, 735, 541, 2, 76, 257]
[:mouse_move, 650, 468, 2, 77, 258]
[:mouse_move, 626, 444, 2, 78, 259]
[:mouse_move, 615, 431, 2, 79, 260]
[:mouse_move, 601, 412, 2, 80, 261]
[:mouse_move, 597, 405, 2, 81, 262]
[:mouse_move, 589, 393, 2, 82, 263]
[:mouse_move, 587, 388, 2, 83, 264]
[:mouse_move, 585, 383, 2, 84, 265]
[:mouse_move, 585, 381, 2, 85, 266]
[:mouse_move, 581, 372, 2, 86, 267]
[:mouse_move, 576, 365, 2, 87, 268]
[:mouse_move, 568, 350, 2, 88, 269]
[:mouse_move, 566, 345, 2, 89, 270]
[:mouse_move, 563, 340, 2, 90, 271]
[:mouse_move, 563, 339, 2, 91, 272]
[:mouse_move, 562, 337, 2, 92, 273]
[:mouse_move, 562, 336, 2, 93, 275]
[:mouse_move, 561, 336, 2, 94, 278]
[:mouse_move, 560, 336, 2, 95, 279]
[:mouse_move, 559, 336, 2, 96, 280]
[:mouse_move, 557, 335, 2, 97, 281]
[:mouse_move, 556, 335, 2, 98, 282]
[:mouse_move, 555, 335, 2, 99, 287]
[:mouse_move, 555, 336, 2, 100, 288]
[:mouse_move, 554, 337, 2, 101, 290]
[:mouse_move, 547, 340, 2, 102, 292]
[:mouse_move, 544, 340, 2, 103, 293]
[:mouse_move, 537, 342, 2, 104, 294]
[:mouse_move, 533, 343, 2, 105, 295]
[:mouse_move, 525, 343, 2, 106, 296]
[:mouse_move, 524, 343, 2, 107, 297]
[:mouse_move, 524, 342, 2, 108, 304]
[:mouse_move, 524, 341, 2, 109, 306]
[:mouse_move, 524, 340, 2, 110, 307]
[:mouse_move, 524, 339, 2, 111, 308]
[:mouse_move, 525, 338, 2, 112, 310]
[:mouse_move, 525, 337, 2, 113, 311]
[:mouse_move, 525, 336, 2, 114, 314]
[:mouse_move, 526, 336, 2, 115, 316]
[:mouse_button_pressed, 1, 0, 1, 116, 318]
[:mouse_move, 520, 336, 2, 117, 321]
[:mouse_move, 512, 337, 2, 118, 322]
[:mouse_move, 488, 340, 2, 119, 323]
[:mouse_move, 471, 344, 2, 120, 324]
[:mouse_move, 426, 353, 2, 121, 325]
[:mouse_move, 400, 359, 2, 122, 326]
[:mouse_move, 354, 371, 2, 123, 327]
[:mouse_move, 335, 375, 2, 124, 328]
[:mouse_move, 306, 381, 2, 125, 329]
[:mouse_move, 295, 385, 2, 126, 330]
[:mouse_move, 278, 390, 2, 127, 331]
[:mouse_move, 268, 393, 2, 128, 332]
[:mouse_move, 247, 401, 2, 129, 333]
[:mouse_move, 238, 407, 2, 130, 334]
[:mouse_move, 225, 416, 2, 131, 335]
[:mouse_move, 219, 420, 2, 132, 336]
[:mouse_move, 208, 428, 2, 133, 337]
[:mouse_move, 201, 434, 2, 134, 338]
[:mouse_move, 186, 444, 2, 135, 339]
[:mouse_move, 181, 448, 2, 136, 340]
[:mouse_move, 177, 452, 2, 137, 341]
[:mouse_button_up, 1, 0, 1, 138, 349]
[:mouse_move, 178, 452, 2, 139, 349]
[:mouse_move, 192, 442, 2, 140, 350]
[:mouse_move, 208, 432, 2, 141, 351]
[:mouse_move, 259, 405, 2, 142, 352]
[:mouse_move, 294, 391, 2, 143, 353]
[:mouse_move, 380, 369, 2, 144, 354]
[:mouse_move, 415, 361, 2, 145, 355]
[:mouse_move, 459, 351, 2, 146, 356]
[:mouse_move, 475, 348, 2, 147, 357]
[:mouse_move, 505, 342, 2, 148, 358]
[:mouse_move, 519, 341, 2, 149, 359]
[:mouse_move, 538, 337, 2, 150, 360]
[:mouse_move, 544, 335, 2, 151, 361]
[:mouse_move, 546, 334, 2, 152, 362]
[:mouse_button_pressed, 1, 0, 1, 153, 379]
[:mouse_move, 542, 336, 2, 154, 383]
[:mouse_move, 537, 340, 2, 155, 384]
[:mouse_move, 509, 356, 2, 156, 385]
[:mouse_move, 489, 366, 2, 157, 386]
[:mouse_move, 435, 389, 2, 158, 387]
[:mouse_move, 409, 400, 2, 159, 388]
[:mouse_move, 353, 423, 2, 160, 389]
[:mouse_move, 332, 432, 2, 161, 390]
[:mouse_move, 301, 445, 2, 162, 391]
[:mouse_move, 290, 450, 2, 163, 392]
[:mouse_move, 278, 457, 2, 164, 393]
[:mouse_move, 275, 459, 2, 165, 394]
[:mouse_move, 267, 465, 2, 166, 395]
[:mouse_move, 261, 469, 2, 167, 396]
[:mouse_move, 254, 474, 2, 168, 397]
[:mouse_move, 244, 481, 2, 169, 398]
[:mouse_move, 241, 484, 2, 170, 399]
[:mouse_move, 240, 485, 2, 171, 400]
[:mouse_move, 237, 488, 2, 172, 402]
[:mouse_move, 234, 491, 2, 173, 404]
[:mouse_move, 231, 493, 2, 174, 405]
[:mouse_move, 224, 500, 2, 175, 406]
[:mouse_move, 222, 502, 2, 176, 407]
[:mouse_move, 219, 505, 2, 177, 408]
[:mouse_move, 218, 505, 2, 178, 409]
[:mouse_move, 214, 506, 2, 179, 412]
[:mouse_move, 211, 507, 2, 180, 413]
[:mouse_move, 202, 509, 2, 181, 414]
[:mouse_move, 198, 509, 2, 182, 415]
[:mouse_move, 183, 511, 2, 183, 416]
[:mouse_move, 174, 511, 2, 184, 417]
[:mouse_move, 169, 511, 2, 185, 418]
[:mouse_move, 166, 511, 2, 186, 420]
[:mouse_move, 164, 511, 2, 187, 421]
[:mouse_move, 163, 511, 2, 188, 422]
[:mouse_move, 164, 511, 2, 189, 434]
[:mouse_move, 164, 510, 2, 190, 436]
[:mouse_move, 166, 510, 2, 191, 437]
[:mouse_move, 167, 510, 2, 192, 438]
[:mouse_move, 171, 509, 2, 193, 439]
[:mouse_move, 174, 508, 2, 194, 440]
[:mouse_move, 186, 504, 2, 195, 441]
[:mouse_move, 197, 501, 2, 196, 442]
[:mouse_move, 247, 487, 2, 197, 443]
[:mouse_move, 298, 477, 2, 198, 444]
[:mouse_move, 429, 457, 2, 199, 445]
[:mouse_move, 481, 451, 2, 200, 446]
[:mouse_move, 586, 439, 2, 201, 447]
[:mouse_move, 632, 437, 2, 202, 448]
[:mouse_move, 728, 439, 2, 203, 449]
[:mouse_move, 785, 440, 2, 204, 450]
[:mouse_move, 905, 441, 2, 205, 451]
[:mouse_move, 943, 445, 2, 206, 452]
[:mouse_move, 981, 449, 2, 207, 453]
[:mouse_move, 997, 451, 2, 208, 454]
[:mouse_move, 1020, 455, 2, 209, 455]
[:mouse_move, 1034, 456, 2, 210, 456]
[:mouse_move, 1049, 458, 2, 211, 457]
[:mouse_move, 1065, 459, 2, 212, 458]
[:mouse_move, 1088, 460, 2, 213, 459]
[:mouse_move, 1101, 461, 2, 214, 460]
[:mouse_move, 1106, 461, 2, 215, 461]
[:mouse_move, 1116, 463, 2, 216, 462]
[:mouse_move, 1123, 465, 2, 217, 463]
[:mouse_move, 1137, 468, 2, 218, 464]
[:mouse_move, 1140, 468, 2, 219, 465]
[:mouse_move, 1141, 468, 2, 220, 466]
[:mouse_move, 1141, 469, 2, 221, 469]
[:mouse_move, 1142, 469, 2, 222, 472]
[:mouse_move, 1143, 469, 2, 223, 476]
[:mouse_move, 1144, 469, 2, 224, 478]
[:mouse_move, 1143, 469, 2, 225, 487]
[:mouse_move, 1137, 469, 2, 226, 488]
[:mouse_move, 1106, 463, 2, 227, 489]
[:mouse_move, 1082, 456, 2, 228, 490]
[:mouse_move, 1025, 442, 2, 229, 491]
[:mouse_move, 985, 434, 2, 230, 492]
[:mouse_move, 889, 417, 2, 231, 493]
[:mouse_move, 841, 410, 2, 232, 494]
[:mouse_move, 749, 402, 2, 233, 495]
[:mouse_move, 705, 399, 2, 234, 496]
[:mouse_move, 647, 396, 2, 235, 497]
[:mouse_move, 632, 396, 2, 236, 498]
[:mouse_move, 609, 398, 2, 237, 499]
[:mouse_move, 597, 400, 2, 238, 500]
[:mouse_move, 585, 403, 2, 239, 501]
[:mouse_move, 584, 403, 2, 240, 502]
[:mouse_move, 585, 403, 2, 241, 521]
[:mouse_move, 585, 402, 2, 242, 522]
[:mouse_move, 587, 400, 2, 243, 523]
[:mouse_move, 595, 395, 2, 244, 524]
[:mouse_move, 602, 392, 2, 245, 525]
[:mouse_move, 617, 388, 2, 246, 526]
[:mouse_move, 626, 387, 2, 247, 527]
[:mouse_move, 641, 385, 2, 248, 528]
[:mouse_move, 645, 385, 2, 249, 529]
[:mouse_move, 646, 385, 2, 250, 530]
[:mouse_move, 646, 384, 2, 251, 539]
[:mouse_move, 645, 384, 2, 252, 545]
[:mouse_move, 644, 384, 2, 253, 547]
[:mouse_button_up, 1, 0, 1, 254, 547]
[:mouse_move, 641, 383, 2, 255, 547]
[:mouse_move, 634, 381, 2, 256, 548]
[:mouse_move, 608, 370, 2, 257, 549]
[:mouse_move, 573, 355, 2, 258, 550]
[:mouse_move, 415, 301, 2, 259, 551]
[:mouse_move, 315, 267, 2, 260, 552]
[:mouse_move, 161, 209, 2, 261, 553]
[:mouse_move, 117, 193, 2, 262, 554]
[:mouse_move, 91, 178, 2, 263, 555]
[:mouse_move, 91, 177, 2, 264, 556]
[:mouse_move, 92, 176, 2, 265, 557]
[:mouse_move, 94, 174, 2, 266, 559]
[:mouse_move, 97, 172, 2, 267, 560]
[:mouse_move, 101, 169, 2, 268, 561]
[:mouse_move, 102, 169, 2, 269, 562]
[:mouse_move, 107, 163, 2, 270, 563]
[:mouse_move, 109, 159, 2, 271, 564]
[:mouse_move, 114, 154, 2, 272, 565]
[:mouse_move, 116, 153, 2, 273, 566]
[:mouse_move, 118, 153, 2, 274, 567]
[:mouse_move, 124, 154, 2, 275, 568]
[:mouse_move, 131, 156, 2, 276, 569]
[:mouse_move, 156, 162, 2, 277, 570]
[:mouse_move, 166, 165, 2, 278, 571]
[:mouse_move, 185, 170, 2, 279, 572]
[:mouse_move, 194, 172, 2, 280, 573]
[:mouse_move, 205, 175, 2, 281, 574]
[:mouse_move, 206, 176, 2, 282, 575]
[:mouse_move, 203, 176, 2, 283, 580]
[:mouse_move, 202, 176, 2, 284, 582]
[:mouse_button_pressed, 1, 0, 1, 285, 607]
[:mouse_move, 206, 176, 2, 286, 615]
[:mouse_move, 210, 176, 2, 287, 616]
[:mouse_move, 222, 178, 2, 288, 617]
[:mouse_move, 228, 180, 2, 289, 618]
[:mouse_move, 243, 183, 2, 290, 619]
[:mouse_move, 248, 184, 2, 291, 620]
[:mouse_move, 254, 185, 2, 292, 621]
[:mouse_move, 263, 186, 2, 293, 622]
[:mouse_move, 268, 187, 2, 294, 623]
[:mouse_move, 274, 187, 2, 295, 624]
[:mouse_move, 276, 188, 2, 296, 625]
[:mouse_move, 280, 188, 2, 297, 626]
[:mouse_move, 282, 188, 2, 298, 627]
[:mouse_move, 291, 189, 2, 299, 628]
[:mouse_move, 295, 189, 2, 300, 629]
[:mouse_move, 303, 191, 2, 301, 630]
[:mouse_move, 307, 191, 2, 302, 631]
[:mouse_move, 313, 192, 2, 303, 632]
[:mouse_move, 317, 192, 2, 304, 633]
[:mouse_move, 327, 193, 2, 305, 634]
[:mouse_move, 332, 193, 2, 306, 635]
[:mouse_move, 339, 193, 2, 307, 636]
[:mouse_move, 341, 193, 2, 308, 637]
[:mouse_move, 344, 193, 2, 309, 638]
[:mouse_move, 346, 194, 2, 310, 639]
[:mouse_move, 350, 194, 2, 311, 640]
[:mouse_move, 352, 194, 2, 312, 641]
[:mouse_move, 353, 194, 2, 313, 642]
[:mouse_move, 354, 194, 2, 314, 643]
[:mouse_move, 354, 193, 2, 315, 644]
[:mouse_move, 355, 193, 2, 316, 645]
[:mouse_move, 356, 193, 2, 317, 649]
[:mouse_move, 357, 192, 2, 318, 651]
[:mouse_move, 358, 192, 2, 319, 654]
[:mouse_move, 359, 191, 2, 320, 659]
[:mouse_move, 360, 191, 2, 321, 660]
[:mouse_move, 361, 190, 2, 322, 661]
[:mouse_move, 362, 189, 2, 323, 662]
[:mouse_move, 363, 189, 2, 324, 663]
[:mouse_move, 364, 188, 2, 325, 664]
[:mouse_move, 365, 188, 2, 326, 665]
[:mouse_move, 366, 188, 2, 327, 667]
[:mouse_move, 367, 187, 2, 328, 669]
[:mouse_move, 368, 187, 2, 329, 671]
[:mouse_move, 369, 187, 2, 330, 672]
[:mouse_move, 370, 186, 2, 331, 673]
[:mouse_move, 371, 186, 2, 332, 675]
[:mouse_move, 371, 185, 2, 333, 676]
[:mouse_move, 372, 185, 2, 334, 678]
[:mouse_move, 372, 184, 2, 335, 679]
[:mouse_move, 372, 183, 2, 336, 683]
[:mouse_move, 371, 183, 2, 337, 684]
[:mouse_move, 372, 183, 2, 338, 692]
[:mouse_move, 372, 182, 2, 339, 694]
[:mouse_move, 373, 182, 2, 340, 695]
[:mouse_move, 374, 182, 2, 341, 696]
[:mouse_move, 374, 181, 2, 342, 698]
[:mouse_move, 375, 181, 2, 343, 699]
[:mouse_move, 376, 180, 2, 344, 700]
[:mouse_move, 377, 180, 2, 345, 702]
[:mouse_move, 376, 180, 2, 346, 709]
[:mouse_move, 375, 180, 2, 347, 711]
[:mouse_move, 374, 180, 2, 348, 715]
[:mouse_move, 371, 180, 2, 349, 730]
[:mouse_move, 363, 180, 2, 350, 731]
[:mouse_move, 358, 180, 2, 351, 732]
[:mouse_move, 352, 180, 2, 352, 733]
[:mouse_move, 338, 182, 2, 353, 734]
[:mouse_move, 333, 182, 2, 354, 735]
[:mouse_move, 327, 183, 2, 355, 736]
[:mouse_move, 324, 183, 2, 356, 737]
[:mouse_move, 309, 183, 2, 357, 738]
[:mouse_move, 303, 183, 2, 358, 739]
[:mouse_move, 295, 182, 2, 359, 740]
[:mouse_move, 293, 182, 2, 360, 741]
[:mouse_move, 292, 182, 2, 361, 742]
[:mouse_move, 291, 182, 2, 362, 744]
[:mouse_move, 288, 182, 2, 363, 746]
[:mouse_move, 286, 182, 2, 364, 747]
[:mouse_move, 281, 182, 2, 365, 748]
[:mouse_move, 278, 182, 2, 366, 749]
[:mouse_move, 270, 183, 2, 367, 750]
[:mouse_move, 268, 183, 2, 368, 751]
[:mouse_move, 266, 183, 2, 369, 752]
[:mouse_move, 265, 183, 2, 370, 753]
[:mouse_move, 262, 183, 2, 371, 754]
[:mouse_move, 257, 183, 2, 372, 755]
[:mouse_move, 252, 183, 2, 373, 756]
[:mouse_move, 251, 183, 2, 374, 757]
[:mouse_move, 250, 183, 2, 375, 758]
[:mouse_move, 249, 183, 2, 376, 769]
[:mouse_move, 247, 182, 2, 377, 773]
[:mouse_move, 243, 182, 2, 378, 774]
[:mouse_move, 242, 182, 2, 379, 775]
[:mouse_move, 243, 182, 2, 380, 796]
[:mouse_move, 244, 182, 2, 381, 800]
[:mouse_move, 245, 182, 2, 382, 806]
[:mouse_move, 246, 182, 2, 383, 824]
[:mouse_move, 247, 182, 2, 384, 827]
[:mouse_move, 248, 183, 2, 385, 850]
[:mouse_move, 249, 183, 2, 386, 855]
[:mouse_button_up, 1, 0, 1, 387, 893]
[:mouse_move, 249, 184, 2, 388, 904]
[:mouse_move, 249, 185, 2, 389, 905]
[:mouse_move, 249, 191, 2, 390, 906]
[:mouse_move, 249, 196, 2, 391, 907]
[:mouse_move, 247, 208, 2, 392, 908]
[:mouse_move, 246, 214, 2, 393, 909]
[:mouse_move, 244, 223, 2, 394, 910]
[:mouse_move, 244, 225, 2, 395, 911]
[:mouse_move, 243, 226, 2, 396, 912]
[:mouse_move, 243, 227, 2, 397, 913]
[:mouse_move, 242, 229, 2, 398, 914]
[:mouse_move, 241, 229, 2, 399, 915]
[:mouse_move, 241, 230, 2, 400, 916]
[:mouse_move, 240, 230, 2, 401, 919]
[:mouse_move, 240, 231, 2, 402, 920]
[:mouse_move, 239, 232, 2, 403, 922]
[:mouse_move, 239, 231, 2, 404, 945]
[:mouse_move, 240, 230, 2, 405, 947]
[:mouse_button_pressed, 1, 0, 1, 406, 960]
[:mouse_button_up, 1, 0, 1, 407, 966]
[:mouse_move, 243, 230, 2, 408, 1005]
[:mouse_move, 245, 229, 2, 409, 1007]
[:mouse_move, 247, 229, 2, 410, 1009]
[:mouse_move, 251, 229, 2, 411, 1010]
[:mouse_move, 259, 228, 2, 412, 1011]
[:mouse_move, 262, 228, 2, 413, 1012]
[:mouse_move, 264, 228, 2, 414, 1013]
[:mouse_move, 271, 227, 2, 415, 1014]
[:mouse_move, 277, 227, 2, 416, 1015]
[:mouse_move, 288, 226, 2, 417, 1016]
[:mouse_move, 292, 225, 2, 418, 1017]
[:mouse_move, 304, 225, 2, 419, 1018]
[:mouse_move, 308, 225, 2, 420, 1019]
[:mouse_move, 316, 225, 2, 421, 1020]
[:mouse_move, 319, 224, 2, 422, 1021]
[:mouse_move, 324, 223, 2, 423, 1022]
[:mouse_move, 327, 222, 2, 424, 1023]
[:mouse_move, 334, 222, 2, 425, 1024]
[:mouse_move, 340, 222, 2, 426, 1025]
[:mouse_move, 351, 224, 2, 427, 1026]
[:mouse_move, 355, 225, 2, 428, 1027]
[:mouse_move, 359, 225, 2, 429, 1028]
[:mouse_move, 360, 225, 2, 430, 1029]
[:mouse_move, 361, 225, 2, 431, 1034]
[:mouse_move, 362, 225, 2, 432, 1036]
[:mouse_move, 363, 225, 2, 433, 1037]
[:mouse_move, 365, 225, 2, 434, 1038]
[:mouse_move, 366, 226, 2, 435, 1040]
[:mouse_move, 367, 227, 2, 436, 1043]
[:mouse_move, 368, 227, 2, 437, 1047]
[:mouse_move, 369, 227, 2, 438, 1051]
[:mouse_move, 370, 227, 2, 439, 1052]
[:mouse_move, 371, 228, 2, 440, 1053]
[:mouse_move, 372, 228, 2, 441, 1054]
[:mouse_move, 373, 228, 2, 442, 1057]
[:mouse_move, 374, 228, 2, 443, 1061]
[:mouse_button_pressed, 1, 0, 1, 444, 1066]
[:mouse_move, 375, 229, 2, 445, 1079]
[:mouse_move, 377, 229, 2, 446, 1080]
[:mouse_move, 378, 229, 2, 447, 1082]
[:mouse_move, 378, 230, 2, 448, 1088]
[:mouse_move, 379, 230, 2, 449, 1089]
[:mouse_move, 380, 230, 2, 450, 1090]
[:mouse_move, 381, 230, 2, 451, 1091]
[:mouse_move, 382, 230, 2, 452, 1092]
[:mouse_move, 383, 230, 2, 453, 1094]
[:mouse_button_up, 1, 0, 1, 454, 1123]
[:mouse_move, 382, 230, 2, 455, 1124]
[:mouse_move, 381, 230, 2, 456, 1125]
[:mouse_move, 377, 229, 2, 457, 1126]
[:mouse_move, 371, 229, 2, 458, 1127]
[:mouse_move, 351, 233, 2, 459, 1128]
[:mouse_move, 340, 236, 2, 460, 1129]
[:mouse_move, 312, 244, 2, 461, 1130]
[:mouse_move, 232, 261, 2, 462, 1132]
[:mouse_move, 213, 263, 2, 463, 1133]
[:mouse_move, 185, 265, 2, 464, 1134]
[:mouse_move, 172, 265, 2, 465, 1135]
[:mouse_move, 155, 265, 2, 466, 1136]
[:mouse_move, 153, 265, 2, 467, 1137]
[:mouse_move, 152, 265, 2, 468, 1143]
[:mouse_move, 152, 264, 2, 469, 1148]
[:mouse_move, 151, 264, 2, 470, 1150]
[:mouse_move, 150, 264, 2, 471, 1151]
[:mouse_move, 147, 266, 2, 472, 1152]
[:mouse_move, 142, 271, 2, 473, 1153]
[:mouse_move, 141, 273, 2, 474, 1154]
[:mouse_move, 139, 279, 2, 475, 1155]
[:mouse_move, 139, 280, 2, 476, 1156]
[:mouse_move, 138, 283, 2, 477, 1157]
[:mouse_move, 138, 285, 2, 478, 1158]
[:mouse_move, 138, 286, 2, 479, 1159]
[:mouse_move, 138, 287, 2, 480, 1163]
[:mouse_move, 138, 288, 2, 481, 1164]
[:mouse_move, 139, 289, 2, 482, 1165]
[:mouse_move, 138, 289, 2, 483, 1171]
[:mouse_move, 137, 288, 2, 484, 1172]
[:mouse_move, 136, 286, 2, 485, 1173]
[:mouse_move, 135, 285, 2, 486, 1174]
[:mouse_move, 135, 284, 2, 487, 1177]
[:mouse_button_pressed, 1, 0, 1, 488, 1183]
[:mouse_button_up, 1, 0, 1, 489, 1186]
[:mouse_move, 136, 284, 2, 490, 1190]
[:mouse_move, 137, 284, 2, 491, 1193]
[:mouse_button_pressed, 1, 0, 1, 492, 1231]
[:mouse_button_up, 1, 0, 1, 493, 1233]
[:mouse_move, 138, 281, 2, 494, 1242]
[:mouse_move, 140, 279, 2, 495, 1243]
[:mouse_move, 150, 267, 2, 496, 1244]
[:mouse_move, 157, 260, 2, 497, 1245]
[:mouse_move, 171, 248, 2, 498, 1246]
[:mouse_move, 180, 242, 2, 499, 1247]
[:mouse_move, 206, 229, 2, 500, 1248]
[:mouse_move, 222, 224, 2, 501, 1249]
[:mouse_move, 248, 217, 2, 502, 1250]
[:mouse_move, 257, 216, 2, 503, 1251]
[:mouse_move, 266, 215, 2, 504, 1252]
[:mouse_move, 266, 214, 2, 505, 1261]
[:mouse_move, 266, 208, 2, 506, 1263]
[:mouse_move, 265, 207, 2, 507, 1264]
[:mouse_move, 258, 202, 2, 508, 1265]
[:mouse_move, 256, 201, 2, 509, 1266]
[:mouse_move, 250, 200, 2, 510, 1267]
[:mouse_move, 249, 200, 2, 511, 1268]
[:mouse_move, 249, 199, 2, 512, 1269]
[:mouse_move, 248, 199, 2, 513, 1272]
[:mouse_move, 248, 200, 2, 514, 1285]
[:mouse_move, 245, 202, 2, 515, 1286]
[:mouse_move, 244, 205, 2, 516, 1287]
[:mouse_move, 243, 205, 2, 517, 1288]
[:mouse_move, 241, 207, 2, 518, 1289]
[:mouse_move, 241, 208, 2, 519, 1290]
[:mouse_move, 240, 208, 2, 520, 1291]
[:mouse_move, 239, 209, 2, 521, 1292]
[:mouse_move, 238, 209, 2, 522, 1293]
[:mouse_move, 238, 208, 2, 523, 1299]
[:mouse_move, 238, 207, 2, 524, 1300]
[:mouse_move, 238, 206, 2, 525, 1302]
[:mouse_move, 238, 205, 2, 526, 1304]
[:mouse_move, 239, 205, 2, 527, 1305]
[:mouse_move, 239, 204, 2, 528, 1306]
[:mouse_move, 243, 205, 2, 529, 1336]
[:mouse_move, 257, 207, 2, 530, 1337]
[:mouse_move, 267, 208, 2, 531, 1338]
[:mouse_move, 294, 210, 2, 532, 1339]
[:mouse_move, 308, 211, 2, 533, 1340]
[:mouse_move, 337, 212, 2, 534, 1341]
[:mouse_move, 350, 212, 2, 535, 1342]
[:mouse_move, 361, 212, 2, 536, 1343]
[:mouse_move, 363, 211, 2, 537, 1344]
[:mouse_move, 365, 211, 2, 538, 1345]
[:mouse_move, 366, 211, 2, 539, 1346]
[:mouse_move, 367, 210, 2, 540, 1348]
[:mouse_button_pressed, 1, 0, 1, 541, 1356]
[:mouse_button_up, 1, 0, 1, 542, 1361]
[:mouse_move, 363, 210, 2, 543, 1388]
[:mouse_move, 346, 213, 2, 544, 1389]
[:mouse_move, 331, 214, 2, 545, 1390]
[:mouse_move, 290, 218, 2, 546, 1391]
[:mouse_move, 271, 218, 2, 547, 1392]
[:mouse_move, 241, 218, 2, 548, 1393]
[:mouse_move, 232, 218, 2, 549, 1394]
[:mouse_move, 224, 217, 2, 550, 1395]
[:mouse_move, 222, 217, 2, 551, 1396]
[:mouse_move, 218, 216, 2, 552, 1397]
[:mouse_move, 215, 215, 2, 553, 1398]
[:mouse_move, 211, 214, 2, 554, 1399]
[:mouse_move, 211, 213, 2, 555, 1400]
[:mouse_move, 210, 213, 2, 556, 1401]
[:mouse_move, 210, 212, 2, 557, 1404]
[:mouse_move, 211, 212, 2, 558, 1405]
[:mouse_move, 212, 212, 2, 559, 1406]
[:mouse_move, 213, 211, 2, 560, 1407]
[:mouse_move, 215, 210, 2, 561, 1408]
[:mouse_move, 218, 210, 2, 562, 1409]
[:mouse_move, 224, 210, 2, 563, 1410]
[:mouse_button_pressed, 1, 0, 1, 564, 1414]
[:mouse_button_up, 1, 0, 1, 565, 1420]
[:mouse_move, 227, 210, 2, 566, 1427]
[:mouse_move, 233, 209, 2, 567, 1428]
[:mouse_move, 236, 209, 2, 568, 1429]
[:mouse_move, 237, 209, 2, 569, 1430]
[:mouse_move, 239, 208, 2, 570, 1431]
[:mouse_move, 241, 208, 2, 571, 1433]
[:mouse_move, 242, 208, 2, 572, 1434]
[:mouse_move, 244, 207, 2, 573, 1435]
[:mouse_move, 245, 207, 2, 574, 1436]
[:mouse_move, 246, 207, 2, 575, 1437]
[:mouse_move, 247, 207, 2, 576, 1450]
[:mouse_move, 249, 207, 2, 577, 1451]
[:mouse_move, 251, 206, 2, 578, 1452]
[:mouse_move, 255, 205, 2, 579, 1453]
[:mouse_move, 256, 205, 2, 580, 1455]
[:mouse_move, 255, 205, 2, 581, 1470]
[:mouse_move, 251, 206, 2, 582, 1471]
[:mouse_move, 233, 207, 2, 583, 1472]
[:mouse_move, 223, 207, 2, 584, 1473]
[:mouse_move, 201, 208, 2, 585, 1474]
[:mouse_move, 185, 209, 2, 586, 1475]
[:mouse_move, 127, 210, 2, 587, 1476]
[:mouse_move, 107, 211, 2, 588, 1477]
[:mouse_move, 97, 211, 2, 589, 1478]
[:mouse_move, 98, 211, 2, 590, 1485]
[:mouse_move, 99, 211, 2, 591, 1486]
[:mouse_move, 100, 211, 2, 592, 1487]
[:mouse_move, 102, 210, 2, 593, 1488]
[:mouse_move, 106, 209, 2, 594, 1489]
[:mouse_move, 110, 208, 2, 595, 1490]
[:mouse_move, 119, 206, 2, 596, 1491]
[:mouse_move, 124, 206, 2, 597, 1492]
[:mouse_move, 126, 205, 2, 598, 1493]
[:mouse_move, 127, 205, 2, 599, 1494]
[:mouse_move, 128, 205, 2, 600, 1499]
[:mouse_move, 128, 204, 2, 601, 1502]
[:mouse_move, 128, 203, 2, 602, 1507]
[:mouse_move, 127, 203, 2, 603, 1511]
[:mouse_button_pressed, 1, 0, 1, 604, 1516]
[:mouse_button_up, 1, 0, 1, 605, 1521]
[:mouse_move, 131, 205, 2, 606, 1534]
[:mouse_move, 132, 206, 2, 607, 1535]
[:mouse_move, 138, 211, 2, 608, 1536]
[:mouse_move, 142, 214, 2, 609, 1537]
[:mouse_move, 151, 223, 2, 610, 1538]
[:mouse_move, 157, 229, 2, 611, 1539]
[:mouse_move, 171, 248, 2, 612, 1540]
[:mouse_move, 179, 259, 2, 613, 1541]
[:mouse_move, 184, 270, 2, 614, 1542]
[:mouse_move, 195, 289, 2, 615, 1543]
[:mouse_move, 200, 297, 2, 616, 1544]
[:mouse_move, 207, 309, 2, 617, 1545]
[:mouse_move, 209, 313, 2, 618, 1546]
[:mouse_move, 211, 316, 2, 619, 1547]
[:mouse_move, 211, 317, 2, 620, 1548]
[:mouse_move, 212, 319, 2, 621, 1549]
[:mouse_move, 212, 320, 2, 622, 1551]
[:mouse_move, 213, 320, 2, 623, 1552]
[:mouse_move, 213, 322, 2, 624, 1554]
[:mouse_move, 216, 327, 2, 625, 1555]
[:mouse_move, 217, 329, 2, 626, 1556]
[:mouse_move, 220, 333, 2, 627, 1557]
[:mouse_move, 221, 335, 2, 628, 1558]
[:mouse_move, 221, 336, 2, 629, 1559]
[:mouse_move, 222, 337, 2, 630, 1560]
[:mouse_move, 223, 340, 2, 631, 1561]
[:mouse_move, 226, 343, 2, 632, 1562]
[:mouse_move, 232, 348, 2, 633, 1563]
[:mouse_move, 235, 350, 2, 634, 1564]
[:mouse_move, 236, 350, 2, 635, 1565]
[:mouse_move, 236, 349, 2, 636, 1602]
[:mouse_move, 241, 339, 2, 637, 1603]
[:mouse_move, 242, 332, 2, 638, 1604]
[:mouse_move, 246, 318, 2, 639, 1605]
[:mouse_move, 246, 311, 2, 640, 1606]
[:mouse_move, 245, 288, 2, 641, 1607]
[:mouse_move, 241, 271, 2, 642, 1608]
[:mouse_move, 226, 240, 2, 643, 1609]
[:mouse_move, 219, 228, 2, 644, 1610]
[:mouse_move, 214, 218, 2, 645, 1611]
[:mouse_move, 214, 217, 2, 646, 1612]
[:mouse_move, 214, 215, 2, 647, 1613]
[:mouse_move, 214, 214, 2, 648, 1614]
[:mouse_move, 214, 213, 2, 649, 1617]
[:mouse_move, 215, 212, 2, 650, 1619]
[:mouse_move, 216, 212, 2, 651, 1620]
[:mouse_move, 223, 214, 2, 652, 1621]
[:mouse_move, 226, 216, 2, 653, 1622]
[:mouse_move, 230, 218, 2, 654, 1623]
[:mouse_move, 231, 218, 2, 655, 1624]
[:mouse_move, 232, 218, 2, 656, 1625]
[:mouse_move, 232, 219, 2, 657, 1628]
[:mouse_move, 233, 220, 2, 658, 1633]
[:mouse_move, 237, 223, 2, 659, 1634]
[:mouse_move, 238, 224, 2, 660, 1635]
[:mouse_move, 241, 226, 2, 661, 1636]
[:mouse_move, 242, 227, 2, 662, 1637]
[:mouse_move, 243, 227, 2, 663, 1640]
[:mouse_move, 244, 227, 2, 664, 1642]
[:mouse_move, 245, 227, 2, 665, 1644]
[:mouse_move, 246, 227, 2, 666, 1651]
[:mouse_move, 247, 227, 2, 667, 1654]
[:mouse_move, 248, 228, 2, 668, 1656]
[:mouse_button_pressed, 1, 0, 1, 669, 1675]
[:mouse_button_up, 1, 0, 1, 670, 1679]
[:mouse_move, 250, 235, 2, 671, 1698]
[:mouse_move, 254, 245, 2, 672, 1699]
[:mouse_move, 265, 275, 2, 673, 1700]
[:mouse_move, 274, 296, 2, 674, 1701]
[:mouse_move, 301, 366, 2, 675, 1702]
[:mouse_move, 322, 417, 2, 676, 1703]
[:mouse_move, 346, 474, 2, 677, 1704]
[:mouse_move, 386, 571, 2, 678, 1705]
[:mouse_move, 409, 614, 2, 679, 1706]
[:mouse_move, 417, 626, 2, 680, 1707]
[:mouse_move, 425, 638, 2, 681, 1708]
[:mouse_move, 435, 657, 2, 682, 1710]
[:mouse_move, 437, 662, 2, 683, 1710]
[:mouse_move, 438, 664, 2, 684, 1711]
[:mouse_move, 438, 665, 2, 685, 1712]
[:mouse_move, 438, 666, 2, 686, 1714]
[:mouse_move, 434, 672, 2, 687, 1715]
[:mouse_move, 430, 675, 2, 688, 1716]
[:mouse_move, 429, 678, 2, 689, 1717]
[:mouse_move, 429, 679, 2, 690, 1721]
[:mouse_move, 428, 683, 2, 691, 1722]
[:mouse_move, 424, 694, 2, 692, 1723]
[:mouse_move, 423, 697, 2, 693, 1724]
[:mouse_move, 423, 698, 2, 694, 1725]
[:mouse_move, 424, 698, 2, 695, 1727]
[:mouse_move, 424, 697, 2, 696, 1728]
[:mouse_move, 430, 686, 2, 697, 1729]
[:mouse_move, 430, 679, 2, 698, 1730]
[:mouse_move, 434, 669, 2, 699, 1731]
[:mouse_move, 435, 667, 2, 700, 1732]
[:mouse_move, 435, 664, 2, 701, 1733]
[:mouse_move, 435, 663, 2, 702, 1734]
[:mouse_move, 435, 662, 2, 703, 1735]
[:mouse_move, 435, 661, 2, 704, 1736]
[:mouse_move, 435, 660, 2, 705, 1738]
[:mouse_move, 436, 659, 2, 706, 1739]
[:mouse_move, 436, 658, 2, 707, 1740]
[:mouse_move, 437, 655, 2, 708, 1741]
[:mouse_move, 440, 649, 2, 709, 1742]
[:mouse_move, 440, 648, 2, 710, 1744]
[:mouse_move, 440, 647, 2, 711, 1744]
[:mouse_move, 458, 647, 2, 712, 1761]
[:mouse_move, 473, 647, 2, 713, 1762]
[:mouse_move, 503, 646, 2, 714, 1763]
[:mouse_move, 511, 644, 2, 715, 1764]
[:mouse_move, 512, 644, 2, 716, 1765]
[:mouse_move, 515, 644, 2, 717, 1770]
[:mouse_move, 533, 641, 2, 718, 1771]
[:mouse_move, 546, 641, 2, 719, 1772]
[:mouse_move, 580, 640, 2, 720, 1773]
[:mouse_move, 600, 640, 2, 721, 1774]
[:mouse_move, 643, 639, 2, 722, 1775]
[:mouse_move, 661, 639, 2, 723, 1776]
[:mouse_move, 682, 639, 2, 724, 1777]
[:mouse_move, 690, 639, 2, 725, 1778]
[:mouse_move, 693, 639, 2, 726, 1779]
[:mouse_move, 696, 639, 2, 727, 1783]
[:mouse_move, 697, 639, 2, 728, 1784]
[:mouse_move, 698, 639, 2, 729, 1785]
[:mouse_move, 699, 639, 2, 730, 1786]
[:mouse_move, 702, 641, 2, 731, 1787]
[:mouse_move, 704, 643, 2, 732, 1788]
[:mouse_move, 709, 645, 2, 733, 1789]
[:mouse_move, 712, 645, 2, 734, 1790]
[:mouse_move, 715, 645, 2, 735, 1791]
[:mouse_move, 717, 645, 2, 736, 1792]
[:mouse_move, 721, 645, 2, 737, 1793]
[:mouse_move, 723, 645, 2, 738, 1794]
[:mouse_move, 724, 645, 2, 739, 1795]
[:mouse_move, 730, 645, 2, 740, 1796]
[:mouse_move, 733, 645, 2, 741, 1797]
[:mouse_move, 734, 645, 2, 742, 1798]
[:mouse_button_pressed, 1, 0, 1, 743, 1800]
[:mouse_move, 735, 645, 2, 744, 1801]
[:mouse_button_up, 1, 0, 1, 745, 1804]
[:mouse_move, 735, 644, 2, 746, 1810]
[:mouse_move, 734, 644, 2, 747, 1818]
[:mouse_move, 728, 642, 2, 748, 1820]
[:mouse_move, 719, 637, 2, 749, 1821]
[:mouse_move, 708, 632, 2, 750, 1822]
[:mouse_move, 665, 611, 2, 751, 1823]
[:mouse_move, 623, 591, 2, 752, 1824]
[:mouse_move, 509, 533, 2, 753, 1825]
[:mouse_move, 473, 514, 2, 754, 1826]
[:mouse_move, 453, 502, 2, 755, 1827]
[:mouse_move, 451, 500, 2, 756, 1829]
[:mouse_move, 448, 495, 2, 757, 1829]
[:mouse_move, 440, 482, 2, 758, 1831]
[:mouse_move, 439, 480, 2, 759, 1832]
[:mouse_move, 434, 474, 2, 760, 1833]
[:mouse_move, 430, 469, 2, 761, 1834]
[:mouse_move, 417, 459, 2, 762, 1835]
[:mouse_move, 411, 456, 2, 763, 1836]
[:mouse_move, 399, 452, 2, 764, 1837]
[:mouse_move, 390, 449, 2, 765, 1838]
[:mouse_move, 369, 442, 2, 766, 1839]
[:mouse_move, 362, 440, 2, 767, 1840]
[:mouse_move, 357, 439, 2, 768, 1841]
[:mouse_move, 360, 440, 2, 769, 1848]
[:mouse_move, 362, 441, 2, 770, 1849]
[:mouse_move, 364, 442, 2, 771, 1850]
[:mouse_move, 365, 442, 2, 772, 1854]
[:mouse_move, 365, 443, 2, 773, 1856]
[:mouse_move, 367, 443, 2, 774, 1856]
[:mouse_move, 368, 443, 2, 775, 1858]
[:mouse_move, 370, 443, 2, 776, 1859]
[:mouse_move, 375, 445, 2, 777, 1860]
[:mouse_move, 378, 445, 2, 778, 1861]
[:mouse_move, 381, 445, 2, 779, 1862]
[:mouse_move, 382, 445, 2, 780, 1863]
[:mouse_move, 383, 445, 2, 781, 1865]
[:mouse_move, 383, 444, 2, 782, 1927]
[:mouse_button_pressed, 1, 0, 1, 783, 1936]
[:mouse_move, 377, 443, 2, 784, 1937]
[:mouse_move, 370, 443, 2, 785, 1939]
[:mouse_move, 347, 443, 2, 786, 1940]
[:mouse_move, 331, 444, 2, 787, 1941]
[:mouse_move, 301, 448, 2, 788, 1942]
[:mouse_move, 281, 452, 2, 789, 1943]
[:mouse_move, 239, 461, 2, 790, 1944]
[:mouse_move, 219, 467, 2, 791, 1945]
[:mouse_move, 188, 476, 2, 792, 1946]
[:mouse_move, 179, 482, 2, 793, 1947]
[:mouse_move, 168, 491, 2, 794, 1948]
[:mouse_move, 164, 495, 2, 795, 1949]
[:mouse_move, 160, 499, 2, 796, 1950]
[:mouse_move, 160, 500, 2, 797, 1951]
[:mouse_move, 162, 498, 2, 798, 1961]
[:mouse_move, 165, 496, 2, 799, 1962]
[:mouse_move, 183, 486, 2, 800, 1963]
[:mouse_move, 200, 476, 2, 801, 1964]
[:mouse_move, 281, 430, 2, 802, 1965]
[:mouse_move, 344, 398, 2, 803, 1966]
[:mouse_move, 506, 339, 2, 804, 1967]
[:mouse_move, 596, 318, 2, 805, 1968]
[:mouse_move, 743, 293, 2, 806, 1969]
[:mouse_move, 795, 284, 2, 807, 1970]
[:mouse_move, 874, 275, 2, 808, 1971]
[:mouse_move, 900, 274, 2, 809, 1972]
[:mouse_move, 942, 275, 2, 810, 1973]
[:mouse_move, 959, 278, 2, 811, 1974]
[:mouse_move, 994, 284, 2, 812, 1975]
[:mouse_move, 1008, 288, 2, 813, 1976]
[:mouse_move, 1031, 295, 2, 814, 1976]
[:mouse_move, 1044, 299, 2, 815, 1978]
[:mouse_move, 1068, 310, 2, 816, 1979]
[:mouse_move, 1080, 314, 2, 817, 1980]
[:mouse_move, 1103, 325, 2, 818, 1981]
[:mouse_move, 1110, 328, 2, 819, 1982]
[:mouse_move, 1121, 334, 2, 820, 1983]
[:mouse_move, 1123, 336, 2, 821, 1984]
[:mouse_move, 1126, 338, 2, 822, 1985]
[:mouse_move, 1127, 338, 2, 823, 1986]
[:mouse_move, 1126, 338, 2, 824, 1995]
[:mouse_move, 1112, 335, 2, 825, 1996]
[:mouse_move, 1097, 332, 2, 826, 1997]
[:mouse_move, 1053, 323, 2, 827, 1998]
[:mouse_move, 1020, 317, 2, 828, 1999]
[:mouse_move, 948, 304, 2, 829, 2000]
[:mouse_move, 915, 298, 2, 830, 2001]
[:mouse_move, 832, 290, 2, 831, 2002]
[:mouse_move, 788, 288, 2, 832, 2003]
[:mouse_move, 722, 287, 2, 833, 2004]
[:mouse_move, 691, 291, 2, 834, 2005]
[:mouse_move, 628, 305, 2, 835, 2006]
[:mouse_move, 601, 315, 2, 836, 2007]
[:mouse_move, 557, 333, 2, 837, 2008]
[:mouse_move, 538, 343, 2, 838, 2009]
[:mouse_move, 502, 361, 2, 839, 2010]
[:mouse_move, 486, 372, 2, 840, 2011]
[:mouse_move, 463, 390, 2, 841, 2012]
[:mouse_move, 458, 394, 2, 842, 2013]
[:mouse_move, 458, 396, 2, 843, 2016]
[:mouse_move, 457, 397, 2, 844, 2017]
[:mouse_move, 457, 398, 2, 845, 2018]
[:mouse_move, 457, 399, 2, 846, 2019]
[:mouse_move, 456, 400, 2, 847, 2020]
[:mouse_move, 454, 405, 2, 848, 2021]
[:mouse_move, 454, 406, 2, 849, 2022]
[:mouse_move, 453, 408, 2, 850, 2029]
[:mouse_move, 453, 409, 2, 851, 2030]
[:mouse_move, 450, 413, 2, 852, 2031]
[:mouse_move, 449, 416, 2, 853, 2032]
[:mouse_move, 448, 418, 2, 854, 2033]
[:mouse_button_up, 1, 0, 1, 855, 2054]
[:mouse_move, 449, 416, 2, 856, 2073]
[:mouse_move, 450, 415, 2, 857, 2074]
[:mouse_move, 450, 414, 2, 858, 2075]
[:mouse_move, 449, 412, 2, 859, 2076]
[:mouse_move, 436, 402, 2, 860, 2077]
[:mouse_move, 419, 393, 2, 861, 2078]
[:mouse_move, 337, 363, 2, 862, 2079]
[:mouse_move, 279, 346, 2, 863, 2080]
[:mouse_move, 192, 321, 2, 864, 2081]
[:mouse_move, 175, 315, 2, 865, 2082]
[:mouse_move, 162, 308, 2, 866, 2083]
[:mouse_move, 161, 307, 2, 867, 2084]
[:mouse_move, 157, 306, 2, 868, 2085]
[:mouse_move, 156, 306, 2, 869, 2086]
[:mouse_move, 154, 306, 2, 870, 2087]
[:mouse_move, 154, 305, 2, 871, 2088]
[:mouse_move, 153, 304, 2, 872, 2090]
[:mouse_move, 150, 301, 2, 873, 2091]
[:mouse_move, 148, 300, 2, 874, 2092]
[:mouse_move, 142, 294, 2, 875, 2093]
[:mouse_move, 136, 289, 2, 876, 2094]
[:mouse_move, 125, 277, 2, 877, 2095]
[:mouse_move, 122, 274, 2, 878, 2096]
[:mouse_move, 122, 273, 2, 879, 2097]
[:mouse_move, 122, 272, 2, 880, 2098]
[:mouse_move, 128, 273, 2, 881, 2104]
[:mouse_move, 137, 274, 2, 882, 2105]
[:mouse_move, 166, 275, 2, 883, 2106]
[:mouse_move, 199, 276, 2, 884, 2107]
[:mouse_move, 320, 286, 2, 885, 2108]
[:mouse_move, 394, 295, 2, 886, 2109]
[:mouse_move, 533, 320, 2, 887, 2110]
[:mouse_move, 591, 335, 2, 888, 2111]
[:mouse_move, 645, 351, 2, 889, 2112]
[:mouse_move, 654, 354, 2, 890, 2113]
[:mouse_move, 667, 360, 2, 891, 2114]
[:mouse_move, 675, 365, 2, 892, 2115]
[:mouse_move, 690, 378, 2, 893, 2116]
[:mouse_move, 695, 383, 2, 894, 2117]
[:mouse_move, 699, 389, 2, 895, 2118]
[:mouse_move, 699, 390, 2, 896, 2119]
[:mouse_move, 700, 390, 2, 897, 2120]
[:mouse_move, 692, 387, 2, 898, 2124]
[:mouse_move, 681, 384, 2, 899, 2125]
[:mouse_move, 656, 381, 2, 900, 2126]
[:mouse_move, 650, 381, 2, 901, 2127]
[:mouse_move, 647, 381, 2, 902, 2128]
[:mouse_move, 649, 381, 2, 903, 2137]
[:mouse_move, 650, 382, 2, 904, 2138]
[:mouse_move, 651, 382, 2, 905, 2139]
[:mouse_button_pressed, 1, 0, 1, 906, 2144]
[:mouse_button_up, 1, 0, 1, 907, 2148]
[:mouse_move, 650, 382, 2, 908, 2165]
[:mouse_move, 647, 383, 2, 909, 2166]
[:mouse_move, 643, 384, 2, 910, 2167]
[:mouse_move, 629, 387, 2, 911, 2168]
[:mouse_move, 617, 390, 2, 912, 2169]
[:mouse_move, 587, 395, 2, 913, 2170]
[:mouse_move, 573, 397, 2, 914, 2171]
[:mouse_move, 541, 399, 2, 915, 2172]
[:mouse_move, 524, 401, 2, 916, 2173]
[:mouse_move, 488, 405, 2, 917, 2174]
[:mouse_move, 476, 407, 2, 918, 2175]
[:mouse_move, 465, 409, 2, 919, 2176]
[:mouse_move, 464, 409, 2, 920, 2177]
[:mouse_move, 462, 410, 2, 921, 2178]
[:mouse_move, 461, 410, 2, 922, 2179]
[:mouse_move, 459, 411, 2, 923, 2180]
[:mouse_move, 459, 412, 2, 924, 2186]
[:mouse_move, 459, 413, 2, 925, 2189]
[:mouse_move, 458, 413, 2, 926, 2190]
[:mouse_move, 451, 419, 2, 927, 2191]
[:mouse_move, 449, 421, 2, 928, 2192]
[:mouse_move, 448, 421, 2, 929, 2193]
[:mouse_move, 447, 421, 2, 930, 2194]
[:mouse_move, 445, 422, 2, 931, 2195]
[:mouse_move, 444, 422, 2, 932, 2196]
[:mouse_button_pressed, 1, 0, 1, 933, 2216]
[:mouse_button_up, 1, 0, 1, 934, 2220]
[:mouse_move, 442, 420, 2, 935, 2229]
[:mouse_move, 424, 411, 2, 936, 2230]
[:mouse_move, 406, 403, 2, 937, 2231]
[:mouse_move, 336, 378, 2, 938, 2232]
[:mouse_move, 286, 359, 2, 939, 2233]
[:mouse_move, 225, 334, 2, 940, 2234]
[:mouse_move, 213, 328, 2, 941, 2235]
[:mouse_move, 201, 321, 2, 942, 2236]
[:mouse_move, 198, 318, 2, 943, 2237]
[:mouse_move, 191, 314, 2, 944, 2238]
[:mouse_move, 185, 310, 2, 945, 2239]
[:mouse_move, 179, 305, 2, 946, 2240]
[:mouse_move, 171, 299, 2, 947, 2241]
[:mouse_move, 170, 297, 2, 948, 2242]
[:mouse_move, 170, 295, 2, 949, 2243]
[:mouse_move, 170, 294, 2, 950, 2244]
[:mouse_move, 169, 293, 2, 951, 2245]
[:mouse_move, 169, 291, 2, 952, 2246]
[:mouse_move, 168, 289, 2, 953, 2247]
[:mouse_move, 167, 289, 2, 954, 2248]
[:mouse_move, 167, 287, 2, 955, 2249]
[:mouse_move, 167, 286, 2, 956, 2250]
[:mouse_move, 164, 283, 2, 957, 2251]
[:mouse_move, 160, 279, 2, 958, 2252]
[:mouse_move, 143, 270, 2, 959, 2253]
[:mouse_move, 136, 267, 2, 960, 2254]
[:mouse_move, 130, 265, 2, 961, 2255]
[:mouse_move, 128, 265, 2, 962, 2256]
[:mouse_move, 125, 264, 2, 963, 2257]
[:mouse_move, 123, 263, 2, 964, 2258]
[:mouse_move, 120, 262, 2, 965, 2259]
[:mouse_move, 120, 261, 2, 966, 2260]
[:mouse_move, 120, 260, 2, 967, 2265]
[:mouse_move, 120, 259, 2, 968, 2267]
[:mouse_move, 121, 259, 2, 969, 2268]
[:mouse_move, 122, 259, 2, 970, 2269]
[:mouse_move, 124, 258, 2, 971, 2270]
[:mouse_move, 126, 257, 2, 972, 2271]
[:mouse_move, 130, 256, 2, 973, 2272]
[:mouse_move, 131, 256, 2, 974, 2274]
[:mouse_move, 132, 256, 2, 975, 2275]
[:mouse_button_pressed, 1, 0, 1, 976, 2290]
[:mouse_button_up, 1, 0, 1, 977, 2292]
[:mouse_move, 132, 258, 2, 978, 2305]
[:mouse_move, 133, 262, 2, 979, 2306]
[:mouse_move, 135, 278, 2, 980, 2307]
[:mouse_move, 136, 290, 2, 981, 2308]
[:mouse_move, 143, 325, 2, 982, 2309]
[:mouse_move, 146, 347, 2, 983, 2310]
[:mouse_move, 162, 411, 2, 984, 2311]
[:mouse_move, 173, 445, 2, 985, 2312]
[:mouse_move, 189, 482, 2, 986, 2313]
[:mouse_move, 195, 490, 2, 987, 2314]
[:mouse_move, 198, 492, 2, 988, 2315]
[:key_down_raw, 96, 0, 2, 989, 2426]
[:key_up_raw, 96, 0, 2, 990, 2430]
[:key_down_raw, 13, 0, 2, 991, 2551]
