class Animation
  def initialize (&block)
    @waiting = 0
    @fiber = Fiber.new do
      instance_eval(&block)
      :done
    end
    puts Fiber.instance_methods(false)
  end

  def update args
    args.outputs.debug << "#{@fiber.instance_methods(false)}"
    return if done?

    if @waiting
      @waiting -= 1
      return
    end
    
    result = @fiber.resume
    if result.is_a?(Integer)
      @waiting = result
    end
  end

  def done?
    !@fiber.alive? && !@waiting
  end

  def wait ticks
    @fiber.yield ticks 
  end

  def set_frame(sprite, path)
    sprite.path = path
  end

  def play_sound(args, path)
    args.outputs.sounds << path
  end
end 

def boot args
  args.state = {}
end

def tick args
  args.state.sprite ||= { x: 100, y: 100, w: 64, h: 64, path: "sprites/square/blue.png" }

  args.state.anim ||= Animation.new do
    set_frame(args.state.sprite, "sprites/square/blue.png")
    wait 120
    set_frame(args.state.sprite, "sprites/square/red.png")
    wait 120
    set_frame(args.state.sprite, "sprites/square/green.png")
    wait 120
  end

  #args.state.anim.update args
  #args.outputs.sprites << args.state.sprite
end
