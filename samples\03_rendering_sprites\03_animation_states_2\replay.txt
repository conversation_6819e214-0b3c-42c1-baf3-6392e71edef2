replay_version 2.1
stopped_at 403
seed 100
recorded_at 2024-12-21 16:08:43 -0600
[:mouse_button_up, 1, 0, 1, 1, 3]
[:mouse_move, 912, 133, 2, 2, 9]
[:mouse_move, 912, 137, 2, 3, 10]
[:mouse_move, 912, 145, 2, 4, 11]
[:mouse_move, 912, 163, 2, 5, 12]
[:mouse_move, 912, 171, 2, 6, 14]
[:mouse_move, 912, 180, 2, 7, 15]
[:mouse_move, 912, 194, 2, 8, 16]
[:mouse_move, 908, 226, 2, 9, 17]
[:mouse_move, 902, 246, 2, 10, 18]
[:mouse_move, 895, 263, 2, 11, 19]
[:mouse_move, 888, 277, 2, 12, 21]
[:mouse_move, 880, 289, 2, 13, 22]
[:mouse_move, 877, 293, 2, 14, 23]
[:mouse_move, 874, 298, 2, 15, 24]
[:mouse_move, 873, 300, 2, 16, 25]
[:mouse_move, 872, 301, 2, 17, 26]
[:key_down_raw, 1073741903, 0, 2, 18, 73]
[:scancode_down_raw, 79, 0, 2, 19, 73]
[:key_down_raw, 1073741903, 0, 2, 20, 87]
[:scancode_down_raw, 79, 0, 2, 21, 87]
[:key_up_raw, 1073741903, 0, 2, 22, 88]
[:scancode_up_raw, 79, 0, 2, 23, 88]
[:key_down_raw, 1073741903, 0, 2, 24, 121]
[:scancode_down_raw, 79, 0, 2, 25, 121]
[:key_up_raw, 1073741903, 0, 2, 26, 124]
[:scancode_up_raw, 79, 0, 2, 27, 124]
[:key_down_raw, 1073741903, 0, 2, 28, 148]
[:scancode_down_raw, 79, 0, 2, 29, 148]
[:key_up_raw, 1073741903, 0, 2, 30, 152]
[:scancode_up_raw, 79, 0, 2, 31, 152]
[:key_down_raw, 1073741903, 0, 2, 32, 177]
[:scancode_down_raw, 79, 0, 2, 33, 177]
[:key_down_raw, 1073741903, 0, 2, 34, 192]
[:scancode_down_raw, 79, 0, 2, 35, 192]
[:key_down_raw, 1073741903, 0, 2, 36, 194]
[:scancode_down_raw, 79, 0, 2, 37, 194]
[:key_down_raw, 1073741903, 0, 2, 38, 196]
[:scancode_down_raw, 79, 0, 2, 39, 196]
[:key_down_raw, 1073741903, 0, 2, 40, 198]
[:scancode_down_raw, 79, 0, 2, 41, 198]
[:key_down_raw, 1073741903, 0, 2, 42, 200]
[:scancode_down_raw, 79, 0, 2, 43, 200]
[:key_down_raw, 1073741903, 0, 2, 44, 202]
[:scancode_down_raw, 79, 0, 2, 45, 202]
[:key_down_raw, 1073741903, 0, 2, 46, 204]
[:scancode_down_raw, 79, 0, 2, 47, 204]
[:key_down_raw, 1073741903, 0, 2, 48, 206]
[:scancode_down_raw, 79, 0, 2, 49, 206]
[:key_down_raw, 1073741903, 0, 2, 50, 208]
[:scancode_down_raw, 79, 0, 2, 51, 208]
[:key_down_raw, 1073741903, 0, 2, 52, 210]
[:scancode_down_raw, 79, 0, 2, 53, 210]
[:key_down_raw, 1073741903, 0, 2, 54, 212]
[:scancode_down_raw, 79, 0, 2, 55, 212]
[:key_down_raw, 1073741903, 0, 2, 56, 214]
[:scancode_down_raw, 79, 0, 2, 57, 214]
[:key_down_raw, 1073741903, 0, 2, 58, 216]
[:scancode_down_raw, 79, 0, 2, 59, 216]
[:key_down_raw, 1073741903, 0, 2, 60, 218]
[:scancode_down_raw, 79, 0, 2, 61, 218]
[:key_down_raw, 1073741903, 0, 2, 62, 220]
[:scancode_down_raw, 79, 0, 2, 63, 220]
[:key_down_raw, 1073741903, 0, 2, 64, 222]
[:scancode_down_raw, 79, 0, 2, 65, 222]
[:key_down_raw, 1073741903, 0, 2, 66, 224]
[:scancode_down_raw, 79, 0, 2, 67, 224]
[:key_down_raw, 1073741903, 0, 2, 68, 226]
[:scancode_down_raw, 79, 0, 2, 69, 226]
[:key_down_raw, 1073741903, 0, 2, 70, 228]
[:scancode_down_raw, 79, 0, 2, 71, 228]
[:key_down_raw, 1073741903, 0, 2, 72, 230]
[:scancode_down_raw, 79, 0, 2, 73, 230]
[:key_down_raw, 1073741903, 0, 2, 74, 232]
[:scancode_down_raw, 79, 0, 2, 75, 232]
[:key_down_raw, 1073741903, 0, 2, 76, 234]
[:scancode_down_raw, 79, 0, 2, 77, 234]
[:key_down_raw, 1073741903, 0, 2, 78, 237]
[:scancode_down_raw, 79, 0, 2, 79, 237]
[:key_down_raw, 1073741903, 0, 2, 80, 239]
[:scancode_down_raw, 79, 0, 2, 81, 239]
[:key_down_raw, 1073741903, 0, 2, 82, 240]
[:scancode_down_raw, 79, 0, 2, 83, 240]
[:key_down_raw, 1073741903, 0, 2, 84, 243]
[:scancode_down_raw, 79, 0, 2, 85, 243]
[:key_down_raw, 1073741903, 0, 2, 86, 245]
[:scancode_down_raw, 79, 0, 2, 87, 245]
[:key_down_raw, 1073741903, 0, 2, 88, 247]
[:scancode_down_raw, 79, 0, 2, 89, 247]
[:key_down_raw, 1073741903, 0, 2, 90, 249]
[:scancode_down_raw, 79, 0, 2, 91, 249]
[:key_up_raw, 1073741903, 0, 2, 92, 250]
[:scancode_up_raw, 79, 0, 2, 93, 250]
[:key_down_raw, 1073741904, 0, 2, 94, 252]
[:scancode_down_raw, 80, 0, 2, 95, 252]
[:key_down_raw, 1073741904, 0, 2, 96, 267]
[:scancode_down_raw, 80, 0, 2, 97, 267]
[:key_down_raw, 1073741904, 0, 2, 98, 269]
[:scancode_down_raw, 80, 0, 2, 99, 269]
[:key_down_raw, 1073741904, 0, 2, 100, 271]
[:scancode_down_raw, 80, 0, 2, 101, 271]
[:key_down_raw, 1073741904, 0, 2, 102, 273]
[:scancode_down_raw, 80, 0, 2, 103, 273]
[:key_down_raw, 1073741904, 0, 2, 104, 275]
[:scancode_down_raw, 80, 0, 2, 105, 275]
[:key_down_raw, 1073741904, 0, 2, 106, 277]
[:scancode_down_raw, 80, 0, 2, 107, 277]
[:key_down_raw, 1073741904, 0, 2, 108, 279]
[:scancode_down_raw, 80, 0, 2, 109, 279]
[:key_down_raw, 1073741904, 0, 2, 110, 281]
[:scancode_down_raw, 80, 0, 2, 111, 281]
[:key_down_raw, 1073741904, 0, 2, 112, 283]
[:scancode_down_raw, 80, 0, 2, 113, 283]
[:key_down_raw, 1073741904, 0, 2, 114, 285]
[:scancode_down_raw, 80, 0, 2, 115, 285]
[:key_down_raw, 1073741904, 0, 2, 116, 287]
[:scancode_down_raw, 80, 0, 2, 117, 287]
[:key_down_raw, 1073741904, 0, 2, 118, 289]
[:scancode_down_raw, 80, 0, 2, 119, 289]
[:key_down_raw, 1073741904, 0, 2, 120, 291]
[:scancode_down_raw, 80, 0, 2, 121, 291]
[:key_down_raw, 1073741904, 0, 2, 122, 293]
[:scancode_down_raw, 80, 0, 2, 123, 293]
[:key_down_raw, 1073741904, 0, 2, 124, 295]
[:scancode_down_raw, 80, 0, 2, 125, 295]
[:key_down_raw, 1073741904, 0, 2, 126, 297]
[:scancode_down_raw, 80, 0, 2, 127, 297]
[:key_down_raw, 1073741904, 0, 2, 128, 299]
[:scancode_down_raw, 80, 0, 2, 129, 299]
[:key_down_raw, 1073741904, 0, 2, 130, 301]
[:scancode_down_raw, 80, 0, 2, 131, 301]
[:key_down_raw, 1073741904, 0, 2, 132, 303]
[:scancode_down_raw, 80, 0, 2, 133, 303]
[:key_down_raw, 1073741904, 0, 2, 134, 305]
[:scancode_down_raw, 80, 0, 2, 135, 305]
[:key_down_raw, 1073741904, 0, 2, 136, 307]
[:scancode_down_raw, 80, 0, 2, 137, 307]
[:key_down_raw, 1073741904, 0, 2, 138, 309]
[:scancode_down_raw, 80, 0, 2, 139, 309]
[:key_down_raw, 1073741904, 0, 2, 140, 311]
[:scancode_down_raw, 80, 0, 2, 141, 311]
[:key_down_raw, 1073741904, 0, 2, 142, 313]
[:scancode_down_raw, 80, 0, 2, 143, 313]
[:key_down_raw, 1073741904, 0, 2, 144, 315]
[:scancode_down_raw, 80, 0, 2, 145, 315]
[:key_down_raw, 1073741904, 0, 2, 146, 318]
[:scancode_down_raw, 80, 0, 2, 147, 318]
[:key_down_raw, 1073741904, 0, 2, 148, 319]
[:scancode_down_raw, 80, 0, 2, 149, 319]
[:key_down_raw, 1073741904, 0, 2, 150, 321]
[:scancode_down_raw, 80, 0, 2, 151, 321]
[:key_down_raw, 1073741904, 0, 2, 152, 323]
[:scancode_down_raw, 80, 0, 2, 153, 323]
[:key_down_raw, 1073741904, 0, 2, 154, 325]
[:scancode_down_raw, 80, 0, 2, 155, 325]
[:key_down_raw, 1073741904, 0, 2, 156, 327]
[:scancode_down_raw, 80, 0, 2, 157, 327]
[:key_down_raw, 1073741904, 0, 2, 158, 329]
[:scancode_down_raw, 80, 0, 2, 159, 329]
[:key_down_raw, 1073741904, 0, 2, 160, 331]
[:scancode_down_raw, 80, 0, 2, 161, 331]
[:key_down_raw, 1073741904, 0, 2, 162, 333]
[:scancode_down_raw, 80, 0, 2, 163, 333]
[:key_down_raw, 1073741904, 0, 2, 164, 336]
[:scancode_down_raw, 80, 0, 2, 165, 336]
[:key_down_raw, 1073741904, 0, 2, 166, 338]
[:scancode_down_raw, 80, 0, 2, 167, 338]
[:key_down_raw, 1073741904, 0, 2, 168, 340]
[:scancode_down_raw, 80, 0, 2, 169, 340]
[:key_down_raw, 1073741904, 0, 2, 170, 342]
[:scancode_down_raw, 80, 0, 2, 171, 342]
[:key_down_raw, 1073741904, 0, 2, 172, 344]
[:scancode_down_raw, 80, 0, 2, 173, 344]
[:key_up_raw, 1073741904, 0, 2, 174, 344]
[:scancode_up_raw, 80, 0, 2, 175, 344]
[:key_down_raw, 1073741903, 0, 2, 176, 348]
[:scancode_down_raw, 79, 0, 2, 177, 348]
[:key_down_raw, 1073741904, 0, 2, 178, 353]
[:scancode_down_raw, 80, 0, 2, 179, 353]
[:key_up_raw, 1073741904, 0, 2, 180, 353]
[:scancode_up_raw, 80, 0, 2, 181, 353]
[:key_up_raw, 1073741903, 0, 2, 182, 354]
[:scancode_up_raw, 79, 0, 2, 183, 354]
[:scancode_down_raw, 53, 0, 2, 184, 399]
[:textinput, "`", 0, 1, 185, 399]
