replay_version 2.0
stopped_at 909
seed 100
recorded_at Sun Sep 29 21:35:19 2019
[:mouse_move, 670, 363, 2, 1, 87]
[:mouse_move, 642, 353, 2, 2, 88]
[:mouse_move, 631, 351, 2, 3, 89]
[:mouse_move, 578, 338, 2, 4, 90]
[:mouse_move, 555, 332, 2, 5, 91]
[:mouse_move, 521, 326, 2, 6, 92]
[:mouse_move, 503, 323, 2, 7, 93]
[:mouse_move, 495, 322, 2, 8, 94]
[:mouse_move, 479, 319, 2, 9, 95]
[:mouse_move, 472, 318, 2, 10, 96]
[:mouse_move, 463, 317, 2, 11, 97]
[:mouse_move, 461, 317, 2, 12, 98]
[:mouse_move, 457, 316, 2, 13, 99]
[:mouse_move, 455, 315, 2, 14, 101]
[:mouse_move, 454, 315, 2, 15, 102]
[:mouse_move, 454, 314, 2, 16, 105]
[:mouse_button_pressed, 1, 0, 1, 17, 110]
[:mouse_button_up, 1, 0, 1, 18, 118]
[:mouse_move, 455, 314, 2, 19, 201]
[:mouse_move, 472, 314, 2, 20, 202]
[:mouse_move, 490, 314, 2, 21, 203]
[:mouse_move, 528, 313, 2, 22, 204]
[:mouse_move, 585, 307, 2, 23, 205]
[:mouse_move, 625, 301, 2, 24, 206]
[:mouse_move, 697, 288, 2, 25, 207]
[:mouse_move, 729, 283, 2, 26, 208]
[:mouse_move, 763, 279, 2, 27, 209]
[:mouse_move, 784, 278, 2, 28, 210]
[:mouse_move, 788, 278, 2, 29, 211]
[:mouse_move, 797, 278, 2, 30, 212]
[:mouse_move, 802, 278, 2, 31, 213]
[:mouse_move, 809, 278, 2, 32, 214]
[:mouse_move, 810, 278, 2, 33, 215]
[:mouse_move, 811, 279, 2, 34, 216]
[:mouse_move, 812, 279, 2, 35, 218]
[:mouse_move, 810, 279, 2, 36, 222]
[:mouse_move, 801, 276, 2, 37, 223]
[:mouse_move, 793, 273, 2, 38, 224]
[:mouse_move, 784, 271, 2, 39, 225]
[:mouse_move, 757, 268, 2, 40, 226]
[:mouse_move, 738, 266, 2, 41, 227]
[:mouse_move, 734, 266, 2, 42, 228]
[:mouse_move, 721, 266, 2, 43, 229]
[:mouse_move, 717, 266, 2, 44, 230]
[:mouse_move, 715, 266, 2, 45, 231]
[:mouse_move, 712, 266, 2, 46, 232]
[:mouse_move, 711, 266, 2, 47, 234]
[:mouse_move, 711, 267, 2, 48, 238]
[:mouse_move, 710, 267, 2, 49, 242]
[:mouse_move, 709, 267, 2, 50, 246]
[:mouse_button_pressed, 1, 0, 1, 51, 247]
[:mouse_button_up, 1, 0, 1, 52, 254]
[:mouse_move, 708, 264, 2, 53, 296]
[:mouse_move, 706, 261, 2, 54, 297]
[:mouse_move, 696, 249, 2, 55, 298]
[:mouse_move, 688, 242, 2, 56, 299]
[:mouse_move, 671, 228, 2, 57, 300]
[:mouse_move, 632, 204, 2, 58, 301]
[:mouse_move, 582, 176, 2, 59, 302]
[:mouse_move, 571, 169, 2, 60, 303]
[:mouse_move, 543, 153, 2, 61, 304]
[:mouse_move, 521, 140, 2, 62, 306]
[:mouse_move, 516, 137, 2, 63, 307]
[:mouse_move, 512, 135, 2, 64, 308]
[:mouse_move, 510, 134, 2, 65, 309]
[:mouse_move, 508, 133, 2, 66, 310]
[:mouse_move, 504, 132, 2, 67, 311]
[:mouse_move, 500, 132, 2, 68, 312]
[:mouse_move, 497, 132, 2, 69, 313]
[:mouse_move, 491, 132, 2, 70, 315]
[:mouse_move, 488, 132, 2, 71, 316]
[:mouse_move, 487, 132, 2, 72, 317]
[:mouse_move, 484, 131, 2, 73, 318]
[:mouse_move, 482, 130, 2, 74, 319]
[:mouse_move, 481, 129, 2, 75, 320]
[:mouse_move, 477, 127, 2, 76, 321]
[:mouse_move, 475, 125, 2, 77, 322]
[:mouse_move, 472, 123, 2, 78, 323]
[:mouse_move, 470, 122, 2, 79, 324]
[:mouse_move, 468, 120, 2, 80, 325]
[:mouse_move, 466, 120, 2, 81, 326]
[:mouse_move, 463, 117, 2, 82, 327]
[:mouse_move, 461, 116, 2, 83, 328]
[:mouse_move, 459, 115, 2, 84, 329]
[:mouse_move, 458, 114, 2, 85, 330]
[:mouse_move, 457, 112, 2, 86, 331]
[:mouse_move, 456, 112, 2, 87, 332]
[:mouse_move, 456, 111, 2, 88, 335]
[:mouse_move, 457, 111, 2, 89, 340]
[:mouse_move, 458, 111, 2, 90, 342]
[:mouse_move, 459, 111, 2, 91, 343]
[:mouse_move, 461, 111, 2, 92, 346]
[:mouse_move, 463, 111, 2, 93, 347]
[:mouse_move, 468, 110, 2, 94, 348]
[:mouse_move, 471, 109, 2, 95, 349]
[:mouse_move, 475, 109, 2, 96, 350]
[:mouse_move, 483, 107, 2, 97, 351]
[:mouse_move, 491, 106, 2, 98, 352]
[:mouse_move, 496, 106, 2, 99, 353]
[:mouse_move, 505, 105, 2, 100, 354]
[:mouse_move, 510, 104, 2, 101, 355]
[:mouse_move, 520, 104, 2, 102, 356]
[:mouse_move, 525, 104, 2, 103, 357]
[:mouse_move, 529, 104, 2, 104, 358]
[:mouse_move, 537, 104, 2, 105, 359]
[:mouse_move, 540, 104, 2, 106, 360]
[:mouse_move, 548, 104, 2, 107, 361]
[:mouse_move, 553, 104, 2, 108, 362]
[:mouse_move, 560, 106, 2, 109, 363]
[:mouse_move, 562, 106, 2, 110, 364]
[:mouse_move, 571, 108, 2, 111, 365]
[:mouse_move, 576, 110, 2, 112, 366]
[:mouse_move, 578, 110, 2, 113, 367]
[:mouse_move, 584, 112, 2, 114, 368]
[:mouse_move, 585, 112, 2, 115, 369]
[:mouse_move, 588, 112, 2, 116, 370]
[:mouse_move, 592, 113, 2, 117, 371]
[:mouse_move, 596, 113, 2, 118, 372]
[:mouse_move, 597, 113, 2, 119, 373]
[:mouse_move, 599, 113, 2, 120, 374]
[:mouse_move, 604, 113, 2, 121, 375]
[:mouse_move, 606, 113, 2, 122, 376]
[:mouse_move, 610, 113, 2, 123, 377]
[:mouse_move, 612, 113, 2, 124, 378]
[:mouse_move, 617, 113, 2, 125, 379]
[:mouse_move, 618, 113, 2, 126, 380]
[:mouse_move, 620, 113, 2, 127, 381]
[:mouse_move, 625, 113, 2, 128, 382]
[:mouse_move, 629, 113, 2, 129, 383]
[:mouse_move, 632, 113, 2, 130, 384]
[:mouse_move, 633, 113, 2, 131, 385]
[:mouse_move, 639, 112, 2, 132, 386]
[:mouse_move, 644, 111, 2, 133, 387]
[:mouse_move, 647, 111, 2, 134, 388]
[:mouse_move, 648, 111, 2, 135, 389]
[:mouse_move, 653, 110, 2, 136, 390]
[:mouse_move, 659, 110, 2, 137, 391]
[:mouse_move, 663, 109, 2, 138, 392]
[:mouse_move, 667, 109, 2, 139, 393]
[:mouse_move, 669, 109, 2, 140, 394]
[:mouse_move, 672, 109, 2, 141, 395]
[:mouse_move, 673, 109, 2, 142, 396]
[:mouse_move, 674, 109, 2, 143, 397]
[:mouse_move, 675, 109, 2, 144, 398]
[:mouse_move, 674, 117, 2, 145, 426]
[:mouse_move, 663, 166, 2, 146, 427]
[:mouse_move, 654, 205, 2, 147, 428]
[:mouse_move, 636, 260, 2, 148, 429]
[:mouse_move, 617, 308, 2, 149, 430]
[:mouse_move, 606, 332, 2, 150, 431]
[:mouse_move, 595, 354, 2, 151, 432]
[:mouse_move, 591, 361, 2, 152, 433]
[:mouse_move, 579, 388, 2, 153, 434]
[:mouse_move, 571, 406, 2, 154, 435]
[:mouse_move, 570, 409, 2, 155, 436]
[:mouse_move, 566, 418, 2, 156, 437]
[:mouse_move, 564, 421, 2, 157, 438]
[:mouse_move, 563, 423, 2, 158, 439]
[:mouse_move, 560, 427, 2, 159, 440]
[:mouse_move, 558, 428, 2, 160, 441]
[:mouse_move, 553, 431, 2, 161, 442]
[:mouse_move, 545, 433, 2, 162, 443]
[:mouse_move, 542, 434, 2, 163, 444]
[:mouse_move, 540, 435, 2, 164, 445]
[:mouse_move, 535, 436, 2, 165, 446]
[:mouse_move, 533, 436, 2, 166, 447]
[:mouse_move, 530, 437, 2, 167, 448]
[:mouse_move, 529, 437, 2, 168, 449]
[:mouse_button_pressed, 1, 0, 1, 169, 456]
[:mouse_button_up, 1, 0, 1, 170, 466]
[:mouse_move, 529, 435, 2, 171, 509]
[:mouse_move, 529, 433, 2, 172, 510]
[:mouse_move, 528, 426, 2, 173, 511]
[:mouse_move, 527, 418, 2, 174, 512]
[:mouse_move, 526, 410, 2, 175, 513]
[:mouse_move, 525, 400, 2, 176, 514]
[:mouse_move, 523, 388, 2, 177, 515]
[:mouse_move, 521, 369, 2, 178, 516]
[:mouse_move, 521, 356, 2, 179, 517]
[:mouse_move, 520, 347, 2, 180, 518]
[:mouse_move, 520, 344, 2, 181, 519]
[:mouse_move, 519, 341, 2, 182, 520]
[:mouse_move, 519, 340, 2, 183, 522]
[:mouse_move, 519, 341, 2, 184, 524]
[:mouse_move, 519, 345, 2, 185, 525]
[:mouse_move, 519, 348, 2, 186, 526]
[:mouse_move, 519, 352, 2, 187, 527]
[:mouse_move, 519, 356, 2, 188, 528]
[:mouse_move, 519, 364, 2, 189, 529]
[:mouse_move, 519, 371, 2, 190, 530]
[:mouse_move, 520, 373, 2, 191, 531]
[:mouse_move, 522, 377, 2, 192, 532]
[:mouse_move, 523, 379, 2, 193, 533]
[:mouse_move, 524, 380, 2, 194, 534]
[:mouse_move, 525, 382, 2, 195, 535]
[:mouse_move, 526, 382, 2, 196, 536]
[:mouse_move, 527, 383, 2, 197, 537]
[:mouse_button_pressed, 1, 0, 1, 198, 540]
[:mouse_button_up, 1, 0, 1, 199, 549]
[:mouse_move, 530, 383, 2, 200, 563]
[:mouse_move, 533, 383, 2, 201, 564]
[:mouse_move, 537, 381, 2, 202, 565]
[:mouse_move, 542, 379, 2, 203, 566]
[:mouse_move, 543, 377, 2, 204, 567]
[:mouse_move, 548, 371, 2, 205, 568]
[:mouse_move, 551, 364, 2, 206, 569]
[:mouse_move, 553, 358, 2, 207, 570]
[:mouse_move, 557, 345, 2, 208, 571]
[:mouse_move, 558, 342, 2, 209, 572]
[:mouse_move, 559, 338, 2, 210, 573]
[:mouse_move, 562, 331, 2, 211, 574]
[:mouse_move, 562, 330, 2, 212, 575]
[:mouse_move, 562, 329, 2, 213, 576]
[:mouse_move, 563, 329, 2, 214, 578]
[:mouse_move, 563, 332, 2, 215, 579]
[:mouse_move, 564, 334, 2, 216, 580]
[:mouse_move, 564, 337, 2, 217, 581]
[:mouse_move, 565, 339, 2, 218, 582]
[:mouse_move, 565, 341, 2, 219, 583]
[:mouse_move, 565, 342, 2, 220, 584]
[:mouse_move, 566, 343, 2, 221, 585]
[:mouse_move, 567, 343, 2, 222, 593]
[:mouse_move, 568, 343, 2, 223, 594]
[:mouse_move, 570, 343, 2, 224, 595]
[:mouse_move, 571, 343, 2, 225, 596]
[:mouse_move, 574, 344, 2, 226, 597]
[:mouse_move, 575, 344, 2, 227, 598]
[:mouse_move, 577, 344, 2, 228, 599]
[:mouse_move, 583, 344, 2, 229, 600]
[:mouse_move, 588, 345, 2, 230, 601]
[:mouse_move, 603, 346, 2, 231, 602]
[:mouse_move, 614, 346, 2, 232, 603]
[:mouse_move, 622, 346, 2, 233, 604]
[:mouse_move, 632, 347, 2, 234, 605]
[:mouse_move, 636, 347, 2, 235, 606]
[:mouse_move, 639, 347, 2, 236, 607]
[:mouse_move, 643, 347, 2, 237, 608]
[:mouse_move, 642, 347, 2, 238, 611]
[:mouse_move, 641, 347, 2, 239, 612]
[:mouse_move, 640, 347, 2, 240, 613]
[:mouse_move, 639, 347, 2, 241, 614]
[:mouse_move, 638, 347, 2, 242, 615]
[:mouse_move, 636, 347, 2, 243, 617]
[:mouse_move, 635, 347, 2, 244, 618]
[:mouse_move, 634, 347, 2, 245, 619]
[:mouse_move, 633, 347, 2, 246, 620]
[:mouse_move, 632, 347, 2, 247, 621]
[:mouse_move, 631, 347, 2, 248, 623]
[:mouse_move, 630, 347, 2, 249, 627]
[:mouse_move, 630, 346, 2, 250, 629]
[:mouse_move, 631, 346, 2, 251, 635]
[:mouse_move, 631, 345, 2, 252, 636]
[:mouse_button_pressed, 1, 0, 1, 253, 642]
[:mouse_button_up, 1, 0, 1, 254, 651]
[:mouse_move, 630, 340, 2, 255, 701]
[:mouse_move, 628, 330, 2, 256, 702]
[:mouse_move, 619, 293, 2, 257, 703]
[:mouse_move, 615, 278, 2, 258, 704]
[:mouse_move, 606, 251, 2, 259, 705]
[:mouse_move, 601, 236, 2, 260, 706]
[:mouse_move, 591, 213, 2, 261, 707]
[:mouse_move, 574, 195, 2, 262, 708]
[:mouse_move, 552, 182, 2, 263, 709]
[:mouse_move, 539, 176, 2, 264, 710]
[:mouse_move, 521, 168, 2, 265, 711]
[:mouse_move, 510, 162, 2, 266, 712]
[:mouse_move, 500, 157, 2, 267, 713]
[:mouse_move, 473, 144, 2, 268, 714]
[:mouse_move, 452, 130, 2, 269, 715]
[:mouse_move, 443, 121, 2, 270, 716]
[:mouse_move, 439, 118, 2, 271, 717]
[:mouse_move, 427, 104, 2, 272, 718]
[:mouse_move, 425, 102, 2, 273, 719]
[:mouse_move, 420, 95, 2, 274, 720]
[:mouse_move, 419, 93, 2, 275, 721]
[:mouse_move, 418, 92, 2, 276, 722]
[:mouse_move, 417, 90, 2, 277, 723]
[:mouse_move, 417, 89, 2, 278, 724]
[:mouse_move, 417, 88, 2, 279, 726]
[:mouse_move, 418, 88, 2, 280, 728]
[:mouse_move, 419, 88, 2, 281, 729]
[:mouse_move, 423, 89, 2, 282, 730]
[:mouse_move, 427, 91, 2, 283, 731]
[:mouse_move, 436, 95, 2, 284, 732]
[:mouse_move, 440, 98, 2, 285, 733]
[:mouse_move, 445, 100, 2, 286, 734]
[:mouse_move, 453, 106, 2, 287, 735]
[:mouse_move, 458, 109, 2, 288, 736]
[:mouse_move, 466, 116, 2, 289, 737]
[:mouse_move, 469, 119, 2, 290, 738]
[:mouse_move, 478, 125, 2, 291, 739]
[:mouse_move, 486, 131, 2, 292, 740]
[:mouse_move, 497, 136, 2, 293, 741]
[:mouse_move, 504, 139, 2, 294, 742]
[:mouse_move, 511, 141, 2, 295, 743]
[:mouse_move, 527, 143, 2, 296, 744]
[:mouse_move, 542, 143, 2, 297, 745]
[:mouse_move, 581, 137, 2, 298, 746]
[:mouse_move, 589, 135, 2, 299, 747]
[:mouse_move, 616, 126, 2, 300, 748]
[:mouse_move, 623, 124, 2, 301, 749]
[:mouse_move, 645, 114, 2, 302, 750]
[:mouse_move, 654, 109, 2, 303, 752]
[:mouse_move, 661, 104, 2, 304, 753]
[:mouse_move, 662, 102, 2, 305, 754]
[:mouse_move, 663, 101, 2, 306, 755]
[:mouse_move, 664, 97, 2, 307, 756]
[:mouse_move, 661, 91, 2, 308, 757]
[:mouse_move, 657, 88, 2, 309, 758]
[:mouse_move, 652, 83, 2, 310, 759]
[:mouse_move, 625, 68, 2, 311, 760]
[:mouse_move, 586, 53, 2, 312, 761]
[:mouse_move, 576, 50, 2, 313, 762]
[:mouse_move, 555, 45, 2, 314, 763]
[:mouse_move, 531, 39, 2, 315, 764]
[:mouse_move, 494, 33, 2, 316, 765]
[:mouse_move, 482, 32, 2, 317, 766]
[:mouse_move, 470, 32, 2, 318, 767]
[:mouse_move, 448, 32, 2, 319, 768]
[:mouse_move, 435, 33, 2, 320, 769]
[:mouse_move, 427, 36, 2, 321, 770]
[:mouse_move, 420, 39, 2, 322, 771]
[:mouse_move, 409, 46, 2, 323, 772]
[:mouse_move, 398, 63, 2, 324, 774]
[:mouse_move, 395, 69, 2, 325, 775]
[:mouse_move, 394, 77, 2, 326, 776]
[:mouse_move, 394, 81, 2, 327, 777]
[:mouse_move, 394, 90, 2, 328, 778]
[:mouse_move, 396, 96, 2, 329, 780]
[:mouse_move, 406, 107, 2, 330, 781]
[:mouse_move, 417, 116, 2, 331, 782]
[:mouse_move, 428, 125, 2, 332, 783]
[:mouse_move, 449, 135, 2, 333, 784]
[:mouse_move, 461, 141, 2, 334, 785]
[:mouse_move, 489, 151, 2, 335, 786]
[:mouse_move, 503, 154, 2, 336, 787]
[:mouse_move, 521, 158, 2, 337, 788]
[:mouse_move, 541, 161, 2, 338, 789]
[:mouse_move, 552, 161, 2, 339, 790]
[:mouse_move, 569, 161, 2, 340, 791]
[:mouse_move, 596, 154, 2, 341, 793]
[:mouse_move, 612, 148, 2, 342, 794]
[:mouse_move, 619, 144, 2, 343, 795]
[:mouse_move, 628, 139, 2, 344, 796]
[:mouse_move, 631, 137, 2, 345, 797]
[:mouse_move, 633, 134, 2, 346, 798]
[:mouse_move, 637, 129, 2, 347, 799]
[:mouse_move, 637, 125, 2, 348, 800]
[:mouse_move, 631, 118, 2, 349, 801]
[:mouse_move, 620, 109, 2, 350, 802]
[:mouse_move, 611, 102, 2, 351, 803]
[:mouse_move, 592, 90, 2, 352, 804]
[:mouse_move, 581, 83, 2, 353, 805]
[:mouse_move, 567, 77, 2, 354, 806]
[:mouse_move, 535, 65, 2, 355, 807]
[:mouse_move, 519, 61, 2, 356, 808]
[:mouse_move, 498, 59, 2, 357, 809]
[:mouse_move, 476, 57, 2, 358, 810]
[:mouse_move, 452, 58, 2, 359, 811]
[:mouse_move, 442, 62, 2, 360, 812]
[:mouse_move, 424, 74, 2, 361, 813]
[:mouse_move, 416, 83, 2, 362, 814]
[:mouse_move, 402, 111, 2, 363, 815]
[:mouse_move, 398, 127, 2, 364, 816]
[:mouse_move, 397, 135, 2, 365, 817]
[:mouse_move, 395, 151, 2, 366, 818]
[:mouse_move, 395, 158, 2, 367, 819]
[:mouse_move, 396, 176, 2, 368, 820]
[:mouse_move, 404, 181, 2, 369, 821]
[:mouse_move, 422, 186, 2, 370, 822]
[:key_down_raw, 1073742051, 1024, 2, 371, 904]
[:key_down_raw, 113, 1024, 2, 372, 908]
