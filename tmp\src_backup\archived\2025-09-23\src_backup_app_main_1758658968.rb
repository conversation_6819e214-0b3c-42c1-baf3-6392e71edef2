class Animation 
  def initialize(commands)
    @commands = commands
    @index = 0
    @wait = 0
  end

  def update(args, sprite)
    return if finished?

    if @wait > 0
      @wait -= 1
      return
    end

    cmd, *params = @commands[@index]

    case cmd
    when :frame
      sprite.path = params[0]
    when :wait
      @wait = params[0]
    when :sound
      args.outputs.sounds << params[0]
    end

    @index += 1
  end

  def finished?
    @index >= @commands.length && @wait == 0
  end
end  

def boot args
  args.state = {}
end

def tick args
  args.state.anim ||= Animation.new([
    [:frame, "sprites/square/blue.png"],
    [:wait, 60],
    [:frame, "sprites/square/red.png"],
    [:wait, 60],
    [:frame, "sprites/square/green.png"],
    [:wait, 60]
  ])

  args.state.sprite ||= { x: 100, y: 100, w: 64, h: 64, path: "sprites/square/blue.png" }
  args.state.anim.update(args, args.state.sprite)
  args.outputs.sprites << args.state.sprite
end
