commit 63551f384b4adc31ad73d70afdd0b16b870dec96
Author: <PERSON> <<EMAIL>>
Date:   Mon Mar 18 06:19:53 2024 -0500

    [mRuby] Bugfix for mrb extensions where == is performed against NONE.

    repro:

    class Child
      attr_reader :bar
      def initialize(bar)
        @bar = bar
      end

      def ==(other)
        @bar == other.bar
      end

      def eq(other)
        @bar == other.bar
      end
    end

    class Parent
      attr_reader :child
      def initialize(child_init)
        @child = Child.new child_init
      end

      def ==(other)
        if other.respond_to? :child
          @child == other.child
        else
          false
        end
      end
    end

    parents = [ Parent.new([1,2,3]), Parent.new([4,5,6]) ]
    puts parents.index(Parent.new([1,2,3]))

diff --git a/mruby/mrbgems/mruby-array-ext/mrblib/array.rb b/mruby/mrbgems/mruby-array-ext/mrblib/array.rb
index 25669a9ab..450e01716 100644
--- a/mruby/mrbgems/mruby-array-ext/mrblib/array.rb
+++ b/mruby/mrbgems/mruby-array-ext/mrblib/array.rb
@@ -333,7 +333,7 @@ class Array
     end
     if idx < 0 || size <= idx
       return block.call(n) if block
-      if ifnone == NONE
+      if NONE == ifnone
         raise IndexError, "index #{n} outside of array bounds: #{-size}...#{size}"
       end
       return ifnone
diff --git a/mruby/mrbgems/mruby-enum-ext/mrblib/enum.rb b/mruby/mrbgems/mruby-enum-ext/mrblib/enum.rb
index f15511925..1f1e7b229 100644
--- a/mruby/mrbgems/mruby-enum-ext/mrblib/enum.rb
+++ b/mruby/mrbgems/mruby-enum-ext/mrblib/enum.rb
@@ -253,7 +253,7 @@ module Enumerable
         count += 1 if block.call(*val)
       end
     else
-      if v == NONE
+      if NONE == v
         self.each { count += 1 }
       else
         self.each do |*val|
@@ -708,7 +708,7 @@ module Enumerable
   #

   def find_index(val=NONE, &block)
-    return to_enum(:find_index, val) if !block && val == NONE
+    return to_enum(:find_index, val) if !block && NONE == val

     idx = 0
     if block
diff --git a/mruby/mrbgems/mruby-enumerator/mrblib/enumerator.rb b/mruby/mrbgems/mruby-enumerator/mrblib/enumerator.rb
index f007b8553..e8c169e31 100644
--- a/mruby/mrbgems/mruby-enumerator/mrblib/enumerator.rb
+++ b/mruby/mrbgems/mruby-enumerator/mrblib/enumerator.rb
@@ -117,7 +117,7 @@ class Enumerator
   def initialize(obj=NONE, meth=:each, *args, &block)
     if block
       obj = Generator.new(&block)
-    elsif obj == NONE
+    elsif NONE == obj
       raise ArgumentError, "wrong number of arguments (given 0, expected 1+)"
     end

@@ -581,7 +581,7 @@ class Enumerator
   def Enumerator.produce(init=NONE, &block)
     raise ArgumentError, "no block given" if block.nil?
     Enumerator.new do |y|
-      if init == NONE
+      if NONE == init
         val = nil
       else
         val = init