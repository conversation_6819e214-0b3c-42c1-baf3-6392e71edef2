# * EXCEPTION:
# * EXCEPTION: ~Runtime#add_to_require_queue~ failed for =app/main.rb=.
# * ERROR: Exception while requiring animation.rb.
# animation.rb does not exist.
# ** Backtrace:
# *** app/main.rb:1
# ** Backtrace:
# *** app/main.rb:1

Game State:
{:state=>{:tick_count=>20639, :player=>{:x=>100, :y=>100, :w=>64, :h=>64, :path=>"sprites/square/green.png"}, :anim=>#<Animation:0x17bb99dfbf0 @fiber=#<Fiber:0x17bb99dfb30>, @waiting=0>}, :temp_state=>{}, :inputs=>{:controller_one=>{:left_analog_x_raw=>0, :left_analog_y_raw=>0, :left_analog_x_perc=>0.0, :left_analog_y_perc=>0.0, :right_analog_x_raw=>0, :right_analog_y_raw=>0, :right_analog_x_perc=>0.0, :right_analog_y_perc=>0.0, :active=>false, :key_down=>{}, :key_held=>{}, :key_up=>{}, :left_analog_angle=>nil, :right_analog_angle=>nil, :left_analog_active=>false, :right_analog_active=>false}, :controller_two=>{:left_analog_x_raw=>0, :left_analog_y_raw=>0, :left_analog_x_perc=>0.0, :left_analog_y_perc=>0.0, :right_analog_x_raw=>0, :right_analog_y_raw=>0, :right_analog_x_perc=>0.0, :right_analog_y_perc=>0.0, :active=>false, :key_down=>{}, :key_held=>{}, :key_up=>{}, :left_analog_angle=>nil, :right_analog_angle=>nil, :left_analog_active=>false, :right_analog_active=>false}, :controller_three=>{:left_analog_x_raw=>0, :left_analog_y_raw=>0, :left_analog_x_perc=>0.0, :left_analog_y_perc=>0.0, :right_analog_x_raw=>0, :right_analog_y_raw=>0, :right_analog_x_perc=>0.0, :right_analog_y_perc=>0.0, :active=>false, :key_down=>{}, :key_held=>{}, :key_up=>{}, :left_analog_angle=>nil, :right_analog_angle=>nil, :left_analog_active=>false, :right_analog_active=>false}, :controller_four=>{:left_analog_x_raw=>0, :left_analog_y_raw=>0, :left_analog_x_perc=>0.0, :left_analog_y_perc=>0.0, :right_analog_x_raw=>0, :right_analog_y_raw=>0, :right_analog_x_perc=>0.0, :right_analog_y_perc=>0.0, :active=>false, :key_down=>{}, :key_held=>{}, :key_up=>{}, :left_analog_angle=>nil, :right_analog_angle=>nil, :left_analog_active=>false, :right_analog_active=>false}, :keyboard=>{:key_up=>{:keycodes=>[], :truthy_keys=>[]}, :key_held=>{:keycodes=>[], :truthy_keys=>[]}, :key_down=>{:keycodes=>[], :truthy_keys=>[]}, :has_focus=>false}, :mouse=>{:x=>0.0, :y=>404.0, :moved=>nil, :moved_at=>20639, :has_focus=>false}, :text=>[], :application_control=>"nil"}, :passes=>[{:solids=>[], :sprites=>[], :lines=>[], :labels=>[], :sounds=>[], :borders=>[], :primitives=>[], :static_solids=>[], :static_borders=>[], :static_sprites=>[], :static_lines=>[], :static_labels=>[], :static_primitives=>[]}], :outputs=>{:solids=>[], :sprites=>[], :lines=>[], :labels=>[], :sounds=>[], :borders=>[], :primitives=>[], :static_solids=>[], :static_borders=>[], :static_sprites=>[], :static_lines=>[], :static_labels=>[], :static_primitives=>[]}, :grid=>{:right_px=>1280, :w_px=>1280, :ffi_draw=>"", :runtime=>{:argv=>"C:\\Users\\<USER>\\work\\the_necromancers_cube\\dragonruby.exe", :platform=>"Windows", :required_files=>["app/main.rb"], :reload_list_history=>{"app/main.rb"=>{:current=>{:path=>"app/main.rb", :global_at=>46203, :event=>:processing}, :history=>[{:path=>"app/main.rb", :global_at=>-1, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>-1, :event=>:processing}, {:path=>"app/main.rb", :global_at=>-1, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>6706, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>6706, :event=>:processing}, {:path=>"app/main.rb", :global_at=>6706, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>7245, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>7245, :event=>:processing}, {:path=>"app/main.rb", :global_at=>7245, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>8625, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>8625, :event=>:processing}, {:path=>"app/main.rb", :global_at=>8625, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>9945, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>9945, :event=>:processing}, {:path=>"app/main.rb", :global_at=>9945, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>12105, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>12105, :event=>:processing}, {:path=>"app/main.rb", :global_at=>12105, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>14746, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>14746, :event=>:processing}, {:path=>"app/main.rb", :global_at=>14746, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>16755, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>16755, :event=>:processing}, {:path=>"app/main.rb", :global_at=>16755, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>17708, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>17708, :event=>:processing}, {:path=>"app/main.rb", :global_at=>17708, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>18366, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>18366, :event=>:processing}, {:path=>"app/main.rb", :global_at=>18366, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>20225, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>20225, :event=>:processing}, {:path=>"app/main.rb", :global_at=>20225, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>20403, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>20403, :event=>:processing}, {:path=>"app/main.rb", :global_at=>20403, :event=>:reload_completed}, {:path=>"app/main.rb", :global_at=>41043, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>41043, :event=>:processing}, {:path=>"app/main.rb", :global_at=>41343, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>41343, :event=>:processing}, {:path=>"app/main.rb", :global_at=>42243, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>42243, :event=>:processing}, {:path=>"app/main.rb", :global_at=>42783, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>42783, :event=>:processing}, {:path=>"app/main.rb", :global_at=>42963, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>42963, :event=>:processing}, {:path=>"app/main.rb", :global_at=>45903, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>45903, :event=>:processing}, {:path=>"app/main.rb", :global_at=>46203, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>46203, :event=>:processing}]}}}, :h_px=>720, :allscreen_w=>1280, :right=>1280, :render_origin_x=>0.0, :allscreen_h=>720, :render_origin_y=>720, :allscreen_top=>720, :high_dpi_scale=>1.0, :allscreen_h_px=>720, :h=>720, :texture_scale_enum=>100, :allscreen_right_px=>1280, :y=>0.0, :allscreen_w_px=>1280, :left=>0, :texture_scale=>1.0, :allscreen_right=>1280, :letterbox=>true, :render_scale=>1.0, :center=>[[:x, 640.0], [:y, 360.0]], :allscreen_offset_x_px=>0, :x=>0.0, :origin_x=>0.0, :allscreen_offset_y=>0, :w=>1280, :left_px=>0, :origin_y=>0.0, :bottom_px=>0, :center_y=>360.0, :allscreen_offset_y_px=>0, :rect=>[[:x, 0.0], [:y, 0.0], [:w, 1280], [:h, 720]], :native_scale=>1.0, :top_px=>720, :center_x=>640.0, :allscreen_w_pt=>1280, :allscreen_bottom_px=>0, :bottom=>0, :allscreen_top_px=>720, :allscreen_bottom=>0, :allscreen_left_px=>0, :top=>720, :allscreen_offset_x=>0, :allscreen_left=>0, :origin_name=>:bottom_left, :allscreen_h_pt=>720}}