require "app/animation.rb"

def boot args
  args.state = {}
end

def tick args
  args.state.player ||= { x: 100, y: 100, w: 64, h: 64, path: "sprites/square/blue.png" }

  args.state.anim ||= anim do
    frame args.state.player, "sprites/square/blue.png"
    wait 120
    frame args.state.player, "sprites/square/red.png"
    wait 120
    frame args.state.player, "sprites/square/green.png"
    wait 120
  end

  if args.state.anim.running? 
    args.state.anim.update args, args.state.player
  end
  args.outputs.sprites << args.state.player
end

GTK.reset