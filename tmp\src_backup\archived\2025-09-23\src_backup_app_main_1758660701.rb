class Animation
  def initialize (&block)
    @waiting = 0
    @fiber = Fiber.new do
      instance_eval(&block)
      :done
    end
  end

  def update args
    return if done?

    if @waiting > 0
      @waiting -= 1
      return
    end
    
    result = @fiber.resume
    if result.is_a?(Integer)
      @waiting = result
    end
  end

  def done?
    puts "#{@fiber.alive?} #{@waiting}"
    !@fiber.alive? && !@waiting
  end

  def wait ticks
    @fiber.yield_self ticks 
  end

  def set_frame(sprite, path)
    sprite.path = path
  end

  def play_sound(args, path)
    args.outputs.sounds << path
  end
end 

def boot args
  args.state = {}
end

def tick args
  args.state.player ||= { x: 100, y: 100, w: 64, h: 64, path: "sprites/square/blue.png" }

  args.state.anim ||= Animation.new do
    set_frame(args.state.player, "sprites/square/blue.png")
    wait 120
    set_frame(args.state.player, "sprites/square/red.png")
    wait 120
    set_frame(args.state.player, "sprites/square/green.png")
    wait 120
  end

  args.state.anim.update args
  args.outputs.sprites << args.state.player
end

GTK.reset