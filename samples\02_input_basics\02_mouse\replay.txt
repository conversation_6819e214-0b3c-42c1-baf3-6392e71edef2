replay_version 2.0
stopped_at 1510
seed 100
recorded_at Sun Sep 29 21:33:51 2019
[:mouse_move, 312, 285, 2, 1, 87]
[:mouse_move, 316, 285, 2, 2, 88]
[:mouse_move, 323, 285, 2, 3, 89]
[:mouse_move, 328, 285, 2, 4, 90]
[:mouse_move, 334, 285, 2, 5, 91]
[:mouse_move, 338, 285, 2, 6, 92]
[:mouse_move, 354, 285, 2, 7, 93]
[:mouse_move, 363, 285, 2, 8, 94]
[:mouse_move, 375, 284, 2, 9, 95]
[:mouse_move, 382, 284, 2, 10, 96]
[:mouse_move, 395, 282, 2, 11, 97]
[:mouse_move, 407, 280, 2, 12, 98]
[:mouse_move, 424, 279, 2, 13, 99]
[:mouse_move, 429, 279, 2, 14, 100]
[:mouse_move, 432, 278, 2, 15, 101]
[:mouse_move, 441, 278, 2, 16, 102]
[:mouse_move, 444, 278, 2, 17, 103]
[:mouse_move, 447, 278, 2, 18, 104]
[:mouse_move, 448, 278, 2, 19, 106]
[:mouse_move, 447, 277, 2, 20, 110]
[:mouse_move, 445, 276, 2, 21, 111]
[:mouse_move, 444, 273, 2, 22, 112]
[:mouse_move, 444, 271, 2, 23, 113]
[:mouse_move, 444, 268, 2, 24, 114]
[:mouse_move, 444, 266, 2, 25, 115]
[:mouse_move, 446, 263, 2, 26, 116]
[:mouse_move, 447, 262, 2, 27, 117]
[:mouse_move, 448, 261, 2, 28, 118]
[:mouse_move, 449, 261, 2, 29, 120]
[:mouse_move, 453, 261, 2, 30, 137]
[:mouse_move, 457, 261, 2, 31, 138]
[:mouse_move, 469, 260, 2, 32, 139]
[:mouse_move, 477, 260, 2, 33, 140]
[:mouse_move, 492, 260, 2, 34, 141]
[:mouse_move, 499, 260, 2, 35, 142]
[:mouse_move, 512, 261, 2, 36, 143]
[:mouse_move, 517, 261, 2, 37, 144]
[:mouse_move, 526, 262, 2, 38, 145]
[:mouse_move, 528, 262, 2, 39, 146]
[:mouse_move, 536, 262, 2, 40, 147]
[:mouse_move, 539, 262, 2, 41, 148]
[:mouse_move, 544, 262, 2, 42, 149]
[:mouse_move, 546, 262, 2, 43, 150]
[:mouse_move, 550, 262, 2, 44, 151]
[:mouse_move, 551, 262, 2, 45, 152]
[:mouse_move, 553, 262, 2, 46, 153]
[:mouse_move, 557, 262, 2, 47, 154]
[:mouse_move, 563, 262, 2, 48, 155]
[:mouse_move, 565, 262, 2, 49, 156]
[:mouse_move, 568, 262, 2, 50, 157]
[:mouse_move, 575, 261, 2, 51, 158]
[:mouse_move, 579, 260, 2, 52, 159]
[:mouse_move, 586, 259, 2, 53, 160]
[:mouse_move, 590, 258, 2, 54, 161]
[:mouse_move, 598, 257, 2, 55, 162]
[:mouse_move, 602, 257, 2, 56, 163]
[:mouse_move, 610, 257, 2, 57, 164]
[:mouse_move, 613, 257, 2, 58, 165]
[:mouse_move, 619, 257, 2, 59, 166]
[:mouse_move, 625, 256, 2, 60, 167]
[:mouse_move, 631, 256, 2, 61, 168]
[:mouse_move, 635, 256, 2, 62, 169]
[:mouse_move, 640, 255, 2, 63, 170]
[:mouse_move, 645, 255, 2, 64, 171]
[:mouse_move, 649, 254, 2, 65, 172]
[:mouse_move, 652, 254, 2, 66, 173]
[:mouse_move, 657, 254, 2, 67, 174]
[:mouse_move, 659, 253, 2, 68, 175]
[:mouse_move, 663, 253, 2, 69, 176]
[:mouse_move, 666, 253, 2, 70, 177]
[:mouse_move, 669, 253, 2, 71, 178]
[:mouse_move, 673, 252, 2, 72, 179]
[:mouse_move, 677, 252, 2, 73, 180]
[:mouse_move, 680, 251, 2, 74, 181]
[:mouse_move, 685, 250, 2, 75, 182]
[:mouse_move, 686, 250, 2, 76, 183]
[:mouse_move, 688, 249, 2, 77, 184]
[:mouse_move, 691, 248, 2, 78, 185]
[:mouse_move, 692, 248, 2, 79, 186]
[:mouse_move, 693, 248, 2, 80, 187]
[:mouse_move, 694, 248, 2, 81, 189]
[:mouse_move, 692, 248, 2, 82, 207]
[:mouse_move, 690, 248, 2, 83, 208]
[:mouse_move, 681, 248, 2, 84, 209]
[:mouse_move, 676, 249, 2, 85, 210]
[:mouse_move, 669, 249, 2, 86, 211]
[:mouse_move, 655, 250, 2, 87, 212]
[:mouse_move, 641, 251, 2, 88, 213]
[:mouse_move, 622, 251, 2, 89, 214]
[:mouse_move, 610, 251, 2, 90, 215]
[:mouse_move, 594, 251, 2, 91, 216]
[:mouse_move, 578, 251, 2, 92, 217]
[:mouse_move, 570, 251, 2, 93, 218]
[:mouse_move, 563, 251, 2, 94, 219]
[:mouse_move, 544, 251, 2, 95, 220]
[:mouse_move, 539, 251, 2, 96, 221]
[:mouse_move, 525, 251, 2, 97, 222]
[:mouse_move, 512, 252, 2, 98, 223]
[:mouse_move, 507, 252, 2, 99, 224]
[:mouse_move, 501, 252, 2, 100, 225]
[:mouse_move, 488, 252, 2, 101, 226]
[:mouse_move, 481, 252, 2, 102, 227]
[:mouse_move, 466, 253, 2, 103, 228]
[:mouse_move, 458, 253, 2, 104, 229]
[:mouse_move, 445, 254, 2, 105, 230]
[:mouse_move, 439, 254, 2, 106, 231]
[:mouse_move, 434, 255, 2, 107, 232]
[:mouse_move, 431, 256, 2, 108, 233]
[:mouse_move, 428, 256, 2, 109, 234]
[:mouse_move, 427, 257, 2, 110, 235]
[:mouse_move, 426, 257, 2, 111, 236]
[:mouse_move, 426, 258, 2, 112, 239]
[:mouse_move, 427, 259, 2, 113, 240]
[:mouse_move, 433, 263, 2, 114, 241]
[:mouse_move, 436, 264, 2, 115, 242]
[:mouse_move, 445, 268, 2, 116, 243]
[:mouse_move, 450, 269, 2, 117, 244]
[:mouse_move, 461, 270, 2, 118, 245]
[:mouse_move, 474, 270, 2, 119, 246]
[:mouse_move, 481, 270, 2, 120, 247]
[:mouse_move, 488, 270, 2, 121, 248]
[:mouse_move, 501, 269, 2, 122, 249]
[:mouse_move, 507, 268, 2, 123, 250]
[:mouse_move, 518, 266, 2, 124, 251]
[:mouse_move, 523, 265, 2, 125, 252]
[:mouse_move, 533, 264, 2, 126, 253]
[:mouse_move, 538, 264, 2, 127, 254]
[:mouse_move, 548, 263, 2, 128, 255]
[:mouse_move, 553, 262, 2, 129, 256]
[:mouse_move, 563, 262, 2, 130, 257]
[:mouse_move, 569, 261, 2, 131, 258]
[:mouse_move, 578, 261, 2, 132, 259]
[:mouse_move, 583, 261, 2, 133, 260]
[:mouse_move, 592, 261, 2, 134, 261]
[:mouse_move, 596, 261, 2, 135, 262]
[:mouse_move, 605, 261, 2, 136, 263]
[:mouse_move, 609, 261, 2, 137, 264]
[:mouse_move, 611, 261, 2, 138, 265]
[:mouse_move, 621, 261, 2, 139, 266]
[:mouse_move, 624, 261, 2, 140, 267]
[:mouse_move, 631, 261, 2, 141, 268]
[:mouse_move, 635, 261, 2, 142, 269]
[:mouse_move, 642, 261, 2, 143, 270]
[:mouse_move, 646, 261, 2, 144, 271]
[:mouse_move, 653, 262, 2, 145, 272]
[:mouse_move, 656, 262, 2, 146, 273]
[:mouse_move, 664, 263, 2, 147, 274]
[:mouse_move, 666, 263, 2, 148, 275]
[:mouse_move, 673, 264, 2, 149, 276]
[:mouse_move, 678, 264, 2, 150, 277]
[:mouse_move, 685, 264, 2, 151, 278]
[:mouse_move, 689, 265, 2, 152, 279]
[:mouse_move, 694, 265, 2, 153, 280]
[:mouse_move, 697, 265, 2, 154, 281]
[:mouse_move, 702, 265, 2, 155, 282]
[:mouse_move, 704, 265, 2, 156, 283]
[:mouse_move, 706, 265, 2, 157, 284]
[:mouse_move, 706, 266, 2, 158, 285]
[:mouse_move, 708, 266, 2, 159, 286]
[:mouse_move, 709, 266, 2, 160, 288]
[:mouse_move, 708, 269, 2, 161, 318]
[:mouse_move, 693, 283, 2, 162, 319]
[:mouse_move, 680, 294, 2, 163, 320]
[:mouse_move, 641, 322, 2, 164, 321]
[:mouse_move, 618, 340, 2, 165, 322]
[:mouse_move, 609, 346, 2, 166, 323]
[:mouse_move, 583, 363, 2, 167, 324]
[:mouse_move, 570, 371, 2, 168, 325]
[:mouse_move, 552, 378, 2, 169, 326]
[:mouse_move, 546, 379, 2, 170, 327]
[:mouse_move, 538, 379, 2, 171, 328]
[:mouse_move, 536, 379, 2, 172, 329]
[:mouse_move, 531, 372, 2, 173, 330]
[:mouse_move, 526, 365, 2, 174, 331]
[:mouse_move, 517, 352, 2, 175, 332]
[:mouse_move, 510, 344, 2, 176, 333]
[:mouse_move, 496, 334, 2, 177, 334]
[:mouse_move, 488, 328, 2, 178, 335]
[:mouse_move, 474, 319, 2, 179, 336]
[:mouse_move, 467, 315, 2, 180, 337]
[:mouse_move, 458, 307, 2, 181, 338]
[:mouse_move, 454, 304, 2, 182, 339]
[:mouse_move, 449, 300, 2, 183, 340]
[:mouse_move, 447, 298, 2, 184, 341]
[:mouse_move, 445, 294, 2, 185, 342]
[:mouse_move, 444, 292, 2, 186, 343]
[:mouse_move, 443, 291, 2, 187, 344]
[:mouse_move, 442, 290, 2, 188, 345]
[:mouse_move, 442, 289, 2, 189, 346]
[:mouse_move, 442, 288, 2, 190, 349]
[:mouse_move, 442, 287, 2, 191, 353]
[:mouse_move, 444, 287, 2, 192, 354]
[:mouse_move, 449, 287, 2, 193, 355]
[:mouse_move, 454, 287, 2, 194, 356]
[:mouse_move, 470, 287, 2, 195, 357]
[:mouse_move, 480, 287, 2, 196, 358]
[:mouse_move, 504, 288, 2, 197, 359]
[:mouse_move, 517, 289, 2, 198, 360]
[:mouse_move, 534, 290, 2, 199, 361]
[:mouse_move, 546, 290, 2, 200, 362]
[:mouse_move, 565, 290, 2, 201, 363]
[:mouse_move, 573, 290, 2, 202, 364]
[:mouse_move, 583, 290, 2, 203, 365]
[:mouse_move, 587, 290, 2, 204, 366]
[:mouse_move, 595, 290, 2, 205, 367]
[:mouse_move, 598, 290, 2, 206, 368]
[:mouse_move, 602, 290, 2, 207, 369]
[:mouse_move, 604, 290, 2, 208, 370]
[:mouse_move, 607, 290, 2, 209, 371]
[:mouse_move, 608, 291, 2, 210, 372]
[:mouse_move, 611, 291, 2, 211, 373]
[:mouse_move, 612, 291, 2, 212, 374]
[:mouse_move, 613, 291, 2, 213, 375]
[:mouse_move, 615, 291, 2, 214, 376]
[:mouse_move, 616, 292, 2, 215, 377]
[:mouse_move, 617, 292, 2, 216, 380]
[:mouse_move, 616, 294, 2, 217, 395]
[:mouse_move, 604, 307, 2, 218, 396]
[:mouse_move, 592, 318, 2, 219, 397]
[:mouse_move, 569, 336, 2, 220, 398]
[:mouse_move, 562, 340, 2, 221, 399]
[:mouse_move, 549, 348, 2, 222, 400]
[:mouse_move, 544, 352, 2, 223, 401]
[:mouse_move, 540, 354, 2, 224, 402]
[:mouse_move, 536, 357, 2, 225, 403]
[:mouse_move, 539, 357, 2, 226, 407]
[:mouse_move, 541, 357, 2, 227, 408]
[:mouse_move, 544, 357, 2, 228, 409]
[:mouse_move, 548, 357, 2, 229, 410]
[:mouse_move, 553, 357, 2, 230, 411]
[:mouse_move, 557, 357, 2, 231, 412]
[:mouse_move, 566, 358, 2, 232, 413]
[:mouse_move, 570, 358, 2, 233, 414]
[:mouse_move, 575, 358, 2, 234, 415]
[:mouse_move, 577, 358, 2, 235, 416]
[:mouse_move, 581, 358, 2, 236, 417]
[:mouse_move, 583, 358, 2, 237, 418]
[:mouse_move, 584, 358, 2, 238, 419]
[:mouse_move, 585, 358, 2, 239, 420]
[:mouse_button_pressed, 1, 0, 1, 240, 428]
[:mouse_button_up, 1, 0, 1, 241, 437]
[:mouse_move, 583, 359, 2, 242, 463]
[:mouse_move, 583, 360, 2, 243, 464]
[:mouse_move, 585, 360, 2, 244, 466]
[:mouse_move, 595, 359, 2, 245, 467]
[:mouse_move, 605, 357, 2, 246, 468]
[:mouse_move, 627, 351, 2, 247, 469]
[:mouse_move, 632, 349, 2, 248, 470]
[:mouse_move, 642, 346, 2, 249, 471]
[:mouse_move, 660, 342, 2, 250, 472]
[:mouse_move, 672, 338, 2, 251, 473]
[:mouse_move, 677, 336, 2, 252, 474]
[:mouse_move, 684, 332, 2, 253, 475]
[:mouse_move, 686, 330, 2, 254, 476]
[:mouse_move, 689, 327, 2, 255, 477]
[:mouse_move, 691, 325, 2, 256, 478]
[:mouse_move, 693, 322, 2, 257, 479]
[:mouse_move, 693, 321, 2, 258, 480]
[:mouse_move, 693, 318, 2, 259, 481]
[:mouse_move, 692, 318, 2, 260, 482]
[:mouse_move, 688, 316, 2, 261, 483]
[:mouse_move, 684, 314, 2, 262, 484]
[:mouse_move, 683, 314, 2, 263, 485]
[:mouse_move, 678, 312, 2, 264, 486]
[:mouse_move, 676, 311, 2, 265, 487]
[:mouse_move, 673, 310, 2, 266, 488]
[:mouse_move, 672, 310, 2, 267, 489]
[:mouse_move, 669, 310, 2, 268, 490]
[:mouse_move, 667, 309, 2, 269, 491]
[:mouse_move, 663, 309, 2, 270, 492]
[:mouse_move, 661, 309, 2, 271, 493]
[:mouse_move, 657, 308, 2, 272, 494]
[:mouse_move, 655, 308, 2, 273, 495]
[:mouse_move, 652, 307, 2, 274, 496]
[:mouse_move, 651, 306, 2, 275, 497]
[:mouse_move, 650, 306, 2, 276, 498]
[:mouse_move, 649, 306, 2, 277, 503]
[:mouse_move, 648, 306, 2, 278, 508]
[:mouse_move, 647, 305, 2, 279, 509]
[:mouse_move, 645, 305, 2, 280, 510]
[:mouse_move, 636, 301, 2, 281, 511]
[:mouse_move, 628, 298, 2, 282, 512]
[:mouse_move, 605, 293, 2, 283, 513]
[:mouse_move, 593, 291, 2, 284, 514]
[:mouse_move, 581, 289, 2, 285, 515]
[:mouse_move, 565, 287, 2, 286, 516]
[:mouse_move, 555, 286, 2, 287, 517]
[:mouse_move, 550, 285, 2, 288, 518]
[:mouse_move, 544, 284, 2, 289, 519]
[:mouse_move, 542, 284, 2, 290, 520]
[:mouse_move, 541, 283, 2, 291, 521]
[:mouse_move, 540, 283, 2, 292, 523]
[:mouse_move, 541, 283, 2, 293, 529]
[:mouse_move, 543, 283, 2, 294, 531]
[:mouse_move, 545, 283, 2, 295, 532]
[:mouse_move, 550, 282, 2, 296, 533]
[:mouse_move, 553, 282, 2, 297, 534]
[:mouse_move, 558, 282, 2, 298, 535]
[:mouse_move, 561, 282, 2, 299, 536]
[:mouse_move, 566, 282, 2, 300, 537]
[:mouse_move, 570, 282, 2, 301, 538]
[:mouse_move, 571, 282, 2, 302, 539]
[:mouse_move, 576, 282, 2, 303, 540]
[:mouse_move, 578, 282, 2, 304, 541]
[:mouse_move, 584, 282, 2, 305, 542]
[:mouse_move, 588, 282, 2, 306, 543]
[:mouse_move, 590, 282, 2, 307, 544]
[:mouse_move, 592, 282, 2, 308, 545]
[:mouse_move, 595, 282, 2, 309, 546]
[:mouse_move, 597, 282, 2, 310, 547]
[:mouse_move, 599, 282, 2, 311, 548]
[:mouse_move, 601, 281, 2, 312, 549]
[:mouse_move, 602, 281, 2, 313, 550]
[:mouse_move, 602, 280, 2, 314, 551]
[:mouse_move, 604, 279, 2, 315, 552]
[:mouse_move, 605, 278, 2, 316, 554]
[:mouse_move, 606, 277, 2, 317, 555]
[:mouse_move, 606, 276, 2, 318, 556]
[:mouse_move, 607, 274, 2, 319, 557]
[:mouse_move, 607, 272, 2, 320, 558]
[:mouse_move, 607, 270, 2, 321, 559]
[:mouse_move, 607, 267, 2, 322, 560]
[:mouse_move, 607, 265, 2, 323, 561]
[:mouse_move, 606, 262, 2, 324, 562]
[:mouse_move, 605, 261, 2, 325, 563]
[:mouse_move, 603, 259, 2, 326, 564]
[:mouse_move, 598, 256, 2, 327, 565]
[:mouse_move, 597, 255, 2, 328, 566]
[:mouse_move, 592, 253, 2, 329, 567]
[:mouse_move, 590, 253, 2, 330, 568]
[:mouse_move, 585, 251, 2, 331, 569]
[:mouse_move, 581, 250, 2, 332, 570]
[:mouse_move, 575, 250, 2, 333, 571]
[:mouse_move, 573, 250, 2, 334, 572]
[:mouse_move, 569, 251, 2, 335, 573]
[:mouse_move, 565, 253, 2, 336, 574]
[:mouse_move, 564, 254, 2, 337, 575]
[:mouse_move, 562, 255, 2, 338, 576]
[:mouse_move, 560, 258, 2, 339, 577]
[:mouse_move, 559, 259, 2, 340, 578]
[:mouse_move, 558, 262, 2, 341, 579]
[:mouse_move, 558, 264, 2, 342, 580]
[:mouse_move, 558, 267, 2, 343, 581]
[:mouse_move, 558, 269, 2, 344, 582]
[:mouse_move, 559, 271, 2, 345, 583]
[:mouse_move, 561, 274, 2, 346, 584]
[:mouse_move, 562, 274, 2, 347, 585]
[:mouse_move, 565, 276, 2, 348, 586]
[:mouse_move, 571, 279, 2, 349, 587]
[:mouse_move, 573, 279, 2, 350, 588]
[:mouse_move, 580, 280, 2, 351, 589]
[:mouse_move, 581, 280, 2, 352, 590]
[:mouse_move, 586, 280, 2, 353, 591]
[:mouse_move, 590, 280, 2, 354, 592]
[:mouse_move, 592, 279, 2, 355, 593]
[:mouse_move, 596, 278, 2, 356, 594]
[:mouse_move, 597, 277, 2, 357, 595]
[:mouse_move, 599, 275, 2, 358, 596]
[:mouse_move, 599, 274, 2, 359, 597]
[:mouse_move, 600, 272, 2, 360, 598]
[:mouse_move, 600, 271, 2, 361, 599]
[:mouse_move, 601, 269, 2, 362, 600]
[:mouse_move, 602, 267, 2, 363, 601]
[:mouse_move, 602, 265, 2, 364, 602]
[:mouse_move, 603, 263, 2, 365, 603]
[:mouse_move, 603, 260, 2, 366, 604]
[:mouse_move, 603, 258, 2, 367, 605]
[:mouse_move, 602, 254, 2, 368, 606]
[:mouse_move, 600, 252, 2, 369, 607]
[:mouse_move, 594, 249, 2, 370, 608]
[:mouse_move, 590, 248, 2, 371, 609]
[:mouse_move, 580, 247, 2, 372, 610]
[:mouse_move, 578, 247, 2, 373, 611]
[:mouse_move, 571, 247, 2, 374, 612]
[:mouse_move, 568, 247, 2, 375, 613]
[:mouse_move, 565, 248, 2, 376, 614]
[:mouse_move, 564, 249, 2, 377, 615]
[:mouse_move, 563, 252, 2, 378, 616]
[:mouse_move, 563, 254, 2, 379, 617]
[:mouse_move, 567, 261, 2, 380, 618]
[:mouse_move, 573, 266, 2, 381, 619]
[:mouse_move, 585, 275, 2, 382, 620]
[:mouse_move, 608, 288, 2, 383, 621]
[:mouse_move, 623, 296, 2, 384, 622]
[:mouse_move, 641, 305, 2, 385, 623]
[:mouse_move, 645, 307, 2, 386, 624]
[:mouse_move, 658, 314, 2, 387, 625]
[:mouse_move, 661, 315, 2, 388, 626]
[:mouse_move, 659, 315, 2, 389, 637]
[:mouse_move, 658, 315, 2, 390, 638]
[:mouse_move, 656, 315, 2, 391, 639]
[:mouse_move, 653, 313, 2, 392, 640]
[:mouse_move, 640, 309, 2, 393, 641]
[:mouse_move, 635, 308, 2, 394, 642]
[:mouse_move, 626, 305, 2, 395, 643]
[:mouse_move, 625, 305, 2, 396, 644]
[:mouse_move, 619, 304, 2, 397, 645]
[:mouse_move, 618, 303, 2, 398, 646]
[:mouse_move, 616, 303, 2, 399, 648]
[:mouse_move, 617, 303, 2, 400, 651]
[:mouse_move, 624, 303, 2, 401, 652]
[:mouse_move, 627, 303, 2, 402, 653]
[:mouse_move, 645, 303, 2, 403, 654]
[:mouse_move, 654, 304, 2, 404, 655]
[:mouse_move, 669, 304, 2, 405, 656]
[:mouse_move, 678, 304, 2, 406, 657]
[:mouse_move, 692, 304, 2, 407, 658]
[:mouse_move, 697, 304, 2, 408, 659]
[:mouse_move, 703, 304, 2, 409, 660]
[:mouse_move, 707, 304, 2, 410, 661]
[:mouse_move, 712, 304, 2, 411, 662]
[:mouse_move, 717, 304, 2, 412, 663]
[:mouse_move, 722, 304, 2, 413, 664]
[:mouse_move, 725, 304, 2, 414, 665]
[:mouse_move, 730, 304, 2, 415, 666]
[:mouse_move, 732, 304, 2, 416, 667]
[:mouse_move, 736, 304, 2, 417, 668]
[:mouse_move, 737, 304, 2, 418, 670]
[:mouse_move, 733, 303, 2, 419, 672]
[:mouse_move, 726, 303, 2, 420, 673]
[:mouse_move, 716, 303, 2, 421, 674]
[:mouse_move, 691, 303, 2, 422, 675]
[:mouse_move, 675, 303, 2, 423, 676]
[:mouse_move, 651, 304, 2, 424, 677]
[:mouse_move, 638, 305, 2, 425, 678]
[:mouse_move, 624, 306, 2, 426, 679]
[:mouse_move, 618, 306, 2, 427, 680]
[:mouse_move, 612, 307, 2, 428, 681]
[:mouse_move, 611, 307, 2, 429, 682]
[:mouse_move, 610, 307, 2, 430, 683]
[:mouse_move, 615, 307, 2, 431, 685]
[:mouse_move, 620, 308, 2, 432, 686]
[:mouse_move, 640, 309, 2, 433, 687]
[:mouse_move, 651, 311, 2, 434, 688]
[:mouse_move, 667, 312, 2, 435, 689]
[:mouse_move, 671, 312, 2, 436, 690]
[:mouse_move, 683, 313, 2, 437, 691]
[:mouse_move, 687, 313, 2, 438, 692]
[:mouse_move, 690, 313, 2, 439, 693]
[:mouse_move, 691, 313, 2, 440, 695]
[:mouse_move, 695, 311, 2, 441, 699]
[:mouse_move, 704, 305, 2, 442, 700]
[:mouse_move, 717, 299, 2, 443, 701]
[:mouse_move, 782, 275, 2, 444, 702]
[:mouse_move, 821, 262, 2, 445, 703]
[:mouse_move, 919, 230, 2, 446, 704]
[:mouse_move, 973, 212, 2, 447, 705]
[:mouse_move, 1042, 188, 2, 448, 706]
[:mouse_move, 1084, 173, 2, 449, 707]
[:mouse_move, 1126, 157, 2, 450, 708]
[:mouse_move, 1135, 154, 2, 451, 709]
[:mouse_move, 1163, 139, 2, 452, 710]
[:mouse_move, 1166, 138, 2, 453, 711]
[:mouse_move, 1175, 131, 2, 454, 712]
[:mouse_move, 1177, 129, 2, 455, 713]
[:mouse_move, 1180, 124, 2, 456, 714]
[:mouse_move, 1180, 123, 2, 457, 715]
[:mouse_move, 1181, 120, 2, 458, 716]
[:mouse_move, 1182, 118, 2, 459, 717]
[:mouse_move, 1183, 113, 2, 460, 718]
[:mouse_move, 1183, 109, 2, 461, 719]
[:mouse_move, 1187, 99, 2, 462, 720]
[:mouse_move, 1189, 91, 2, 463, 721]
[:mouse_move, 1198, 72, 2, 464, 722]
[:mouse_move, 1203, 61, 2, 465, 723]
[:mouse_move, 1213, 45, 2, 466, 724]
[:mouse_move, 1219, 37, 2, 467, 725]
[:mouse_move, 1232, 24, 2, 468, 726]
[:mouse_move, 1237, 19, 2, 469, 727]
[:mouse_move, 1242, 17, 2, 470, 728]
[:mouse_move, 1247, 15, 2, 471, 729]
[:mouse_move, 1250, 14, 2, 472, 730]
[:mouse_move, 1251, 14, 2, 473, 754]
[:mouse_move, 1252, 14, 2, 474, 755]
[:mouse_move, 1255, 14, 2, 475, 756]
[:mouse_move, 1256, 14, 2, 476, 757]
[:mouse_move, 1258, 14, 2, 477, 758]
[:mouse_move, 1260, 13, 2, 478, 760]
[:mouse_button_pressed, 1, 0, 1, 479, 768]
[:mouse_button_up, 1, 0, 1, 480, 776]
[:mouse_move, 1254, 17, 2, 481, 801]
[:mouse_move, 1242, 23, 2, 482, 802]
[:mouse_move, 1193, 43, 2, 483, 803]
[:mouse_move, 1154, 56, 2, 484, 804]
[:mouse_move, 1045, 90, 2, 485, 805]
[:mouse_move, 978, 113, 2, 486, 806]
[:mouse_move, 845, 154, 2, 487, 807]
[:mouse_move, 821, 161, 2, 488, 808]
[:mouse_move, 772, 176, 2, 489, 809]
[:mouse_move, 722, 190, 2, 490, 810]
[:mouse_move, 713, 193, 2, 491, 811]
[:mouse_move, 689, 200, 2, 492, 812]
[:mouse_move, 678, 203, 2, 493, 813]
[:mouse_move, 666, 207, 2, 494, 814]
[:mouse_move, 663, 208, 2, 495, 815]
[:mouse_move, 656, 211, 2, 496, 816]
[:mouse_move, 654, 212, 2, 497, 817]
[:mouse_move, 649, 215, 2, 498, 818]
[:mouse_move, 646, 217, 2, 499, 819]
[:mouse_move, 641, 222, 2, 500, 820]
[:mouse_move, 638, 224, 2, 501, 821]
[:mouse_move, 632, 231, 2, 502, 822]
[:mouse_move, 630, 235, 2, 503, 823]
[:mouse_move, 625, 245, 2, 504, 824]
[:mouse_move, 621, 255, 2, 505, 825]
[:mouse_move, 618, 265, 2, 506, 826]
[:mouse_move, 618, 267, 2, 507, 827]
[:mouse_move, 616, 275, 2, 508, 828]
[:mouse_move, 616, 277, 2, 509, 829]
[:mouse_move, 616, 285, 2, 510, 830]
[:mouse_move, 616, 288, 2, 511, 831]
[:mouse_move, 616, 293, 2, 512, 832]
[:mouse_move, 617, 295, 2, 513, 833]
[:mouse_move, 617, 298, 2, 514, 834]
[:mouse_move, 618, 300, 2, 515, 835]
[:mouse_move, 618, 302, 2, 516, 837]
[:mouse_move, 618, 303, 2, 517, 839]
[:mouse_move, 619, 303, 2, 518, 841]
[:mouse_move, 621, 303, 2, 519, 842]
[:mouse_move, 626, 303, 2, 520, 843]
[:mouse_move, 630, 303, 2, 521, 844]
[:mouse_move, 642, 303, 2, 522, 845]
[:mouse_move, 649, 302, 2, 523, 846]
[:mouse_move, 664, 300, 2, 524, 847]
[:mouse_move, 669, 299, 2, 525, 848]
[:mouse_move, 680, 298, 2, 526, 849]
[:mouse_move, 685, 298, 2, 527, 850]
[:mouse_move, 695, 298, 2, 528, 851]
[:mouse_move, 699, 298, 2, 529, 852]
[:mouse_move, 705, 298, 2, 530, 853]
[:mouse_move, 708, 298, 2, 531, 854]
[:mouse_move, 714, 299, 2, 532, 855]
[:mouse_move, 718, 301, 2, 533, 856]
[:mouse_move, 725, 303, 2, 534, 857]
[:mouse_move, 729, 304, 2, 535, 858]
[:mouse_move, 736, 306, 2, 536, 859]
[:mouse_move, 743, 309, 2, 537, 861]
[:mouse_move, 745, 309, 2, 538, 862]
[:mouse_move, 747, 309, 2, 539, 863]
[:mouse_move, 748, 309, 2, 540, 864]
[:mouse_move, 749, 309, 2, 541, 866]
[:mouse_move, 750, 309, 2, 542, 867]
[:mouse_move, 762, 316, 2, 543, 889]
[:mouse_move, 793, 340, 2, 544, 890]
[:mouse_move, 824, 367, 2, 545, 891]
[:mouse_move, 905, 445, 2, 546, 892]
[:mouse_move, 926, 465, 2, 547, 893]
[:mouse_move, 966, 506, 2, 548, 894]
[:mouse_move, 1010, 548, 2, 549, 895]
[:mouse_move, 1033, 569, 2, 550, 896]
[:mouse_move, 1072, 604, 2, 551, 897]
[:mouse_move, 1087, 619, 2, 552, 898]
[:mouse_move, 1113, 647, 2, 553, 899]
[:mouse_move, 1118, 653, 2, 554, 900]
[:mouse_move, 1134, 670, 2, 555, 901]
[:mouse_move, 1136, 673, 2, 556, 902]
[:mouse_move, 1144, 680, 2, 557, 903]
[:mouse_move, 1150, 685, 2, 558, 904]
[:mouse_move, 1153, 687, 2, 559, 905]
[:mouse_move, 1155, 687, 2, 560, 918]
[:mouse_move, 1160, 687, 2, 561, 919]
[:mouse_move, 1173, 688, 2, 562, 920]
[:mouse_move, 1179, 691, 2, 563, 921]
[:mouse_move, 1197, 698, 2, 564, 922]
[:mouse_move, 1203, 701, 2, 565, 923]
[:mouse_move, 1216, 705, 2, 566, 924]
[:mouse_move, 1222, 708, 2, 567, 925]
[:mouse_move, 1231, 711, 2, 568, 926]
[:mouse_move, 1238, 712, 2, 569, 927]
[:mouse_move, 1245, 713, 2, 570, 928]
[:mouse_move, 1249, 713, 2, 571, 929]
[:mouse_move, 1254, 713, 2, 572, 930]
[:mouse_move, 1256, 713, 2, 573, 931]
[:mouse_move, 1259, 713, 2, 574, 932]
[:mouse_move, 1261, 713, 2, 575, 934]
[:mouse_move, 1261, 712, 2, 576, 936]
[:mouse_move, 1261, 710, 2, 577, 938]
[:mouse_move, 1261, 709, 2, 578, 939]
[:mouse_move, 1261, 707, 2, 579, 940]
[:mouse_move, 1261, 706, 2, 580, 941]
[:mouse_move, 1261, 703, 2, 581, 942]
[:mouse_move, 1261, 702, 2, 582, 943]
[:mouse_move, 1261, 700, 2, 583, 944]
[:mouse_move, 1261, 699, 2, 584, 945]
[:mouse_move, 1261, 697, 2, 585, 947]
[:mouse_move, 1261, 696, 2, 586, 949]
[:mouse_move, 1261, 695, 2, 587, 951]
[:mouse_button_pressed, 1, 0, 1, 588, 953]
[:mouse_button_up, 1, 0, 1, 589, 959]
[:mouse_move, 1243, 677, 2, 590, 974]
[:mouse_move, 1229, 666, 2, 591, 975]
[:mouse_move, 1186, 633, 2, 592, 976]
[:mouse_move, 1134, 597, 2, 593, 977]
[:mouse_move, 1024, 527, 2, 594, 978]
[:mouse_move, 1003, 516, 2, 595, 979]
[:mouse_move, 924, 478, 2, 596, 980]
[:mouse_move, 911, 474, 2, 597, 981]
[:mouse_move, 851, 449, 2, 598, 982]
[:mouse_move, 829, 440, 2, 599, 983]
[:mouse_move, 804, 427, 2, 600, 984]
[:mouse_move, 793, 422, 2, 601, 985]
[:mouse_move, 782, 416, 2, 602, 986]
[:mouse_move, 771, 409, 2, 603, 987]
[:mouse_move, 759, 399, 2, 604, 988]
[:mouse_move, 754, 395, 2, 605, 989]
[:mouse_move, 746, 389, 2, 606, 990]
[:mouse_move, 742, 386, 2, 607, 991]
[:mouse_move, 733, 380, 2, 608, 992]
[:mouse_move, 727, 377, 2, 609, 993]
[:mouse_move, 709, 368, 2, 610, 994]
[:mouse_move, 699, 363, 2, 611, 995]
[:mouse_move, 686, 355, 2, 612, 996]
[:mouse_move, 679, 350, 2, 613, 997]
[:mouse_move, 672, 346, 2, 614, 998]
[:mouse_move, 669, 343, 2, 615, 999]
[:mouse_move, 667, 341, 2, 616, 1000]
[:mouse_move, 663, 339, 2, 617, 1001]
[:mouse_move, 662, 337, 2, 618, 1002]
[:mouse_move, 658, 332, 2, 619, 1003]
[:mouse_move, 655, 329, 2, 620, 1004]
[:mouse_move, 652, 325, 2, 621, 1005]
[:mouse_move, 650, 322, 2, 622, 1006]
[:mouse_move, 646, 318, 2, 623, 1007]
[:mouse_move, 645, 317, 2, 624, 1008]
[:mouse_move, 644, 315, 2, 625, 1009]
[:mouse_move, 643, 314, 2, 626, 1010]
[:mouse_move, 640, 313, 2, 627, 1011]
[:mouse_move, 639, 312, 2, 628, 1012]
[:mouse_move, 636, 310, 2, 629, 1013]
[:mouse_move, 635, 310, 2, 630, 1014]
[:mouse_move, 634, 309, 2, 631, 1015]
[:mouse_move, 633, 308, 2, 632, 1016]
[:mouse_move, 632, 308, 2, 633, 1017]
[:mouse_move, 633, 308, 2, 634, 1024]
[:mouse_move, 634, 308, 2, 635, 1025]
[:mouse_move, 635, 308, 2, 636, 1027]
[:mouse_move, 637, 308, 2, 637, 1028]
[:mouse_move, 639, 308, 2, 638, 1029]
[:mouse_move, 643, 308, 2, 639, 1030]
[:mouse_move, 646, 308, 2, 640, 1031]
[:mouse_move, 650, 308, 2, 641, 1032]
[:mouse_move, 652, 308, 2, 642, 1033]
[:mouse_move, 658, 308, 2, 643, 1034]
[:mouse_move, 661, 308, 2, 644, 1035]
[:mouse_move, 670, 308, 2, 645, 1036]
[:mouse_move, 674, 308, 2, 646, 1037]
[:mouse_move, 680, 307, 2, 647, 1038]
[:mouse_move, 683, 307, 2, 648, 1039]
[:mouse_move, 691, 306, 2, 649, 1040]
[:mouse_move, 694, 305, 2, 650, 1041]
[:mouse_move, 702, 304, 2, 651, 1042]
[:mouse_move, 705, 304, 2, 652, 1043]
[:mouse_move, 712, 303, 2, 653, 1044]
[:mouse_move, 715, 303, 2, 654, 1045]
[:mouse_move, 722, 303, 2, 655, 1046]
[:mouse_move, 727, 303, 2, 656, 1047]
[:mouse_move, 735, 303, 2, 657, 1048]
[:mouse_move, 740, 303, 2, 658, 1049]
[:mouse_move, 748, 303, 2, 659, 1050]
[:mouse_move, 750, 303, 2, 660, 1051]
[:mouse_move, 756, 303, 2, 661, 1052]
[:mouse_move, 758, 304, 2, 662, 1053]
[:mouse_move, 760, 304, 2, 663, 1054]
[:mouse_move, 762, 304, 2, 664, 1055]
[:mouse_move, 763, 304, 2, 665, 1056]
[:mouse_move, 764, 304, 2, 666, 1057]
[:mouse_move, 756, 304, 2, 667, 1075]
[:mouse_move, 744, 306, 2, 668, 1076]
[:mouse_move, 707, 310, 2, 669, 1077]
[:mouse_move, 682, 311, 2, 670, 1078]
[:mouse_move, 548, 334, 2, 671, 1079]
[:mouse_move, 472, 350, 2, 672, 1080]
[:mouse_move, 432, 363, 2, 673, 1081]
[:mouse_move, 289, 420, 2, 674, 1082]
[:mouse_move, 226, 452, 2, 675, 1083]
[:mouse_move, 156, 504, 2, 676, 1084]
[:mouse_move, 124, 536, 2, 677, 1085]
[:mouse_move, 97, 576, 2, 678, 1086]
[:mouse_move, 93, 584, 2, 679, 1087]
[:mouse_move, 84, 606, 2, 680, 1088]
[:mouse_move, 81, 610, 2, 681, 1100]
[:mouse_move, 75, 620, 2, 682, 1101]
[:mouse_move, 59, 642, 2, 683, 1102]
[:mouse_move, 52, 649, 2, 684, 1103]
[:mouse_move, 45, 658, 2, 685, 1104]
[:mouse_move, 39, 666, 2, 686, 1105]
[:mouse_move, 31, 676, 2, 687, 1106]
[:mouse_move, 30, 678, 2, 688, 1107]
[:mouse_move, 28, 681, 2, 689, 1108]
[:mouse_move, 27, 682, 2, 690, 1109]
[:mouse_move, 27, 683, 2, 691, 1111]
[:mouse_move, 27, 684, 2, 692, 1113]
[:mouse_move, 27, 685, 2, 693, 1116]
[:mouse_move, 27, 686, 2, 694, 1117]
[:mouse_move, 27, 689, 2, 695, 1119]
[:mouse_move, 26, 690, 2, 696, 1120]
[:mouse_move, 23, 694, 2, 697, 1121]
[:mouse_move, 22, 695, 2, 698, 1122]
[:mouse_move, 18, 697, 2, 699, 1123]
[:mouse_move, 17, 698, 2, 700, 1124]
[:mouse_move, 16, 699, 2, 701, 1125]
[:mouse_move, 15, 699, 2, 702, 1126]
[:mouse_move, 15, 700, 2, 703, 1127]
[:mouse_move, 15, 701, 2, 704, 1131]
[:mouse_button_pressed, 1, 0, 1, 705, 1141]
[:mouse_button_up, 1, 0, 1, 706, 1148]
[:mouse_move, 21, 699, 2, 707, 1160]
[:mouse_move, 31, 694, 2, 708, 1161]
[:mouse_move, 77, 673, 2, 709, 1162]
[:mouse_move, 153, 638, 2, 710, 1163]
[:mouse_move, 225, 604, 2, 711, 1164]
[:mouse_move, 406, 525, 2, 712, 1165]
[:mouse_move, 512, 479, 2, 713, 1166]
[:mouse_move, 655, 417, 2, 714, 1167]
[:mouse_move, 737, 382, 2, 715, 1168]
[:mouse_move, 788, 360, 2, 716, 1169]
[:mouse_move, 831, 339, 2, 717, 1170]
[:mouse_move, 849, 332, 2, 718, 1171]
[:mouse_move, 864, 325, 2, 719, 1172]
[:mouse_move, 879, 317, 2, 720, 1173]
[:mouse_move, 880, 316, 2, 721, 1174]
[:mouse_move, 877, 316, 2, 722, 1175]
[:mouse_move, 875, 316, 2, 723, 1176]
[:mouse_move, 871, 316, 2, 724, 1177]
[:mouse_move, 866, 315, 2, 725, 1178]
[:mouse_move, 855, 314, 2, 726, 1179]
[:mouse_move, 842, 313, 2, 727, 1180]
[:mouse_move, 808, 313, 2, 728, 1181]
[:mouse_move, 783, 313, 2, 729, 1182]
[:mouse_move, 728, 313, 2, 730, 1183]
[:mouse_move, 715, 313, 2, 731, 1184]
[:mouse_move, 670, 313, 2, 732, 1185]
[:mouse_move, 652, 313, 2, 733, 1186]
[:mouse_move, 641, 313, 2, 734, 1187]
[:mouse_move, 634, 313, 2, 735, 1188]
[:mouse_move, 625, 313, 2, 736, 1189]
[:mouse_move, 623, 313, 2, 737, 1190]
[:mouse_move, 622, 313, 2, 738, 1191]
[:mouse_move, 621, 312, 2, 739, 1192]
[:mouse_move, 622, 312, 2, 740, 1201]
[:mouse_move, 624, 312, 2, 741, 1202]
[:mouse_move, 626, 312, 2, 742, 1203]
[:mouse_move, 632, 311, 2, 743, 1204]
[:mouse_move, 636, 311, 2, 744, 1205]
[:mouse_move, 647, 311, 2, 745, 1206]
[:mouse_move, 653, 311, 2, 746, 1207]
[:mouse_move, 665, 310, 2, 747, 1208]
[:mouse_move, 669, 310, 2, 748, 1209]
[:mouse_move, 676, 310, 2, 749, 1210]
[:mouse_move, 683, 310, 2, 750, 1211]
[:mouse_move, 687, 310, 2, 751, 1212]
[:mouse_move, 689, 310, 2, 752, 1213]
[:mouse_move, 693, 310, 2, 753, 1214]
[:mouse_move, 696, 310, 2, 754, 1215]
[:mouse_move, 702, 310, 2, 755, 1216]
[:mouse_move, 706, 310, 2, 756, 1217]
[:mouse_move, 709, 310, 2, 757, 1218]
[:mouse_move, 714, 310, 2, 758, 1219]
[:mouse_move, 716, 310, 2, 759, 1220]
[:mouse_move, 721, 310, 2, 760, 1221]
[:mouse_move, 724, 310, 2, 761, 1222]
[:mouse_move, 727, 310, 2, 762, 1223]
[:mouse_move, 728, 310, 2, 763, 1224]
[:mouse_move, 730, 310, 2, 764, 1225]
[:mouse_move, 731, 310, 2, 765, 1227]
[:mouse_move, 730, 310, 2, 766, 1245]
[:mouse_move, 687, 306, 2, 767, 1246]
[:mouse_move, 658, 303, 2, 768, 1247]
[:mouse_move, 570, 289, 2, 769, 1248]
[:mouse_move, 503, 280, 2, 770, 1249]
[:mouse_move, 371, 259, 2, 771, 1250]
[:mouse_move, 345, 254, 2, 772, 1251]
[:mouse_move, 248, 229, 2, 773, 1252]
[:mouse_move, 212, 215, 2, 774, 1253]
[:mouse_move, 177, 195, 2, 775, 1254]
[:mouse_move, 159, 184, 2, 776, 1255]
[:mouse_move, 141, 168, 2, 777, 1256]
[:mouse_move, 123, 151, 2, 778, 1257]
[:mouse_move, 96, 121, 2, 779, 1258]
[:mouse_move, 91, 114, 2, 780, 1259]
[:mouse_move, 71, 87, 2, 781, 1260]
[:mouse_move, 62, 74, 2, 782, 1261]
[:mouse_move, 51, 59, 2, 783, 1262]
[:mouse_move, 45, 52, 2, 784, 1263]
[:mouse_move, 36, 40, 2, 785, 1264]
[:mouse_move, 34, 39, 2, 786, 1279]
[:mouse_move, 32, 39, 2, 787, 1280]
[:mouse_move, 30, 37, 2, 788, 1281]
[:mouse_move, 29, 35, 2, 789, 1282]
[:mouse_move, 26, 32, 2, 790, 1283]
[:mouse_move, 25, 31, 2, 791, 1284]
[:mouse_move, 24, 30, 2, 792, 1285]
[:mouse_move, 23, 29, 2, 793, 1286]
[:mouse_move, 22, 27, 2, 794, 1287]
[:mouse_move, 22, 26, 2, 795, 1289]
[:mouse_move, 21, 25, 2, 796, 1290]
[:mouse_move, 21, 23, 2, 797, 1291]
[:mouse_move, 21, 22, 2, 798, 1292]
[:mouse_move, 20, 20, 2, 799, 1293]
[:mouse_move, 20, 19, 2, 800, 1295]
[:mouse_move, 19, 19, 2, 801, 1296]
[:mouse_move, 19, 18, 2, 802, 1297]
[:mouse_move, 18, 17, 2, 803, 1298]
[:mouse_move, 17, 16, 2, 804, 1299]
[:mouse_move, 16, 15, 2, 805, 1300]
[:mouse_move, 15, 14, 2, 806, 1302]
[:mouse_move, 15, 13, 2, 807, 1303]
[:mouse_move, 14, 13, 2, 808, 1304]
[:mouse_move, 14, 12, 2, 809, 1307]
[:mouse_button_pressed, 1, 0, 1, 810, 1322]
[:mouse_button_up, 1, 0, 1, 811, 1328]
[:mouse_move, 36, 23, 2, 812, 1345]
[:mouse_move, 57, 34, 2, 813, 1346]
[:mouse_move, 231, 131, 2, 814, 1347]
[:mouse_move, 347, 196, 2, 815, 1348]
[:mouse_move, 530, 298, 2, 816, 1349]
[:mouse_move, 582, 323, 2, 817, 1350]
[:mouse_move, 707, 381, 2, 818, 1351]
[:mouse_move, 769, 405, 2, 819, 1352]
[:mouse_move, 786, 409, 2, 820, 1353]
[:mouse_move, 807, 414, 2, 821, 1354]
[:mouse_move, 826, 420, 2, 822, 1355]
[:mouse_move, 844, 425, 2, 823, 1356]
[:mouse_move, 850, 426, 2, 824, 1357]
[:mouse_move, 850, 425, 2, 825, 1358]
[:mouse_move, 847, 423, 2, 826, 1359]
[:mouse_move, 838, 418, 2, 827, 1360]
[:mouse_move, 833, 414, 2, 828, 1361]
[:mouse_move, 816, 403, 2, 829, 1362]
[:mouse_move, 809, 398, 2, 830, 1363]
[:mouse_move, 781, 378, 2, 831, 1364]
[:mouse_move, 773, 373, 2, 832, 1365]
[:mouse_move, 745, 360, 2, 833, 1366]
[:mouse_move, 730, 354, 2, 834, 1367]
[:mouse_move, 709, 348, 2, 835, 1368]
[:mouse_move, 697, 345, 2, 836, 1369]
[:mouse_move, 685, 342, 2, 837, 1370]
[:mouse_move, 680, 342, 2, 838, 1371]
[:mouse_move, 672, 340, 2, 839, 1372]
[:mouse_move, 670, 340, 2, 840, 1373]
[:mouse_move, 666, 340, 2, 841, 1374]
[:mouse_move, 665, 339, 2, 842, 1375]
[:mouse_move, 661, 339, 2, 843, 1376]
[:mouse_move, 660, 338, 2, 844, 1377]
[:mouse_move, 655, 337, 2, 845, 1378]
[:mouse_move, 652, 336, 2, 846, 1379]
[:mouse_move, 650, 333, 2, 847, 1380]
[:mouse_move, 644, 328, 2, 848, 1381]
[:mouse_move, 642, 326, 2, 849, 1382]
[:mouse_move, 638, 322, 2, 850, 1383]
[:mouse_move, 637, 321, 2, 851, 1384]
[:mouse_move, 636, 318, 2, 852, 1385]
[:mouse_move, 635, 318, 2, 853, 1386]
[:mouse_move, 635, 317, 2, 854, 1387]
[:mouse_move, 636, 317, 2, 855, 1392]
[:mouse_move, 642, 317, 2, 856, 1393]
[:mouse_move, 648, 317, 2, 857, 1394]
[:mouse_move, 658, 318, 2, 858, 1395]
[:mouse_move, 664, 318, 2, 859, 1396]
[:mouse_move, 671, 318, 2, 860, 1397]
[:mouse_move, 676, 318, 2, 861, 1398]
[:mouse_move, 684, 318, 2, 862, 1399]
[:mouse_move, 687, 318, 2, 863, 1400]
[:mouse_move, 693, 318, 2, 864, 1401]
[:mouse_move, 694, 318, 2, 865, 1402]
[:mouse_move, 701, 318, 2, 866, 1403]
[:mouse_move, 702, 318, 2, 867, 1404]
[:mouse_move, 708, 318, 2, 868, 1405]
[:mouse_move, 711, 318, 2, 869, 1406]
[:mouse_move, 713, 318, 2, 870, 1407]
[:mouse_move, 718, 318, 2, 871, 1408]
[:mouse_move, 721, 318, 2, 872, 1409]
[:mouse_move, 726, 317, 2, 873, 1410]
[:mouse_move, 728, 317, 2, 874, 1411]
[:mouse_move, 731, 317, 2, 875, 1412]
[:mouse_move, 733, 317, 2, 876, 1413]
[:mouse_move, 736, 317, 2, 877, 1414]
[:mouse_move, 738, 317, 2, 878, 1415]
[:mouse_move, 739, 317, 2, 879, 1416]
[:mouse_move, 740, 317, 2, 880, 1417]
[:mouse_move, 741, 317, 2, 881, 1418]
[:mouse_move, 705, 270, 2, 882, 1455]
[:mouse_move, 659, 221, 2, 883, 1456]
[:mouse_move, 515, 112, 2, 884, 1457]
[:mouse_move, 474, 92, 2, 885, 1458]
[:mouse_move, 315, 31, 2, 886, 1459]
[:mouse_move, 238, 13, 2, 887, 1460]
[:mouse_move, 168, 3, 2, 888, 1461]
[:mouse_move, 92, 0, 2, 889, 1462]
