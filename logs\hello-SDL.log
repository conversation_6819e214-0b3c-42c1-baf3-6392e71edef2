* [Engine] Log started at 2025/9/24 8:56:43
* [Engine] DragonRuby Game Toolkit
* [Engine]   Version: 6.33
* [Engine]   Tier: Pro
* [Engine]   GIT Hash: cc47d35e9a679671c4b3d5c01d1954108e0fea50
* [Engine]   Build Date: Sep  3 2025 00:19:29
* [Engine] Platform: Windows
! [Engine] Metadata file does not have all required fields!
- [Engine] Process Working Directory: C:\Users\<USER>\work\the_necromancers_cube\
- [Engine] Game Dir: C:\Users\<USER>\work\the_necromancers_cube\/mygame
- [Engine] Game ID: hello-SDL
- [Engine] Game Title: Update metadata/game_metadata.txt in your mygame directory to change this title.
- [Engine] Game Version: 1.0
- [Engine] Game Package ID: org.dragonruby.hello-SDL
- [Engine] Game Developer ID: dragonruby
- [Engine] Game Developer Title: DragonRuby LLC
- [Engine] Production Build: no
- [Engine] Remote Hotload: no
? [Engine] Loading cvars from file 'metadata/cvars.txt' ...
? [Engine] File 'metadata/cvars.txt' line 44 setting cvar 'log.filter_subsystems' to 'HTTPServer,HTTP'
? [Engine] File 'metadata/cvars.txt' complete
- [Engine] Render Driver: direct3d11
- [Render] SDL video backend: windows
- [Render] SDL renderer backend: direct3d11
- [Render] Window size: 1280x720 (requested 1280x720)
- [Render] Fullscreen Desktop mode: no
- [Render] HD: no
- [Render] HighDPI: no
- [Render] Max texture size: 16384x16384 (system reported 16384x16384)
- [Render] Logical render size: 1280x720
- [Render] Native render size: 1280x720
? [Engine] RNG seed has been set to 1758700604.
- [Engine] Janitor is using 4 threads.
- [Game] * INFO - Game state and exception will be written to logs/exceptions/game_state_130.txt and logs/exceptions/current.txt.
- [Game]   global_tick_count: 130
- [Game]   tick_count: 130
- [Game] * EXCEPTION: stack level too deep
** Backtrace:
*** app/entities.rb:17:in render
*** app/main.rb:50:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick
*** app/cutscenes.rb:17:in tick
*** app/main.rb:62:in tick
*** app/scenes.rb:43:in tick
*** app/main.rb:84:in tick (130)
* [Engine] Logging shutting down
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               ? [Engine] Reloaded app/main.rb. (2415288)
? [Engine] ~reset~ has been invoked (2415708).
? [Engine] RNG seed has been set to 1758660557.
? [Engine] Reloaded app/main.rb. (2415708)
