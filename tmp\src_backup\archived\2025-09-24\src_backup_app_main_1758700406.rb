require "app/animations.rb"
require "app/entities.rb"
require "app/scenes.rb"
require "app/cutscenes.rb"

# Menu
class MenuScene < Scene
  def tick(args)
    args.outputs.labels << [100,400,"Menu - Press SPACE to start"]
    args.state.scene_manager.fade_to(:game, args, duration: 60) if args.inputs.keyboard.key_down.space
  end
end

# Game
class GameScene < Scene
  def enter(args)
    args.state.player ||= Entity.new(x:0,y:300,color:[100,200,255])
    args.state.enemy  ||= Entity.new(x:800,y:500,color:[255,100,100])
    args.state.cutscene_manager ||= CutsceneManager.new

    # Cutscene 1
    anim1 = Animation.new do
      args.state.cutscene_manager.queue(self)
      show_message "A wild enemy appears!", duration: 120
      play_sound "alert.wav"
      wait 30
      parallel do
        tween :x, to: 600, duration: 80, easing: ->(t){t*t}
        set :frame, 2; wait 20
        spawn_effect "sparkle", x: 600, y: 300
      end
    end

    # Cutscene 2
    anim2 = Animation.new do
      show_message "Prepare for battle!", duration: 90
      wait 60
    end

    args.state.cutscene_manager.queue(anim1)
    args.state.cutscene_manager.queue(anim2)
  end

  def tick(args)
    # Tick entidades
    args.state.player.tick(args)
    args.state.enemy.tick(args)

    # Render
    args.state.player.render(args)
    args.state.enemy.render(args)

    # Tick efeitos
    args.state.effects ||= []
    args.state.effects.each do |e|
      args.outputs.solids << [e[:x], e[:y], 32,32,255,255,0]
      e[:duration] -= 1
    end
    args.state.effects.reject! { |e| e[:duration] <= 0 }

    # Cutscenes
    args.state.cutscene_manager.tick(args)

    # Mensagens
    args.state.messages ||= []
    args.state.messages.each do |m|
      args.outputs.labels << [400,600,m[:text]]
      m[:duration] -= 1
    end
    args.state.messages.reject! { |m| m[:duration] <= 0 }
  end
end

# Tick principal
def tick(args)
  args.state.scene_manager ||= begin
    sm = SceneManager.new
    sm.add(:menu, MenuScene.new)
    sm.add(:game, GameScene.new)
    sm.switch(:menu,args)
    sm
  end

  args.state.scene_manager.tick(args)
end