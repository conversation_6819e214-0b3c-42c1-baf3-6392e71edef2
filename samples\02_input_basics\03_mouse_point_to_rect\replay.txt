replay_version 2.0
stopped_at 796
seed 100
recorded_at Sun Sep 29 21:34:41 2019
[:mouse_move, 1191, 0, 2, 1, 90]
[:mouse_move, 1115, 68, 2, 2, 90]
[:mouse_move, 1039, 143, 2, 3, 91]
[:mouse_move, 963, 216, 2, 4, 92]
[:mouse_move, 928, 249, 2, 5, 92]
[:mouse_move, 866, 308, 2, 6, 93]
[:mouse_move, 843, 329, 2, 7, 94]
[:mouse_move, 787, 381, 2, 8, 95]
[:mouse_move, 776, 391, 2, 9, 96]
[:mouse_move, 755, 410, 2, 10, 97]
[:mouse_move, 745, 418, 2, 11, 98]
[:mouse_move, 738, 424, 2, 12, 99]
[:mouse_move, 736, 425, 2, 13, 100]
[:mouse_move, 736, 424, 2, 14, 103]
[:mouse_move, 736, 423, 2, 15, 105]
[:mouse_move, 736, 422, 2, 16, 106]
[:mouse_move, 736, 421, 2, 17, 107]
[:mouse_move, 736, 419, 2, 18, 107]
[:mouse_move, 736, 416, 2, 19, 108]
[:mouse_move, 735, 413, 2, 20, 109]
[:mouse_move, 735, 411, 2, 21, 109]
[:mouse_move, 734, 408, 2, 22, 110]
[:mouse_move, 732, 404, 2, 23, 111]
[:mouse_move, 731, 401, 2, 24, 111]
[:mouse_move, 729, 393, 2, 25, 112]
[:mouse_move, 728, 385, 2, 26, 113]
[:mouse_move, 728, 376, 2, 27, 113]
[:mouse_move, 729, 367, 2, 28, 114]
[:mouse_move, 734, 355, 2, 29, 115]
[:mouse_move, 741, 344, 2, 30, 115]
[:mouse_move, 748, 333, 2, 31, 116]
[:mouse_move, 752, 329, 2, 32, 117]
[:mouse_move, 755, 326, 2, 33, 117]
[:mouse_move, 758, 322, 2, 34, 118]
[:mouse_move, 760, 319, 2, 35, 119]
[:mouse_move, 762, 317, 2, 36, 119]
[:mouse_move, 762, 316, 2, 37, 120]
[:mouse_move, 755, 318, 2, 38, 122]
[:mouse_move, 748, 321, 2, 39, 123]
[:mouse_move, 721, 326, 2, 40, 124]
[:mouse_move, 705, 329, 2, 41, 125]
[:mouse_move, 674, 330, 2, 42, 126]
[:mouse_move, 645, 328, 2, 43, 127]
[:mouse_move, 602, 310, 2, 44, 128]
[:mouse_move, 590, 304, 2, 45, 129]
[:mouse_move, 577, 298, 2, 46, 130]
[:mouse_move, 567, 293, 2, 47, 131]
[:mouse_move, 555, 287, 2, 48, 132]
[:mouse_move, 553, 286, 2, 49, 133]
[:mouse_move, 551, 284, 2, 50, 134]
[:mouse_move, 550, 284, 2, 51, 134]
[:mouse_move, 550, 285, 2, 52, 140]
[:mouse_move, 550, 286, 2, 53, 141]
[:mouse_move, 549, 288, 2, 54, 142]
[:mouse_move, 548, 291, 2, 55, 142]
[:mouse_move, 546, 295, 2, 56, 143]
[:mouse_move, 542, 302, 2, 57, 144]
[:mouse_move, 539, 305, 2, 58, 144]
[:mouse_move, 535, 311, 2, 59, 145]
[:mouse_move, 530, 317, 2, 60, 146]
[:mouse_move, 524, 321, 2, 61, 146]
[:mouse_move, 518, 327, 2, 62, 147]
[:mouse_move, 513, 331, 2, 63, 148]
[:mouse_move, 507, 335, 2, 64, 148]
[:mouse_move, 504, 336, 2, 65, 149]
[:mouse_move, 500, 338, 2, 66, 150]
[:mouse_move, 494, 341, 2, 67, 151]
[:mouse_move, 492, 342, 2, 68, 152]
[:mouse_move, 491, 342, 2, 69, 152]
[:mouse_move, 489, 342, 2, 70, 153]
[:mouse_move, 486, 343, 2, 71, 154]
[:mouse_move, 482, 343, 2, 72, 155]
[:mouse_move, 480, 343, 2, 73, 156]
[:mouse_move, 477, 343, 2, 74, 157]
[:mouse_move, 475, 343, 2, 75, 158]
[:mouse_move, 473, 343, 2, 76, 159]
[:mouse_move, 472, 343, 2, 77, 160]
[:mouse_move, 471, 343, 2, 78, 162]
[:mouse_move, 471, 342, 2, 79, 163]
[:mouse_move, 471, 341, 2, 80, 167]
[:mouse_move, 471, 340, 2, 81, 169]
[:mouse_move, 472, 340, 2, 82, 171]
[:mouse_move, 473, 340, 2, 83, 172]
[:mouse_move, 473, 339, 2, 84, 173]
[:mouse_move, 474, 339, 2, 85, 174]
[:mouse_move, 475, 339, 2, 86, 175]
[:mouse_move, 476, 339, 2, 87, 178]
[:mouse_move, 479, 338, 2, 88, 180]
[:mouse_move, 482, 338, 2, 89, 181]
[:mouse_move, 489, 336, 2, 90, 182]
[:mouse_move, 493, 335, 2, 91, 183]
[:mouse_move, 502, 334, 2, 92, 184]
[:mouse_move, 505, 334, 2, 93, 185]
[:mouse_move, 515, 333, 2, 94, 186]
[:mouse_move, 525, 333, 2, 95, 187]
[:mouse_move, 529, 333, 2, 96, 188]
[:mouse_move, 536, 333, 2, 97, 188]
[:mouse_move, 539, 333, 2, 98, 189]
[:mouse_move, 544, 332, 2, 99, 190]
[:mouse_move, 548, 332, 2, 100, 190]
[:mouse_move, 555, 332, 2, 101, 191]
[:mouse_move, 559, 332, 2, 102, 192]
[:mouse_move, 563, 332, 2, 103, 192]
[:mouse_move, 568, 332, 2, 104, 193]
[:mouse_move, 572, 332, 2, 105, 194]
[:mouse_move, 577, 332, 2, 106, 194]
[:mouse_move, 579, 332, 2, 107, 195]
[:mouse_move, 583, 332, 2, 108, 196]
[:mouse_move, 585, 332, 2, 109, 196]
[:mouse_move, 588, 332, 2, 110, 197]
[:mouse_move, 589, 332, 2, 111, 198]
[:mouse_move, 591, 332, 2, 112, 198]
[:mouse_move, 594, 332, 2, 113, 199]
[:mouse_move, 597, 332, 2, 114, 200]
[:mouse_move, 600, 332, 2, 115, 200]
[:mouse_move, 602, 332, 2, 116, 201]
[:mouse_move, 605, 332, 2, 117, 202]
[:mouse_move, 610, 332, 2, 118, 202]
[:mouse_move, 614, 332, 2, 119, 203]
[:mouse_move, 618, 332, 2, 120, 204]
[:mouse_move, 634, 333, 2, 121, 205]
[:mouse_move, 638, 334, 2, 122, 206]
[:mouse_move, 654, 337, 2, 123, 207]
[:mouse_move, 660, 338, 2, 124, 208]
[:mouse_move, 675, 340, 2, 125, 209]
[:mouse_move, 681, 340, 2, 126, 210]
[:mouse_move, 688, 341, 2, 127, 211]
[:mouse_move, 692, 341, 2, 128, 212]
[:mouse_move, 696, 341, 2, 129, 212]
[:mouse_move, 698, 341, 2, 130, 213]
[:mouse_move, 700, 340, 2, 131, 214]
[:mouse_move, 702, 340, 2, 132, 215]
[:mouse_move, 703, 339, 2, 133, 215]
[:mouse_move, 704, 339, 2, 134, 216]
[:mouse_move, 705, 338, 2, 135, 217]
[:mouse_move, 706, 338, 2, 136, 217]
[:mouse_move, 707, 338, 2, 137, 220]
[:mouse_move, 707, 337, 2, 138, 221]
[:mouse_move, 704, 337, 2, 139, 242]
[:mouse_move, 697, 337, 2, 140, 242]
[:mouse_move, 683, 337, 2, 141, 243]
[:mouse_move, 666, 337, 2, 142, 244]
[:mouse_move, 655, 337, 2, 143, 244]
[:mouse_move, 638, 337, 2, 144, 245]
[:mouse_move, 619, 337, 2, 145, 246]
[:mouse_move, 602, 338, 2, 146, 246]
[:mouse_move, 586, 340, 2, 147, 247]
[:mouse_move, 572, 342, 2, 148, 248]
[:mouse_move, 559, 344, 2, 149, 248]
[:mouse_move, 547, 347, 2, 150, 249]
[:mouse_move, 542, 348, 2, 151, 250]
[:mouse_move, 532, 349, 2, 152, 250]
[:mouse_move, 525, 351, 2, 153, 251]
[:mouse_move, 517, 352, 2, 154, 252]
[:mouse_move, 512, 352, 2, 155, 252]
[:mouse_move, 506, 353, 2, 156, 253]
[:mouse_move, 501, 353, 2, 157, 254]
[:mouse_move, 495, 354, 2, 158, 254]
[:mouse_move, 490, 354, 2, 159, 255]
[:mouse_move, 488, 354, 2, 160, 256]
[:mouse_move, 484, 354, 2, 161, 256]
[:mouse_move, 481, 354, 2, 162, 257]
[:mouse_move, 480, 354, 2, 163, 258]
[:mouse_move, 477, 354, 2, 164, 259]
[:mouse_move, 476, 354, 2, 165, 260]
[:mouse_move, 474, 354, 2, 166, 261]
[:mouse_move, 473, 354, 2, 167, 262]
[:mouse_move, 471, 354, 2, 168, 263]
[:mouse_move, 470, 354, 2, 169, 265]
[:mouse_move, 470, 355, 2, 170, 267]
[:mouse_move, 473, 355, 2, 171, 269]
[:mouse_move, 481, 356, 2, 172, 269]
[:mouse_move, 492, 357, 2, 173, 270]
[:mouse_move, 508, 357, 2, 174, 271]
[:mouse_move, 519, 357, 2, 175, 271]
[:mouse_move, 536, 357, 2, 176, 272]
[:mouse_move, 544, 357, 2, 177, 273]
[:mouse_move, 556, 357, 2, 178, 273]
[:mouse_move, 568, 357, 2, 179, 274]
[:mouse_move, 578, 355, 2, 180, 275]
[:mouse_move, 588, 355, 2, 181, 275]
[:mouse_move, 597, 353, 2, 182, 276]
[:mouse_move, 605, 353, 2, 183, 277]
[:mouse_move, 612, 353, 2, 184, 277]
[:mouse_move, 619, 352, 2, 185, 278]
[:mouse_move, 625, 352, 2, 186, 279]
[:mouse_move, 631, 352, 2, 187, 279]
[:mouse_move, 637, 352, 2, 188, 280]
[:mouse_move, 642, 352, 2, 189, 281]
[:mouse_move, 647, 352, 2, 190, 281]
[:mouse_move, 652, 352, 2, 191, 282]
[:mouse_move, 659, 352, 2, 192, 283]
[:mouse_move, 665, 351, 2, 193, 283]
[:mouse_move, 671, 351, 2, 194, 284]
[:mouse_move, 673, 350, 2, 195, 285]
[:mouse_move, 679, 349, 2, 196, 286]
[:mouse_move, 681, 348, 2, 197, 287]
[:mouse_move, 683, 347, 2, 198, 288]
[:mouse_move, 684, 347, 2, 199, 289]
[:mouse_move, 687, 347, 2, 200, 298]
[:mouse_move, 691, 347, 2, 201, 299]
[:mouse_move, 698, 347, 2, 202, 300]
[:mouse_move, 714, 347, 2, 203, 300]
[:mouse_move, 728, 347, 2, 204, 301]
[:mouse_move, 738, 346, 2, 205, 302]
[:mouse_move, 752, 343, 2, 206, 302]
[:mouse_move, 759, 342, 2, 207, 303]
[:mouse_move, 770, 340, 2, 208, 304]
[:mouse_move, 779, 338, 2, 209, 304]
[:mouse_move, 782, 337, 2, 210, 305]
[:mouse_move, 794, 335, 2, 211, 306]
[:mouse_move, 796, 335, 2, 212, 306]
[:mouse_move, 801, 334, 2, 213, 307]
[:mouse_move, 804, 333, 2, 214, 308]
[:mouse_move, 806, 332, 2, 215, 308]
[:mouse_move, 808, 332, 2, 216, 309]
[:mouse_move, 810, 331, 2, 217, 310]
[:mouse_move, 811, 331, 2, 218, 310]
[:mouse_move, 812, 330, 2, 219, 311]
[:mouse_move, 813, 329, 2, 220, 313]
[:mouse_move, 814, 327, 2, 221, 315]
[:mouse_move, 814, 326, 2, 222, 316]
[:mouse_move, 814, 324, 2, 223, 317]
[:mouse_move, 815, 323, 2, 224, 319]
[:mouse_button_pressed, 1, 0, 1, 225, 326]
[:mouse_button_up, 1, 0, 1, 226, 334]
[:mouse_move, 815, 324, 2, 227, 367]
[:mouse_move, 814, 327, 2, 228, 369]
[:mouse_move, 811, 330, 2, 229, 370]
[:mouse_move, 800, 337, 2, 230, 371]
[:mouse_move, 791, 341, 2, 231, 372]
[:mouse_move, 753, 353, 2, 232, 373]
[:mouse_move, 743, 355, 2, 233, 374]
[:mouse_move, 708, 362, 2, 234, 375]
[:mouse_move, 692, 364, 2, 235, 376]
[:mouse_move, 676, 364, 2, 236, 377]
[:mouse_move, 656, 365, 2, 237, 378]
[:mouse_move, 645, 365, 2, 238, 379]
[:mouse_move, 628, 365, 2, 239, 380]
[:mouse_move, 623, 364, 2, 240, 381]
[:mouse_move, 612, 364, 2, 241, 382]
[:mouse_move, 610, 364, 2, 242, 383]
[:mouse_move, 600, 363, 2, 243, 384]
[:mouse_move, 599, 362, 2, 244, 385]
[:mouse_move, 595, 361, 2, 245, 386]
[:mouse_move, 594, 361, 2, 246, 387]
[:mouse_move, 593, 361, 2, 247, 388]
[:mouse_move, 597, 361, 2, 248, 392]
[:mouse_move, 601, 361, 2, 249, 393]
[:mouse_move, 609, 361, 2, 250, 394]
[:mouse_move, 612, 361, 2, 251, 395]
[:mouse_move, 620, 360, 2, 252, 396]
[:mouse_move, 626, 360, 2, 253, 397]
[:mouse_move, 633, 358, 2, 254, 398]
[:mouse_move, 637, 357, 2, 255, 399]
[:mouse_move, 646, 354, 2, 256, 400]
[:mouse_move, 650, 352, 2, 257, 401]
[:mouse_move, 658, 349, 2, 258, 402]
[:mouse_move, 662, 346, 2, 259, 403]
[:mouse_move, 663, 344, 2, 260, 404]
[:mouse_move, 671, 339, 2, 261, 405]
[:mouse_move, 672, 338, 2, 262, 406]
[:mouse_move, 677, 334, 2, 263, 407]
[:mouse_move, 678, 332, 2, 264, 408]
[:mouse_move, 681, 330, 2, 265, 409]
[:mouse_move, 681, 329, 2, 266, 410]
[:mouse_move, 683, 327, 2, 267, 411]
[:mouse_move, 684, 326, 2, 268, 412]
[:mouse_move, 685, 323, 2, 269, 413]
[:mouse_move, 685, 322, 2, 270, 414]
[:mouse_move, 686, 319, 2, 271, 415]
[:mouse_move, 686, 317, 2, 272, 416]
[:mouse_move, 686, 312, 2, 273, 417]
[:mouse_move, 685, 311, 2, 274, 418]
[:mouse_move, 680, 307, 2, 275, 419]
[:mouse_move, 677, 305, 2, 276, 420]
[:mouse_move, 672, 303, 2, 277, 421]
[:mouse_move, 669, 302, 2, 278, 422]
[:mouse_move, 663, 299, 2, 279, 423]
[:mouse_move, 658, 299, 2, 280, 424]
[:mouse_move, 652, 299, 2, 281, 425]
[:mouse_move, 649, 299, 2, 282, 426]
[:mouse_move, 645, 300, 2, 283, 427]
[:mouse_move, 642, 301, 2, 284, 428]
[:mouse_move, 639, 303, 2, 285, 429]
[:mouse_move, 637, 304, 2, 286, 430]
[:mouse_move, 633, 308, 2, 287, 431]
[:mouse_move, 631, 311, 2, 288, 432]
[:mouse_move, 629, 313, 2, 289, 433]
[:mouse_move, 623, 319, 2, 290, 434]
[:mouse_move, 620, 322, 2, 291, 435]
[:mouse_move, 616, 327, 2, 292, 436]
[:mouse_move, 614, 331, 2, 293, 437]
[:mouse_move, 611, 336, 2, 294, 438]
[:mouse_move, 610, 338, 2, 295, 439]
[:mouse_move, 609, 345, 2, 296, 440]
[:mouse_move, 610, 350, 2, 297, 441]
[:mouse_move, 617, 360, 2, 298, 442]
[:mouse_move, 620, 364, 2, 299, 443]
[:mouse_move, 629, 373, 2, 300, 444]
[:mouse_move, 634, 376, 2, 301, 445]
[:mouse_move, 640, 379, 2, 302, 446]
[:mouse_move, 646, 380, 2, 303, 447]
[:mouse_move, 651, 380, 2, 304, 448]
[:mouse_move, 654, 378, 2, 305, 449]
[:mouse_move, 659, 373, 2, 306, 450]
[:mouse_move, 662, 370, 2, 307, 451]
[:mouse_move, 667, 362, 2, 308, 452]
[:mouse_move, 670, 358, 2, 309, 453]
[:mouse_move, 673, 354, 2, 310, 454]
[:mouse_move, 676, 349, 2, 311, 455]
[:mouse_move, 679, 343, 2, 312, 456]
[:mouse_move, 680, 340, 2, 313, 457]
[:mouse_move, 681, 332, 2, 314, 458]
[:mouse_move, 682, 327, 2, 315, 459]
[:mouse_move, 682, 321, 2, 316, 460]
[:mouse_move, 682, 310, 2, 317, 461]
[:mouse_move, 682, 308, 2, 318, 462]
[:mouse_move, 681, 301, 2, 319, 463]
[:mouse_move, 679, 296, 2, 320, 464]
[:mouse_move, 674, 291, 2, 321, 465]
[:mouse_move, 670, 289, 2, 322, 466]
[:mouse_move, 659, 287, 2, 323, 467]
[:mouse_move, 654, 287, 2, 324, 468]
[:mouse_move, 644, 287, 2, 325, 469]
[:mouse_move, 642, 287, 2, 326, 470]
[:mouse_move, 633, 288, 2, 327, 471]
[:mouse_move, 629, 289, 2, 328, 472]
[:mouse_move, 623, 295, 2, 329, 473]
[:mouse_move, 619, 298, 2, 330, 474]
[:mouse_move, 613, 306, 2, 331, 475]
[:mouse_move, 611, 310, 2, 332, 476]
[:mouse_move, 608, 315, 2, 333, 477]
[:mouse_move, 608, 318, 2, 334, 478]
[:mouse_move, 607, 323, 2, 335, 479]
[:mouse_move, 607, 327, 2, 336, 480]
[:mouse_move, 607, 334, 2, 337, 481]
[:mouse_move, 608, 337, 2, 338, 482]
[:mouse_move, 613, 344, 2, 339, 483]
[:mouse_move, 617, 347, 2, 340, 484]
[:mouse_move, 625, 355, 2, 341, 485]
[:mouse_move, 630, 358, 2, 342, 486]
[:mouse_move, 635, 361, 2, 343, 487]
[:mouse_move, 648, 366, 2, 344, 488]
[:mouse_move, 654, 367, 2, 345, 489]
[:mouse_move, 667, 368, 2, 346, 490]
[:mouse_move, 672, 368, 2, 347, 491]
[:mouse_move, 681, 364, 2, 348, 492]
[:mouse_move, 684, 360, 2, 349, 493]
[:mouse_move, 690, 354, 2, 350, 494]
[:mouse_move, 690, 352, 2, 351, 495]
[:mouse_move, 693, 346, 2, 352, 496]
[:mouse_move, 693, 343, 2, 353, 497]
[:mouse_move, 694, 335, 2, 354, 498]
[:mouse_move, 693, 332, 2, 355, 499]
[:mouse_move, 687, 326, 2, 356, 500]
[:mouse_move, 684, 323, 2, 357, 501]
[:mouse_move, 679, 320, 2, 358, 502]
[:mouse_move, 677, 319, 2, 359, 503]
[:mouse_move, 674, 318, 2, 360, 504]
[:mouse_move, 674, 317, 2, 361, 505]
[:mouse_move, 675, 317, 2, 362, 519]
[:mouse_move, 682, 317, 2, 363, 520]
[:mouse_move, 700, 321, 2, 364, 521]
[:mouse_move, 710, 324, 2, 365, 522]
[:mouse_move, 726, 326, 2, 366, 523]
[:mouse_move, 741, 328, 2, 367, 524]
[:mouse_move, 774, 330, 2, 368, 525]
[:mouse_move, 779, 330, 2, 369, 526]
[:mouse_move, 799, 330, 2, 370, 527]
[:mouse_move, 808, 330, 2, 371, 528]
[:mouse_move, 832, 330, 2, 372, 529]
[:mouse_move, 835, 331, 2, 373, 530]
[:mouse_move, 850, 331, 2, 374, 531]
[:mouse_move, 852, 332, 2, 375, 532]
[:mouse_move, 860, 332, 2, 376, 533]
[:mouse_move, 863, 332, 2, 377, 534]
[:mouse_move, 868, 332, 2, 378, 535]
[:mouse_move, 870, 332, 2, 379, 536]
[:mouse_move, 873, 332, 2, 380, 537]
[:mouse_move, 875, 332, 2, 381, 538]
[:mouse_move, 878, 332, 2, 382, 539]
[:mouse_move, 881, 332, 2, 383, 540]
[:mouse_move, 883, 332, 2, 384, 541]
[:mouse_move, 887, 331, 2, 385, 542]
[:mouse_move, 890, 331, 2, 386, 543]
[:mouse_move, 894, 330, 2, 387, 544]
[:mouse_move, 895, 330, 2, 388, 545]
[:mouse_move, 898, 330, 2, 389, 546]
[:mouse_move, 899, 330, 2, 390, 547]
[:mouse_move, 900, 330, 2, 391, 548]
[:mouse_move, 901, 330, 2, 392, 549]
[:mouse_button_pressed, 1, 0, 1, 393, 552]
[:mouse_button_up, 1, 0, 1, 394, 560]
[:mouse_move, 895, 336, 2, 395, 598]
[:mouse_move, 888, 341, 2, 396, 599]
[:mouse_move, 856, 356, 2, 397, 600]
[:mouse_move, 834, 364, 2, 398, 601]
[:mouse_move, 779, 375, 2, 399, 602]
[:mouse_move, 747, 379, 2, 400, 603]
[:mouse_move, 701, 382, 2, 401, 604]
[:mouse_move, 675, 382, 2, 402, 605]
[:mouse_move, 650, 382, 2, 403, 606]
[:mouse_move, 637, 381, 2, 404, 607]
[:mouse_move, 626, 378, 2, 405, 608]
[:mouse_move, 622, 376, 2, 406, 609]
[:mouse_move, 616, 372, 2, 407, 610]
[:mouse_move, 615, 371, 2, 408, 611]
[:mouse_move, 614, 369, 2, 409, 612]
[:mouse_move, 614, 368, 2, 410, 614]
[:mouse_move, 614, 367, 2, 411, 615]
[:mouse_move, 614, 366, 2, 412, 616]
[:mouse_move, 615, 365, 2, 413, 618]
[:mouse_move, 616, 364, 2, 414, 620]
[:mouse_move, 617, 364, 2, 415, 623]
[:mouse_move, 617, 363, 2, 416, 624]
[:mouse_move, 618, 363, 2, 417, 626]
[:mouse_move, 623, 363, 2, 418, 627]
[:mouse_move, 626, 364, 2, 419, 628]
[:mouse_move, 635, 367, 2, 420, 629]
[:mouse_move, 641, 368, 2, 421, 630]
[:mouse_move, 654, 370, 2, 422, 631]
[:mouse_move, 661, 370, 2, 423, 632]
[:mouse_move, 678, 369, 2, 424, 633]
[:mouse_move, 681, 367, 2, 425, 634]
[:mouse_move, 694, 361, 2, 426, 635]
[:mouse_move, 699, 356, 2, 427, 636]
[:mouse_move, 707, 348, 2, 428, 637]
[:mouse_move, 709, 346, 2, 429, 638]
[:mouse_move, 714, 338, 2, 430, 639]
[:mouse_move, 715, 335, 2, 431, 640]
[:mouse_move, 715, 327, 2, 432, 641]
[:mouse_move, 715, 323, 2, 433, 642]
[:mouse_move, 709, 315, 2, 434, 643]
[:mouse_move, 706, 311, 2, 435, 644]
[:mouse_move, 697, 304, 2, 436, 645]
[:mouse_move, 688, 299, 2, 437, 646]
[:mouse_move, 672, 296, 2, 438, 647]
[:mouse_move, 667, 296, 2, 439, 648]
[:mouse_move, 654, 295, 2, 440, 649]
[:mouse_move, 647, 295, 2, 441, 650]
[:mouse_move, 642, 295, 2, 442, 651]
[:mouse_move, 631, 296, 2, 443, 652]
[:mouse_move, 625, 298, 2, 444, 653]
[:mouse_move, 619, 301, 2, 445, 654]
[:mouse_move, 616, 303, 2, 446, 655]
[:mouse_move, 611, 306, 2, 447, 656]
[:mouse_move, 608, 309, 2, 448, 657]
[:mouse_move, 607, 314, 2, 449, 658]
[:mouse_move, 606, 317, 2, 450, 659]
[:mouse_move, 606, 324, 2, 451, 660]
[:mouse_move, 606, 329, 2, 452, 661]
[:mouse_move, 611, 338, 2, 453, 662]
[:mouse_move, 612, 340, 2, 454, 663]
[:mouse_move, 615, 345, 2, 455, 664]
[:mouse_move, 617, 347, 2, 456, 665]
[:mouse_move, 622, 351, 2, 457, 666]
[:mouse_move, 624, 352, 2, 458, 667]
[:mouse_move, 632, 355, 2, 459, 668]
[:mouse_move, 636, 356, 2, 460, 669]
[:mouse_move, 645, 358, 2, 461, 670]
[:mouse_move, 650, 359, 2, 462, 671]
[:mouse_move, 667, 360, 2, 463, 672]
[:mouse_move, 675, 360, 2, 464, 673]
[:mouse_move, 686, 360, 2, 465, 674]
[:mouse_move, 693, 360, 2, 466, 675]
[:mouse_move, 699, 360, 2, 467, 676]
[:mouse_move, 707, 356, 2, 468, 677]
[:mouse_move, 710, 354, 2, 469, 678]
[:mouse_move, 716, 348, 2, 470, 679]
[:mouse_move, 717, 343, 2, 471, 680]
[:mouse_move, 718, 334, 2, 472, 681]
[:mouse_move, 718, 329, 2, 473, 682]
[:mouse_move, 717, 319, 2, 474, 683]
[:mouse_move, 714, 314, 2, 475, 684]
[:mouse_move, 705, 305, 2, 476, 685]
[:mouse_move, 699, 301, 2, 477, 686]
[:mouse_move, 688, 295, 2, 478, 687]
[:mouse_move, 682, 293, 2, 479, 688]
[:mouse_move, 674, 290, 2, 480, 689]
[:mouse_move, 666, 288, 2, 481, 690]
[:mouse_move, 657, 287, 2, 482, 691]
[:mouse_move, 651, 287, 2, 483, 692]
[:mouse_move, 642, 288, 2, 484, 693]
[:mouse_move, 638, 290, 2, 485, 694]
[:mouse_move, 628, 295, 2, 486, 695]
[:mouse_move, 624, 298, 2, 487, 696]
[:mouse_move, 617, 305, 2, 488, 697]
[:mouse_move, 614, 309, 2, 489, 698]
[:mouse_move, 611, 317, 2, 490, 699]
[:mouse_move, 610, 322, 2, 491, 700]
[:mouse_move, 610, 329, 2, 492, 701]
[:mouse_move, 610, 333, 2, 493, 702]
[:mouse_move, 615, 341, 2, 494, 703]
[:mouse_move, 618, 345, 2, 495, 704]
[:mouse_move, 622, 348, 2, 496, 705]
[:mouse_move, 631, 355, 2, 497, 706]
[:mouse_move, 636, 359, 2, 498, 707]
[:mouse_move, 646, 365, 2, 499, 708]
[:mouse_move, 650, 368, 2, 500, 709]
[:mouse_move, 659, 371, 2, 501, 710]
[:mouse_move, 663, 372, 2, 502, 711]
[:mouse_move, 668, 372, 2, 503, 712]
[:mouse_move, 671, 372, 2, 504, 713]
[:mouse_move, 676, 372, 2, 505, 714]
[:mouse_move, 678, 371, 2, 506, 715]
[:mouse_move, 681, 369, 2, 507, 716]
[:mouse_move, 682, 369, 2, 508, 717]
[:mouse_move, 683, 368, 2, 509, 718]
[:key_down_raw, 1073742051, 1024, 2, 510, 794]
[:key_down_raw, 113, 1024, 2, 511, 795]
