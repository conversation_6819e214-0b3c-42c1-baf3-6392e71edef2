class Scene
  def enter(args); end
  def leave(args); end
  def pause(args); end
  def resume(args); end
  def tick(args); end
end

class SceneManager
  def initialize
    @scenes = {}
    @stack = []
    @transition = nil
  end

  def add(name, scene)
    @scenes[name] = scene
  end

  def switch(name, args)
    @stack.last&.leave(args)
    @stack = [@scenes[name]]
    @stack.last&.enter(args)
  end

  def fade_to(name, args, duration: 30)
    @transition = { target: @scenes[name], ticks: duration, counter: 0, old: @stack.last }
  end

  def tick(args)
    if @transition
      t = @transition
      alpha = (t[:counter].to_f / t[:ticks]) * 255
      t[:old]&.tick(args)
      args.outputs.solids << { x: 0, y: 0, w: 1280, h: 720, r: 0, g: 0, b: 0, a: alpha }
      t[:counter] += 1
      if t[:counter] >= t[:ticks]
        @stack = [t[:targs]]
        @stack.last.enter(args)
        @transition = nil
      end
    else
      @stack.last&.tick(args)
    end
  end
end