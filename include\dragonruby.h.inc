#ifndef DRB_FFI_EXPOSE
#error "Define DRB_FFI_EXPOSE before including this file"
#endif

DRB_FFI_EXPOSE(void (*mrb_raise)(mrb_state *mrb, struct RClass *c, const char *msg), mrb_raise)
DRB_FFI_EXPOSE(void (*mrb_raisef)(mrb_state *mrb, struct RClass *c, const char *fmt, ...), mrb_raisef)
DRB_FFI_EXPOSE(struct RClass *(*mrb_module_get)(mrb_state *mrb, const char *name), mrb_module_get)
DRB_FFI_EXPOSE(struct RClass *(*mrb_module_get_under)(mrb_state *mrb, struct RClass *outer, const char *name), mrb_module_get_under)
DRB_FFI_EXPOSE(struct RClass *(*mrb_define_module_under)(mrb_state *mrb, struct RClass *outer, const char *name), mrb_define_module_under)
DRB_FFI_EXPOSE(struct RClass *(*mrb_class_get_under)(mrb_state *mrb, struct RClass *outer, const char *name), mrb_class_get_under)
DRB_FFI_EXPOSE(struct RClass *(*mrb_define_class_under)(mrb_state *mrb, struct RClass *outer, const char *name, struct RClass *super), mrb_define_class_under)
DRB_FFI_EXPOSE(void (*mrb_define_module_function)(mrb_state *mrb, struct RClass *cla, const char *name, mrb_func_t fun, mrb_aspec aspec), mrb_define_module_function)
DRB_FFI_EXPOSE(void (*mrb_define_method)(mrb_state *mrb, struct RClass *cla, const char *name, mrb_func_t func, mrb_aspec aspec), mrb_define_method)
DRB_FFI_EXPOSE(void (*mrb_define_class_method)(mrb_state *mrb, struct RClass *cla, const char *name, mrb_func_t fun, mrb_aspec aspec), mrb_define_class_method)
DRB_FFI_EXPOSE(mrb_int (*mrb_get_args)(mrb_state *mrb, mrb_args_format format, ...), mrb_get_args)
DRB_FFI_EXPOSE(mrb_value (*mrb_str_new_cstr)(mrb_state *, const char *), mrb_str_new_cstr)
DRB_FFI_EXPOSE(struct RData *(*mrb_data_object_alloc)(mrb_state *mrb, struct RClass *klass, void *datap, const mrb_data_type *type), mrb_data_object_alloc)
DRB_FFI_EXPOSE(void (*drb_free_foreign_object)(struct mrb_state *mrb, void *ptr), drb_free_foreign_object)
DRB_FFI_EXPOSE(void (*drb_typecheck_float)(struct mrb_state *mrb, mrb_value self), drb_typecheck_float)
DRB_FFI_EXPOSE(void (*drb_typecheck_int)(struct mrb_state *mrb, mrb_value self), drb_typecheck_int)
DRB_FFI_EXPOSE(void (*drb_typecheck_bool)(struct mrb_state *mrb, mrb_value self), drb_typecheck_bool)
DRB_FFI_EXPOSE(mrb_value (*drb_float_value)(struct mrb_state *mrb, mrb_float f), drb_float_value)
DRB_FFI_EXPOSE(struct RClass *(*drb_getruntime_error)(struct mrb_state *mrb), drb_getruntime_error)
DRB_FFI_EXPOSE(struct RClass *(*drb_getargument_error)(struct mrb_state *mrb), drb_getargument_error)
DRB_FFI_EXPOSE(void (*drb_typecheck_aggregate)(struct mrb_state *mrb, mrb_value, struct RClass *expected, struct mrb_data_type *data_type), drb_typecheck_aggregate)
DRB_FFI_EXPOSE(void (*drb_upload_pixel_array)(const char *name, const int w, const int h, const Uint32 *pixels), drb_upload_pixel_array)
DRB_FFI_EXPOSE(void *(*drb_load_image)(const char *fname, int *w, int *h), drb_load_image)
DRB_FFI_EXPOSE(void (*drb_free_image)(void *pixels), drb_free_image)
DRB_FFI_EXPOSE(void (*drb_log_write)(const char *subsystem, const int level, const char *str), drb_log_write)
DRB_FFI_EXPOSE(mrb_value (*mrb_Float)(mrb_state *mrb, mrb_value val), mrb_Float)
DRB_FFI_EXPOSE(mrb_value (*mrb_Integer)(mrb_state *mrb, mrb_value val), mrb_Integer)
DRB_FFI_EXPOSE(mrb_irep *(*mrb_add_irep)(mrb_state *mrb), mrb_add_irep)
DRB_FFI_EXPOSE(void (*mrb_alias_method)(mrb_state *, struct RClass *c, mrb_sym a, mrb_sym b), mrb_alias_method)
DRB_FFI_EXPOSE(void *(*mrb_alloca)(mrb_state *mrb, size_t), mrb_alloca)
DRB_FFI_EXPOSE(mrb_value (*mrb_any_to_s)(mrb_state *mrb, mrb_value obj), mrb_any_to_s)
DRB_FFI_EXPOSE(void (*mrb_argnum_error)(mrb_state *mrb, mrb_int argc, int min, int max), mrb_argnum_error)
DRB_FFI_EXPOSE(mrb_value (*mrb_ary_clear)(mrb_state *mrb, mrb_value self), mrb_ary_clear)
DRB_FFI_EXPOSE(void (*mrb_ary_concat)(mrb_state *mrb, mrb_value self, mrb_value other), mrb_ary_concat)
DRB_FFI_EXPOSE(mrb_value (*mrb_ary_entry)(mrb_value ary, mrb_int offset), mrb_ary_entry)
DRB_FFI_EXPOSE(mrb_value (*mrb_ary_join)(mrb_state *mrb, mrb_value ary, mrb_value sep), mrb_ary_join)
DRB_FFI_EXPOSE(void (*mrb_ary_modify)(mrb_state *, struct RArray *), mrb_ary_modify)
DRB_FFI_EXPOSE(mrb_value (*mrb_ary_new)(mrb_state *mrb), mrb_ary_new)
DRB_FFI_EXPOSE(mrb_value (*mrb_ary_new_capa)(mrb_state *, mrb_int), mrb_ary_new_capa)
DRB_FFI_EXPOSE(mrb_value (*mrb_ary_new_from_values)(mrb_state *mrb, mrb_int size, const mrb_value *vals), mrb_ary_new_from_values)
DRB_FFI_EXPOSE(mrb_value (*mrb_ary_pop)(mrb_state *mrb, mrb_value ary), mrb_ary_pop)
DRB_FFI_EXPOSE(void (*mrb_ary_push)(mrb_state *mrb, mrb_value array, mrb_value value), mrb_ary_push)
DRB_FFI_EXPOSE(mrb_value (*mrb_ary_ref)(mrb_state *mrb, mrb_value ary, mrb_int n), mrb_ary_ref)
DRB_FFI_EXPOSE(void (*mrb_ary_replace)(mrb_state *mrb, mrb_value self, mrb_value other), mrb_ary_replace)
DRB_FFI_EXPOSE(mrb_value (*mrb_ary_resize)(mrb_state *mrb, mrb_value ary, mrb_int new_len), mrb_ary_resize)
DRB_FFI_EXPOSE(void (*mrb_ary_set)(mrb_state *mrb, mrb_value ary, mrb_int n, mrb_value val), mrb_ary_set)
DRB_FFI_EXPOSE(mrb_value (*mrb_ary_shift)(mrb_state *mrb, mrb_value self), mrb_ary_shift)
DRB_FFI_EXPOSE(mrb_value (*mrb_ary_splat)(mrb_state *mrb, mrb_value value), mrb_ary_splat)
DRB_FFI_EXPOSE(mrb_value (*mrb_ary_splice)(mrb_state *mrb, mrb_value self, mrb_int head, mrb_int len, mrb_value rpl), mrb_ary_splice)
DRB_FFI_EXPOSE(mrb_value (*mrb_ary_unshift)(mrb_state *mrb, mrb_value self, mrb_value item), mrb_ary_unshift)
DRB_FFI_EXPOSE(mrb_value (*mrb_assoc_new)(mrb_state *mrb, mrb_value car, mrb_value cdr), mrb_assoc_new)
DRB_FFI_EXPOSE(mrb_value (*mrb_attr_get)(mrb_state *mrb, mrb_value obj, mrb_sym id), mrb_attr_get)
DRB_FFI_EXPOSE(void (*mrb_bug)(mrb_state *mrb, const char *fmt, ...), mrb_bug)
DRB_FFI_EXPOSE(void *(*mrb_calloc)(mrb_state *, size_t, size_t), mrb_calloc)
DRB_FFI_EXPOSE(mrb_value (*mrb_check_array_type)(mrb_state *mrb, mrb_value self), mrb_check_array_type)
DRB_FFI_EXPOSE(mrb_value (*mrb_check_hash_type)(mrb_state *mrb, mrb_value hash), mrb_check_hash_type)
DRB_FFI_EXPOSE(mrb_value (*mrb_check_intern)(mrb_state *, const char *, size_t), mrb_check_intern)
DRB_FFI_EXPOSE(mrb_value (*mrb_check_intern_cstr)(mrb_state *, const char *), mrb_check_intern_cstr)
DRB_FFI_EXPOSE(mrb_value (*mrb_check_intern_str)(mrb_state *, mrb_value), mrb_check_intern_str)
DRB_FFI_EXPOSE(mrb_value (*mrb_check_string_type)(mrb_state *mrb, mrb_value str), mrb_check_string_type)
DRB_FFI_EXPOSE(void (*mrb_check_type)(mrb_state *mrb, mrb_value x, enum mrb_vtype t), mrb_check_type)
DRB_FFI_EXPOSE(mrb_bool (*mrb_class_defined)(mrb_state *mrb, const char *name), mrb_class_defined)
DRB_FFI_EXPOSE(mrb_bool (*mrb_class_defined_id)(mrb_state *mrb, mrb_sym name), mrb_class_defined_id)
DRB_FFI_EXPOSE(mrb_bool (*mrb_class_defined_under)(mrb_state *mrb, struct RClass *outer, const char *name), mrb_class_defined_under)
DRB_FFI_EXPOSE(mrb_bool (*mrb_class_defined_under_id)(mrb_state *mrb, struct RClass *outer, mrb_sym name), mrb_class_defined_under_id)
DRB_FFI_EXPOSE(struct RClass *(*mrb_class_get)(mrb_state *mrb, const char *name), mrb_class_get)
DRB_FFI_EXPOSE(struct RClass *(*mrb_class_get_id)(mrb_state *mrb, mrb_sym name), mrb_class_get_id)
DRB_FFI_EXPOSE(struct RClass *(*mrb_class_get_under_id)(mrb_state *mrb, struct RClass *outer, mrb_sym name), mrb_class_get_under_id)
DRB_FFI_EXPOSE(const char *(*mrb_class_name)(mrb_state *mrb, struct RClass *klass), mrb_class_name)
DRB_FFI_EXPOSE(struct RClass *(*mrb_class_new)(mrb_state *mrb, struct RClass *super), mrb_class_new)
DRB_FFI_EXPOSE(mrb_value (*mrb_class_path)(mrb_state *mrb, struct RClass *c), mrb_class_path)
DRB_FFI_EXPOSE(struct RClass *(*mrb_class_real)(struct RClass *cl), mrb_class_real)
DRB_FFI_EXPOSE(void (*mrb_close)(mrb_state *mrb), mrb_close)
DRB_FFI_EXPOSE(struct RProc *(*mrb_closure_new_cfunc)(mrb_state *mrb, mrb_func_t func, int nlocals), mrb_closure_new_cfunc)
DRB_FFI_EXPOSE(mrb_int (*mrb_cmp)(mrb_state *mrb, mrb_value obj1, mrb_value obj2), mrb_cmp)
DRB_FFI_EXPOSE(mrb_bool (*mrb_const_defined)(mrb_state *, mrb_value, mrb_sym), mrb_const_defined)
DRB_FFI_EXPOSE(mrb_bool (*mrb_const_defined_at)(mrb_state *mrb, mrb_value mod, mrb_sym id), mrb_const_defined_at)
DRB_FFI_EXPOSE(mrb_value (*mrb_const_get)(mrb_state *, mrb_value, mrb_sym), mrb_const_get)
DRB_FFI_EXPOSE(void (*mrb_const_remove)(mrb_state *, mrb_value, mrb_sym), mrb_const_remove)
DRB_FFI_EXPOSE(void (*mrb_const_set)(mrb_state *, mrb_value, mrb_sym, mrb_value), mrb_const_set)
DRB_FFI_EXPOSE(mrb_value (*mrb_convert_to_integer)(mrb_state *mrb, mrb_value val, mrb_int base), mrb_convert_to_integer)
DRB_FFI_EXPOSE(double (*mrb_cstr_to_dbl)(mrb_state *mrb, const char *s, mrb_bool badcheck), mrb_cstr_to_dbl)
DRB_FFI_EXPOSE(mrb_value (*mrb_cstr_to_inum)(mrb_state *mrb, const char *s, mrb_int base, mrb_bool badcheck), mrb_cstr_to_inum)
DRB_FFI_EXPOSE(mrb_bool (*mrb_cv_defined)(mrb_state *mrb, mrb_value mod, mrb_sym sym), mrb_cv_defined)
DRB_FFI_EXPOSE(mrb_value (*mrb_cv_get)(mrb_state *mrb, mrb_value mod, mrb_sym sym), mrb_cv_get)
DRB_FFI_EXPOSE(void (*mrb_cv_set)(mrb_state *mrb, mrb_value mod, mrb_sym sym, mrb_value v), mrb_cv_set)
DRB_FFI_EXPOSE(void *(*mrb_data_check_get_ptr)(mrb_state *mrb, mrb_value, const mrb_data_type *), mrb_data_check_get_ptr)
DRB_FFI_EXPOSE(void (*mrb_data_check_type)(mrb_state *mrb, mrb_value, const mrb_data_type *), mrb_data_check_type)
DRB_FFI_EXPOSE(void *(*mrb_data_get_ptr)(mrb_state *mrb, mrb_value, const mrb_data_type *), mrb_data_get_ptr)
DRB_FFI_EXPOSE(const char *(*mrb_debug_get_filename)(mrb_state *mrb, const mrb_irep *irep, uint32_t pc), mrb_debug_get_filename)
DRB_FFI_EXPOSE(int32_t (*mrb_debug_get_line)(mrb_state *mrb, const mrb_irep *irep, uint32_t pc), mrb_debug_get_line)
DRB_FFI_EXPOSE(mrb_irep_debug_info *(*mrb_debug_info_alloc)(mrb_state *mrb, mrb_irep *irep), mrb_debug_info_alloc)
DRB_FFI_EXPOSE(mrb_irep_debug_info_file *(*mrb_debug_info_append_file)(mrb_state *mrb, mrb_irep_debug_info *info, const char *filename, uint16_t *lines, uint32_t start_pos, uint32_t end_pos), mrb_debug_info_append_file)
DRB_FFI_EXPOSE(void (*mrb_debug_info_free)(mrb_state *mrb, mrb_irep_debug_info *d), mrb_debug_info_free)
DRB_FFI_EXPOSE(void *(*mrb_default_allocf)(mrb_state *, void *, size_t, void *), mrb_default_allocf)
DRB_FFI_EXPOSE(void (*mrb_define_alias)(mrb_state *mrb, struct RClass *c, const char *a, const char *b), mrb_define_alias)
DRB_FFI_EXPOSE(void (*mrb_define_alias_id)(mrb_state *mrb, struct RClass *c, mrb_sym a, mrb_sym b), mrb_define_alias_id)
DRB_FFI_EXPOSE(struct RClass *(*mrb_define_class)(mrb_state *mrb, const char *name, struct RClass *super), mrb_define_class)
DRB_FFI_EXPOSE(struct RClass *(*mrb_define_class_id)(mrb_state *mrb, mrb_sym name, struct RClass *super), mrb_define_class_id)
DRB_FFI_EXPOSE(void (*mrb_define_class_method_id)(mrb_state *mrb, struct RClass *cla, mrb_sym name, mrb_func_t fun, mrb_aspec aspec), mrb_define_class_method_id)
DRB_FFI_EXPOSE(struct RClass *(*mrb_define_class_under_id)(mrb_state *mrb, struct RClass *outer, mrb_sym name, struct RClass *super), mrb_define_class_under_id)
DRB_FFI_EXPOSE(void (*mrb_define_const)(mrb_state *mrb, struct RClass *cla, const char *name, mrb_value val), mrb_define_const)
DRB_FFI_EXPOSE(void (*mrb_define_const_id)(mrb_state *mrb, struct RClass *cla, mrb_sym name, mrb_value val), mrb_define_const_id)
DRB_FFI_EXPOSE(void (*mrb_define_global_const)(mrb_state *mrb, const char *name, mrb_value val), mrb_define_global_const)
DRB_FFI_EXPOSE(void (*mrb_define_method_id)(mrb_state *mrb, struct RClass *c, mrb_sym mid, mrb_func_t func, mrb_aspec aspec), mrb_define_method_id)
DRB_FFI_EXPOSE(void (*mrb_define_method_raw)(mrb_state *, struct RClass *, mrb_sym, mrb_method_t), mrb_define_method_raw)
DRB_FFI_EXPOSE(struct RClass *(*mrb_define_module)(mrb_state *mrb, const char *name), mrb_define_module)
DRB_FFI_EXPOSE(void (*mrb_define_module_function_id)(mrb_state *mrb, struct RClass *cla, mrb_sym name, mrb_func_t fun, mrb_aspec aspec), mrb_define_module_function_id)
DRB_FFI_EXPOSE(struct RClass *(*mrb_define_module_id)(mrb_state *mrb, mrb_sym name), mrb_define_module_id)
DRB_FFI_EXPOSE(struct RClass *(*mrb_define_module_under_id)(mrb_state *mrb, struct RClass *outer, mrb_sym name), mrb_define_module_under_id)
DRB_FFI_EXPOSE(void (*mrb_define_singleton_method)(mrb_state *mrb, struct RObject *cla, const char *name, mrb_func_t fun, mrb_aspec aspec), mrb_define_singleton_method)
DRB_FFI_EXPOSE(void (*mrb_define_singleton_method_id)(mrb_state *mrb, struct RObject *cla, mrb_sym name, mrb_func_t fun, mrb_aspec aspec), mrb_define_singleton_method_id)
DRB_FFI_EXPOSE(mrb_value (*mrb_ensure)(mrb_state *mrb, mrb_func_t body, mrb_value b_data, mrb_func_t ensure, mrb_value e_data), mrb_ensure)
DRB_FFI_EXPOSE(mrb_value (*mrb_ensure_array_type)(mrb_state *mrb, mrb_value self), mrb_ensure_array_type)
DRB_FFI_EXPOSE(mrb_value (*mrb_ensure_hash_type)(mrb_state *mrb, mrb_value hash), mrb_ensure_hash_type)
DRB_FFI_EXPOSE(mrb_value (*mrb_ensure_string_type)(mrb_state *mrb, mrb_value str), mrb_ensure_string_type)
DRB_FFI_EXPOSE(mrb_bool (*mrb_eql)(mrb_state *mrb, mrb_value obj1, mrb_value obj2), mrb_eql)
DRB_FFI_EXPOSE(mrb_bool (*mrb_equal)(mrb_state *mrb, mrb_value obj1, mrb_value obj2), mrb_equal)
DRB_FFI_EXPOSE(mrb_value (*mrb_exc_backtrace)(mrb_state *mrb, mrb_value exc), mrb_exc_backtrace)
DRB_FFI_EXPOSE(struct RClass *(*mrb_exc_get_id)(mrb_state *mrb, mrb_sym name), mrb_exc_get_id)
DRB_FFI_EXPOSE(mrb_value (*mrb_exc_new)(mrb_state *mrb, struct RClass *c, const char *ptr, size_t len), mrb_exc_new)
DRB_FFI_EXPOSE(mrb_value (*mrb_exc_new_str)(mrb_state *mrb, struct RClass *c, mrb_value str), mrb_exc_new_str)
DRB_FFI_EXPOSE(void (*mrb_exc_raise)(mrb_state *mrb, mrb_value exc), mrb_exc_raise)
DRB_FFI_EXPOSE(mrb_value (*mrb_f_raise)(mrb_state *, mrb_value), mrb_f_raise)
DRB_FFI_EXPOSE(mrb_value (*mrb_fiber_alive_p)(mrb_state *mrb, mrb_value fib), mrb_fiber_alive_p)
DRB_FFI_EXPOSE(mrb_value (*mrb_fiber_resume)(mrb_state *mrb, mrb_value fib, mrb_int argc, const mrb_value *argv), mrb_fiber_resume)
DRB_FFI_EXPOSE(mrb_value (*mrb_fiber_yield)(mrb_state *mrb, mrb_int argc, const mrb_value *argv), mrb_fiber_yield)
DRB_FFI_EXPOSE(void (*mrb_field_write_barrier)(mrb_state *, struct RBasic *, struct RBasic *), mrb_field_write_barrier)
DRB_FFI_EXPOSE(mrb_value (*mrb_fixnum_to_str)(mrb_state *mrb, mrb_value x, mrb_int base), mrb_fixnum_to_str)
DRB_FFI_EXPOSE(mrb_value (*mrb_flo_to_fixnum)(mrb_state *mrb, mrb_value val), mrb_flo_to_fixnum)
DRB_FFI_EXPOSE(double (*mrb_float_read)(const char *, char **), mrb_float_read)
DRB_FFI_EXPOSE(int (*mrb_float_to_cstr)(mrb_state *mrb, char *buf, size_t len, const char *fmt, mrb_float f), mrb_float_to_cstr)
DRB_FFI_EXPOSE(mrb_value (*mrb_float_to_str)(mrb_state *mrb, mrb_value x, const char *fmt), mrb_float_to_str)
DRB_FFI_EXPOSE(mrb_value (*mrb_format)(mrb_state *mrb, const char *format, ...), mrb_format)
DRB_FFI_EXPOSE(void (*mrb_free)(mrb_state *, void *), mrb_free)
DRB_FFI_EXPOSE(void (*mrb_free_context)(struct mrb_state *mrb, struct mrb_context *c), mrb_free_context)
DRB_FFI_EXPOSE(void (*mrb_frozen_error)(mrb_state *mrb, void *frozen_obj), mrb_frozen_error)
DRB_FFI_EXPOSE(void (*mrb_full_gc)(mrb_state *), mrb_full_gc)
DRB_FFI_EXPOSE(mrb_bool (*mrb_func_basic_p)(mrb_state *mrb, mrb_value obj, mrb_sym mid, mrb_func_t func), mrb_func_basic_p)
DRB_FFI_EXPOSE(mrb_value (*mrb_funcall)(mrb_state *mrb, mrb_value val, const char *name, mrb_int argc, ...), mrb_funcall)
DRB_FFI_EXPOSE(mrb_value (*mrb_funcall_argv)(mrb_state *mrb, mrb_value val, mrb_sym name, mrb_int argc, const mrb_value *argv), mrb_funcall_argv)
DRB_FFI_EXPOSE(mrb_value (*mrb_funcall_id)(mrb_state *mrb, mrb_value val, mrb_sym mid, mrb_int argc, ...), mrb_funcall_id)
DRB_FFI_EXPOSE(mrb_value (*mrb_funcall_with_block)(mrb_state *mrb, mrb_value val, mrb_sym name, mrb_int argc, const mrb_value *argv, mrb_value block), mrb_funcall_with_block)
DRB_FFI_EXPOSE(void (*mrb_garbage_collect)(mrb_state *), mrb_garbage_collect)
DRB_FFI_EXPOSE(void (*mrb_gc_mark)(mrb_state *, struct RBasic *), mrb_gc_mark)
DRB_FFI_EXPOSE(void (*mrb_gc_protect)(mrb_state *mrb, mrb_value obj), mrb_gc_protect)
DRB_FFI_EXPOSE(void (*mrb_gc_register)(mrb_state *mrb, mrb_value obj), mrb_gc_register)
DRB_FFI_EXPOSE(void (*mrb_gc_unregister)(mrb_state *mrb, mrb_value obj), mrb_gc_unregister)
DRB_FFI_EXPOSE(struct RProc *(*mrb_generate_code)(mrb_state *, struct mrb_parser_state *), mrb_generate_code)
DRB_FFI_EXPOSE(mrb_value (*mrb_get_arg1)(mrb_state *mrb), mrb_get_arg1)
DRB_FFI_EXPOSE(mrb_int (*mrb_get_argc)(mrb_state *mrb), mrb_get_argc)
DRB_FFI_EXPOSE(const mrb_value *(*mrb_get_argv)(mrb_state *mrb), mrb_get_argv)
DRB_FFI_EXPOSE(mrb_value (*mrb_get_backtrace)(mrb_state *mrb), mrb_get_backtrace)
DRB_FFI_EXPOSE(mrb_value (*mrb_gv_get)(mrb_state *mrb, mrb_sym sym), mrb_gv_get)
DRB_FFI_EXPOSE(void (*mrb_gv_remove)(mrb_state *mrb, mrb_sym sym), mrb_gv_remove)
DRB_FFI_EXPOSE(void (*mrb_gv_set)(mrb_state *mrb, mrb_sym sym, mrb_value val), mrb_gv_set)
DRB_FFI_EXPOSE(mrb_value (*mrb_hash_clear)(mrb_state *mrb, mrb_value hash), mrb_hash_clear)
DRB_FFI_EXPOSE(mrb_value (*mrb_hash_delete_key)(mrb_state *mrb, mrb_value hash, mrb_value key), mrb_hash_delete_key)
DRB_FFI_EXPOSE(mrb_value (*mrb_hash_dup)(mrb_state *mrb, mrb_value hash), mrb_hash_dup)
DRB_FFI_EXPOSE(mrb_bool (*mrb_hash_empty_p)(mrb_state *mrb, mrb_value self), mrb_hash_empty_p)
DRB_FFI_EXPOSE(mrb_value (*mrb_hash_fetch)(mrb_state *mrb, mrb_value hash, mrb_value key, mrb_value def), mrb_hash_fetch)
DRB_FFI_EXPOSE(void (*mrb_hash_foreach)(mrb_state *mrb, struct RHash *hash, mrb_hash_foreach_func *func, void *p), mrb_hash_foreach)
DRB_FFI_EXPOSE(mrb_value (*mrb_hash_get)(mrb_state *mrb, mrb_value hash, mrb_value key), mrb_hash_get)
DRB_FFI_EXPOSE(mrb_bool (*mrb_hash_key_p)(mrb_state *mrb, mrb_value hash, mrb_value key), mrb_hash_key_p)
DRB_FFI_EXPOSE(mrb_value (*mrb_hash_keys)(mrb_state *mrb, mrb_value hash), mrb_hash_keys)
DRB_FFI_EXPOSE(void (*mrb_hash_merge)(mrb_state *mrb, mrb_value hash1, mrb_value hash2), mrb_hash_merge)
DRB_FFI_EXPOSE(mrb_value (*mrb_hash_new)(mrb_state *mrb), mrb_hash_new)
DRB_FFI_EXPOSE(mrb_value (*mrb_hash_new_capa)(mrb_state *mrb, mrb_int capa), mrb_hash_new_capa)
DRB_FFI_EXPOSE(void (*mrb_hash_set)(mrb_state *mrb, mrb_value hash, mrb_value key, mrb_value val), mrb_hash_set)
DRB_FFI_EXPOSE(mrb_int (*mrb_hash_size)(mrb_state *mrb, mrb_value hash), mrb_hash_size)
DRB_FFI_EXPOSE(mrb_value (*mrb_hash_values)(mrb_state *mrb, mrb_value hash), mrb_hash_values)
DRB_FFI_EXPOSE(void (*mrb_include_module)(mrb_state *mrb, struct RClass *cla, struct RClass *included), mrb_include_module)
DRB_FFI_EXPOSE(void (*mrb_incremental_gc)(mrb_state *), mrb_incremental_gc)
DRB_FFI_EXPOSE(mrb_value (*mrb_inspect)(mrb_state *mrb, mrb_value obj), mrb_inspect)
DRB_FFI_EXPOSE(mrb_value (*mrb_instance_new)(mrb_state *mrb, mrb_value cv), mrb_instance_new)
DRB_FFI_EXPOSE(mrb_sym (*mrb_intern)(mrb_state *, const char *, size_t), mrb_intern)
DRB_FFI_EXPOSE(mrb_sym (*mrb_intern_check)(mrb_state *, const char *, size_t), mrb_intern_check)
DRB_FFI_EXPOSE(mrb_sym (*mrb_intern_check_cstr)(mrb_state *, const char *), mrb_intern_check_cstr)
DRB_FFI_EXPOSE(mrb_sym (*mrb_intern_check_str)(mrb_state *, mrb_value), mrb_intern_check_str)
DRB_FFI_EXPOSE(mrb_sym (*mrb_intern_cstr)(mrb_state *mrb, const char *str), mrb_intern_cstr)
DRB_FFI_EXPOSE(mrb_sym (*mrb_intern_static)(mrb_state *, const char *, size_t), mrb_intern_static)
DRB_FFI_EXPOSE(mrb_sym (*mrb_intern_str)(mrb_state *, mrb_value), mrb_intern_str)
DRB_FFI_EXPOSE(void (*mrb_iv_copy)(mrb_state *mrb, mrb_value dst, mrb_value src), mrb_iv_copy)
DRB_FFI_EXPOSE(mrb_bool (*mrb_iv_defined)(mrb_state *, mrb_value, mrb_sym), mrb_iv_defined)
DRB_FFI_EXPOSE(void (*mrb_iv_foreach)(mrb_state *mrb, mrb_value obj, mrb_iv_foreach_func *func, void *p), mrb_iv_foreach)
DRB_FFI_EXPOSE(mrb_value (*mrb_iv_get)(mrb_state *mrb, mrb_value obj, mrb_sym sym), mrb_iv_get)
DRB_FFI_EXPOSE(void (*mrb_iv_name_sym_check)(mrb_state *mrb, mrb_sym sym), mrb_iv_name_sym_check)
DRB_FFI_EXPOSE(mrb_bool (*mrb_iv_name_sym_p)(mrb_state *mrb, mrb_sym sym), mrb_iv_name_sym_p)
DRB_FFI_EXPOSE(mrb_value (*mrb_iv_remove)(mrb_state *mrb, mrb_value obj, mrb_sym sym), mrb_iv_remove)
DRB_FFI_EXPOSE(void (*mrb_iv_set)(mrb_state *mrb, mrb_value obj, mrb_sym sym, mrb_value v), mrb_iv_set)
DRB_FFI_EXPOSE(mrb_value (*mrb_load_detect_file_cxt)(mrb_state *mrb, FILE *fp, mrbc_context *c), mrb_load_detect_file_cxt)
DRB_FFI_EXPOSE(mrb_value (*mrb_load_exec)(mrb_state *mrb, struct mrb_parser_state *p, mrbc_context *c), mrb_load_exec)
DRB_FFI_EXPOSE(mrb_value (*mrb_load_file)(mrb_state *, FILE *), mrb_load_file)
DRB_FFI_EXPOSE(mrb_value (*mrb_load_file_cxt)(mrb_state *, FILE *, mrbc_context *cxt), mrb_load_file_cxt)
DRB_FFI_EXPOSE(mrb_value (*mrb_load_irep)(mrb_state *, const uint8_t *), mrb_load_irep)
DRB_FFI_EXPOSE(mrb_value (*mrb_load_irep_buf)(mrb_state *, const void *, size_t), mrb_load_irep_buf)
DRB_FFI_EXPOSE(mrb_value (*mrb_load_irep_buf_cxt)(mrb_state *, const void *, size_t, mrbc_context *), mrb_load_irep_buf_cxt)
DRB_FFI_EXPOSE(mrb_value (*mrb_load_irep_cxt)(mrb_state *, const uint8_t *, mrbc_context *), mrb_load_irep_cxt)
DRB_FFI_EXPOSE(mrb_value (*mrb_load_irep_file)(mrb_state *, FILE *), mrb_load_irep_file)
DRB_FFI_EXPOSE(mrb_value (*mrb_load_irep_file_cxt)(mrb_state *, FILE *, mrbc_context *), mrb_load_irep_file_cxt)
DRB_FFI_EXPOSE(mrb_value (*mrb_load_nstring)(mrb_state *mrb, const char *s, size_t len), mrb_load_nstring)
DRB_FFI_EXPOSE(mrb_value (*mrb_load_nstring_cxt)(mrb_state *mrb, const char *s, size_t len, mrbc_context *cxt), mrb_load_nstring_cxt)
DRB_FFI_EXPOSE(mrb_value (*mrb_load_proc)(mrb_state *mrb, const struct RProc *proc), mrb_load_proc)
DRB_FFI_EXPOSE(mrb_value (*mrb_load_string)(mrb_state *mrb, const char *s), mrb_load_string)
DRB_FFI_EXPOSE(mrb_value (*mrb_load_string_cxt)(mrb_state *mrb, const char *s, mrbc_context *cxt), mrb_load_string_cxt)
DRB_FFI_EXPOSE(mrb_value (*mrb_make_exception)(mrb_state *mrb, mrb_int argc, const mrb_value *argv), mrb_make_exception)
DRB_FFI_EXPOSE(void *(*mrb_malloc)(mrb_state *, size_t), mrb_malloc)
DRB_FFI_EXPOSE(void *(*mrb_malloc_simple)(mrb_state *, size_t), mrb_malloc_simple)
DRB_FFI_EXPOSE(mrb_method_t (*mrb_method_search)(mrb_state *, struct RClass *, mrb_sym), mrb_method_search)
DRB_FFI_EXPOSE(mrb_method_t (*mrb_method_search_vm)(mrb_state *, struct RClass **, mrb_sym), mrb_method_search_vm)
DRB_FFI_EXPOSE(void (*mrb_mod_cv_set)(mrb_state *mrb, struct RClass *c, mrb_sym sym, mrb_value v), mrb_mod_cv_set)
DRB_FFI_EXPOSE(struct RClass *(*mrb_module_get_id)(mrb_state *mrb, mrb_sym name), mrb_module_get_id)
DRB_FFI_EXPOSE(struct RClass *(*mrb_module_get_under_id)(mrb_state *mrb, struct RClass *outer, mrb_sym name), mrb_module_get_under_id)
DRB_FFI_EXPOSE(struct RClass *(*mrb_module_new)(mrb_state *mrb), mrb_module_new)
DRB_FFI_EXPOSE(void (*mrb_mt_foreach)(mrb_state *, struct RClass *, mrb_mt_foreach_func *, void *), mrb_mt_foreach)
DRB_FFI_EXPOSE(void (*mrb_name_error)(mrb_state *mrb, mrb_sym id, const char *fmt, ...), mrb_name_error)
DRB_FFI_EXPOSE(void (*mrb_no_method_error)(mrb_state *mrb, mrb_sym id, mrb_value args, const char *fmt, ...), mrb_no_method_error)
DRB_FFI_EXPOSE(void (*mrb_notimplement)(mrb_state *), mrb_notimplement)
DRB_FFI_EXPOSE(mrb_value (*mrb_notimplement_m)(mrb_state *, mrb_value), mrb_notimplement_m)
DRB_FFI_EXPOSE(mrb_value (*mrb_num_minus)(mrb_state *mrb, mrb_value x, mrb_value y), mrb_num_minus)
DRB_FFI_EXPOSE(mrb_value (*mrb_num_mul)(mrb_state *mrb, mrb_value x, mrb_value y), mrb_num_mul)
DRB_FFI_EXPOSE(mrb_value (*mrb_num_plus)(mrb_state *mrb, mrb_value x, mrb_value y), mrb_num_plus)
DRB_FFI_EXPOSE(struct RBasic *(*mrb_obj_alloc)(mrb_state *, enum mrb_vtype, struct RClass *), mrb_obj_alloc)
DRB_FFI_EXPOSE(mrb_value (*mrb_obj_as_string)(mrb_state *mrb, mrb_value obj), mrb_obj_as_string)
DRB_FFI_EXPOSE(struct RClass *(*mrb_obj_class)(mrb_state *mrb, mrb_value obj), mrb_obj_class)
DRB_FFI_EXPOSE(const char *(*mrb_obj_classname)(mrb_state *mrb, mrb_value obj), mrb_obj_classname)
DRB_FFI_EXPOSE(mrb_value (*mrb_obj_clone)(mrb_state *mrb, mrb_value self), mrb_obj_clone)
DRB_FFI_EXPOSE(mrb_value (*mrb_obj_dup)(mrb_state *mrb, mrb_value obj), mrb_obj_dup)
DRB_FFI_EXPOSE(mrb_bool (*mrb_obj_eq)(mrb_state *mrb, mrb_value a, mrb_value b), mrb_obj_eq)
DRB_FFI_EXPOSE(mrb_bool (*mrb_obj_equal)(mrb_state *mrb, mrb_value a, mrb_value b), mrb_obj_equal)
DRB_FFI_EXPOSE(mrb_value (*mrb_obj_freeze)(mrb_state *, mrb_value), mrb_obj_freeze)
DRB_FFI_EXPOSE(mrb_int (*mrb_obj_id)(mrb_value obj), mrb_obj_id)
DRB_FFI_EXPOSE(mrb_value (*mrb_obj_inspect)(mrb_state *mrb, mrb_value self), mrb_obj_inspect)
DRB_FFI_EXPOSE(mrb_bool (*mrb_obj_is_instance_of)(mrb_state *mrb, mrb_value obj, struct RClass *c), mrb_obj_is_instance_of)
DRB_FFI_EXPOSE(mrb_bool (*mrb_obj_is_kind_of)(mrb_state *mrb, mrb_value obj, struct RClass *c), mrb_obj_is_kind_of)
DRB_FFI_EXPOSE(mrb_bool (*mrb_obj_iv_defined)(mrb_state *mrb, struct RObject *obj, mrb_sym sym), mrb_obj_iv_defined)
DRB_FFI_EXPOSE(mrb_value (*mrb_obj_iv_get)(mrb_state *mrb, struct RObject *obj, mrb_sym sym), mrb_obj_iv_get)
DRB_FFI_EXPOSE(void (*mrb_obj_iv_set)(mrb_state *mrb, struct RObject *obj, mrb_sym sym, mrb_value v), mrb_obj_iv_set)
DRB_FFI_EXPOSE(mrb_value (*mrb_obj_new)(mrb_state *mrb, struct RClass *c, mrb_int argc, const mrb_value *argv), mrb_obj_new)
DRB_FFI_EXPOSE(mrb_bool (*mrb_obj_respond_to)(mrb_state *mrb, struct RClass *c, mrb_sym mid), mrb_obj_respond_to)
DRB_FFI_EXPOSE(mrb_sym (*mrb_obj_to_sym)(mrb_state *mrb, mrb_value name), mrb_obj_to_sym)
DRB_FFI_EXPOSE(mrb_bool (*mrb_object_dead_p)(struct mrb_state *mrb, struct RBasic *object), mrb_object_dead_p)
DRB_FFI_EXPOSE(mrb_state *(*mrb_open)(), mrb_open)
DRB_FFI_EXPOSE(mrb_state *(*mrb_open_allocf)(mrb_allocf f, void *ud), mrb_open_allocf)
DRB_FFI_EXPOSE(mrb_state *(*mrb_open_core)(mrb_allocf f, void *ud), mrb_open_core)
DRB_FFI_EXPOSE(void (*mrb_p)(mrb_state *, mrb_value), mrb_p)
DRB_FFI_EXPOSE(struct mrb_parser_state *(*mrb_parse_file)(mrb_state *, FILE *, mrbc_context *), mrb_parse_file)
DRB_FFI_EXPOSE(struct mrb_parser_state *(*mrb_parse_nstring)(mrb_state *, const char *, size_t, mrbc_context *), mrb_parse_nstring)
DRB_FFI_EXPOSE(struct mrb_parser_state *(*mrb_parse_string)(mrb_state *, const char *, mrbc_context *), mrb_parse_string)
DRB_FFI_EXPOSE(void (*mrb_parser_free)(struct mrb_parser_state *), mrb_parser_free)
DRB_FFI_EXPOSE(mrb_sym (*mrb_parser_get_filename)(struct mrb_parser_state *, uint16_t idx), mrb_parser_get_filename)
DRB_FFI_EXPOSE(struct mrb_parser_state *(*mrb_parser_new)(mrb_state *), mrb_parser_new)
DRB_FFI_EXPOSE(void (*mrb_parser_parse)(struct mrb_parser_state *, mrbc_context *), mrb_parser_parse)
DRB_FFI_EXPOSE(void (*mrb_parser_set_filename)(struct mrb_parser_state *, const char *), mrb_parser_set_filename)
DRB_FFI_EXPOSE(void *(*mrb_pool_alloc)(struct mrb_pool *, size_t), mrb_pool_alloc)
DRB_FFI_EXPOSE(mrb_bool (*mrb_pool_can_realloc)(struct mrb_pool *, void *, size_t), mrb_pool_can_realloc)
DRB_FFI_EXPOSE(void (*mrb_pool_close)(struct mrb_pool *), mrb_pool_close)
DRB_FFI_EXPOSE(struct mrb_pool *(*mrb_pool_open)(mrb_state *), mrb_pool_open)
DRB_FFI_EXPOSE(void *(*mrb_pool_realloc)(struct mrb_pool *, void *, size_t oldlen, size_t newlen), mrb_pool_realloc)
DRB_FFI_EXPOSE(void (*mrb_prepend_module)(mrb_state *mrb, struct RClass *cla, struct RClass *prepended), mrb_prepend_module)
DRB_FFI_EXPOSE(void (*mrb_print_backtrace)(mrb_state *mrb), mrb_print_backtrace)
DRB_FFI_EXPOSE(void (*mrb_print_error)(mrb_state *mrb), mrb_print_error)
DRB_FFI_EXPOSE(mrb_value (*mrb_proc_cfunc_env_get)(mrb_state *mrb, mrb_int idx), mrb_proc_cfunc_env_get)
DRB_FFI_EXPOSE(struct RProc *(*mrb_proc_new_cfunc)(mrb_state *, mrb_func_t), mrb_proc_new_cfunc)
DRB_FFI_EXPOSE(struct RProc *(*mrb_proc_new_cfunc_with_env)(mrb_state *mrb, mrb_func_t func, mrb_int argc, const mrb_value *argv), mrb_proc_new_cfunc_with_env)
DRB_FFI_EXPOSE(mrb_value (*mrb_protect)(mrb_state *mrb, mrb_func_t body, mrb_value data, mrb_bool *state), mrb_protect)
DRB_FFI_EXPOSE(mrb_value (*mrb_ptr_to_str)(mrb_state *mrb, void *p), mrb_ptr_to_str)
DRB_FFI_EXPOSE(enum mrb_range_beg_len (*mrb_range_beg_len)(mrb_state *mrb, mrb_value range, mrb_int *begp, mrb_int *lenp, mrb_int len, mrb_bool trunc), mrb_range_beg_len)
DRB_FFI_EXPOSE(mrb_value (*mrb_range_new)(mrb_state *mrb, mrb_value start, mrb_value end, mrb_bool exclude), mrb_range_new)
DRB_FFI_EXPOSE(struct RRange *(*mrb_range_ptr)(mrb_state *mrb, mrb_value range), mrb_range_ptr)
DRB_FFI_EXPOSE(mrb_irep *(*mrb_read_irep)(mrb_state *, const uint8_t *), mrb_read_irep)
DRB_FFI_EXPOSE(mrb_irep *(*mrb_read_irep_buf)(mrb_state *, const void *, size_t), mrb_read_irep_buf)
DRB_FFI_EXPOSE(void *(*mrb_realloc)(mrb_state *, void *, size_t), mrb_realloc)
DRB_FFI_EXPOSE(void *(*mrb_realloc_simple)(mrb_state *, void *, size_t), mrb_realloc_simple)
DRB_FFI_EXPOSE(void (*mrb_remove_method)(mrb_state *mrb, struct RClass *c, mrb_sym sym), mrb_remove_method)
DRB_FFI_EXPOSE(mrb_value (*mrb_rescue)(mrb_state *mrb, mrb_func_t body, mrb_value b_data, mrb_func_t rescue, mrb_value r_data), mrb_rescue)
DRB_FFI_EXPOSE(mrb_value (*mrb_rescue_exceptions)(mrb_state *mrb, mrb_func_t body, mrb_value b_data, mrb_func_t rescue, mrb_value r_data, mrb_int len, struct RClass **classes), mrb_rescue_exceptions)
DRB_FFI_EXPOSE(mrb_bool (*mrb_respond_to)(mrb_state *mrb, mrb_value obj, mrb_sym mid), mrb_respond_to)
DRB_FFI_EXPOSE(void (*mrb_show_copyright)(mrb_state *mrb), mrb_show_copyright)
DRB_FFI_EXPOSE(void (*mrb_show_version)(mrb_state *mrb), mrb_show_version)
DRB_FFI_EXPOSE(mrb_value (*mrb_singleton_class)(mrb_state *mrb, mrb_value val), mrb_singleton_class)
DRB_FFI_EXPOSE(struct RClass *(*mrb_singleton_class_ptr)(mrb_state *mrb, mrb_value val), mrb_singleton_class_ptr)
DRB_FFI_EXPOSE(void (*mrb_stack_extend)(mrb_state *, mrb_int), mrb_stack_extend)
DRB_FFI_EXPOSE(void (*mrb_state_atexit)(mrb_state *mrb, mrb_atexit_func func), mrb_state_atexit)
DRB_FFI_EXPOSE(mrb_value (*mrb_str_append)(mrb_state *mrb, mrb_value str, mrb_value str2), mrb_str_append)
DRB_FFI_EXPOSE(mrb_value (*mrb_str_cat)(mrb_state *mrb, mrb_value str, const char *ptr, size_t len), mrb_str_cat)
DRB_FFI_EXPOSE(mrb_value (*mrb_str_cat_cstr)(mrb_state *mrb, mrb_value str, const char *ptr), mrb_str_cat_cstr)
DRB_FFI_EXPOSE(mrb_value (*mrb_str_cat_str)(mrb_state *mrb, mrb_value str, mrb_value str2), mrb_str_cat_str)
DRB_FFI_EXPOSE(int (*mrb_str_cmp)(mrb_state *mrb, mrb_value str1, mrb_value str2), mrb_str_cmp)
DRB_FFI_EXPOSE(void (*mrb_str_concat)(mrb_state *mrb, mrb_value self, mrb_value other), mrb_str_concat)
DRB_FFI_EXPOSE(mrb_value (*mrb_str_dup)(mrb_state *mrb, mrb_value str), mrb_str_dup)
DRB_FFI_EXPOSE(mrb_bool (*mrb_str_equal)(mrb_state *mrb, mrb_value str1, mrb_value str2), mrb_str_equal)
DRB_FFI_EXPOSE(mrb_int (*mrb_str_index)(mrb_state *mrb, mrb_value str, const char *p, mrb_int len, mrb_int offset), mrb_str_index)
DRB_FFI_EXPOSE(mrb_value (*mrb_str_intern)(mrb_state *mrb, mrb_value self), mrb_str_intern)
DRB_FFI_EXPOSE(void (*mrb_str_modify)(mrb_state *mrb, struct RString *s), mrb_str_modify)
DRB_FFI_EXPOSE(void (*mrb_str_modify_keep_ascii)(mrb_state *mrb, struct RString *s), mrb_str_modify_keep_ascii)
DRB_FFI_EXPOSE(mrb_value (*mrb_str_new)(mrb_state *mrb, const char *p, size_t len), mrb_str_new)
DRB_FFI_EXPOSE(mrb_value (*mrb_str_new_capa)(mrb_state *mrb, size_t capa), mrb_str_new_capa)
DRB_FFI_EXPOSE(mrb_value (*mrb_str_new_static)(mrb_state *mrb, const char *p, size_t len), mrb_str_new_static)
DRB_FFI_EXPOSE(mrb_value (*mrb_str_plus)(mrb_state *mrb, mrb_value a, mrb_value b), mrb_str_plus)
DRB_FFI_EXPOSE(mrb_value (*mrb_str_resize)(mrb_state *mrb, mrb_value str, mrb_int len), mrb_str_resize)
DRB_FFI_EXPOSE(mrb_int (*mrb_str_strlen)(mrb_state *, struct RString *), mrb_str_strlen)
DRB_FFI_EXPOSE(mrb_value (*mrb_str_substr)(mrb_state *mrb, mrb_value str, mrb_int beg, mrb_int len), mrb_str_substr)
DRB_FFI_EXPOSE(char *(*mrb_str_to_cstr)(mrb_state *mrb, mrb_value str), mrb_str_to_cstr)
DRB_FFI_EXPOSE(double (*mrb_str_to_dbl)(mrb_state *mrb, mrb_value str, mrb_bool badcheck), mrb_str_to_dbl)
DRB_FFI_EXPOSE(mrb_value (*mrb_str_to_inum)(mrb_state *mrb, mrb_value str, mrb_int base, mrb_bool badcheck), mrb_str_to_inum)
DRB_FFI_EXPOSE(const char *(*mrb_string_cstr)(mrb_state *mrb, mrb_value str), mrb_string_cstr)
DRB_FFI_EXPOSE(mrb_value (*mrb_string_type)(mrb_state *mrb, mrb_value str), mrb_string_type)
DRB_FFI_EXPOSE(const char *(*mrb_string_value_cstr)(mrb_state *mrb, mrb_value *str), mrb_string_value_cstr)
DRB_FFI_EXPOSE(mrb_int (*mrb_string_value_len)(mrb_state *mrb, mrb_value str), mrb_string_value_len)
DRB_FFI_EXPOSE(const char *(*mrb_string_value_ptr)(mrb_state *mrb, mrb_value str), mrb_string_value_ptr)
DRB_FFI_EXPOSE(const char *(*mrb_sym_dump)(mrb_state *, mrb_sym), mrb_sym_dump)
DRB_FFI_EXPOSE(const char *(*mrb_sym_name)(mrb_state *, mrb_sym), mrb_sym_name)
DRB_FFI_EXPOSE(const char *(*mrb_sym_name_len)(mrb_state *, mrb_sym, mrb_int *), mrb_sym_name_len)
DRB_FFI_EXPOSE(mrb_value (*mrb_sym_str)(mrb_state *, mrb_sym), mrb_sym_str)
DRB_FFI_EXPOSE(void (*mrb_sys_fail)(mrb_state *mrb, const char *mesg), mrb_sys_fail)
DRB_FFI_EXPOSE(mrb_float (*mrb_to_flo)(mrb_state *mrb, mrb_value x), mrb_to_flo)
DRB_FFI_EXPOSE(mrb_value (*mrb_to_int)(mrb_state *mrb, mrb_value val), mrb_to_int)
DRB_FFI_EXPOSE(mrb_value (*mrb_to_str)(mrb_state *mrb, mrb_value val), mrb_to_str)
DRB_FFI_EXPOSE(mrb_value (*mrb_top_run)(mrb_state *mrb, const struct RProc *proc, mrb_value self, mrb_int stack_keep), mrb_top_run)
DRB_FFI_EXPOSE(mrb_value (*mrb_top_self)(mrb_state *mrb), mrb_top_self)
DRB_FFI_EXPOSE(mrb_value (*mrb_type_convert)(mrb_state *mrb, mrb_value val, enum mrb_vtype type, mrb_sym method), mrb_type_convert)
DRB_FFI_EXPOSE(mrb_value (*mrb_type_convert_check)(mrb_state *mrb, mrb_value val, enum mrb_vtype type, mrb_sym method), mrb_type_convert_check)
DRB_FFI_EXPOSE(void (*mrb_undef_class_method)(mrb_state *mrb, struct RClass *cls, const char *name), mrb_undef_class_method)
DRB_FFI_EXPOSE(void (*mrb_undef_class_method_id)(mrb_state *mrb, struct RClass *cls, mrb_sym name), mrb_undef_class_method_id)
DRB_FFI_EXPOSE(void (*mrb_undef_method)(mrb_state *mrb, struct RClass *cla, const char *name), mrb_undef_method)
DRB_FFI_EXPOSE(void (*mrb_undef_method_id)(mrb_state *, struct RClass *, mrb_sym), mrb_undef_method_id)
DRB_FFI_EXPOSE(mrb_value (*mrb_vformat)(mrb_state *mrb, const char *format, va_list ap), mrb_vformat)
DRB_FFI_EXPOSE(mrb_value (*mrb_vm_const_get)(mrb_state *, mrb_sym), mrb_vm_const_get)
DRB_FFI_EXPOSE(void (*mrb_vm_const_set)(mrb_state *, mrb_sym, mrb_value), mrb_vm_const_set)
DRB_FFI_EXPOSE(mrb_value (*mrb_vm_cv_get)(mrb_state *, mrb_sym), mrb_vm_cv_get)
DRB_FFI_EXPOSE(void (*mrb_vm_cv_set)(mrb_state *, mrb_sym, mrb_value), mrb_vm_cv_set)
DRB_FFI_EXPOSE(struct RClass *(*mrb_vm_define_class)(mrb_state *, mrb_value, mrb_value, mrb_sym), mrb_vm_define_class)
DRB_FFI_EXPOSE(struct RClass *(*mrb_vm_define_module)(mrb_state *, mrb_value, mrb_sym), mrb_vm_define_module)
DRB_FFI_EXPOSE(mrb_value (*mrb_vm_exec)(mrb_state *mrb, const struct RProc *proc, const mrb_code *iseq), mrb_vm_exec)
DRB_FFI_EXPOSE(mrb_value (*mrb_vm_run)(mrb_state *mrb, const struct RProc *proc, mrb_value self, mrb_int stack_keep), mrb_vm_run)
DRB_FFI_EXPOSE(mrb_value (*mrb_vm_special_get)(mrb_state *, mrb_sym), mrb_vm_special_get)
DRB_FFI_EXPOSE(void (*mrb_vm_special_set)(mrb_state *, mrb_sym, mrb_value), mrb_vm_special_set)
DRB_FFI_EXPOSE(void (*mrb_warn)(mrb_state *mrb, const char *fmt, ...), mrb_warn)
DRB_FFI_EXPOSE(mrb_value (*mrb_word_boxing_cptr_value)(struct mrb_state *, void *), mrb_word_boxing_cptr_value)
DRB_FFI_EXPOSE(mrb_value (*mrb_word_boxing_float_value)(struct mrb_state *, mrb_float), mrb_word_boxing_float_value)
DRB_FFI_EXPOSE(mrb_value (*mrb_word_boxing_int_value)(struct mrb_state *, mrb_int), mrb_word_boxing_int_value)
DRB_FFI_EXPOSE(void (*mrb_write_barrier)(mrb_state *, struct RBasic *), mrb_write_barrier)
DRB_FFI_EXPOSE(mrb_value (*mrb_yield)(mrb_state *mrb, mrb_value b, mrb_value arg), mrb_yield)
DRB_FFI_EXPOSE(mrb_value (*mrb_yield_argv)(mrb_state *mrb, mrb_value b, mrb_int argc, const mrb_value *argv), mrb_yield_argv)
DRB_FFI_EXPOSE(mrb_value (*mrb_yield_with_class)(mrb_state *mrb, mrb_value b, mrb_int argc, const mrb_value *argv, mrb_value self, struct RClass *c), mrb_yield_with_class)
DRB_FFI_EXPOSE(void (*mrbc_cleanup_local_variables)(mrb_state *mrb, mrbc_context *c), mrbc_cleanup_local_variables)
DRB_FFI_EXPOSE(void (*mrbc_context_free)(mrb_state *mrb, mrbc_context *cxt), mrbc_context_free)
DRB_FFI_EXPOSE(mrbc_context *(*mrbc_context_new)(mrb_state *mrb), mrbc_context_new)
DRB_FFI_EXPOSE(const char *(*mrbc_filename)(mrb_state *mrb, mrbc_context *c, const char *s), mrbc_filename)
DRB_FFI_EXPOSE(void (*mrbc_partial_hook)(mrb_state *mrb, mrbc_context *c, int (*partial_hook)(struct mrb_parser_state *), void *data), mrbc_partial_hook)
DRB_FFI_EXPOSE(mrb_value (*mrb_obj_value)(void *p), mrb_obj_value)
DRB_FFI_EXPOSE(mrb_value (*mrb_nil_value)(), mrb_nil_value)
DRB_FFI_EXPOSE(mrb_value (*mrb_int_value)(struct mrb_state *mrb, mrb_int i), mrb_int_value)
DRB_FFI_EXPOSE(mrb_value (*mrb_float_value)(struct mrb_state *mrb, mrb_float f), mrb_float_value)
DRB_FFI_EXPOSE(mrb_value (*mrb_symbol_value)(mrb_sym i), mrb_symbol_value)
DRB_FFI_EXPOSE(const char *(*SDL_GetPlatform)(), SDL_GetPlatform)
DRB_FFI_EXPOSE(int (*SDL_abs)(int x), SDL_abs)
DRB_FFI_EXPOSE(double (*SDL_acos)(double x), SDL_acos)
DRB_FFI_EXPOSE(float (*SDL_acosf)(float x), SDL_acosf)
DRB_FFI_EXPOSE(double (*SDL_asin)(double x), SDL_asin)
DRB_FFI_EXPOSE(float (*SDL_asinf)(float x), SDL_asinf)
DRB_FFI_EXPOSE(int (*SDL_asprintf)(char **strp, const char *fmt, ...), SDL_asprintf)
DRB_FFI_EXPOSE(double (*SDL_atan)(double x), SDL_atan)
DRB_FFI_EXPOSE(double (*SDL_atan2)(double y, double x), SDL_atan2)
DRB_FFI_EXPOSE(float (*SDL_atan2f)(float y, float x), SDL_atan2f)
DRB_FFI_EXPOSE(float (*SDL_atanf)(float x), SDL_atanf)
DRB_FFI_EXPOSE(double (*SDL_atof)(const char *str), SDL_atof)
DRB_FFI_EXPOSE(int (*SDL_atoi)(const char *str), SDL_atoi)
DRB_FFI_EXPOSE(void *(*SDL_calloc)(size_t nmemb, size_t size), SDL_calloc)
DRB_FFI_EXPOSE(double (*SDL_ceil)(double x), SDL_ceil)
DRB_FFI_EXPOSE(float (*SDL_ceilf)(float x), SDL_ceilf)
DRB_FFI_EXPOSE(double (*SDL_copysign)(double x, double y), SDL_copysign)
DRB_FFI_EXPOSE(float (*SDL_copysignf)(float x, float y), SDL_copysignf)
DRB_FFI_EXPOSE(double (*SDL_cos)(double x), SDL_cos)
DRB_FFI_EXPOSE(float (*SDL_cosf)(float x), SDL_cosf)
DRB_FFI_EXPOSE(Uint32 (*SDL_crc32)(Uint32 crc, const void *data, size_t len), SDL_crc32)
DRB_FFI_EXPOSE(double (*SDL_exp)(double x), SDL_exp)
DRB_FFI_EXPOSE(float (*SDL_expf)(float x), SDL_expf)
DRB_FFI_EXPOSE(double (*SDL_fabs)(double x), SDL_fabs)
DRB_FFI_EXPOSE(float (*SDL_fabsf)(float x), SDL_fabsf)
DRB_FFI_EXPOSE(double (*SDL_floor)(double x), SDL_floor)
DRB_FFI_EXPOSE(float (*SDL_floorf)(float x), SDL_floorf)
DRB_FFI_EXPOSE(double (*SDL_fmod)(double x, double y), SDL_fmod)
DRB_FFI_EXPOSE(float (*SDL_fmodf)(float x, float y), SDL_fmodf)
DRB_FFI_EXPOSE(void (*SDL_free)(void *mem), SDL_free)
DRB_FFI_EXPOSE(char *(*SDL_getenv)(const char *name), SDL_getenv)
DRB_FFI_EXPOSE(size_t (*SDL_iconv)(SDL_iconv_t cd, const char **inbuf, size_t *inbytesleft, char **outbuf, size_t *outbytesleft), SDL_iconv)
DRB_FFI_EXPOSE(int (*SDL_iconv_close)(SDL_iconv_t cd), SDL_iconv_close)
DRB_FFI_EXPOSE(SDL_iconv_t(*SDL_iconv_open)(const char *tocode, const char *fromcode), SDL_iconv_open)
DRB_FFI_EXPOSE(char *(*SDL_iconv_string)(const char *tocode, const char *fromcode, const char *inbuf, size_t inbytesleft), SDL_iconv_string)
DRB_FFI_EXPOSE(int (*SDL_isalnum)(int x), SDL_isalnum)
DRB_FFI_EXPOSE(int (*SDL_isalpha)(int x), SDL_isalpha)
DRB_FFI_EXPOSE(int (*SDL_isblank)(int x), SDL_isblank)
DRB_FFI_EXPOSE(int (*SDL_iscntrl)(int x), SDL_iscntrl)
DRB_FFI_EXPOSE(int (*SDL_isdigit)(int x), SDL_isdigit)
DRB_FFI_EXPOSE(int (*SDL_isgraph)(int x), SDL_isgraph)
DRB_FFI_EXPOSE(int (*SDL_islower)(int x), SDL_islower)
DRB_FFI_EXPOSE(int (*SDL_isprint)(int x), SDL_isprint)
DRB_FFI_EXPOSE(int (*SDL_ispunct)(int x), SDL_ispunct)
DRB_FFI_EXPOSE(int (*SDL_isspace)(int x), SDL_isspace)
DRB_FFI_EXPOSE(int (*SDL_isupper)(int x), SDL_isupper)
DRB_FFI_EXPOSE(int (*SDL_isxdigit)(int x), SDL_isxdigit)
DRB_FFI_EXPOSE(char *(*SDL_itoa)(int value, char *str, int radix), SDL_itoa)
DRB_FFI_EXPOSE(char *(*SDL_lltoa)(Sint64 value, char *str, int radix), SDL_lltoa)
DRB_FFI_EXPOSE(double (*SDL_log)(double x), SDL_log)
DRB_FFI_EXPOSE(double (*SDL_log10)(double x), SDL_log10)
DRB_FFI_EXPOSE(float (*SDL_log10f)(float x), SDL_log10f)
DRB_FFI_EXPOSE(float (*SDL_logf)(float x), SDL_logf)
DRB_FFI_EXPOSE(long (*SDL_lround)(double x), SDL_lround)
DRB_FFI_EXPOSE(long (*SDL_lroundf)(float x), SDL_lroundf)
DRB_FFI_EXPOSE(char *(*SDL_ltoa)(long value, char *str, int radix), SDL_ltoa)
DRB_FFI_EXPOSE(void *(*SDL_malloc)(size_t size), SDL_malloc)
DRB_FFI_EXPOSE(int (*SDL_memcmp)(const void *s1, const void *s2, size_t len), SDL_memcmp)
DRB_FFI_EXPOSE(void *(*SDL_memcpy)(void *dst, const void *src, size_t len), SDL_memcpy)
DRB_FFI_EXPOSE(void *(*SDL_memmove)(void *dst, const void *src, size_t len), SDL_memmove)
DRB_FFI_EXPOSE(void *(*SDL_memset)(void *dst, int c, size_t len), SDL_memset)
DRB_FFI_EXPOSE(double (*SDL_pow)(double x, double y), SDL_pow)
DRB_FFI_EXPOSE(float (*SDL_powf)(float x, float y), SDL_powf)
DRB_FFI_EXPOSE(void (*SDL_qsort)(void *base, size_t nmemb, size_t size, int (*compare)(const void *, const void *)), SDL_qsort)
DRB_FFI_EXPOSE(void *(*SDL_realloc)(void *mem, size_t size), SDL_realloc)
DRB_FFI_EXPOSE(double (*SDL_round)(double x), SDL_round)
DRB_FFI_EXPOSE(float (*SDL_roundf)(float x), SDL_roundf)
DRB_FFI_EXPOSE(double (*SDL_scalbn)(double x, int n), SDL_scalbn)
DRB_FFI_EXPOSE(float (*SDL_scalbnf)(float x, int n), SDL_scalbnf)
DRB_FFI_EXPOSE(int (*SDL_setenv)(const char *name, const char *value, int overwrite), SDL_setenv)
DRB_FFI_EXPOSE(double (*SDL_sin)(double x), SDL_sin)
DRB_FFI_EXPOSE(float (*SDL_sinf)(float x), SDL_sinf)
DRB_FFI_EXPOSE(int (*SDL_snprintf)(char *text, size_t maxlen, const char *fmt, ...), SDL_snprintf)
DRB_FFI_EXPOSE(double (*SDL_sqrt)(double x), SDL_sqrt)
DRB_FFI_EXPOSE(float (*SDL_sqrtf)(float x), SDL_sqrtf)
DRB_FFI_EXPOSE(int (*SDL_sscanf)(const char *text, const char *fmt, ...), SDL_sscanf)
DRB_FFI_EXPOSE(int (*SDL_strcasecmp)(const char *str1, const char *str2), SDL_strcasecmp)
DRB_FFI_EXPOSE(char *(*SDL_strchr)(const char *str, int c), SDL_strchr)
DRB_FFI_EXPOSE(int (*SDL_strcmp)(const char *str1, const char *str2), SDL_strcmp)
DRB_FFI_EXPOSE(char *(*SDL_strdup)(const char *str), SDL_strdup)
DRB_FFI_EXPOSE(size_t (*SDL_strlcat)(char *dst, const char *src, size_t maxlen), SDL_strlcat)
DRB_FFI_EXPOSE(size_t (*SDL_strlcpy)(char *dst, const char *src, size_t maxlen), SDL_strlcpy)
DRB_FFI_EXPOSE(size_t (*SDL_strlen)(const char *str), SDL_strlen)
DRB_FFI_EXPOSE(char *(*SDL_strlwr)(char *str), SDL_strlwr)
DRB_FFI_EXPOSE(int (*SDL_strncasecmp)(const char *str1, const char *str2, size_t len), SDL_strncasecmp)
DRB_FFI_EXPOSE(int (*SDL_strncmp)(const char *str1, const char *str2, size_t maxlen), SDL_strncmp)
DRB_FFI_EXPOSE(char *(*SDL_strrchr)(const char *str, int c), SDL_strrchr)
DRB_FFI_EXPOSE(char *(*SDL_strrev)(char *str), SDL_strrev)
DRB_FFI_EXPOSE(char *(*SDL_strstr)(const char *haystack, const char *needle), SDL_strstr)
DRB_FFI_EXPOSE(double (*SDL_strtod)(const char *str, char **endp), SDL_strtod)
DRB_FFI_EXPOSE(char *(*SDL_strtokr)(char *s1, const char *s2, char **saveptr), SDL_strtokr)
DRB_FFI_EXPOSE(long (*SDL_strtol)(const char *str, char **endp, int base), SDL_strtol)
DRB_FFI_EXPOSE(Sint64 (*SDL_strtoll)(const char *str, char **endp, int base), SDL_strtoll)
DRB_FFI_EXPOSE(unsigned long (*SDL_strtoul)(const char *str, char **endp, int base), SDL_strtoul)
DRB_FFI_EXPOSE(Uint64 (*SDL_strtoull)(const char *str, char **endp, int base), SDL_strtoull)
DRB_FFI_EXPOSE(char *(*SDL_strupr)(char *str), SDL_strupr)
DRB_FFI_EXPOSE(double (*SDL_tan)(double x), SDL_tan)
DRB_FFI_EXPOSE(float (*SDL_tanf)(float x), SDL_tanf)
DRB_FFI_EXPOSE(int (*SDL_tolower)(int x), SDL_tolower)
DRB_FFI_EXPOSE(int (*SDL_toupper)(int x), SDL_toupper)
DRB_FFI_EXPOSE(double (*SDL_trunc)(double x), SDL_trunc)
DRB_FFI_EXPOSE(float (*SDL_truncf)(float x), SDL_truncf)
DRB_FFI_EXPOSE(char *(*SDL_uitoa)(unsigned int value, char *str, int radix), SDL_uitoa)
DRB_FFI_EXPOSE(char *(*SDL_ulltoa)(Uint64 value, char *str, int radix), SDL_ulltoa)
DRB_FFI_EXPOSE(char *(*SDL_ultoa)(unsigned long value, char *str, int radix), SDL_ultoa)
DRB_FFI_EXPOSE(size_t (*SDL_utf8strlcpy)(char *dst, const char *src, size_t dst_bytes), SDL_utf8strlcpy)
DRB_FFI_EXPOSE(size_t (*SDL_utf8strlen)(const char *str), SDL_utf8strlen)
DRB_FFI_EXPOSE(int (*SDL_vasprintf)(char **strp, const char *fmt, va_list ap), SDL_vasprintf)
DRB_FFI_EXPOSE(int (*SDL_vsnprintf)(char *text, size_t maxlen, const char *fmt, va_list ap), SDL_vsnprintf)
DRB_FFI_EXPOSE(int (*SDL_vsscanf)(const char *text, const char *fmt, va_list ap), SDL_vsscanf)
DRB_FFI_EXPOSE(int (*SDL_wcscasecmp)(const wchar_t *str1, const wchar_t *str2), SDL_wcscasecmp)
DRB_FFI_EXPOSE(int (*SDL_wcscmp)(const wchar_t *str1, const wchar_t *str2), SDL_wcscmp)
DRB_FFI_EXPOSE(wchar_t *(*SDL_wcsdup)(const wchar_t *wstr), SDL_wcsdup)
DRB_FFI_EXPOSE(size_t (*SDL_wcslcat)(wchar_t *dst, const wchar_t *src, size_t maxlen), SDL_wcslcat)
DRB_FFI_EXPOSE(size_t (*SDL_wcslcpy)(wchar_t *dst, const wchar_t *src, size_t maxlen), SDL_wcslcpy)
DRB_FFI_EXPOSE(size_t (*SDL_wcslen)(const wchar_t *wstr), SDL_wcslen)
DRB_FFI_EXPOSE(int (*SDL_wcsncasecmp)(const wchar_t *str1, const wchar_t *str2, size_t len), SDL_wcsncasecmp)
DRB_FFI_EXPOSE(int (*SDL_wcsncmp)(const wchar_t *str1, const wchar_t *str2, size_t maxlen), SDL_wcsncmp)
DRB_FFI_EXPOSE(wchar_t *(*SDL_wcsstr)(const wchar_t *haystack, const wchar_t *needle), SDL_wcsstr)
DRB_FFI_EXPOSE(int (*PHYSFS_close)(PHYSFS_File *handle), PHYSFS_close)
DRB_FFI_EXPOSE(int (*PHYSFS_delete)(const char *filename), PHYSFS_delete)
DRB_FFI_EXPOSE(char **(*PHYSFS_enumerateFiles)(const char *dir), PHYSFS_enumerateFiles)
DRB_FFI_EXPOSE(int (*PHYSFS_eof)(PHYSFS_File *handle), PHYSFS_eof)
DRB_FFI_EXPOSE(int (*PHYSFS_exists)(const char *fname), PHYSFS_exists)
DRB_FFI_EXPOSE(PHYSFS_sint64 (*PHYSFS_fileLength)(PHYSFS_File *handle), PHYSFS_fileLength)
DRB_FFI_EXPOSE(int (*PHYSFS_flush)(PHYSFS_File *handle), PHYSFS_flush)
DRB_FFI_EXPOSE(void (*PHYSFS_freeList)(void *listVar), PHYSFS_freeList)
DRB_FFI_EXPOSE(const char *(*PHYSFS_getErrorByCode)(PHYSFS_ErrorCode code), PHYSFS_getErrorByCode)
DRB_FFI_EXPOSE(PHYSFS_ErrorCode (*PHYSFS_getLastErrorCode)(), PHYSFS_getLastErrorCode)
DRB_FFI_EXPOSE(int (*PHYSFS_mkdir)(const char *dirName), PHYSFS_mkdir)
DRB_FFI_EXPOSE(PHYSFS_File *(*PHYSFS_openAppend)(const char *filename), PHYSFS_openAppend)
DRB_FFI_EXPOSE(PHYSFS_File *(*PHYSFS_openRead)(const char *filename), PHYSFS_openRead)
DRB_FFI_EXPOSE(PHYSFS_File *(*PHYSFS_openWrite)(const char *filename), PHYSFS_openWrite)
DRB_FFI_EXPOSE(PHYSFS_sint64 (*PHYSFS_readBytes)(PHYSFS_File *handle, void *buffer, PHYSFS_uint64 len), PHYSFS_readBytes)
DRB_FFI_EXPOSE(int (*PHYSFS_seek)(PHYSFS_File *handle, PHYSFS_uint64 pos), PHYSFS_seek)
DRB_FFI_EXPOSE(int (*PHYSFS_stat)(const char *fname, PHYSFS_Stat *stat), PHYSFS_stat)
DRB_FFI_EXPOSE(PHYSFS_sint64 (*PHYSFS_tell)(PHYSFS_File *handle), PHYSFS_tell)
DRB_FFI_EXPOSE(PHYSFS_sint64 (*PHYSFS_writeBytes)(PHYSFS_File *handle, const void *buffer, PHYSFS_uint64 len), PHYSFS_writeBytes)
DRB_FFI_EXPOSE(void *(*drb_android_get_jni_env)(), drb_android_get_jni_env)
DRB_FFI_EXPOSE(void *(*drb_android_get_sdl_activity)(), drb_android_get_sdl_activity)
DRB_FFI_EXPOSE(void *(*drb_android_get_bridge)(), drb_android_get_bridge)
DRB_FFI_EXPOSE(SDL_threadID (*SDL_ThreadID)(void), SDL_ThreadID)
DRB_FFI_EXPOSE(SDL_threadID (*SDL_GetThreadID)(SDL_Thread *thread), SDL_GetThreadID)
DRB_FFI_EXPOSE(SDL_Thread *(*SDL_CreateThread)(SDL_ThreadFunction fn, const char *name, void *data), SDL_CreateThread)
DRB_FFI_EXPOSE(void (*SDL_DetachThread)(SDL_Thread *thread), SDL_DetachThread)
DRB_FFI_EXPOSE(void (*SDL_WaitThread)(SDL_Thread *thread, int *status), SDL_WaitThread)
DRB_FFI_EXPOSE(int (*SDL_AtomicSet)(SDL_atomic_t *a, int v), SDL_AtomicSet)
DRB_FFI_EXPOSE(int (*SDL_AtomicGet)(SDL_atomic_t *a), SDL_AtomicGet)
DRB_FFI_EXPOSE(void (*SDL_Delay)(Uint32 ms), SDL_Delay);
DRB_FFI_EXPOSE(Uint32 (*SDL_GetTicks)(), SDL_GetTicks);
DRB_FFI_EXPOSE(int (*mz_compress)(unsigned char *pDest, unsigned long *pDest_len, const unsigned char *pSource, unsigned long source_len), mz_compress);
DRB_FFI_EXPOSE(int (*mz_uncompress)(unsigned char *pDest, unsigned long *pDest_len, const unsigned char *pSource, unsigned long source_len), mz_uncompress);