replay_version 2.0
stopped_at 820
seed 100
recorded_at 2023-07-15 13:09:03 -0500
[:mouse_button_up, 1, 0, 1, 1, 3]
[:mouse_move, 931, 142, 2, 2, 20]
[:mouse_move, 930, 144, 2, 3, 21]
[:mouse_move, 929, 149, 2, 4, 22]
[:mouse_move, 923, 169, 2, 5, 23]
[:mouse_move, 914, 220, 2, 6, 24]
[:mouse_move, 909, 249, 2, 7, 25]
[:mouse_move, 902, 284, 2, 8, 26]
[:mouse_move, 878, 367, 2, 9, 27]
[:mouse_move, 872, 383, 2, 10, 28]
[:mouse_move, 856, 424, 2, 11, 29]
[:mouse_move, 847, 444, 2, 12, 30]
[:mouse_move, 837, 462, 2, 13, 31]
[:mouse_move, 832, 471, 2, 14, 32]
[:mouse_move, 828, 479, 2, 15, 33]
[:mouse_move, 826, 480, 2, 16, 34]
[:mouse_move, 825, 481, 2, 17, 35]
[:mouse_move, 824, 481, 2, 18, 36]
[:mouse_move, 824, 480, 2, 19, 39]
[:mouse_move, 824, 479, 2, 20, 41]
[:mouse_move, 826, 478, 2, 21, 43]
[:mouse_move, 827, 477, 2, 22, 44]
[:mouse_move, 829, 477, 2, 23, 45]
[:mouse_move, 829, 476, 2, 24, 46]
[:mouse_move, 830, 476, 2, 25, 47]
[:mouse_move, 831, 476, 2, 26, 49]
[:mouse_move, 834, 475, 2, 27, 51]
[:mouse_move, 836, 474, 2, 28, 52]
[:mouse_move, 840, 473, 2, 29, 53]
[:mouse_move, 841, 472, 2, 30, 54]
[:mouse_move, 845, 471, 2, 31, 55]
[:mouse_move, 848, 470, 2, 32, 56]
[:mouse_move, 852, 470, 2, 33, 57]
[:mouse_move, 855, 470, 2, 34, 58]
[:mouse_move, 856, 470, 2, 35, 59]
[:mouse_button_pressed, 1, 0, 1, 36, 103]
[:mouse_move, 854, 469, 2, 37, 126]
[:mouse_move, 850, 467, 2, 38, 127]
[:mouse_move, 844, 462, 2, 39, 128]
[:mouse_move, 836, 456, 2, 40, 129]
[:mouse_move, 827, 449, 2, 41, 130]
[:mouse_move, 816, 442, 2, 42, 131]
[:mouse_move, 807, 436, 2, 43, 132]
[:mouse_move, 801, 432, 2, 44, 133]
[:mouse_move, 794, 428, 2, 45, 134]
[:mouse_move, 786, 425, 2, 46, 135]
[:mouse_move, 781, 423, 2, 47, 136]
[:mouse_move, 776, 421, 2, 48, 137]
[:mouse_move, 772, 420, 2, 49, 139]
[:mouse_move, 767, 419, 2, 50, 139]
[:mouse_move, 763, 418, 2, 51, 140]
[:mouse_move, 760, 417, 2, 52, 141]
[:mouse_move, 755, 417, 2, 53, 143]
[:mouse_move, 746, 415, 2, 54, 143]
[:mouse_move, 739, 414, 2, 55, 144]
[:mouse_move, 733, 413, 2, 56, 145]
[:mouse_move, 727, 412, 2, 57, 146]
[:mouse_move, 722, 411, 2, 58, 147]
[:mouse_move, 715, 410, 2, 59, 148]
[:mouse_move, 706, 409, 2, 60, 149]
[:mouse_move, 693, 408, 2, 61, 150]
[:mouse_move, 673, 408, 2, 62, 151]
[:mouse_move, 648, 408, 2, 63, 152]
[:mouse_move, 622, 408, 2, 64, 153]
[:mouse_move, 596, 409, 2, 65, 154]
[:mouse_move, 572, 411, 2, 66, 155]
[:mouse_move, 551, 414, 2, 67, 156]
[:mouse_move, 534, 417, 2, 68, 157]
[:mouse_move, 522, 419, 2, 69, 158]
[:mouse_move, 513, 422, 2, 70, 159]
[:mouse_move, 507, 424, 2, 71, 160]
[:mouse_move, 503, 425, 2, 72, 161]
[:mouse_move, 502, 426, 2, 73, 162]
[:mouse_move, 501, 426, 2, 74, 163]
[:mouse_move, 501, 427, 2, 75, 164]
[:mouse_move, 501, 428, 2, 76, 166]
[:mouse_move, 501, 429, 2, 77, 166]
[:mouse_move, 500, 431, 2, 78, 167]
[:mouse_move, 499, 433, 2, 79, 168]
[:mouse_move, 499, 434, 2, 80, 169]
[:mouse_move, 499, 435, 2, 81, 170]
[:mouse_move, 499, 436, 2, 82, 171]
[:mouse_move, 499, 437, 2, 83, 173]
[:mouse_button_up, 1, 0, 1, 84, 178]
[:mouse_move, 500, 437, 2, 85, 178]
[:mouse_move, 499, 436, 2, 86, 191]
[:mouse_move, 495, 430, 2, 87, 192]
[:mouse_move, 486, 419, 2, 88, 193]
[:mouse_move, 482, 415, 2, 89, 194]
[:mouse_move, 469, 398, 2, 90, 195]
[:mouse_move, 462, 391, 2, 91, 197]
[:mouse_move, 452, 379, 2, 92, 197]
[:mouse_move, 449, 375, 2, 93, 198]
[:mouse_move, 445, 369, 2, 94, 199]
[:mouse_move, 440, 361, 2, 95, 201]
[:mouse_move, 439, 359, 2, 96, 203]
[:mouse_move, 437, 357, 2, 97, 203]
[:mouse_move, 436, 356, 2, 98, 205]
[:mouse_move, 435, 355, 2, 99, 205]
[:mouse_move, 435, 354, 2, 100, 206]
[:mouse_move, 434, 353, 2, 101, 207]
[:mouse_move, 433, 353, 2, 102, 208]
[:mouse_move, 432, 352, 2, 103, 209]
[:mouse_button_pressed, 1, 0, 1, 104, 233]
[:mouse_move, 437, 357, 2, 105, 234]
[:mouse_move, 443, 362, 2, 106, 235]
[:mouse_move, 448, 367, 2, 107, 236]
[:mouse_move, 459, 377, 2, 108, 237]
[:mouse_move, 473, 388, 2, 109, 238]
[:mouse_move, 483, 397, 2, 110, 239]
[:mouse_move, 494, 407, 2, 111, 240]
[:mouse_move, 504, 416, 2, 112, 241]
[:mouse_move, 513, 425, 2, 113, 242]
[:mouse_move, 522, 434, 2, 114, 243]
[:mouse_move, 531, 443, 2, 115, 244]
[:mouse_move, 539, 451, 2, 116, 245]
[:mouse_move, 545, 456, 2, 117, 246]
[:mouse_move, 550, 462, 2, 118, 247]
[:mouse_move, 555, 468, 2, 119, 248]
[:mouse_move, 560, 474, 2, 120, 249]
[:mouse_move, 564, 478, 2, 121, 250]
[:mouse_move, 568, 482, 2, 122, 251]
[:mouse_move, 572, 486, 2, 123, 252]
[:mouse_move, 577, 491, 2, 124, 253]
[:mouse_move, 582, 496, 2, 125, 254]
[:mouse_move, 587, 502, 2, 126, 255]
[:mouse_move, 591, 508, 2, 127, 256]
[:mouse_move, 595, 514, 2, 128, 257]
[:mouse_move, 599, 520, 2, 129, 258]
[:mouse_move, 603, 525, 2, 130, 259]
[:mouse_move, 606, 531, 2, 131, 260]
[:mouse_move, 610, 537, 2, 132, 261]
[:mouse_move, 613, 541, 2, 133, 262]
[:mouse_move, 615, 544, 2, 134, 263]
[:mouse_move, 616, 547, 2, 135, 264]
[:mouse_move, 617, 548, 2, 136, 265]
[:mouse_move, 618, 549, 2, 137, 266]
[:mouse_move, 618, 550, 2, 138, 268]
[:mouse_button_up, 1, 0, 1, 139, 285]
[:mouse_move, 579, 546, 2, 140, 285]
[:mouse_move, 518, 537, 2, 141, 286]
[:mouse_move, 501, 533, 2, 142, 287]
[:mouse_move, 427, 518, 2, 143, 288]
[:mouse_move, 400, 512, 2, 144, 289]
[:mouse_move, 368, 504, 2, 145, 290]
[:mouse_move, 349, 500, 2, 146, 292]
[:mouse_move, 323, 496, 2, 147, 292]
[:mouse_move, 313, 495, 2, 148, 293]
[:mouse_move, 297, 493, 2, 149, 294]
[:mouse_move, 290, 493, 2, 150, 296]
[:mouse_move, 277, 489, 2, 151, 296]
[:mouse_move, 274, 488, 2, 152, 297]
[:mouse_move, 267, 485, 2, 153, 298]
[:mouse_move, 264, 482, 2, 154, 299]
[:mouse_move, 262, 478, 2, 155, 300]
[:mouse_move, 262, 476, 2, 156, 301]
[:mouse_move, 262, 475, 2, 157, 302]
[:mouse_move, 262, 472, 2, 158, 303]
[:mouse_move, 262, 471, 2, 159, 305]
[:mouse_move, 263, 471, 2, 160, 307]
[:mouse_move, 264, 471, 2, 161, 308]
[:mouse_move, 265, 472, 2, 162, 309]
[:mouse_move, 266, 472, 2, 163, 310]
[:mouse_move, 267, 473, 2, 164, 311]
[:mouse_move, 268, 473, 2, 165, 312]
[:mouse_move, 269, 473, 2, 166, 315]
[:mouse_button_pressed, 1, 0, 1, 167, 317]
[:mouse_button_up, 1, 0, 1, 168, 345]
[:mouse_move, 269, 474, 2, 169, 345]
[:mouse_button_pressed, 1, 0, 1, 170, 370]
[:mouse_move, 272, 475, 2, 171, 371]
[:mouse_move, 279, 479, 2, 172, 372]
[:mouse_move, 298, 488, 2, 173, 373]
[:mouse_move, 326, 501, 2, 174, 374]
[:mouse_move, 355, 512, 2, 175, 375]
[:mouse_move, 383, 522, 2, 176, 376]
[:mouse_move, 405, 530, 2, 177, 377]
[:mouse_move, 420, 535, 2, 178, 378]
[:mouse_move, 435, 540, 2, 179, 379]
[:mouse_move, 447, 543, 2, 180, 380]
[:mouse_move, 454, 545, 2, 181, 381]
[:mouse_move, 459, 547, 2, 182, 382]
[:mouse_move, 464, 548, 2, 183, 383]
[:mouse_move, 467, 548, 2, 184, 384]
[:mouse_move, 472, 549, 2, 185, 385]
[:mouse_move, 477, 550, 2, 186, 386]
[:mouse_move, 479, 550, 2, 187, 387]
[:mouse_move, 483, 551, 2, 188, 388]
[:mouse_move, 486, 551, 2, 189, 389]
[:mouse_move, 489, 552, 2, 190, 390]
[:mouse_move, 491, 552, 2, 191, 391]
[:mouse_move, 495, 554, 2, 192, 392]
[:mouse_move, 500, 556, 2, 193, 393]
[:mouse_move, 505, 558, 2, 194, 394]
[:mouse_move, 508, 559, 2, 195, 395]
[:mouse_move, 513, 560, 2, 196, 396]
[:mouse_move, 519, 562, 2, 197, 397]
[:mouse_move, 525, 564, 2, 198, 398]
[:mouse_move, 533, 565, 2, 199, 399]
[:mouse_move, 544, 567, 2, 200, 400]
[:mouse_move, 556, 568, 2, 201, 401]
[:mouse_move, 568, 569, 2, 202, 402]
[:mouse_move, 578, 569, 2, 203, 403]
[:mouse_move, 586, 569, 2, 204, 404]
[:mouse_move, 592, 570, 2, 205, 405]
[:mouse_move, 597, 570, 2, 206, 406]
[:mouse_move, 599, 570, 2, 207, 407]
[:mouse_move, 601, 571, 2, 208, 408]
[:mouse_move, 602, 571, 2, 209, 410]
[:mouse_move, 603, 571, 2, 210, 413]
[:mouse_move, 604, 571, 2, 211, 419]
[:mouse_button_up, 1, 0, 1, 212, 438]
[:mouse_move, 608, 566, 2, 213, 438]
[:mouse_move, 613, 558, 2, 214, 438]
[:mouse_move, 615, 556, 2, 215, 439]
[:mouse_move, 621, 547, 2, 216, 440]
[:mouse_move, 623, 544, 2, 217, 442]
[:mouse_move, 624, 542, 2, 218, 442]
[:mouse_move, 625, 541, 2, 219, 442]
[:mouse_move, 625, 540, 2, 220, 444]
[:mouse_move, 626, 540, 2, 221, 444]
[:mouse_move, 626, 539, 2, 222, 446]
[:mouse_move, 627, 539, 2, 223, 447]
[:mouse_move, 628, 539, 2, 224, 448]
[:mouse_move, 631, 539, 2, 225, 448]
[:mouse_move, 632, 540, 2, 226, 448]
[:mouse_move, 635, 542, 2, 227, 449]
[:mouse_move, 637, 543, 2, 228, 451]
[:mouse_move, 641, 546, 2, 229, 451]
[:mouse_move, 642, 547, 2, 230, 453]
[:mouse_move, 647, 549, 2, 231, 453]
[:mouse_move, 649, 550, 2, 232, 454]
[:mouse_move, 654, 552, 2, 233, 455]
[:mouse_move, 656, 553, 2, 234, 457]
[:mouse_move, 658, 554, 2, 235, 458]
[:mouse_move, 659, 554, 2, 236, 459]
[:mouse_move, 660, 554, 2, 237, 462]
[:mouse_move, 661, 554, 2, 238, 464]
[:mouse_button_pressed, 1, 0, 1, 239, 498]
[:mouse_move, 660, 553, 2, 240, 498]
[:mouse_move, 654, 551, 2, 241, 499]
[:mouse_move, 646, 548, 2, 242, 501]
[:mouse_move, 638, 544, 2, 243, 501]
[:mouse_move, 631, 541, 2, 244, 502]
[:mouse_move, 624, 537, 2, 245, 503]
[:mouse_move, 616, 533, 2, 246, 504]
[:mouse_move, 608, 529, 2, 247, 506]
[:mouse_move, 601, 525, 2, 248, 506]
[:mouse_move, 595, 521, 2, 249, 507]
[:mouse_move, 588, 518, 2, 250, 508]
[:mouse_move, 583, 515, 2, 251, 510]
[:mouse_move, 579, 513, 2, 252, 511]
[:mouse_move, 575, 509, 2, 253, 511]
[:mouse_move, 571, 505, 2, 254, 511]
[:mouse_move, 567, 502, 2, 255, 512]
[:mouse_move, 563, 498, 2, 256, 514]
[:mouse_move, 561, 495, 2, 257, 514]
[:mouse_move, 559, 492, 2, 258, 515]
[:mouse_move, 558, 490, 2, 259, 516]
[:mouse_move, 557, 489, 2, 260, 518]
[:mouse_move, 556, 487, 2, 261, 519]
[:mouse_move, 554, 484, 2, 262, 519]
[:mouse_move, 552, 482, 2, 263, 521]
[:mouse_move, 550, 480, 2, 264, 521]
[:mouse_move, 548, 478, 2, 265, 522]
[:mouse_move, 545, 475, 2, 266, 524]
[:mouse_move, 544, 474, 2, 267, 524]
[:mouse_move, 542, 472, 2, 268, 524]
[:mouse_move, 540, 470, 2, 269, 526]
[:mouse_move, 539, 469, 2, 270, 526]
[:mouse_move, 539, 468, 2, 271, 528]
[:mouse_move, 538, 468, 2, 272, 529]
[:mouse_move, 538, 467, 2, 273, 529]
[:mouse_button_up, 1, 0, 1, 274, 551]
[:mouse_move, 538, 460, 2, 275, 551]
[:mouse_move, 540, 445, 2, 276, 552]
[:mouse_move, 542, 435, 2, 277, 553]
[:mouse_move, 548, 413, 2, 278, 553]
[:mouse_move, 555, 393, 2, 279, 555]
[:mouse_move, 562, 373, 2, 280, 556]
[:mouse_move, 567, 362, 2, 281, 556]
[:mouse_move, 572, 351, 2, 282, 557]
[:mouse_move, 575, 346, 2, 283, 558]
[:mouse_move, 577, 342, 2, 284, 559]
[:mouse_move, 580, 337, 2, 285, 561]
[:mouse_move, 581, 336, 2, 286, 562]
[:mouse_move, 583, 334, 2, 287, 563]
[:mouse_move, 584, 334, 2, 288, 564]
[:mouse_move, 585, 334, 2, 289, 564]
[:mouse_move, 586, 333, 2, 290, 566]
[:mouse_move, 587, 333, 2, 291, 567]
[:mouse_move, 588, 333, 2, 292, 569]
[:mouse_move, 589, 332, 2, 293, 573]
[:mouse_move, 590, 331, 2, 294, 573]
[:mouse_move, 590, 330, 2, 295, 575]
[:mouse_move, 592, 329, 2, 296, 575]
[:mouse_move, 593, 328, 2, 297, 575]
[:mouse_move, 594, 328, 2, 298, 577]
[:mouse_button_pressed, 1, 0, 1, 299, 594]
[:mouse_move, 596, 329, 2, 300, 594]
[:mouse_move, 606, 336, 2, 301, 595]
[:mouse_move, 618, 344, 2, 302, 596]
[:mouse_move, 637, 356, 2, 303, 598]
[:mouse_move, 661, 371, 2, 304, 598]
[:mouse_move, 680, 383, 2, 305, 599]
[:mouse_move, 697, 393, 2, 306, 600]
[:mouse_move, 709, 400, 2, 307, 601]
[:mouse_move, 721, 407, 2, 308, 602]
[:mouse_move, 731, 413, 2, 309, 604]
[:mouse_move, 737, 416, 2, 310, 605]
[:mouse_move, 743, 419, 2, 311, 605]
[:mouse_move, 748, 423, 2, 312, 606]
[:mouse_move, 751, 425, 2, 313, 607]
[:mouse_move, 753, 426, 2, 314, 608]
[:mouse_move, 754, 427, 2, 315, 609]
[:mouse_move, 755, 429, 2, 316, 610]
[:mouse_move, 756, 430, 2, 317, 612]
[:mouse_move, 756, 433, 2, 318, 612]
[:mouse_move, 757, 437, 2, 319, 613]
[:mouse_move, 759, 444, 2, 320, 614]
[:mouse_move, 761, 452, 2, 321, 615]
[:mouse_move, 762, 462, 2, 322, 617]
[:mouse_move, 764, 471, 2, 323, 617]
[:mouse_move, 766, 480, 2, 324, 618]
[:mouse_move, 767, 491, 2, 325, 619]
[:mouse_move, 770, 507, 2, 326, 620]
[:mouse_move, 771, 523, 2, 327, 621]
[:mouse_move, 772, 535, 2, 328, 623]
[:mouse_move, 773, 545, 2, 329, 623]
[:mouse_move, 774, 553, 2, 330, 625]
[:mouse_move, 774, 558, 2, 331, 625]
[:mouse_move, 774, 560, 2, 332, 627]
[:mouse_move, 774, 561, 2, 333, 627]
[:mouse_move, 774, 562, 2, 334, 628]
[:mouse_move, 774, 561, 2, 335, 636]
[:mouse_move, 774, 560, 2, 336, 637]
[:mouse_move, 774, 558, 2, 337, 638]
[:mouse_move, 774, 556, 2, 338, 639]
[:mouse_move, 774, 554, 2, 339, 641]
[:mouse_move, 774, 553, 2, 340, 642]
[:mouse_move, 774, 552, 2, 341, 643]
[:mouse_move, 774, 551, 2, 342, 644]
[:mouse_move, 774, 550, 2, 343, 645]
[:mouse_move, 774, 549, 2, 344, 645]
[:mouse_move, 774, 547, 2, 345, 647]
[:mouse_move, 774, 546, 2, 346, 648]
[:mouse_move, 774, 545, 2, 347, 648]
[:mouse_move, 774, 544, 2, 348, 649]
[:mouse_move, 775, 543, 2, 349, 651]
[:mouse_move, 776, 543, 2, 350, 659]
[:mouse_move, 777, 543, 2, 351, 661]
[:mouse_move, 779, 543, 2, 352, 663]
[:mouse_move, 781, 543, 2, 353, 664]
[:mouse_move, 782, 543, 2, 354, 665]
[:mouse_move, 783, 543, 2, 355, 665]
[:mouse_move, 784, 543, 2, 356, 666]
[:mouse_button_up, 1, 0, 1, 357, 684]
[:mouse_move, 793, 541, 2, 358, 684]
[:mouse_move, 835, 532, 2, 359, 685]
[:mouse_move, 868, 521, 2, 360, 687]
[:mouse_move, 945, 471, 2, 361, 688]
[:mouse_move, 982, 441, 2, 362, 689]
[:mouse_move, 1043, 364, 2, 363, 689]
[:mouse_move, 1049, 347, 2, 364, 690]
[:mouse_move, 1060, 313, 2, 365, 692]
[:mouse_move, 1062, 299, 2, 366, 692]
[:mouse_move, 1062, 288, 2, 367, 693]
[:mouse_move, 1062, 283, 2, 368, 694]
[:mouse_move, 1061, 280, 2, 369, 695]
[:mouse_move, 1056, 276, 2, 370, 696]
[:mouse_move, 1053, 276, 2, 371, 697]
[:mouse_move, 1044, 276, 2, 372, 698]
[:mouse_move, 1037, 276, 2, 373, 700]
[:mouse_move, 1015, 280, 2, 374, 700]
[:mouse_move, 1000, 284, 2, 375, 701]
[:mouse_move, 968, 293, 2, 376, 702]
[:mouse_move, 950, 299, 2, 377, 704]
[:mouse_move, 913, 310, 2, 378, 704]
[:mouse_move, 906, 312, 2, 379, 705]
[:mouse_move, 901, 314, 2, 380, 706]
[:mouse_move, 885, 318, 2, 381, 707]
[:mouse_move, 876, 320, 2, 382, 708]
[:mouse_move, 874, 320, 2, 383, 709]
[:mouse_move, 871, 320, 2, 384, 710]
[:mouse_move, 870, 320, 2, 385, 711]
[:mouse_move, 869, 320, 2, 386, 712]
[:mouse_move, 868, 320, 2, 387, 713]
[:mouse_move, 867, 320, 2, 388, 714]
[:mouse_move, 868, 320, 2, 389, 731]
[:key_down_raw, 96, 0, 2, 390, 745]
[:key_up_raw, 96, 0, 2, 391, 749]
[:key_down_raw, 13, 0, 2, 392, 820]
