replay_version 2.0
stopped_at 2343
seed 100
recorded_at Sun Sep 29 22:15:31 2019
[:mouse_move, 1039, 23, 2, 1, 71]
[:mouse_move, 1005, 37, 2, 2, 72]
[:mouse_move, 862, 94, 2, 3, 73]
[:mouse_move, 791, 114, 2, 4, 74]
[:mouse_move, 664, 128, 2, 5, 75]
[:mouse_move, 610, 128, 2, 6, 76]
[:mouse_move, 577, 121, 2, 7, 77]
[:mouse_move, 566, 117, 2, 8, 78]
[:mouse_move, 566, 115, 2, 9, 90]
[:mouse_move, 566, 110, 2, 10, 91]
[:mouse_move, 566, 104, 2, 11, 91]
[:mouse_move, 566, 98, 2, 12, 92]
[:mouse_move, 567, 92, 2, 13, 93]
[:mouse_move, 567, 85, 2, 14, 94]
[:mouse_move, 567, 83, 2, 15, 95]
[:mouse_move, 568, 78, 2, 16, 96]
[:mouse_move, 568, 77, 2, 17, 97]
[:mouse_move, 568, 76, 2, 18, 97]
[:mouse_move, 568, 75, 2, 19, 98]
[:mouse_move, 567, 75, 2, 20, 102]
[:mouse_move, 566, 74, 2, 21, 103]
[:mouse_move, 565, 74, 2, 22, 106]
[:mouse_move, 565, 73, 2, 23, 106]
[:mouse_move, 565, 72, 2, 24, 110]
[:mouse_move, 565, 71, 2, 25, 111]
[:mouse_move, 565, 70, 2, 26, 112]
[:mouse_move, 567, 69, 2, 27, 112]
[:mouse_move, 569, 67, 2, 28, 114]
[:mouse_move, 571, 66, 2, 29, 114]
[:mouse_move, 577, 65, 2, 30, 115]
[:mouse_move, 581, 64, 2, 31, 116]
[:mouse_move, 584, 64, 2, 32, 116]
[:mouse_move, 589, 64, 2, 33, 117]
[:mouse_move, 592, 63, 2, 34, 118]
[:mouse_move, 597, 63, 2, 35, 118]
[:mouse_move, 601, 62, 2, 36, 119]
[:mouse_move, 605, 62, 2, 37, 120]
[:mouse_move, 610, 61, 2, 38, 120]
[:mouse_move, 613, 61, 2, 39, 121]
[:mouse_move, 617, 61, 2, 40, 122]
[:mouse_move, 620, 60, 2, 41, 122]
[:mouse_move, 622, 60, 2, 42, 123]
[:mouse_move, 627, 60, 2, 43, 124]
[:mouse_move, 632, 60, 2, 44, 125]
[:mouse_move, 635, 60, 2, 45, 126]
[:mouse_move, 638, 59, 2, 46, 126]
[:mouse_move, 640, 59, 2, 47, 127]
[:mouse_move, 642, 59, 2, 48, 129]
[:mouse_move, 644, 59, 2, 49, 129]
[:mouse_move, 650, 58, 2, 50, 130]
[:mouse_move, 655, 57, 2, 51, 131]
[:mouse_move, 657, 57, 2, 52, 132]
[:mouse_move, 661, 56, 2, 53, 133]
[:mouse_move, 665, 55, 2, 54, 134]
[:mouse_move, 671, 54, 2, 55, 135]
[:mouse_move, 673, 54, 2, 56, 136]
[:mouse_move, 677, 54, 2, 57, 137]
[:mouse_move, 680, 54, 2, 58, 138]
[:mouse_move, 684, 54, 2, 59, 139]
[:mouse_move, 686, 54, 2, 60, 140]
[:mouse_move, 688, 54, 2, 61, 141]
[:mouse_move, 690, 54, 2, 62, 141]
[:mouse_move, 691, 54, 2, 63, 142]
[:mouse_move, 694, 54, 2, 64, 143]
[:mouse_move, 696, 54, 2, 65, 144]
[:mouse_move, 698, 54, 2, 66, 146]
[:mouse_move, 699, 55, 2, 67, 147]
[:mouse_move, 700, 55, 2, 68, 149]
[:mouse_move, 700, 56, 2, 69, 176]
[:mouse_move, 700, 57, 2, 70, 176]
[:mouse_move, 701, 58, 2, 71, 177]
[:mouse_move, 701, 60, 2, 72, 178]
[:mouse_move, 702, 65, 2, 73, 179]
[:mouse_move, 702, 69, 2, 74, 180]
[:mouse_move, 702, 75, 2, 75, 181]
[:mouse_move, 700, 79, 2, 76, 182]
[:mouse_move, 689, 88, 2, 77, 183]
[:mouse_move, 682, 91, 2, 78, 184]
[:mouse_move, 663, 98, 2, 79, 185]
[:mouse_move, 651, 100, 2, 80, 186]
[:mouse_move, 626, 105, 2, 81, 187]
[:mouse_move, 612, 108, 2, 82, 188]
[:mouse_move, 600, 108, 2, 83, 189]
[:mouse_move, 595, 109, 2, 84, 189]
[:mouse_move, 586, 109, 2, 85, 190]
[:mouse_move, 577, 110, 2, 86, 191]
[:mouse_move, 572, 110, 2, 87, 192]
[:mouse_move, 569, 110, 2, 88, 193]
[:mouse_move, 567, 110, 2, 89, 194]
[:mouse_move, 564, 110, 2, 90, 196]
[:mouse_move, 562, 110, 2, 91, 197]
[:mouse_move, 561, 110, 2, 92, 197]
[:mouse_move, 558, 110, 2, 93, 198]
[:mouse_move, 557, 110, 2, 94, 199]
[:mouse_move, 556, 110, 2, 95, 199]
[:mouse_move, 555, 110, 2, 96, 200]
[:mouse_move, 554, 110, 2, 97, 201]
[:mouse_move, 553, 110, 2, 98, 203]
[:mouse_move, 555, 110, 2, 99, 206]
[:mouse_move, 556, 110, 2, 100, 207]
[:mouse_move, 560, 110, 2, 101, 208]
[:mouse_move, 562, 110, 2, 102, 209]
[:mouse_move, 566, 110, 2, 103, 209]
[:mouse_move, 569, 110, 2, 104, 210]
[:mouse_move, 572, 110, 2, 105, 211]
[:mouse_move, 579, 110, 2, 106, 212]
[:mouse_move, 582, 110, 2, 107, 213]
[:mouse_move, 590, 110, 2, 108, 215]
[:mouse_move, 597, 110, 2, 109, 216]
[:mouse_move, 601, 110, 2, 110, 216]
[:mouse_move, 605, 110, 2, 111, 217]
[:mouse_move, 612, 110, 2, 112, 218]
[:mouse_move, 614, 110, 2, 113, 219]
[:mouse_move, 620, 110, 2, 114, 220]
[:mouse_move, 622, 110, 2, 115, 220]
[:mouse_move, 627, 110, 2, 116, 221]
[:mouse_move, 633, 110, 2, 117, 222]
[:mouse_move, 635, 110, 2, 118, 223]
[:mouse_move, 640, 110, 2, 119, 224]
[:mouse_move, 643, 110, 2, 120, 224]
[:mouse_move, 644, 110, 2, 121, 225]
[:mouse_move, 647, 110, 2, 122, 226]
[:mouse_move, 649, 110, 2, 123, 226]
[:mouse_move, 653, 109, 2, 124, 227]
[:mouse_move, 656, 109, 2, 125, 228]
[:mouse_move, 659, 109, 2, 126, 229]
[:mouse_move, 662, 109, 2, 127, 230]
[:mouse_move, 668, 109, 2, 128, 231]
[:mouse_move, 671, 109, 2, 129, 232]
[:mouse_move, 673, 109, 2, 130, 232]
[:mouse_move, 678, 109, 2, 131, 233]
[:mouse_move, 681, 109, 2, 132, 234]
[:mouse_move, 686, 109, 2, 133, 235]
[:mouse_move, 689, 109, 2, 134, 236]
[:mouse_move, 692, 109, 2, 135, 236]
[:mouse_move, 694, 110, 2, 136, 237]
[:mouse_move, 696, 110, 2, 137, 238]
[:mouse_move, 698, 110, 2, 138, 238]
[:mouse_move, 699, 110, 2, 139, 239]
[:mouse_move, 700, 110, 2, 140, 240]
[:mouse_move, 702, 110, 2, 141, 241]
[:mouse_move, 703, 110, 2, 142, 242]
[:mouse_move, 704, 110, 2, 143, 245]
[:mouse_move, 704, 111, 2, 144, 290]
[:mouse_move, 695, 111, 2, 145, 347]
[:mouse_move, 691, 111, 2, 146, 348]
[:mouse_move, 689, 111, 2, 147, 348]
[:mouse_move, 687, 111, 2, 148, 349]
[:mouse_move, 685, 111, 2, 149, 350]
[:mouse_move, 684, 111, 2, 150, 351]
[:mouse_move, 683, 112, 2, 151, 353]
[:mouse_move, 683, 114, 2, 152, 365]
[:mouse_move, 684, 114, 2, 153, 366]
[:mouse_move, 684, 116, 2, 154, 367]
[:mouse_move, 686, 118, 2, 155, 368]
[:mouse_move, 686, 119, 2, 156, 369]
[:mouse_move, 687, 120, 2, 157, 370]
[:mouse_move, 687, 121, 2, 158, 371]
[:mouse_move, 687, 122, 2, 159, 374]
[:mouse_move, 682, 121, 2, 160, 378]
[:mouse_move, 678, 120, 2, 161, 379]
[:mouse_move, 668, 118, 2, 162, 380]
[:mouse_move, 660, 117, 2, 163, 381]
[:mouse_move, 653, 115, 2, 164, 382]
[:mouse_move, 628, 111, 2, 165, 383]
[:mouse_move, 619, 110, 2, 166, 384]
[:mouse_move, 609, 109, 2, 167, 384]
[:mouse_move, 604, 109, 2, 168, 385]
[:mouse_move, 596, 109, 2, 169, 385]
[:mouse_move, 586, 109, 2, 170, 387]
[:mouse_move, 581, 110, 2, 171, 388]
[:mouse_move, 574, 112, 2, 172, 388]
[:mouse_move, 571, 113, 2, 173, 389]
[:mouse_move, 567, 115, 2, 174, 390]
[:mouse_move, 562, 120, 2, 175, 391]
[:mouse_move, 559, 123, 2, 176, 392]
[:mouse_move, 556, 126, 2, 177, 392]
[:mouse_move, 553, 130, 2, 178, 393]
[:mouse_move, 550, 135, 2, 179, 394]
[:mouse_move, 547, 139, 2, 180, 394]
[:mouse_move, 545, 143, 2, 181, 395]
[:mouse_move, 543, 148, 2, 182, 396]
[:mouse_move, 540, 156, 2, 183, 397]
[:mouse_move, 539, 161, 2, 184, 398]
[:mouse_move, 537, 171, 2, 185, 399]
[:mouse_move, 537, 177, 2, 186, 400]
[:mouse_move, 537, 179, 2, 187, 400]
[:mouse_move, 536, 183, 2, 188, 401]
[:mouse_move, 536, 188, 2, 189, 402]
[:mouse_move, 536, 197, 2, 190, 403]
[:mouse_move, 538, 202, 2, 191, 404]
[:mouse_move, 541, 206, 2, 192, 405]
[:mouse_move, 544, 211, 2, 193, 405]
[:mouse_move, 548, 216, 2, 194, 406]
[:mouse_move, 557, 226, 2, 195, 407]
[:mouse_move, 566, 233, 2, 196, 408]
[:mouse_move, 575, 238, 2, 197, 409]
[:mouse_move, 583, 241, 2, 198, 409]
[:mouse_move, 591, 245, 2, 199, 410]
[:mouse_move, 602, 248, 2, 200, 411]
[:mouse_move, 613, 250, 2, 201, 411]
[:mouse_move, 625, 251, 2, 202, 412]
[:mouse_move, 637, 253, 2, 203, 413]
[:mouse_move, 650, 254, 2, 204, 413]
[:mouse_move, 663, 254, 2, 205, 414]
[:mouse_move, 669, 254, 2, 206, 415]
[:mouse_move, 680, 254, 2, 207, 415]
[:mouse_move, 690, 252, 2, 208, 416]
[:mouse_move, 699, 250, 2, 209, 417]
[:mouse_move, 707, 247, 2, 210, 417]
[:mouse_move, 714, 244, 2, 211, 418]
[:mouse_move, 720, 241, 2, 212, 419]
[:mouse_move, 726, 238, 2, 213, 419]
[:mouse_move, 730, 235, 2, 214, 420]
[:mouse_move, 734, 232, 2, 215, 421]
[:mouse_move, 741, 225, 2, 216, 422]
[:mouse_move, 744, 221, 2, 217, 423]
[:mouse_move, 747, 216, 2, 218, 424]
[:mouse_move, 748, 213, 2, 219, 425]
[:mouse_move, 751, 206, 2, 220, 426]
[:mouse_move, 751, 203, 2, 221, 427]
[:mouse_move, 750, 196, 2, 222, 428]
[:mouse_move, 748, 193, 2, 223, 429]
[:mouse_move, 740, 184, 2, 224, 430]
[:mouse_move, 735, 180, 2, 225, 431]
[:mouse_move, 726, 171, 2, 226, 432]
[:mouse_move, 722, 169, 2, 227, 432]
[:mouse_move, 710, 160, 2, 228, 433]
[:mouse_move, 691, 149, 2, 229, 434]
[:mouse_move, 680, 144, 2, 230, 435]
[:mouse_move, 663, 137, 2, 231, 436]
[:mouse_move, 652, 134, 2, 232, 437]
[:mouse_move, 642, 131, 2, 233, 438]
[:mouse_move, 634, 130, 2, 234, 438]
[:mouse_move, 624, 128, 2, 235, 439]
[:mouse_move, 616, 127, 2, 236, 440]
[:mouse_move, 608, 126, 2, 237, 440]
[:mouse_move, 594, 126, 2, 238, 441]
[:mouse_move, 586, 126, 2, 239, 442]
[:mouse_move, 573, 127, 2, 240, 443]
[:mouse_move, 566, 129, 2, 241, 444]
[:mouse_move, 559, 132, 2, 242, 444]
[:mouse_move, 553, 135, 2, 243, 445]
[:mouse_move, 548, 138, 2, 244, 446]
[:mouse_move, 543, 142, 2, 245, 446]
[:mouse_move, 539, 146, 2, 246, 447]
[:mouse_move, 536, 150, 2, 247, 448]
[:mouse_move, 529, 158, 2, 248, 449]
[:mouse_move, 527, 162, 2, 249, 450]
[:mouse_move, 525, 170, 2, 250, 451]
[:mouse_move, 524, 183, 2, 251, 453]
[:mouse_move, 524, 187, 2, 252, 454]
[:mouse_move, 525, 192, 2, 253, 455]
[:mouse_move, 528, 197, 2, 254, 456]
[:mouse_move, 532, 202, 2, 255, 457]
[:mouse_move, 536, 204, 2, 256, 458]
[:mouse_move, 549, 212, 2, 257, 459]
[:mouse_move, 558, 216, 2, 258, 460]
[:mouse_move, 583, 223, 2, 259, 461]
[:mouse_move, 597, 226, 2, 260, 462]
[:mouse_move, 616, 228, 2, 261, 463]
[:mouse_move, 627, 228, 2, 262, 464]
[:mouse_move, 636, 228, 2, 263, 465]
[:mouse_move, 643, 227, 2, 264, 465]
[:mouse_move, 645, 226, 2, 265, 466]
[:mouse_move, 649, 225, 2, 266, 467]
[:mouse_move, 652, 224, 2, 267, 467]
[:mouse_move, 654, 223, 2, 268, 468]
[:mouse_move, 655, 222, 2, 269, 469]
[:mouse_move, 656, 222, 2, 270, 469]
[:mouse_move, 657, 221, 2, 271, 470]
[:mouse_move, 657, 220, 2, 272, 480]
[:mouse_move, 657, 219, 2, 273, 481]
[:mouse_move, 657, 218, 2, 274, 481]
[:mouse_move, 656, 215, 2, 275, 482]
[:mouse_move, 655, 212, 2, 276, 483]
[:mouse_move, 653, 207, 2, 277, 484]
[:mouse_move, 652, 205, 2, 278, 485]
[:mouse_move, 651, 201, 2, 279, 486]
[:mouse_move, 650, 200, 2, 280, 487]
[:mouse_move, 650, 198, 2, 281, 488]
[:mouse_button_pressed, 1, 0, 1, 282, 500]
[:mouse_button_up, 1, 0, 1, 283, 509]
[:mouse_move, 650, 199, 2, 284, 581]
[:mouse_move, 649, 202, 2, 285, 582]
[:mouse_move, 646, 214, 2, 286, 583]
[:mouse_move, 643, 223, 2, 287, 584]
[:mouse_move, 637, 245, 2, 288, 585]
[:mouse_move, 633, 257, 2, 289, 586]
[:mouse_move, 627, 282, 2, 290, 587]
[:mouse_move, 625, 294, 2, 291, 588]
[:mouse_move, 621, 312, 2, 292, 589]
[:mouse_move, 618, 320, 2, 293, 590]
[:mouse_move, 613, 337, 2, 294, 591]
[:mouse_move, 611, 343, 2, 295, 592]
[:mouse_move, 607, 349, 2, 296, 593]
[:mouse_move, 602, 359, 2, 297, 594]
[:mouse_move, 599, 363, 2, 298, 595]
[:mouse_move, 592, 371, 2, 299, 596]
[:mouse_move, 589, 375, 2, 300, 597]
[:mouse_move, 581, 382, 2, 301, 598]
[:mouse_move, 577, 385, 2, 302, 599]
[:mouse_move, 569, 390, 2, 303, 600]
[:mouse_move, 564, 393, 2, 304, 601]
[:mouse_move, 556, 398, 2, 305, 602]
[:mouse_move, 552, 399, 2, 306, 603]
[:mouse_move, 542, 403, 2, 307, 604]
[:mouse_move, 538, 405, 2, 308, 605]
[:mouse_move, 528, 408, 2, 309, 606]
[:mouse_move, 523, 410, 2, 310, 607]
[:mouse_move, 515, 414, 2, 311, 608]
[:mouse_move, 510, 415, 2, 312, 609]
[:mouse_move, 502, 419, 2, 313, 610]
[:mouse_move, 500, 420, 2, 314, 611]
[:mouse_move, 495, 424, 2, 315, 612]
[:mouse_move, 490, 427, 2, 316, 613]
[:mouse_move, 488, 428, 2, 317, 614]
[:mouse_move, 486, 431, 2, 318, 615]
[:mouse_move, 484, 434, 2, 319, 616]
[:mouse_move, 483, 435, 2, 320, 617]
[:mouse_move, 483, 436, 2, 321, 618]
[:mouse_move, 483, 437, 2, 322, 619]
[:mouse_move, 483, 438, 2, 323, 620]
[:mouse_move, 483, 439, 2, 324, 621]
[:mouse_move, 483, 440, 2, 325, 624]
[:mouse_move, 484, 440, 2, 326, 625]
[:mouse_move, 485, 441, 2, 327, 628]
[:mouse_move, 486, 441, 2, 328, 629]
[:mouse_move, 487, 441, 2, 329, 630]
[:mouse_move, 488, 441, 2, 330, 631]
[:mouse_move, 490, 442, 2, 331, 633]
[:mouse_move, 492, 442, 2, 332, 635]
[:mouse_move, 494, 442, 2, 333, 637]
[:mouse_move, 496, 442, 2, 334, 639]
[:mouse_move, 498, 442, 2, 335, 641]
[:mouse_move, 499, 442, 2, 336, 643]
[:mouse_move, 501, 442, 2, 337, 644]
[:mouse_move, 504, 442, 2, 338, 645]
[:mouse_move, 506, 442, 2, 339, 646]
[:mouse_move, 510, 442, 2, 340, 647]
[:mouse_move, 512, 442, 2, 341, 648]
[:mouse_move, 513, 442, 2, 342, 649]
[:mouse_move, 515, 442, 2, 343, 650]
[:mouse_move, 516, 442, 2, 344, 651]
[:mouse_move, 517, 442, 2, 345, 653]
[:mouse_move, 518, 442, 2, 346, 658]
[:mouse_move, 520, 442, 2, 347, 660]
[:mouse_move, 521, 442, 2, 348, 661]
[:mouse_move, 524, 442, 2, 349, 662]
[:mouse_move, 526, 442, 2, 350, 663]
[:mouse_move, 528, 442, 2, 351, 664]
[:mouse_move, 530, 442, 2, 352, 665]
[:mouse_move, 531, 442, 2, 353, 666]
[:mouse_move, 532, 442, 2, 354, 667]
[:mouse_move, 533, 442, 2, 355, 668]
[:mouse_move, 534, 442, 2, 356, 671]
[:mouse_move, 533, 442, 2, 357, 676]
[:mouse_move, 528, 442, 2, 358, 677]
[:mouse_move, 525, 442, 2, 359, 678]
[:mouse_move, 518, 442, 2, 360, 679]
[:mouse_move, 515, 442, 2, 361, 680]
[:mouse_move, 508, 442, 2, 362, 681]
[:mouse_move, 506, 442, 2, 363, 682]
[:mouse_move, 504, 442, 2, 364, 683]
[:mouse_move, 503, 442, 2, 365, 684]
[:mouse_button_pressed, 1, 0, 1, 366, 698]
[:mouse_button_up, 1, 0, 1, 367, 703]
[:mouse_move, 502, 444, 2, 368, 780]
[:mouse_move, 501, 446, 2, 369, 781]
[:mouse_move, 497, 451, 2, 370, 782]
[:mouse_move, 495, 453, 2, 371, 783]
[:mouse_move, 493, 455, 2, 372, 784]
[:mouse_move, 492, 457, 2, 373, 785]
[:mouse_move, 490, 460, 2, 374, 786]
[:mouse_move, 496, 456, 2, 375, 787]
[:mouse_move, 513, 441, 2, 376, 788]
[:mouse_move, 517, 437, 2, 377, 789]
[:mouse_move, 527, 429, 2, 378, 790]
[:mouse_move, 529, 427, 2, 379, 791]
[:mouse_move, 528, 428, 2, 380, 792]
[:mouse_move, 504, 456, 2, 381, 793]
[:mouse_move, 498, 462, 2, 382, 794]
[:mouse_move, 490, 471, 2, 383, 795]
[:mouse_move, 483, 477, 2, 384, 796]
[:mouse_move, 489, 468, 2, 385, 797]
[:mouse_move, 495, 461, 2, 386, 798]
[:mouse_move, 502, 454, 2, 387, 799]
[:mouse_move, 505, 450, 2, 388, 800]
[:mouse_move, 511, 445, 2, 389, 801]
[:mouse_move, 511, 446, 2, 390, 802]
[:mouse_move, 508, 450, 2, 391, 803]
[:mouse_move, 507, 452, 2, 392, 804]
[:mouse_move, 505, 454, 2, 393, 809]
[:mouse_move, 504, 455, 2, 394, 810]
[:mouse_move, 503, 457, 2, 395, 811]
[:mouse_move, 504, 454, 2, 396, 813]
[:mouse_move, 505, 452, 2, 397, 814]
[:mouse_move, 507, 450, 2, 398, 815]
[:mouse_move, 508, 449, 2, 399, 816]
[:mouse_move, 508, 448, 2, 400, 817]
[:mouse_move, 509, 448, 2, 401, 818]
[:mouse_move, 509, 447, 2, 402, 819]
[:mouse_move, 509, 446, 2, 403, 821]
[:mouse_move, 509, 445, 2, 404, 824]
[:mouse_move, 509, 442, 2, 405, 826]
[:mouse_move, 509, 440, 2, 406, 827]
[:mouse_move, 510, 428, 2, 407, 828]
[:mouse_move, 512, 419, 2, 408, 829]
[:mouse_move, 524, 385, 2, 409, 830]
[:mouse_move, 531, 366, 2, 410, 831]
[:mouse_move, 546, 332, 2, 411, 832]
[:mouse_move, 554, 311, 2, 412, 833]
[:mouse_move, 571, 269, 2, 413, 834]
[:mouse_move, 579, 250, 2, 414, 835]
[:mouse_move, 592, 215, 2, 415, 836]
[:mouse_move, 598, 202, 2, 416, 837]
[:mouse_move, 601, 189, 2, 417, 838]
[:mouse_move, 605, 172, 2, 418, 839]
[:mouse_move, 609, 154, 2, 419, 840]
[:mouse_move, 610, 151, 2, 420, 841]
[:mouse_move, 612, 136, 2, 421, 842]
[:mouse_move, 613, 130, 2, 422, 843]
[:mouse_move, 613, 126, 2, 423, 844]
[:mouse_move, 614, 116, 2, 424, 845]
[:mouse_move, 614, 111, 2, 425, 846]
[:mouse_move, 614, 105, 2, 426, 847]
[:mouse_move, 614, 102, 2, 427, 848]
[:mouse_move, 614, 97, 2, 428, 849]
[:mouse_move, 614, 96, 2, 429, 850]
[:mouse_move, 614, 95, 2, 430, 851]
[:mouse_move, 614, 94, 2, 431, 852]
[:mouse_move, 614, 92, 2, 432, 853]
[:mouse_move, 613, 92, 2, 433, 854]
[:mouse_move, 612, 91, 2, 434, 855]
[:mouse_move, 611, 91, 2, 435, 856]
[:mouse_move, 607, 91, 2, 436, 857]
[:mouse_move, 605, 91, 2, 437, 858]
[:mouse_move, 601, 93, 2, 438, 859]
[:mouse_move, 600, 94, 2, 439, 860]
[:mouse_move, 598, 96, 2, 440, 861]
[:mouse_move, 597, 96, 2, 441, 862]
[:mouse_move, 596, 97, 2, 442, 863]
[:mouse_move, 596, 99, 2, 443, 865]
[:mouse_move, 596, 100, 2, 444, 866]
[:mouse_move, 597, 104, 2, 445, 867]
[:mouse_move, 599, 106, 2, 446, 868]
[:mouse_move, 605, 110, 2, 447, 869]
[:mouse_move, 609, 113, 2, 448, 870]
[:mouse_move, 618, 116, 2, 449, 871]
[:mouse_move, 624, 118, 2, 450, 872]
[:mouse_move, 629, 119, 2, 451, 873]
[:mouse_move, 638, 121, 2, 452, 874]
[:mouse_move, 642, 123, 2, 453, 875]
[:mouse_move, 651, 124, 2, 454, 876]
[:mouse_move, 655, 124, 2, 455, 877]
[:mouse_move, 662, 125, 2, 456, 878]
[:mouse_move, 665, 125, 2, 457, 879]
[:mouse_move, 673, 125, 2, 458, 880]
[:mouse_move, 676, 125, 2, 459, 881]
[:mouse_move, 681, 123, 2, 460, 882]
[:mouse_move, 684, 123, 2, 461, 883]
[:mouse_move, 688, 120, 2, 462, 884]
[:mouse_move, 690, 118, 2, 463, 885]
[:mouse_move, 694, 115, 2, 464, 886]
[:mouse_move, 695, 113, 2, 465, 887]
[:mouse_move, 696, 110, 2, 466, 888]
[:mouse_move, 697, 108, 2, 467, 889]
[:mouse_move, 698, 106, 2, 468, 890]
[:mouse_move, 698, 105, 2, 469, 891]
[:mouse_move, 698, 103, 2, 470, 892]
[:mouse_move, 698, 102, 2, 471, 893]
[:mouse_move, 698, 99, 2, 472, 894]
[:mouse_move, 698, 98, 2, 473, 895]
[:mouse_move, 695, 94, 2, 474, 896]
[:mouse_move, 693, 91, 2, 475, 897]
[:mouse_move, 686, 85, 2, 476, 898]
[:mouse_move, 682, 83, 2, 477, 899]
[:mouse_move, 680, 82, 2, 478, 900]
[:mouse_move, 671, 76, 2, 479, 901]
[:mouse_move, 667, 75, 2, 480, 902]
[:mouse_move, 652, 71, 2, 481, 903]
[:mouse_move, 643, 71, 2, 482, 904]
[:mouse_move, 621, 69, 2, 483, 905]
[:mouse_move, 615, 69, 2, 484, 906]
[:mouse_move, 595, 69, 2, 485, 907]
[:mouse_move, 586, 69, 2, 486, 908]
[:mouse_move, 568, 71, 2, 487, 909]
[:mouse_move, 556, 74, 2, 488, 910]
[:mouse_move, 547, 79, 2, 489, 911]
[:mouse_move, 541, 81, 2, 490, 912]
[:mouse_move, 532, 89, 2, 491, 913]
[:mouse_move, 529, 93, 2, 492, 914]
[:mouse_move, 527, 98, 2, 493, 915]
[:mouse_move, 524, 109, 2, 494, 916]
[:mouse_move, 524, 128, 2, 495, 917]
[:mouse_move, 527, 132, 2, 496, 918]
[:mouse_move, 536, 143, 2, 497, 919]
[:mouse_move, 542, 147, 2, 498, 920]
[:mouse_move, 558, 155, 2, 499, 921]
[:mouse_move, 566, 156, 2, 500, 922]
[:mouse_move, 585, 156, 2, 501, 923]
[:mouse_move, 594, 156, 2, 502, 924]
[:mouse_move, 617, 154, 2, 503, 925]
[:mouse_move, 622, 153, 2, 504, 926]
[:mouse_move, 632, 150, 2, 505, 927]
[:mouse_move, 648, 143, 2, 506, 928]
[:mouse_move, 655, 139, 2, 507, 929]
[:mouse_move, 667, 131, 2, 508, 930]
[:mouse_move, 673, 127, 2, 509, 931]
[:mouse_move, 681, 119, 2, 510, 932]
[:mouse_move, 684, 115, 2, 511, 933]
[:mouse_move, 688, 110, 2, 512, 934]
[:mouse_move, 690, 107, 2, 513, 935]
[:mouse_move, 692, 101, 2, 514, 936]
[:mouse_move, 693, 99, 2, 515, 937]
[:mouse_move, 694, 93, 2, 516, 938]
[:mouse_move, 693, 90, 2, 517, 939]
[:mouse_move, 683, 82, 2, 518, 940]
[:mouse_move, 677, 79, 2, 519, 941]
[:mouse_move, 660, 72, 2, 520, 942]
[:mouse_move, 648, 70, 2, 521, 943]
[:mouse_move, 632, 67, 2, 522, 944]
[:mouse_move, 623, 66, 2, 523, 945]
[:mouse_move, 608, 66, 2, 524, 946]
[:mouse_move, 602, 66, 2, 525, 947]
[:mouse_move, 591, 68, 2, 526, 948]
[:mouse_move, 587, 70, 2, 527, 949]
[:mouse_move, 579, 77, 2, 528, 950]
[:mouse_move, 572, 85, 2, 529, 951]
[:mouse_move, 566, 94, 2, 530, 952]
[:mouse_move, 563, 101, 2, 531, 954]
[:mouse_move, 558, 117, 2, 532, 955]
[:mouse_move, 555, 131, 2, 533, 956]
[:mouse_move, 555, 141, 2, 534, 957]
[:mouse_move, 555, 147, 2, 535, 958]
[:mouse_move, 558, 155, 2, 536, 959]
[:mouse_move, 561, 159, 2, 537, 960]
[:mouse_move, 569, 167, 2, 538, 961]
[:mouse_move, 575, 171, 2, 539, 962]
[:mouse_move, 588, 178, 2, 540, 963]
[:mouse_move, 593, 182, 2, 541, 964]
[:mouse_move, 604, 187, 2, 542, 965]
[:mouse_move, 609, 191, 2, 543, 966]
[:mouse_move, 618, 194, 2, 544, 967]
[:mouse_move, 622, 196, 2, 545, 968]
[:mouse_move, 630, 199, 2, 546, 969]
[:mouse_move, 635, 200, 2, 547, 970]
[:mouse_move, 642, 201, 2, 548, 971]
[:mouse_move, 651, 202, 2, 549, 973]
[:mouse_move, 658, 202, 2, 550, 974]
[:mouse_move, 662, 200, 2, 551, 975]
[:mouse_move, 664, 199, 2, 552, 976]
[:mouse_move, 666, 198, 2, 553, 977]
[:mouse_move, 667, 198, 2, 554, 980]
[:mouse_move, 667, 197, 2, 555, 985]
[:mouse_button_pressed, 1, 0, 1, 556, 992]
[:mouse_button_up, 1, 0, 1, 557, 1000]
[:mouse_move, 671, 212, 2, 558, 1043]
[:mouse_move, 678, 231, 2, 559, 1044]
[:mouse_move, 698, 290, 2, 560, 1045]
[:mouse_move, 702, 304, 2, 561, 1046]
[:mouse_move, 712, 341, 2, 562, 1047]
[:mouse_move, 717, 358, 2, 563, 1048]
[:mouse_move, 721, 382, 2, 564, 1049]
[:mouse_move, 722, 391, 2, 565, 1050]
[:mouse_move, 723, 398, 2, 566, 1051]
[:mouse_move, 723, 410, 2, 567, 1052]
[:mouse_move, 720, 426, 2, 568, 1053]
[:mouse_move, 714, 439, 2, 569, 1054]
[:mouse_move, 705, 453, 2, 570, 1055]
[:mouse_move, 694, 464, 2, 571, 1056]
[:mouse_move, 670, 474, 2, 572, 1057]
[:mouse_move, 654, 476, 2, 573, 1058]
[:mouse_move, 635, 476, 2, 574, 1059]
[:mouse_move, 606, 476, 2, 575, 1060]
[:mouse_move, 589, 476, 2, 576, 1061]
[:mouse_move, 568, 476, 2, 577, 1062]
[:mouse_move, 556, 476, 2, 578, 1063]
[:mouse_move, 545, 476, 2, 579, 1064]
[:mouse_move, 536, 476, 2, 580, 1065]
[:mouse_move, 524, 476, 2, 581, 1066]
[:mouse_move, 522, 476, 2, 582, 1067]
[:mouse_move, 519, 476, 2, 583, 1068]
[:mouse_move, 518, 476, 2, 584, 1069]
[:mouse_move, 517, 476, 2, 585, 1071]
[:mouse_move, 516, 475, 2, 586, 1072]
[:mouse_move, 515, 473, 2, 587, 1074]
[:mouse_move, 514, 472, 2, 588, 1075]
[:mouse_move, 512, 469, 2, 589, 1076]
[:mouse_move, 512, 468, 2, 590, 1077]
[:mouse_move, 510, 465, 2, 591, 1078]
[:mouse_move, 509, 463, 2, 592, 1079]
[:mouse_move, 508, 460, 2, 593, 1080]
[:mouse_move, 507, 459, 2, 594, 1081]
[:mouse_move, 507, 457, 2, 595, 1082]
[:mouse_move, 506, 456, 2, 596, 1083]
[:mouse_move, 506, 455, 2, 597, 1084]
[:mouse_move, 505, 455, 2, 598, 1085]
[:mouse_move, 507, 454, 2, 599, 1097]
[:mouse_move, 510, 452, 2, 600, 1098]
[:mouse_move, 516, 449, 2, 601, 1099]
[:mouse_move, 521, 446, 2, 602, 1100]
[:mouse_move, 533, 440, 2, 603, 1101]
[:mouse_move, 539, 437, 2, 604, 1102]
[:mouse_move, 547, 434, 2, 605, 1103]
[:mouse_move, 551, 433, 2, 606, 1104]
[:mouse_move, 557, 432, 2, 607, 1105]
[:mouse_move, 559, 432, 2, 608, 1106]
[:mouse_move, 560, 432, 2, 609, 1107]
[:mouse_move, 561, 433, 2, 610, 1108]
[:mouse_move, 561, 434, 2, 611, 1109]
[:mouse_move, 561, 437, 2, 612, 1110]
[:mouse_move, 561, 438, 2, 613, 1111]
[:mouse_move, 561, 440, 2, 614, 1112]
[:mouse_move, 561, 441, 2, 615, 1113]
[:mouse_move, 561, 443, 2, 616, 1114]
[:mouse_move, 560, 446, 2, 617, 1116]
[:mouse_move, 560, 447, 2, 618, 1117]
[:mouse_move, 560, 448, 2, 619, 1118]
[:mouse_move, 560, 449, 2, 620, 1119]
[:mouse_move, 559, 450, 2, 621, 1122]
[:mouse_move, 560, 449, 2, 622, 1129]
[:mouse_move, 563, 448, 2, 623, 1130]
[:mouse_move, 565, 447, 2, 624, 1131]
[:mouse_move, 566, 446, 2, 625, 1132]
[:mouse_move, 569, 445, 2, 626, 1133]
[:mouse_move, 572, 444, 2, 627, 1134]
[:mouse_move, 574, 444, 2, 628, 1135]
[:mouse_move, 575, 444, 2, 629, 1136]
[:mouse_move, 579, 444, 2, 630, 1137]
[:mouse_move, 582, 444, 2, 631, 1138]
[:mouse_move, 584, 444, 2, 632, 1139]
[:mouse_move, 591, 444, 2, 633, 1140]
[:mouse_move, 593, 444, 2, 634, 1141]
[:mouse_move, 598, 445, 2, 635, 1142]
[:mouse_move, 600, 446, 2, 636, 1143]
[:mouse_move, 603, 446, 2, 637, 1144]
[:mouse_move, 605, 447, 2, 638, 1145]
[:mouse_move, 606, 448, 2, 639, 1146]
[:mouse_move, 607, 448, 2, 640, 1147]
[:mouse_move, 607, 449, 2, 641, 1148]
[:mouse_move, 608, 449, 2, 642, 1149]
[:mouse_move, 608, 450, 2, 643, 1152]
[:mouse_move, 608, 451, 2, 644, 1155]
[:mouse_move, 607, 451, 2, 645, 1167]
[:mouse_move, 605, 451, 2, 646, 1168]
[:mouse_move, 600, 450, 2, 647, 1169]
[:mouse_move, 591, 449, 2, 648, 1170]
[:mouse_move, 587, 449, 2, 649, 1171]
[:mouse_move, 580, 449, 2, 650, 1172]
[:mouse_move, 578, 448, 2, 651, 1173]
[:mouse_move, 575, 448, 2, 652, 1174]
[:mouse_move, 574, 448, 2, 653, 1175]
[:mouse_move, 574, 447, 2, 654, 1178]
[:mouse_move, 575, 447, 2, 655, 1179]
[:mouse_move, 576, 447, 2, 656, 1180]
[:mouse_move, 580, 446, 2, 657, 1181]
[:mouse_move, 586, 446, 2, 658, 1182]
[:mouse_move, 589, 446, 2, 659, 1183]
[:mouse_move, 594, 446, 2, 660, 1184]
[:mouse_move, 597, 446, 2, 661, 1185]
[:mouse_move, 601, 446, 2, 662, 1186]
[:mouse_move, 603, 446, 2, 663, 1187]
[:mouse_move, 604, 446, 2, 664, 1188]
[:mouse_move, 605, 447, 2, 665, 1190]
[:mouse_move, 606, 447, 2, 666, 1192]
[:mouse_move, 608, 447, 2, 667, 1194]
[:mouse_move, 609, 447, 2, 668, 1196]
[:mouse_move, 610, 448, 2, 669, 1197]
[:mouse_move, 612, 449, 2, 670, 1198]
[:mouse_move, 614, 449, 2, 671, 1199]
[:mouse_move, 615, 449, 2, 672, 1200]
[:mouse_move, 617, 450, 2, 673, 1201]
[:mouse_move, 618, 450, 2, 674, 1202]
[:mouse_move, 619, 451, 2, 675, 1203]
[:mouse_move, 620, 451, 2, 676, 1210]
[:mouse_button_pressed, 1, 0, 1, 677, 1226]
[:mouse_button_up, 1, 0, 1, 678, 1233]
[:mouse_move, 615, 449, 2, 679, 1295]
[:mouse_move, 607, 447, 2, 680, 1296]
[:mouse_move, 605, 447, 2, 681, 1297]
[:mouse_move, 600, 445, 2, 682, 1298]
[:mouse_move, 597, 445, 2, 683, 1299]
[:mouse_move, 595, 444, 2, 684, 1300]
[:mouse_move, 594, 444, 2, 685, 1301]
[:mouse_button_pressed, 1, 0, 1, 686, 1307]
[:mouse_button_up, 1, 0, 1, 687, 1317]
[:mouse_move, 595, 444, 2, 688, 1343]
[:mouse_move, 602, 444, 2, 689, 1344]
[:mouse_move, 606, 445, 2, 690, 1345]
[:mouse_move, 617, 445, 2, 691, 1346]
[:mouse_move, 622, 445, 2, 692, 1347]
[:mouse_move, 633, 445, 2, 693, 1348]
[:mouse_move, 635, 445, 2, 694, 1349]
[:mouse_move, 642, 446, 2, 695, 1350]
[:mouse_move, 645, 446, 2, 696, 1351]
[:mouse_move, 648, 447, 2, 697, 1352]
[:mouse_move, 649, 447, 2, 698, 1353]
[:mouse_move, 651, 447, 2, 699, 1354]
[:mouse_move, 652, 447, 2, 700, 1356]
[:mouse_move, 652, 448, 2, 701, 1357]
[:mouse_move, 653, 448, 2, 702, 1361]
[:mouse_move, 654, 448, 2, 703, 1363]
[:mouse_move, 656, 448, 2, 704, 1365]
[:mouse_move, 657, 448, 2, 705, 1366]
[:mouse_move, 659, 448, 2, 706, 1367]
[:mouse_move, 660, 448, 2, 707, 1368]
[:mouse_move, 661, 448, 2, 708, 1369]
[:mouse_move, 661, 449, 2, 709, 1370]
[:mouse_move, 662, 449, 2, 710, 1371]
[:mouse_button_pressed, 1, 0, 1, 711, 1388]
[:mouse_button_up, 1, 0, 1, 712, 1396]
[:mouse_move, 662, 441, 2, 713, 1555]
[:mouse_move, 664, 434, 2, 714, 1556]
[:mouse_move, 669, 398, 2, 715, 1557]
[:mouse_move, 671, 349, 2, 716, 1558]
[:mouse_move, 673, 289, 2, 717, 1559]
[:mouse_move, 675, 248, 2, 718, 1560]
[:mouse_move, 675, 222, 2, 719, 1561]
[:mouse_move, 675, 199, 2, 720, 1562]
[:mouse_move, 675, 167, 2, 721, 1563]
[:mouse_move, 675, 157, 2, 722, 1564]
[:mouse_move, 670, 144, 2, 723, 1565]
[:mouse_move, 668, 139, 2, 724, 1566]
[:mouse_move, 664, 136, 2, 725, 1567]
[:mouse_move, 655, 129, 2, 726, 1568]
[:mouse_move, 651, 126, 2, 727, 1569]
[:mouse_move, 641, 120, 2, 728, 1570]
[:mouse_move, 633, 116, 2, 729, 1571]
[:mouse_move, 628, 115, 2, 730, 1572]
[:mouse_move, 624, 113, 2, 731, 1573]
[:mouse_move, 617, 111, 2, 732, 1574]
[:mouse_move, 612, 110, 2, 733, 1575]
[:mouse_move, 604, 110, 2, 734, 1576]
[:mouse_move, 594, 110, 2, 735, 1577]
[:mouse_move, 591, 110, 2, 736, 1578]
[:mouse_move, 588, 110, 2, 737, 1579]
[:mouse_move, 587, 110, 2, 738, 1580]
[:mouse_move, 586, 110, 2, 739, 1581]
[:mouse_move, 587, 110, 2, 740, 1585]
[:mouse_move, 589, 110, 2, 741, 1586]
[:mouse_move, 595, 110, 2, 742, 1587]
[:mouse_move, 599, 111, 2, 743, 1588]
[:mouse_move, 607, 112, 2, 744, 1589]
[:mouse_move, 613, 112, 2, 745, 1590]
[:mouse_move, 623, 113, 2, 746, 1591]
[:mouse_move, 625, 114, 2, 747, 1592]
[:mouse_move, 634, 114, 2, 748, 1593]
[:mouse_move, 637, 114, 2, 749, 1594]
[:mouse_move, 645, 114, 2, 750, 1595]
[:mouse_move, 649, 114, 2, 751, 1596]
[:mouse_move, 656, 113, 2, 752, 1597]
[:mouse_move, 660, 113, 2, 753, 1598]
[:mouse_move, 663, 113, 2, 754, 1599]
[:mouse_move, 671, 113, 2, 755, 1600]
[:mouse_move, 679, 113, 2, 756, 1601]
[:mouse_move, 683, 113, 2, 757, 1602]
[:mouse_move, 687, 114, 2, 758, 1603]
[:mouse_move, 692, 115, 2, 759, 1604]
[:mouse_move, 695, 115, 2, 760, 1605]
[:mouse_move, 699, 116, 2, 761, 1606]
[:mouse_move, 701, 116, 2, 762, 1607]
[:mouse_move, 704, 116, 2, 763, 1608]
[:mouse_move, 705, 116, 2, 764, 1609]
[:mouse_move, 707, 116, 2, 765, 1610]
[:mouse_move, 708, 117, 2, 766, 1612]
[:mouse_move, 709, 117, 2, 767, 1613]
[:mouse_move, 703, 112, 2, 768, 1645]
[:mouse_move, 699, 109, 2, 769, 1646]
[:mouse_move, 671, 93, 2, 770, 1647]
[:mouse_move, 655, 86, 2, 771, 1648]
[:mouse_move, 628, 75, 2, 772, 1649]
[:mouse_move, 612, 70, 2, 773, 1650]
[:mouse_move, 592, 66, 2, 774, 1651]
[:mouse_move, 582, 64, 2, 775, 1652]
[:mouse_move, 579, 63, 2, 776, 1653]
[:mouse_move, 568, 61, 2, 777, 1654]
[:mouse_move, 564, 61, 2, 778, 1655]
[:mouse_move, 560, 60, 2, 779, 1656]
[:mouse_move, 559, 60, 2, 780, 1657]
[:mouse_move, 557, 59, 2, 781, 1658]
[:mouse_move, 558, 59, 2, 782, 1664]
[:mouse_move, 559, 59, 2, 783, 1665]
[:mouse_move, 562, 59, 2, 784, 1666]
[:mouse_move, 564, 59, 2, 785, 1667]
[:mouse_move, 571, 60, 2, 786, 1668]
[:mouse_move, 574, 60, 2, 787, 1669]
[:mouse_move, 580, 60, 2, 788, 1670]
[:mouse_move, 586, 60, 2, 789, 1671]
[:mouse_move, 593, 60, 2, 790, 1672]
[:mouse_move, 597, 61, 2, 791, 1673]
[:mouse_move, 604, 61, 2, 792, 1674]
[:mouse_move, 607, 61, 2, 793, 1675]
[:mouse_move, 614, 62, 2, 794, 1676]
[:mouse_move, 617, 62, 2, 795, 1677]
[:mouse_move, 623, 63, 2, 796, 1678]
[:mouse_move, 627, 63, 2, 797, 1679]
[:mouse_move, 629, 64, 2, 798, 1680]
[:mouse_move, 637, 64, 2, 799, 1681]
[:mouse_move, 640, 65, 2, 800, 1682]
[:mouse_move, 648, 65, 2, 801, 1683]
[:mouse_move, 650, 65, 2, 802, 1684]
[:mouse_move, 657, 65, 2, 803, 1685]
[:mouse_move, 663, 65, 2, 804, 1686]
[:mouse_move, 670, 65, 2, 805, 1687]
[:mouse_move, 673, 65, 2, 806, 1688]
[:mouse_move, 681, 66, 2, 807, 1689]
[:mouse_move, 685, 66, 2, 808, 1690]
[:mouse_move, 691, 67, 2, 809, 1691]
[:mouse_move, 694, 67, 2, 810, 1692]
[:mouse_move, 698, 67, 2, 811, 1693]
[:mouse_move, 700, 68, 2, 812, 1694]
[:mouse_move, 702, 68, 2, 813, 1695]
[:mouse_move, 703, 68, 2, 814, 1696]
[:mouse_move, 704, 68, 2, 815, 1699]
[:mouse_move, 703, 75, 2, 816, 1708]
[:mouse_move, 702, 81, 2, 817, 1709]
[:mouse_move, 697, 101, 2, 818, 1710]
[:mouse_move, 696, 106, 2, 819, 1711]
[:mouse_move, 691, 122, 2, 820, 1712]
[:mouse_move, 687, 129, 2, 821, 1713]
[:mouse_move, 681, 142, 2, 822, 1714]
[:mouse_move, 679, 147, 2, 823, 1715]
[:mouse_move, 674, 156, 2, 824, 1716]
[:mouse_move, 674, 158, 2, 825, 1717]
[:mouse_move, 670, 166, 2, 826, 1718]
[:mouse_move, 669, 169, 2, 827, 1719]
[:mouse_move, 666, 177, 2, 828, 1720]
[:mouse_move, 665, 182, 2, 829, 1721]
[:mouse_move, 663, 189, 2, 830, 1722]
[:mouse_move, 662, 193, 2, 831, 1723]
[:mouse_move, 660, 199, 2, 832, 1724]
[:mouse_move, 660, 201, 2, 833, 1725]
[:mouse_move, 659, 202, 2, 834, 1726]
[:mouse_move, 658, 203, 2, 835, 1727]
[:mouse_move, 658, 202, 2, 836, 1733]
[:mouse_move, 658, 201, 2, 837, 1734]
[:mouse_move, 658, 199, 2, 838, 1735]
[:mouse_move, 658, 197, 2, 839, 1736]
[:mouse_move, 658, 196, 2, 840, 1737]
[:mouse_move, 658, 195, 2, 841, 1739]
[:mouse_button_pressed, 1, 0, 1, 842, 1739]
[:mouse_button_up, 1, 0, 1, 843, 1746]
[:mouse_move, 662, 207, 2, 844, 1794]
[:mouse_move, 668, 224, 2, 845, 1795]
[:mouse_move, 672, 238, 2, 846, 1796]
[:mouse_move, 686, 283, 2, 847, 1797]
[:mouse_move, 702, 338, 2, 848, 1798]
[:mouse_move, 705, 353, 2, 849, 1799]
[:mouse_move, 713, 382, 2, 850, 1800]
[:mouse_move, 717, 398, 2, 851, 1801]
[:mouse_move, 721, 419, 2, 852, 1802]
[:mouse_move, 722, 426, 2, 853, 1803]
[:mouse_move, 723, 435, 2, 854, 1804]
[:mouse_move, 724, 437, 2, 855, 1805]
[:mouse_move, 725, 443, 2, 856, 1806]
[:mouse_move, 725, 444, 2, 857, 1807]
[:mouse_move, 726, 447, 2, 858, 1808]
[:mouse_move, 727, 448, 2, 859, 1809]
[:mouse_move, 727, 449, 2, 860, 1811]
[:mouse_move, 728, 449, 2, 861, 1812]
[:mouse_move, 729, 450, 2, 862, 1815]
[:mouse_move, 730, 450, 2, 863, 1817]
[:mouse_move, 731, 450, 2, 864, 1818]
[:mouse_move, 732, 450, 2, 865, 1819]
[:mouse_move, 733, 449, 2, 866, 1820]
[:mouse_move, 734, 449, 2, 867, 1821]
[:mouse_move, 734, 448, 2, 868, 1822]
[:mouse_move, 735, 448, 2, 869, 1823]
[:mouse_move, 736, 447, 2, 870, 1825]
[:mouse_button_pressed, 1, 0, 1, 871, 1835]
[:mouse_button_up, 1, 0, 1, 872, 1843]
[:mouse_move, 737, 447, 2, 873, 1876]
[:mouse_move, 735, 447, 2, 874, 1886]
[:mouse_move, 734, 447, 2, 875, 1887]
[:mouse_move, 730, 446, 2, 876, 1888]
[:mouse_move, 726, 445, 2, 877, 1889]
[:mouse_move, 719, 445, 2, 878, 1890]
[:mouse_move, 716, 444, 2, 879, 1891]
[:mouse_move, 713, 444, 2, 880, 1892]
[:mouse_move, 709, 444, 2, 881, 1893]
[:mouse_move, 708, 443, 2, 882, 1894]
[:mouse_button_pressed, 1, 0, 1, 883, 1914]
[:mouse_button_up, 1, 0, 1, 884, 1923]
[:mouse_move, 707, 442, 2, 885, 1971]
[:mouse_move, 702, 432, 2, 886, 1972]
[:mouse_move, 695, 416, 2, 887, 1973]
[:mouse_move, 677, 373, 2, 888, 1974]
[:mouse_move, 671, 360, 2, 889, 1975]
[:mouse_move, 666, 351, 2, 890, 1976]
[:mouse_move, 658, 334, 2, 891, 1977]
[:mouse_move, 648, 318, 2, 892, 1978]
[:mouse_move, 643, 313, 2, 893, 1979]
[:key_down_raw, 92, 0, 2, 894, 2046]
[:key_up_raw, 92, 0, 2, 895, 2046]
[:key_down_raw, 92, 0, 2, 896, 2050]
[:key_down_raw, 92, 0, 2, 897, 2075]
[:key_down_raw, 92, 0, 2, 898, 2077]
[:key_down_raw, 92, 0, 2, 899, 2079]
[:key_down_raw, 92, 0, 2, 900, 2081]
[:key_down_raw, 92, 0, 2, 901, 2083]
[:key_down_raw, 92, 0, 2, 902, 2085]
[:key_down_raw, 92, 0, 2, 903, 2087]
[:key_down_raw, 92, 0, 2, 904, 2089]
[:key_down_raw, 92, 0, 2, 905, 2091]
[:key_down_raw, 92, 0, 2, 906, 2093]
[:key_down_raw, 92, 0, 2, 907, 2095]
[:key_down_raw, 92, 0, 2, 908, 2097]
[:key_down_raw, 92, 0, 2, 909, 2099]
[:key_down_raw, 92, 0, 2, 910, 2100]
[:key_down_raw, 92, 0, 2, 911, 2102]
[:key_down_raw, 92, 0, 2, 912, 2105]
[:key_down_raw, 92, 0, 2, 913, 2107]
[:key_down_raw, 92, 0, 2, 914, 2108]
[:key_down_raw, 92, 0, 2, 915, 2110]
[:key_down_raw, 92, 0, 2, 916, 2112]
[:key_down_raw, 92, 0, 2, 917, 2114]
[:key_down_raw, 92, 0, 2, 918, 2116]
[:key_down_raw, 92, 0, 2, 919, 2118]
[:key_down_raw, 92, 0, 2, 920, 2120]
[:key_down_raw, 92, 0, 2, 921, 2122]
[:key_down_raw, 92, 0, 2, 922, 2124]
[:key_down_raw, 92, 0, 2, 923, 2127]
[:key_down_raw, 92, 0, 2, 924, 2128]
[:key_down_raw, 92, 0, 2, 925, 2130]
[:key_down_raw, 92, 0, 2, 926, 2132]
[:key_down_raw, 92, 0, 2, 927, 2134]
[:key_down_raw, 92, 0, 2, 928, 2136]
[:key_down_raw, 92, 0, 2, 929, 2138]
[:key_down_raw, 92, 0, 2, 930, 2140]
[:key_down_raw, 92, 0, 2, 931, 2142]
[:key_down_raw, 113, 0, 2, 932, 2143]
[:key_up_raw, 113, 0, 2, 933, 2149]
[:key_up_raw, 92, 0, 2, 934, 2217]
[:mouse_move, 643, 310, 2, 935, 2263]
[:mouse_move, 643, 295, 2, 936, 2264]
[:mouse_move, 643, 289, 2, 937, 2265]
[:mouse_move, 643, 279, 2, 938, 2266]
[:mouse_move, 644, 274, 2, 939, 2267]
[:mouse_move, 644, 269, 2, 940, 2268]
[:mouse_move, 645, 267, 2, 941, 2269]
[:mouse_move, 647, 256, 2, 942, 2270]
[:mouse_move, 648, 248, 2, 943, 2271]
[:mouse_move, 652, 227, 2, 944, 2272]
[:mouse_move, 653, 219, 2, 945, 2273]
[:mouse_move, 655, 204, 2, 946, 2274]
[:mouse_move, 659, 188, 2, 947, 2275]
[:mouse_move, 661, 178, 2, 948, 2276]
[:mouse_move, 664, 169, 2, 949, 2277]
[:mouse_move, 667, 160, 2, 950, 2278]
[:mouse_move, 669, 158, 2, 951, 2279]
[:mouse_move, 671, 153, 2, 952, 2280]
[:mouse_move, 674, 146, 2, 953, 2281]
[:mouse_move, 675, 141, 2, 954, 2282]
[:mouse_move, 677, 133, 2, 955, 2283]
[:mouse_move, 678, 129, 2, 956, 2284]
[:mouse_move, 679, 123, 2, 957, 2285]
[:mouse_move, 679, 120, 2, 958, 2286]
[:mouse_move, 680, 116, 2, 959, 2287]
[:key_down_raw, 1073742051, 1024, 2, 960, 2340]
[:key_down_raw, 113, 1024, 2, 961, 2342]
