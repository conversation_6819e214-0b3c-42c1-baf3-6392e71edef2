# Information about the contents of `dragonruby-mruby.patch`

The `dragonruby-mruby-*.patch` files contain the patches we've made to
mruby core. You can create these same changes to mR<PERSON><PERSON> by doing the following:

```sh
# assuming that you are inside of your dragonruby directory
cd ./docs
<NAME_EMAIL>:mruby/mruby.git
cd ./mruby
git checkout 3.0.0
git apply ../dragonruby-mruby-1.patch
git apply ../dragonruby-mruby-2.patch
etc ...
rake
```
