* 6.33
** [Pro] [Android] Native binaries (DragonRuby and SDL 2.0.20) recompiled against NDK version r28c.
   Native libraries have been aligned to 16kb (Google Play Store compliance).
* 6.32
** [Pro] [Support] Updated Layout.rect apis to support layout considerations for edge to edge rendering.
** [Samples] Added Squares arcade advanced sample app that shows multi-orientation + edge to edge rendering
   Sample app location: =./samples/99_genre_arcade/squares_advanced_multi_orientation=
* 6.31
** [Samples] Added Mode 7 sample app.
   Special thanks to mysterdip@discord for creating contributing this sample.
   Sample app location: =samples/99_genre_3d/05_mode7=
** [Samples] Squares arcade sample app upgraded to use modern DR apis.
   Note: This sample app is a solid reference implementation for a non-trivial game structure.
   Sample app location: =samples/99_genre_arcade/squares=
** [Support] Improved recording/replay system to capture inputs live (to repro situations where a segfault occurs).
** [Support] During dragonruby-publish, you will be warned in screaming caps if you have sprites with potentially problematic characters.
** [Support] Disabling Controller Config Wizard in DR until we figure out how to deal with false positives.
* 6.30
** [Pro] [Android] [Support] Target SDK for Android bumped to API 35 (Google Play compliance). NDK dependency upgraded to r27d.
** [Samples] Advanced Ray Caster sample app updated to remove some magic numbers
   Special thanks to james-stocks@github for initial sample app and AlexMooney@github for further clean up.
   Sample app location =samples/99_genre_3d/04_ray_caster_advanced=
** [Samples] Sample app added to show how to use line achors to center a piece of text vertically
   Sample app location =samples/01_rendering_basics/01_labels_anchors=
** [Bugfix] Grid.allscreen_top and Grid.allscreen_right gives correct values.
** [Support] Tests can exist under a class name that ends in Tests
   Tests can be defined at the top level or under a class that ends in the word "Tests".
   #+begin_src ruby
     def test_example args, assert
       assert.equal! 1, 1
     end
   #+end_src
   Can also be written as this (to avoid polluting Object):
   #+begin_src ruby
     class ExampleTests
       def test_example args, assert
         assert.equal! 1, 1
       end
     end
   #+end_src
* 6.29
** [Support] Setting ~args.state = {}~ within ~boot~ disables OpenEntity and Array-based primitives
   Given ~Hash~ quack like ~attr_accessor~, the need for ~OpenEntity~
   is significantly lessened and its improved egronomics makes
   Array-based primitives less needed / not worth the performance overhead.

   Opting into ~boot~ will prepare your game for removal of these
   constructs in the very very far future.

   Expect to see deprecation warnings over time.

   Example:
   #+begin_src ruby
     def boot args
       # doing this within boot will opt-into disabling OpenEntity
       # and Array-based primitives
       args.state = {}
     end

     def tick args
       # this will throw an exception because player.armor hasn't been
       # initialized to a Hash
       args.state.player.armor.hp = 10

       # this will throw an exception (convert to Hash)
       args.outputs.sprites << [100, 100, 80, 80, "sprites/square/blue.png"]
     end
   #+end_src
** [Samples] Added cooking game sample app.
   Sample app location =samples/99_genre_crafting/cooking_game_starting_point=
** [Samples] Camera construct within camera based sample apps has been improved.
   Sample app locations:
   - =samples/99_genre_platformer/map_editor=
   - =samples/07_advanced_rendering/16_camera_space_world_space_simple=
   - =samples/07_advanced_rendering_hd/05_camera_ultrawide_allscreen=
** [Samples] Improved check box component sample app.
   Sample app location: =samples/09_ui_controls/01_checkboxes=
** [Samples] Added sample app that show particle simulation using Hooke's and Coulomb's Law
   Sample app location: =samples/04_physics_and_collisions/14_hookes_law_coulombs_law=
** [Support] ~args.audio~ can also be accessed via ~args.outputs.audio~.
** [Support] ~args.audio[].path~ will be consulted if ~args.audio[].input~ is ~nil~.
** [Support] Removed extrenous log messages for 404's within the in-game webserver.
** [mRuby] Following patch was applied from upstream: https://github.com/mruby/mruby/commit/2d8f4fa92
   This patch allows ~Proc~ creation within modules that are mixed-in
   via ~prepend~.
** [OSS] ~GTK::Runtime~ (the class that's accessed via ~$gtk~ has been open sourced).
   Long term plan is to open source all of toolkit that's written in Ruby.
* 6.28
** [Samples] Fifteen Puzzle sample app cleaned up.
   Sample app location: =samples/99_genre_board_game/01_fifteen_puzzle=
** [Samples] "Shadows" platformer sample app cleaned up.
   Sample app location: =samples/99_genre_platformer/shadows/app/main.rb=
** [Support] Added ~Inputs#key_(up|held|down).(left|right|up|down)~ which inspects both ~keyboard~ and ~controller_one~.
   Just a nice helper method if the need arises.
   Example:
   #+begin_src ruby
     def tick args
       # consults keyboard arrow keys + wasd, and controller_one's DPad and left analog
       args.inputs.key_down.left
     end
   #+end_src
** [Bugfix] Initial playtime for `args.audio[key]` is respected instead of being set to `0`.
** [mRuby] Incorporated upstream patch for endless methods without parenthesis and heap overflow for heredocs.

    commit 07c847027875a955ba64d3c84f8f456e96155b31 (HEAD)
    Author: Yukihiro "Matz" Matsumoto <<EMAIL>>
    Date:   Tue May 18 16:26:36 2021 +0900

        parse.y: allow "command" syntax in endless method definition.

        This change allows `def hello = puts "Hello"` without parentheses.
        This syntax has been introduced since Ruby3.1.

    commit c43dd75ea9e2b2f3387e40617d4f4cd86d3841dc
    Author: Yukihiro "Matz" Matsumoto <<EMAIL>>
    Date:   Wed Feb 3 13:04:32 2021 +0900

        Avoid Heap Overflow in `heredoc_remove_indent`; fix #5316
* 6.27
** [Bugfix] Disallow GTK.set_window_scale values that would cause the game to be larger than the display.
   Window size will be clamped to the display size.
* 6.26
** [Pro] [C Extensions] Exposed miniz string compressions functions: ~mz_compress~, ~mz_uncompress~.
** [Samples] Added sample app that shows how you can orchestrate a cutscene (in a JRPG for example).
   Sample app location: =samples/99_genre_jrpg/cutscenes=
** [Samples] Added sample app that shows how to use matricies to create a rotating camera.
   Sample app location: =samples/07_advanced_rendering/16_matrix_camera_space_with_rotation=
** [Samples] Added sample app that shows how to use matricies to create a pinch, zoom, and pan camera.
   Sample app location: =samples/07_advanced_rendering/16_matrix_camera_pinch_zoom_pan=
** [Samples] Minor improvements to "cube world" sample app.
   Sample app location: =samples/07_advanced_rendering/16_matrix_cubeworld=
** [Support] Added class level variant for ~(Hash|Array)::all?~ and ~(Hash|Array)::any?~
** [Support] ~Grid.origin_center~ can be supplied within the ~boot~ function.
   If definined within ~boot~, invoking ~GTK.(reset|reboot)~ will remember the orientation used.
** [Bugfix] ~Geometry::point_on_line?~ returns true if the point is actually on the line as opposed to just within the bounding box.
* 6.25
** [Bugfix] Fixed replay of input history captured via ~$recording.start~ (off by one frame errors are the worst).
** [Bugfix] Fixed tracking speed of single touch input for mobile devices.
** [Bugfix] ~GTK.console.*_color=~ accepts Array, or Hash.
** [Samples] Added sample app that shows how to animate a bouncing box using ~Easing.spline~.
   Sample app location: =samples/08_tweening_lerping_easing_functions/03_easing_using_splines_bouncing_box=
** [Samples] Cleaned up simple collision sample app.
   Sample app location: =samples/04_physics_and_collisions/01_simple=
* 6.24
** [Bugfix] [Pro] Android HTTP/POST failures fixed.
** [Bugfix] ~Numeric.rand~ when given a Range yielding a size of zero returns ~nil~.
** [Samples] Created sample app that shows how to use Layout extendeded parameters within a render target.
   Sample app location: =samples/07_advanced_rendering/19_layouts_extended_parameters_in_render_target=
** [Samples] Added sample app that shows twinstick controls using either keyboard or controller.
   Sample app location: =samples/99_genre_arcade/twinstick_gamepad=
** [Samples] Added sample app that shows how to create "particles" out of labels.
   Sample app location: =samples/07_advanced_rendering/02_render_targets_label_particles=
** [Samples] Added sample app that shows a layout for a card game with animations.
   Sample app location: =samples/99_genre_card_game/01_board_and_card_layout=
** [Samples] Added sample app that shows an HTTP/POST to an external endpoint.
   Sample app location: =samples/11_http/04_http_post_external_server=
** [Support] Added ~GTK.calcstringbox_h~ which returns the string size as a hash.
** [Support] Added ~GTK.raise_window~ which will bring the game window to the top and make it active.
   Method is for development/debugging purposes only and will noop in production.
* 6.23
** [Bugfix] Fixed regression with ~args.inputs.mouse.click=~.
* 6.22
** [Pro] [Bugfix] ~Grid.allscreen_*~ properties return correct values for ~game_metadata.hd_maxscale=100-400~ plus ~game_metadata.hd_letterbox=false~.
** [Bugfix] ~args.inputs.mouse.(held|down|up)~ forwards to ~args.inputs.mouse.key_(held|down|up).left~.
** [Support] Added ~Geometry.vec2_(add|sub|subtract|scale)~.
** [Support] Added ~GTK.set_window_size~, ~GTK.set_window_position~.
   Docs have been updated with warnings about which window functions are okay to
   use in production vs those that should only be used for development/debugging purposes.
** [Samples] Added sample app that shows ball bouncing physics against static circular pegs.
   Sample app location: =samples/04_physics_and_collisions/08_ball_and_peg_bouncing=
** [Samples] Added sample app that shows how to create a camera that supports ultrawide displays (all screen mode)
   Sample app location: =samples/07_advanced_rendering_hd/05_camera_ultrawide_allscreen=
* 6.21
** [Bugfix] String#split handles correctly handles empty entries between delimiters.
* 6.20
** [Pro] [Bugfix] [iOS] Fixed path resolutions of staging directory within iOS Wizard.
   Regression was introduced in 6.13.
** [Pro] [Support] Added ~Grid.native_scale~, ~Grid.render_scale~, and ~Grid.texture_scale~ to help with HD render debugging.
** [Pro] [Support] Added support for HD rendering for 3K, 4.5K, and 6K displays.
** [Pro] [Bugfix] Ensure pixel perfect rendering at all window sizes if ~hd_max_scale~ is set to a value other than ~0~.
* 6.19
** [Bugfix] Fixed in-game web server failing to start in dev mode.
** [Bugfix] Fixed segfault that could occur if GTK.reboot is invoked with Tweetcarts.
* 6.18
** [mRuby] mRuby recompiled with -DMRB_UTF8_STRING=1 reverted.
* 6.17
** [Support] The Standard license can load C Extensions in development mode!
   All license tiers can load C Extensions in dev mode. The Standard license does not include
   the dependencies required to create C Extensions however.

   During the publishing process, any C Extension directories will be
   ignored during packaging and an exception will be thrown if
   ~dlopen~ is called in Production (for Standard license only).

   This gives Standard license users the ability to load/try out C Extensions in development mode.

   An Indie or Pro license is required to compile C Extensions/publish games that have C Extensions.
** [mRuby] mRuby recompiled with -DMRB_UTF8_STRING=1.
** [Bugfix] GTK.reboot will not segfault if there is an http request in-flight during the reboot process.
* 6.16
** [Pro] [Support] Added ~GTK.get_dlopen_path~ which returns the search path for C Extensions.
** [mRuby] [Bugfix] String#split results in cstrings with incorrect null termination.
   The mRuby String#split has been reimplemented to fix this issue.
** [Support] Added ~GTK.getenv~, ~GTK.setenv~ which can be used to retrieve and update environment variables.
** [Support] ~Geometry::rect_center_point~ takes ~anchor_(x|y)~ into consideration.
* 6.15
** [Bugfix] Web builds render correctly regardless of the browser's zoom level.
* 6.14
** [Bugfix] Layout.rect returns correct values with ~Grid.origin_center!~.
** [Support] Fixed numeric ~1~ glpyh in lowrez.ttf.
** [Samples] Added simplified ramp collision sample app.
   Sample app location: =samples/04_physics_and_collisions/12_ramp_collision_simple=
** [Samples] Simplified Nokia Jam sample app.
   Sample app location: =samples/99_genre_lowrez/nokia_3310=
** [Samples] Added Nokia Jam sample app snake reference implementation.
   Sample app location: =samples/99_genre_lowrez/nokia_3310_snake=
* 6.13
** [Pro] [Bugfix] [iOS] Do not reset the mouse position if VoiceOver is enabled.
** [Pro] [iOS] Improved path resolution of game when running iOS Wizard.
** [Samples] Added sample app that shows advanced scene management.
   Sample app location: =samples/02_input_basics/07_managing_scenes_advanced=
** [Samples] Added Tower of Hanoi sample app that shows advanced lerping techniques.
   Sample app location: =samples/08_tweening_lerping_easing_functions/09_tower_of_hanoi=
** [Support] Splash screen for html games removed (game automatically starts if able).
   For browsers other than Chrome, the html game still needs to be served up outside of an iFrame.
   The link that redirects the user to the game will open within the same window as opposed to a new
   one (href's target attribute is set to "_top" instead of "_blank").
** [Support] On Windows the game will start with focus instead of the terminal window during development mode.
* 6.12
** [Bugfix] Console correctly displays if the Grid's origin is center instead of bottom left.
** [Bugfix] Corrected render locations within render targets for ~Grid.origin_center!~.
** [Support] Better pop-out handling for html builds if Safari or mobile web is used.
** [Support] DR will warn you if you attempt to load audio that has an invalid sample rate.
** [Support] Increased analog stick deadzone from 3200 to 3600.
   You can lower this via ~args.inputs.controller_(one|two|three|four).analog_dead_zone=~. Steam Deck
   analog sticks are finicky so test thoroughly if you plan on lowering this value.
* 6.11
** [Bugfix] Fixed issue related to high CPU usage. Regression was introduced in 5.36.
* 6.10
** [Support] Added ~args.inputs.keyboard.key_repeat.KEY~.
   Key repeat frequency is based off of OS's keyboard configuration.
* 6.9
** [Bugfix] For HTML builds, popout game into a new window if game is in nested iFrames.
   Itch.io's Game Jam Submission Randomizer nests iFrames which breaks COOP/COEP. If this is
   detected then a link will be presented that will pop the game out into a new window.
** [Support] Increased security for ~$gtk.deserialize_state~, ~$gtk.load_state~.
   Before state data is deserialized via ~eval~, the data is inspected to ensure that
   it does not contain executable Ruby code outside of a Hash definition.
* 6.8
** [Samples] [Pro] [C Extensions] Added sample app that shows how to create background threads with atomic read/write of variables.
** [Support] Added Zlib ~compress~, ~uncompress~ APIs.
* 6.7
** [Samples] [Pro] [Android] [C Extensions] Added sample app that shows how to create C Extensions that leverage Java classes.
** [Bugfix] Invoking ~$gtk.reset~ no longer blips a white rectangle on a black background in a weird location.
* 6.6
** [Bugfix] [Pro] [VR] Fixed crash related to rendering non-hash primitives.
* 6.5
** [Samples] Added sample app that shows how to crop a large sprite to viewport.
   Sample app location: =samples/07_advanced_rendering/10_camera_and_large_sprites=
** [Samples] Added sample app that shows how to create a C Extension for MacOS.
   Sample app location: =samples/12_c_extensions/06_handcrafted_mac_extension=
** [Samples] Added sample app that shows how to create a C Extension for Steam.
   NOTE: This is still a work in progress and will be improved over time.
   Sample app location: =samples/12_c_extensions/07_handcrafted_steam_extensions=
** [Support] ~./dragonruby --version~ will print the version number and exit.
** [Bugfix] ~./dragonruby-publish~ for game names that contain a apostrophe no longer fails on Mac and Linux.
* 6.4
** [Support] [Pro] [iOS] iOS Wizard sets hotload IP Address to localhost when running game on the simulator.
** [Support] Improved how mult-touch maps to mouse location.
** [Support] Added ~origin~ and ~safe_area~ optional parameters to ~Layout.rect~
   These additional parameters will give you more control over the rectangle returned from Layout.rect.
   See the following sample app for usage of these parameters: =samples/07_advanced_rendering/19_layouts_extended_parameters=
* 6.3
** [Support] [Pro] [C Extensions] Reincorporated deprecated mRuby C APIs.
   Notes:
   - ~mrb_ary_ref~ is deprecated in future versions of Ruby. Use ~mrb_ary_entry~ instead.
   - If you are using C Extensions, it's strongly recommended that you recompile your extensions
     against the new release.
* 6.2
** [Support] Significant improvement to GC events.
   Decision points on when DR will GC during down time has been
   significantly improved. A full mark and sweep GC no longer occurs and
   has been replaced with an incremental GC.

   This yields performance improvements accross the board (most noticeable in games with a large,
   long-running game state).

   Thermal levels on mobile devices also gain significant improvements
   because of this change.

   Special thanks to the podo@discord and pvande@discord for helping
   to verify this new GC behavior, and thecire@discord for providing a
   an old codebase that suffered from GC performance issues.
** [Support] ARM64 Linux exports added.
   Running ~dragonruby-publish~ will now generate an ARM64 Linux
   export. This binary is compatible with Raspberry PI 5 and the
   rumored ARM64 Linux Steam Deck.
** [Support] Render Targets no longer have to be marked as ~transient!~.
   You no longer after to worry about marking a render target as
   ~transient!~. The function now no-ops and can be removed from your
   code bases at your leisure.

   Background context (feel free to skip over this):

   DX9 will lose render target data if the window is resized. The
   really confusing ~transient!~ flag was added to help decide which
   render targets should be recoverable, and which ones should skip
   the expensive serialization process. This issue has been
   side-stepped by setting the render driver explicitly to DX12, DX11,
   or OpenGL (in this order). An error message will be presented
   stating that one of these render drivers must be installed.

   DX9 is over two decades old at this point and boxes that can't run
   DX11 or DX12 have a reliable fallback of OpenGL.
** [Support] Added functions for getting key states to ~args.inputs.keyboard~ and ~args.inputs.controller_*~.
   More info in the docs: [[https://docs.dragonruby.org/#/api/inputs?id=key_downkey-key_upkey-key_heldkey-key_down_or_heldkey]]

   Example:
   #+begin_src ruby
     def tick args
       # the following code blocks are equivalent
       if args.inputs.keyboard.key_down.space
         puts "hello world"
       end

       if args.inputs.keyboard.key_down?(:space)
         puts "hello world"
       end
     end
   #+end_src

** [Bugfix] [Pro] Touch apis report correct x, y positions for all ~hd_max_scale~ values.
   Setting your game resizing behavior to pixel-perfect (~hd_max_scale~ values other than ~0~) no longer
   yields incorrect touch positions.
** [Support] Mouse button apis significantly improved.
   Mouse button apis have parity with the conventions within ~args.inputs.keyboard~ and ~args.inputs.controller_*~.

   Example:
   #+begin_src ruby
     def tick args
       if args.inputs.mouse.key_down.left
         puts "hello world"
       end
     end
   #+end_src

   For a full api demonstration, see =samples/02_input_basics/02_mouse_properties=.

   Special thinks to sophira@discord for kicking the tires on this new api.
** [Support] Added ~$gtk.reboot~.
   Multiple hotloads during development can leave methods within your
   game that no longer exist within source code. ~$gtk.reboot~ will
   reset your entire game code as if you shutdown and restarted DR.

   This can be triggered via shift+ctrl+r or executing ~$gtk.reboot~
   within the Console.
** [Support] Windows now has a terminal associated with it when running a game in dev mode.
   Mac and Linux would print standard out messages to the terminal if
   DR is started from the command line. On Windows, getting this same
   behavior was painful. During dev mode on a Window box, DR will
   start up a terminal for you automatically.
** [Bugfix] Mouse Y position was off by one pixel.
   This has been fixed. Stupid off by one errors.

   Special thanks to sophira@discord for finding this issue.
** [Bugfix] [Pro] Rendering a cropped area of a Render Target in HD mode works.
   Render targets were incorrectly cropped when provided ~source_(x|y|w|h)~ values.

   This has been fixed. Special thanks to podo@discord for help in troubleshooting.
** [Support] Added ~$gtk.speedup!~ to compliment ~$gtk.slowmo!~.
   An initial cut of ~$gtk.speedup!(factor)~ has been added. This function is useful if you want
   to move quickly past points in the game to continue feature development. Adding this function call to
   the top of your ~tick~ method will speed up your simulation by the factor that's passed in.
** [Support] Initial cut of ~args.inputs.pinch_zoom~ for touch devices.
   Touch apis emulate mouse events more accurately. Specifically if there are multiple
   fingers touching the screen. Resolution of the mouse position is
   averaged between the touch points for example.

   Pinch and zoom value is now available via ~args.inputs.pinch_zoom~ (more improvements to come).
** [Bugfix] Console evaluating a symbol no longer throws an exception.
** [Bugfix] ~Geometry.find(_all)_intersect_rect~ raises an exception if a flattened array isn't given as a second parameter.
   This up front verification also resolves issues related to non array-based primitives being inadvertently flattened.

   Special thanks to RedComputer@discord for providing a repro for this bug.
** [OSS] Incorporated bugfix from upstream mRuby.

   https://github.com/mruby/mruby/commit/30b1287df5d2dc23b797a578e4916199715754ef:

   state.c: check irep reference count overflow.
* 6.1
** [Pro] [iOS] [Bugfix] Fixed issue in iOS wizard that would cause package creation to fail.
   Bug was introduced in 6.0. If you're having issues creating an iOS package, upgrade
   to 6.1.
** [Pro] [iOS] [Android] [Bugfix] Fixed issue in ~$gtk.delete_file~ on mobile platforms would cause an exception.
** [Bugfix] Standard License triangle rendering wasn't fully enabled. This has been fixed.
** [Samples] =samples/99_genre_mario= sample apps cleaned up.
   Simplified code in the following sample apps which show platforming physics and AABB collision:
   - =samples/99_genre_mario/02_jumping_and_collisions=
   - =samples/99_genre_mario/01_jumping=
** [Support] Better formatting of method missing exceptions.
** [Support] String#wrapped_lines performance improvements.
** [Support] Improvements to experimental method ~$gtk.reboot~.
   ~$gtk.reboot~ attempts to remove methods from classes that were added because of hotloading and
   simulates the behavior of your game as if it were started for the first time. There are still
   kinks to work out, but you may find it useful to experiment with.
* 6.0
** [IMPORTANT] This release does NOT contain breaking changes. This is an epoch version bump.
** [Support] The ability to render triangles is now available at ALL license tiers woo hoo!
   Special thanks to lyniat@discord for continuing to explore unique usages of triangle rendering.
** [Samples] Sample app added to show how to do Verlet Integrations.
   Sample app location: =samples/04_physics_and_collisions/13_verlet_integration=
** [Support] ~Kernel#p~ sends output to console.
   Special thanks to levi@discord for validating ~Kernel#p~ behavior.
** [Bugfix] Fixed an issue where scale quality applied to sprites caused a side-effect when rendering fonts.
   Special thanks to dishcandantly@discord for help in isolating this difficult to reproduce issue.
* 5.36
** [Pro] [iOS] Default audio volume set to 40% on iOS (iPhone speakers are loud).
** [Pro] [iOS] Deploying to the simulator does not require =metadata/ios_metadata.txt= to be filled out.
** [Support] Better drift correction of the fixed-update simulation thread.
** [Support] Framerate calculation is recomputed on `$gtk.reset` or when the Console is closed.
** [Support] Orientation for game can be set to "both".
   This is an initial cut and should be used for testing only. Setting the ~orientation~
   to ~landscape,portrait~ or ~portrait,landscape~ will automatically change the orientation of
   your game based on the width/height ratio of the window. The value that is specified first
   signifies the starting orientation of your game.
** [Samples] Added sample app that shows how to calibrate a rhythm based game.
   Sample app location: =samples/07_advanced_audio/03_rhythm_game_calibration=
** [A11y] A11y emulation can be controlled via gamepad.
** [Bugfix] Printing ~OpenEntities~ with recursive references no longer causes a stack overflow.
* 5.35
** [Samples] Added sample app that demonstrates how to create particles.
   Sample app location: =samples/03_rendering_sprites/05_particles=
** [Support] Added ~FFI::Misc#setclipboard~ (accessible via ~gtk.ffi_misc~).
** [Support] ~Inputs#last_active~ set to a reasonable default value based on platform.
** [Bugfix] Inputs#finger_(left|right) reports correct locations when in portrait mode.
** [Bugfix] Improved ~Runtime#calcstringbox~'s accuracy improved for ~size_px:~ parameter.
** [Bugfix] Named parameters are respected for ~Geometry::line_to_rect~.
** [Bugfix] ~Layout#rect~ returns correct values after ~Runtime#toggle_orientation~ has been invoked.
** [Bugfix] DR class macros moved to ~Module~ from ~Object~.
* 5.34
** [Pro] [iOS] Deploying to the simulator no longer requires a provisioning profile.
   Deploying to the device can be done through the Console, but requires provisioning profiles
   create from https://developer.apple.com:
   #+begin_src ruby
     $wizards.ios.start env: :dev
   #+end_src

   You can deploy to the simulator without a provisioning profile:
   #+begin_src ruby
     $wizards.ios.start env: :sim
   #+end_src

   Additional details for iOS deployment available via ~help~:
   #+begin_src ruby
     $wizards.ios.help
   #+end_src
** [Pro] [iOS] General improvements to A11y emulation to support VoiceOver functionality on iOS.
** [Support] Added ~Geometry::each_intersect_rect~ (many to many collision).
   ~Geometry::each_intersect_rect~ executes a callback for each collision found.

   Example:
   #+begin_src ruby
     rects_1 = [{ x: 0, y: 0, w: 100, h: 100 }]
     rects_2 = [{ x: 50, y: 50, w: 100, h: 100 }]

     args.geometry.each_intersect_rect(rects_1, rects_2) do |rect_1, rect_2|
       # do something with the intersecting rectangles
     end
   #+end_src

   The following sample app has been updated to leverage the new function:
   =samples/09_performance/09_collision_limits_many_to_many=

   See docs for more info: https://docs.dragonruby.org/#/api/geometry?id=each_intersect_rect
** [Support] Rendering performance improvements for classes that use the ~attr_sprite~ class macro.
   If a class uses ~attr_sprite~, DragonRuby will assume that sprite properties are provided via
   iVars and will pull those values directly instead of invoking methods.
** [Support] Initial cut of ~$gtk.reboot~.
   Invoking ~$gtk.reboot~ will reset your game as if it were started for the first time. Any
   methods that were added to classes during hotload will be removed (leaving you with a pristine
   environment). This function is in a beta state (report issues on the Discord Server).
** [Bugfix] Console no longer shown for method missing exceptions that have been handled.
* 5.33
** [Pro] [iOS] iOS wizard respects ~orientation_ios~ override if specified in =metadata/game_metadata.txt=
** [Pro] [Support] ~$gtk.set_window_scale~ accepts optional aspect ratio.
   See docs for usage details: https://docs.dragonruby.org/#/api/runtime?id=set_window_scale
   This is useful for testing different aspect ratios and how it'll
   render when ~hd_letterbox=false~ (All Screen Mode).
** [Bugfix] Exceptions within ~boot~ presents the Console instead of segfaulting.
** [Support] Analog deadzone increased from 1600 to 3200.
   Steam OS's default deadzone is 8192 which has been criticized for
   being too damn high and a value of 1600 is below the minimum value
   that the Steam Deck/Steam OS supports. After testing multiple
   controllers, values below 3200 turned out to be too sensitive and
   resulted in unintended anolog activity.
** [Support] ~args.outputs.watch~ added as an alias to ~args.outputs.debug.watch~.
** [Support] Class level ~Array~ methods added.
   Here are some benchmarks of the Class level methods vs Instance
   level methods:
   - ~Array.compact!~:   2250% faster
   - ~Array.compact~:    2075% faster
   - ~Array.flat_map~:    918% faster
   - ~Array.map~:         196% faster
   - ~Array.find_all~:    141% faster
   - ~Array.select~:      141% faster
   - ~Array.filter_map~:   93% faster
   - ~Array.map!~:         85% faster
   - ~Array.select!~:      72% faster
   - ~Array.reject~:       59% faster
   - ~Array.reject!~:      47% faster

   NOTE: These functions are still somewhat experimental and do not
         conform to the ~Enumerable~ interface and may not support
         all the capabilities that instance-based functions
         have (they are also a bit more cumbersome to chain).
** [Support] Added aliases for common easing/lerping functions.
   See docs for usage details: https://docs.dragonruby.org/#/api/easing?id=easinglerping
   - ~Easing.smooth_start~
   - ~Easing.smooth_stop~
   - ~Easing.smooth_step~
   - ~Easing.mix~
* 5.32
** [Pro] [Android] API 34 support added.
   Target SDK for Android bumped to API 34 (Google Play compliance). NDK dependency upgraded to r26d.
** [Pro] [Support] Added ability to change your game's HD Max Scale and HD Letterbox while the game is running.
   The following functions are accessible via ~$gtk~, ~GTK~, or ~args.gtk~:
   - ~set_hd_max_scale(integer)~
   - ~toggle_hd_letterbox~, ~set_hd_letterbox(bool)~

   Details located in docs under Runtime -> Window Functions: https://docs.dragonruby.org/#/api/runtime?id=window-functions
** [Pro] [Bugfix] Fixed behavior of ~game_metadata.hd_max_scale~ for non-standard device resolutions.
** [Pro] [Support] Grid's ~allscreen_*~ properties are more accurate.
** [Pro] [Android] [Support] Removed size limitations of render targets (sprites must still be under 4000x4000 for Android).
** [Support] Various window and rendering specific functions have been added.
   The following functions are accessible via ~$gtk~, ~GTK~, or ~args.gtk~:
   - ~maximize_window~
   - ~toggle_orientation~, ~set_orientation(bool)~
   - ~move_window_to_next_display~
   - ~toggle_orientation~, ~set_orientation(symbol)~

   Details located in docs under Runtime -> Window Functions: https://docs.dragonruby.org/#/api/runtime?id=window-functions
** [OSS] ~require~ and ~require_relative~ machinery have been open sourced.
   Location: =docs/oss/dragon/runtime/require.rb=
** [Bugfix] ~require_relative~ works for =.rb= files that are not inside of a directory.
   Special thanks to kfischer_okarin@discord for submitting a patch.
** [Support] Changed console output terminal colors to use non-bright ansi escape sequences.
** [Support] In-Game webserver disabled by default.
   The in-game web server is disabled by default. To enable it, update
   =metadata/cvars.txt= in your game folder with the following:

   # Enable the use of In-Game Web Server.
   webserver.enabled=true

   # Port that will be used for In-Game Web Server
   webserver.port=9001

   # Allow Remote Clients.
   # only needs to be true if you want to accept client connections originating from
   # sources other than localhost.
   webserver.remote_clients=true

   NOTE: For remote hotload to work, the webserver needs to be enabled, the ~port~ set to 9001,
         and ~remote_clients~ must be set to ~true~.
* 5.31
** [Pro] [Android] Notes added to game_metadata.txt that packageid is required for Android.
** [Pro] [Android] Controller configuration wizard disabled by default on Anroid (and iOS).
** [Support] More control over RNG when ~GTK.reset~ is invoked.
   See docs for the ~reset~ function under Runtime -> Top Level Functions.
** [Support] Statements evaluated in the Console will show object's ~.inspect~ representation as opposed to ~.to_s~.
** [Support] A ~LoadError~ will be raised if an exception occurs during ~require~.
** [Support] ~Grid.allscreen_(x|y|w|h)~ provide logical pixel values in both HD and SD modes.
   This is useful for resizing/positioning a render target that has an aspect ratio other
   than 16:9 (eg: a lowrez game with an aspect ratio of 1:1).
** [Bugfix] ~args.inputs~ related to attributes where the shift key is held are reported correctly.
   Method aliasing and NumLock status were affecting these attributes.
** [Bugfix] Input for replays of recorded gameplay were off by one frame.
** [Samples] Added platformer sample app that demonstrates AABB collision, camera movement, plus in-game map editor.
   Sample app location: =samples/99_genre_platformer/map_editor=
* 5.30
** [Support] Added ~Grid#landscape?~ and ~Grid#portrait?~
   These are helper methods which check ~Grid.orientation == :landscape~
   and ~Grid.orientation == :portrait~.
** [Support] Added windowing helper functions.
   - ~$gtk.can_resize_window?~
   - ~$gtk.can_close_window?~
   - ~$gtk.toggle_window_fullscreen~
** [Support] ~$gtk.set_window_scale~ centers window after setting the window scale.
** [Support] References to ~args.state.tick_count~ updated to ~Kernel.tick_count~
   While ~args.state.tick_count~ will continue to be supported, it is recommeded to use
   ~Kernel.tick_count~ instead. Sample apps and docs have been updated to use ~Kernel.tick_count~.
** [Support] Better validation/error messages for invalid primitives sent to ~args.outputs~.
   This should reduce segfaults related to sending objects that can't be rendered.
* 5.29
** [Pro] [Support] [iOS] [Android] Game's orientation can be overridden for mobile platforms.
   Example:
   #+begin_src
     # orientation for desktop/web
     orientation=landscape
     # if the following entries are provided, the orientation will be overridden
     orientation_android=portrait
     orientation_ios=portrait
   #+end_src
** [Support] ~$gtk.notify~ takes into consideration whether remote hotloading is enabled.
   Notifications will come through if remote hotloading is enabled even in production builds.
** [Support] ~Layout.debug_primitives~ renders additional seperators to help with alignment.
   Example of how to show Layout debug primitives:
   #+begin_src ruby
     def tick args
       if args.state.tick_count == 0
         args.outputs.static_primitives << Layout.debug_primitives
       end
     end
   #+end_src
** [Bugfix] Fixed issue of simulation loop running "catch up" ticks causing the game to inadvertantly speed up.
   This is a regression that was introduced in v5.18.
** [Bugfix] Fixed issue where ~$gtk.create_uuid~ would return the last generated UUID on a subsequent invocation.
** [OSS] ~GTK::Outputs~ and ~GTK::Runtime::CBridge~ have been open sourced.
* 5.28
** [Indie] [Pro] [Support] Triangle primitives accept ~r, g, b, a~ for point.
   #+begin_src ruby
     def tick args
       args.outputs.primitives << {
         x: 0, y: 0,
         source_x: 0, source_y: 0,
         r: 255, g: 0, b: 0, a: 255,
         x2: 0, y2: 100,
         source_x2: 0, source_y2: 100,
         r2: 0, g2: 255, b2: 0, a2: 64,
         x3: 100, y3: 0,
         source_x3: 100, source_y3: 0,
         r3: 0, g3: 0, b3: 255, a3: 32,
         path: :pixel
       }
     end
   #+end_src
** [Support] Added ~$gtk.create_uuid~ that returns a ~String~.
   NOTE: This function does not generate cryptographically secure UUIDs.
** [Support] [OSS] Function keys, and numpad keys added ~args.inputs.keyboard~.
   Thank you to Hiro@discord for adding this functionality. Thank you
   Podo@discord for testing.
** [Samples] Added sample app that demonstrates how to create a scroll view.
   Sample app location: =samples/09_ui_controls/03_scroll_view=
** [Bugfix] Inner exception is captured if a file raising an exception during ~require~.
** [Bugfix] Ensure that ~require~ doesn't require the same file multiple times on the same tick.
* 5.27
** [mRuby] [Bugfix] Incoporated fix: Array#shuffle(!) result distribution #6227
   https://github.com/mruby/mruby/pull/6227

   Patch info located at =./docs/oss/mruby/dragonruby-mruby-3.patch=

   Congrats to levi@discord for this getting merged into mRuby!
   Special thanks to Pineapple@discord for creating a minimum repro to verify fix in DR.
* 5.26
** [Pro] [iOS] Initial cut of accessibility APIs that interact with iOS's VoiceOver capabilities.
   A sample app has been created showing the release candidate api.
   =./samples/09_ui_controls/04_accessiblity_for_the_blind/=

   These accessibility functions will be expanded to cover more platforms over time and will be
   available at all License tiers.

   Users on the Standard License can run this sample app which will emulate iOS VoiceOver behavior by
   posting notification entries during dev mode.

   Pro users can release to their device and enable VoiceOver to demo the fully integrated experience.

   To use VoiceOver on your iOS device:
   1. Deploy app to device.
   2. Say "Hey Siri, enable voice over.".
   3. Make a capital Z gesture using two fingers to dismiss Siri.
   4. Swipe left or right to navigate to your app.
   5. Double tap to open your app.
   6. Swipe left and right to navigate within your app.
   7. Double tap accessibility elements and a click event will be issued.
** [Pro] [Bugfix] Fixed letterbox background color rendering for games that have ~hd_max_scale~ set to a non-zero value.
** [Pro] [Android] AndroidManifest.xml updated to be in compliance with Google Play Pass requirements.
** [Support] Added top level function ~shutdown~ which will be invoked before your game exits.
   There are four top level functions that DragonRuby will be aware of:
   - ~tick~: Your game loop.
   - ~boot~: Invokes once when your game starts up.
   - ~reset~: Invoked before ~$gtk.reset~ occurs.
   - ~shutdown~: Invoked before your game exists.

   See docs API -> Runtime -> Top Level Functions documentation for detailed explanations.
** [Bugfix] ~$gtk.calcstringbox~ respects ~size_enum:~ named parameter.
* 5.25
** [Pro] [iOS] Added new collection of icons that cover new devices.
** [Pro] [Android] [Bugfix] Fixed warning message about sprites being too large for Android to load.
** [Support] Added support for ~ignore_directories~ to =game_metadata.txt= which will not be packaged during ~dragonruby-publish~.
   A comma delimited list of directories that should be ignored during the
   publishing process. For example, if your game supports saves, you'd want to ignore
   that directory (example format: ignore_directories=tmp,saves,dev,assets).
     IMPORTANT:
     Any directory that matches the ignore list will be ignored (REGARDLESS OF HIERARCHY).
     For example:
       ignore_directories value: saves
       Directory structure:
       - mygame
         - saves <---- This directory will be ignored
         - libs
           - saves <---- This directory will be ignored
   ignore_directories=saves
** [Support] Controllers now have a ~name~ property that will be set when a controller is connected.
** [Support] Removed custom overrides of ~const_defined?~ and ~const_missing~ (no longer needed).
** [Support] ~Geometry::rect_navigate~ left right wrapping behavior improved.
** [Support] ~Runtime#calcstringbox~ supports ~size_px~ via a named parameter.
** [Support] Source code backups now organized under =YYYY-MM-DDDD= directories.
** [OSS] ~Runtime#platform?~, ~Runtime#platform?~, and ~Runtime#platform_mappings~ open sourced.
** [Bugfix] ~Inputs#up_down_perc~ consults WASD/has parity with ~Inputs#left_right_perc~.
* 5.24
** [Pro] [Android] Fixed IP Address retrieval for remote hotload on Window.
** [Support] ~$gtk.notify! STRING~ respects new lines and will increase height accordingly.
** [Samples] Added sample app that shows how to create a radial menu that works with mouse and controller.
   Sample app location: =samples/09_ui_controls/03_radial_menu=
** [Bugfix] Reverted experimental terminal stdin/stdout for MacOS and Linux.
* 5.23
** [Pro] [Support] Improved documentation for All Screen Mode.
** [Pro] [Indie] [Shaders] A beta of ~dragonruby-shadersim~ has been released.
   Login and download at dragonruby.org.
   The Keeper tech demo [https://youtu.be/xEqggoZoYAo] is the main sample app that ships with DragonRuby ShaderSim.
** [Pro] [Indie] [Shaders] Documentation added for shaders API.
** [Support] Docsify version of docs can now be run locally.
   You can now view the docs offline using ~./dragonruby-httpd ./docs~
** [Support] ~args.outputs.debug~ when given a String is newline aware.
   #+begin_src ruby
     def tick args
       # two watch labels will be generated given the newline character
       args.outputs.debug << "hello\nworld"
     end
   #+end_src
** [Support] Added ~args.outputs.debug.watch(o, label_style:, background_style:)~
   ~args.outputs.debug~ has a ~watch~ function which lets you pass in a non-string value
   and optional ~label_style:~, and ~background_style~ named parameters.

   #+begin_src ruby
     def tick args
       args.state.enemies ||= 10.map do |i|
         {
           x: rand(1280),
           y: rand(720),
           w: 100,
           h: 100
         }
       end

       args.outputs.debug.watch pretty_format(args.state),
                                label_style: { r: 0,
                                               g: 0,
                                               b: 255,
                                               size_px: 10 },
                                background_style: { r: 0,
                                                    g: 255,
                                                    b: 0,
                                                    a: 128,
                                                    path: :solid }
     end
   #+end_src
** [Support] Replay captures textinput events.
   Recordings created via ~$recording.start_recording~ or via the
   DragonRuby Console's "Record Gameplay" button now populate ~args.inputs.text~.
** [Support] Ctrl+R will stop a replay that is currently running.
** [Support] ~$gtk.reset_and_replay~ now takes in an optional implicit block.
   The implicit block can be used to provide debug information during a replay execution.

   #+begin_src ruby
     def tick args
       args.state.player ||= {
         x: 100,
         y: 100
       }

       if args.inputs.keyboard.key_down.space
         args.state.player.x += 1
         args.gtk.notify! "space pressed!"
       end
     end

     # implicit block is called after tick is completed.
     $gtk.reset_and_replay "replay.txt", speed: 1 do |args|
       if args.state.player.x != 100
         puts args.state.player.x
       end
     end
   #+end_src
** [Bugfix] Warning for sprites greater than 1600x1600 correctly prints sprite path.
* 5.22
** [Support] [Pro] ~$wizards.ios.start~ sends build output immediately to standard out.
** [Support] Added ~Geometry::rect_navigate~ to support the navigation of menus using the keyboard/dpad.
   Sample app demonstrating menu navigation added at =./samples/09_ui_controls/02_menu_navigation=.
** [Support] ~Geometry::center~ supports rects and lines.
   ~Geometry.center~ check to for w/h properties to determine if center point
   computation should occur for a rect or a line.
** [Support] Added ~args.inputs.keyboard.key_(up|down|held).directional_vector.
** [Support] SDL_SetHint(SDL_HINT_GAMECONTROLLER_USE_BUTTON_LABELS, "1") enabled.
** [Support] Warnings added for render targets and sprites above 1600x1600 pixels.
   Android has a hard limit 4096x4096 for textures. A warning has been added to let you know
   if you are exceeding this limit. Androids max hd scaling is also capped at 1800p (scale 2.5x).
   Browser games opened on Android's mobile browser are also capped at 1800p with HighDPI disabled
   to prevent skewing of aspect ratios.
** [Support] Warning is presented if ~:[]~ is invoked on an Entity, use ~.~ accessor instead.
** [Support] ~$gtk.delete_file~ checks to see if file exists before attempting to delete it.
** [Support] Docs migrated to use Docsify.
   =./docs= directory contains markdown files. One page version of docs moved to =./docs/static=.

   Special thanks to Redspark@discord and f_3r@discord for providing a Docsify proof of concept.
** [Bugfix] [mRuby] Fixed bug in Enumerable gems.
   Exception would be raised for some functions on classes that override ~==~. Patch information
   is located at =./docs/oss/mruby/dragonruby-mruby-2.patch=

   Special thanks to jujule@discord, kfischer_okarin@discord, and levi@discord for troubleshooting
   and finding a fix to mRuby gems.
** [Bugfix] ~Ctrl+R~ to reset game works even if Console is open.
** [Bugfix] ~args.inputs.last_active~ set to ~:mouse~ if touch event occurs.
** [Bugfix] ~args.inputs.(left_right|up_down) checks key_down states instead of key_up states.
** [Bugfix] Added ~args.inputs.up_down_directional_perc~ to compliment ~left_down_directional_perc~.
   ~left_down_directional_perc~ was flattered by the compliment.
* 5.21
** [Support] ~args.grid~ and ~args.layout~ can be accessed as a singleton via class functions.
   Instead of calling ~$args.layout.rect~ or ~$args.grid.w~, you can now do ~Layout::rect~ or
   ~Grid::w~. Top level class functions forward to ~$args~. This is an initial cut and
   all functions may not be forwarded.
** [Support] ~args.gtk~ functions can be accessed as a singleton via class functions.
   Instead of calling ~$gtk.reset~, you can now do ~GTK::reset~. Top level class
   functions forward to ~$gtk~. This is an initial cut and all functions may not be forwarded.
** [Support] Initial work for ~args.events.raw~
   Property is an array of hashes ray representing all events from SDL occurred during a tick.
   Mapping of all event types is pending. This is primarily used for debugging/observing what
   could potential be codified into args.inputs.
** [Bugfix] Fixed exception when hotloading a file which includes a call ~$gtk.reset~.
   Thanks to akzidenz@discord for reporting this issue.
** [Support] Better descriptions in ~$gtk.benchmark~ plus ~duration~ override.
   ~$gtk.benchmark~ now supports either passing in ~interations:~ or
   ~durations:~ in seconds. If ~iterations~ is passed in then the
   winner is decided based on time to complete target iterations. If
   ~duration~ is passed in then winner is determined based on number
   of iterations completed within the ~duration~ provided.
* 5.20
** [Bugfix] Fixed scenario where audio synchronization could throw an unhandled exception.
** [Support] DragonRuby version is logged to the console on startup.
* 5.19
** [Pro] [Bugfix] [iOS] [Android] Remote hotload fixed.
** [Support] Added ~args.inputs.keyboard.key_(down|held|up).keycodes~.
   The ~keycodes~ attribute returns a ~Hash~ where the key is the SDL keycode and the value is the
   ~tick_count~. This contains all keycodes providing you access even if we don't have a named attribute.
   For a list of all keycodes, see: https://wiki.libsdl.org/SDL2/SDLKeycodeLookup
** [Support] Added ~args.inputs.keyboard.key_(down|held|up).ac_back~
   The ~ac_back~ key represents the "Application Control Back Button" on Android Devices.
** [Support] Added ~args.gtk.set_window_title(string)~.
   This function takes in a string updates the title of the game in the Menu Bar.
   Note: The default title for your game is specified in via the ~gametitle~ property in ~mygame/metadata/game_metadata.txt~.
** [Samples] Added sample app that shows off how an alchemy game can be created.
   Sample app location: =samples/99_genre_crafting/alchemy_starting_point=
** [Bugfix] Fixed clipping of sound volume for very short sound effects.
** [Bugfix] Removed divergent behavior of web builds which marked render_targets as ~transient!~ by default.
   Render targets by default are cached by DragonRuby. This allows for the texture to be reconstituted
   in the event a window is resized. If the render target you are creating it updated every frame, it's
   important to mark the render_target as ~transient~ (you'll get a warning recommending to do this if we
   detect consequtive updates to a render target).
** [Bugfix] ~args.outputs.background_color~ can accept a Hash with keys ~r~, ~g~, ~b~(, ~a~).
   The ~a~ key of the hash will be ignored for the top level output.
** [Bugfix] [mRuby] Fixed arithmetic comparison errors in web builds for integers with values greater than 1 Billion.
   For web builds the following returned ~true~ incorrectly:

   #+begin_src
     puts (1_073_741_824 < 1_000_000_000) # returned true in web builds, wtf?
   #+end_src

   The mRuby virtual machine was patched to fix this error.
** [OSS] Open Sourced parts of the engine are now packaged within the zip as seperate ruby files under ~./docs/oss~.
   Given the number of files that have been open sourced, having a single ~./docs/oss.txt~ file was getting a bit too large.
** [OSS] Custom patches made to mRuby are now packaged within the zip under ~./docs/dragonruby-mruby.patch~.
   The arithmetic bugfix that was made for web builds is included within this patch file for your reference.
* 5.18
** [Performance] Rendering sprites using hashes improved (in some cases it's 30% faster).
** [Performance] Experimental enumerable/hash apis added.
   The following experimental apis have been added. While they don't have exact parity to
   core lib apis, the can be up to 300% faster:

   - Array::map(array) do ... end
   - Array::map!(array) do ... end
   - Array::each(array) do ... end
   - Enumerable::find_all(enum) do ... end
   - Hash::merge(h_1, h_2) do ... end
   - Hash::merge!(h_1, h_2) do ... end
   - Hash::find_all(h) do ... end

   NOTE: These apis are class level functions (the instance level functions are left unchanged).
** [Bugfix] The default font.ttf correctly renders Cryllic characters.
   A special thanks to kota@discord and funkyloverone@discord for troubleshooting and fixing the default font.

   Unfortunately, this fix did increase the size to 3.1MB (which is still less than the core engine which clocks in at 4.2MB).
** [Bugfix] Exceptions are correctly reported if main.rb has exceptions on startup as opposed to presenting a blank screen.
** [Bugfix] ~Enumberable#sum~ moved to ~Array#sum~.
** [Bugfix] ~Geometry::(line|ray)_intersect~ no longer throws an exception if given parallel lines.
** [Bugfix] Sounds queued up on the same frame via ~args.audio~ and ~args.outputs.sounds~ are synchronized.
** [Support] ~$gtk.reset~ additionaly garbage collects render_targets.
   NOTE: You can use ~$gtk.reset_sprite(rt_name)~ to garbage collect render_targets that you are no longer using.
* 5.17
** [Samples] Path-finding sample app added that shows a preview of a player's path around walls.
   Sample app location: =samples/13_path_finding_algorithms/10_moveable_squares=
** [Samples] Bullet Heaven sample app added.
   Sample app location: =samples/99_genre_arcade/bullet_heaven=
** [Support] Added Geometry#angle_turn_direction
   ~args.geometry.angle_turn_direction angle, target_angle~

   Returns ~1~ or -1 depending on which direction the ~angle~ needs to
   turn to reach the ~target_angle~ most efficiently. The angles are
   assumed to be in degrees. ~1~ means turn clockwise, and ~-1~ means
   turn counter-clockwise.
** [Support] Added Geometry#angle(start_point, end_point)
   This is an alias to ~args.geometry.angle_to~.
** [Support] ~Numeric#frame_index~ accepts either ~count:~ or ~frame_count:~ for determining animation length.
   If both are provided, ~frame_count~ will be used.
   #+begin_src
     def tick args
       index = Numeric.frame_index start_at: 0,
                                   frame_count: 7,
                                   hold_for: 8,
                                   repeat: true
       args.outputs.sprites << {
         x: 0,
         y: 0,
         w: 64,
         h: 64,
         source_x: 32 * index,
         source_y: 0,
         source_w: 32,
         source_h: 32,
         path: "sprites/misc/explosion-sheet.png"
       }
     end
   #+end_src
** [Bugfix] ~Numeric#frame_index~ ~repeat_index:~ optional parameter resets correctly for animations with frames > 3.
   See =samples/03_rendering_sprites/03_animation_states_2= for example usage.
* 5.16
** [Support] Increased precision of the percentage and raw values returned by controller left/right analog sticks.
** [Support] ~args.inputs.left_right~ consults WASD scancodes as opposed to keycodes.
   More scancode values will be supported in the future. Docs have been updated to enumerate new scancode properties
   and behavior.
** [Support] Web builds will automatically mute if the game doesn't have focus.
** [Samples] Added sample app that shows how to create a repeating texture.
   Sample app location: =samples/07_advanced_rendering/02_render_targets_repeating_texture=
** [Samples] Sample apps will include preview videos over time to mirror https://samples.dragonruby.org (be sure to check out the website).
   The preview videos are fairly small (trying to keep them to less than 500KB each).
** [Bugfix] Fixed the letter "S" glyph in lowrez.ttf.
** [Bugfix] Added polyfill for renamed/normalized keyboard properties for backwards compatability with keyboard libraries.
** [Bugfix] ~$gtk.set_window_scale~ invalidates font textures so they can be redrawn at the new window size.
* 5.15
** [Pro] [iOS] [Bugfix] Fixed issue where game would crash after tomb-stoned for an extended period of time.
** [Pro] [iOS] [Bugfix] Fixed remote hotloading to device when app is signed with developer provisioning profile.
** [Bugfix] Keyboard input changes are reported correctly when modifier keys are exclusively pressed, held, or released.
* 5.14
** [Pro] [iOS] Warnings added if thermal state of the device increases above "fair".
   This will be expanded to support more platforms.
** [Bugfix] DR starts up correctly even if =app/main.rb= contains syntax errors.
   Syntax errors will be shown in the console on startup.
** [Bugfix] Fixed placement of summary information for ~args.layout.debug_primitives~.
** [Samples] Dueling Starships sample app cleaned up.
   Sample app location: =./samples/99_genre_arcade/dueling_starships=
** [Support] Warning of file names containing capital letter/spaces will not show up in production.
** [Support] Adding warnings related to Array based primitives containing nested arrays/hashes.
   While DR allows for this (is helpful for quick debugging), it's expensive to flatten
   these jagged data structures.

   NOTE: To audit your codebase of array based primitives usage, add ~$gtk.warn_array_primitives!~
   to the bottom of =app/main.rb= (outside of your ~tick~ method).
* 5.13
** [Pro] [iOS] [Bugfix] iOS remote hotload continues to query for new code updates regardless of current exceptions.
** [Pro] [Android] minSDKVersion has been dropped to API 26 with targetSDKVersion set to 31.
   This change is in compliance with Google and expands the devices that can run DR games.
** [Bugfix] dragonruby-publish updated to write marker files that aren't screaming caps.
** [Bugfix] Removed unnecessary parameter from ~Geometry::line_angle~.
** [Bugfix] Intermittent re-rendering of an already rendered frame no longer occurs.
   You should see a reduction of checker box render_targets (that have been defined)
   and a reduction in "studders" on high refresh rate monitors.
** [Bugfix] Hash methods that have been deprecated now get reported in logs.
   This also fixes an issue with using these deprecated methods with compile_ruby=true.
** [Bugfix] ~$gtk.reset_and_replay~ starts replay on the correct frame.
   Replays invoked via this fucntion are no longer shifted by one frame.
** [Bugfix] Mouse grab configuration is reinitialized if a game enters or leaves fullscreen mode.
   Setting mouse grab and then entering/leving fullscreen mode no longer restricts mouse
   movement to the previous window size.
* 5.12
** [Pro] [Android] minSdk for Android APK exports lowered to API 30.
   Lowering the minSdk for APK export ensures compatibility with
   Retroid Pocket (which is a great test device).

   NOTE: The Google Play export remains unchanged (minSDK 31) to ensure
   compliance with game submission requirements.
** [Pro] [Bugfix] ~args.grid.offset_(x|y)~ reports correct values when your game metadata has ~hd_letterbox~ set to ~false~.
** [Samples] Bouncing ball sample app reworked/simplified.
   Sample app location: =samples/04_physics_and_collisions/11_bouncing_ball_with_gravity=
** [Support] New geometry functions + docs added (extracted from bouncing ball sample app).
   - Geometry::line_angle
   - Geometry::vec2_dot_product
   - Geometry::vec2_normalize
   - Geometry::line_vec2
   - Geometry::vec2_magnitude
   - Geometry::distance_squared
   - Geometry::vec2_normal
   - Geometry::circle_intersect_line?
   - Geometry::line_normal
   - Geometry::point_on_line?
** [Support] args.audio.volume added.
   All sounds for your game can be globally controlled via the ~args.outputs.volume~
   property (float value between 0.0 and 1.0).
** [Support] CTRL+R resets game.
   Pressing CTRL+R will invoke ~$gtk.reset_next_tick~ and is now baked-in (so you don't
   have to code this functionality in every game).
** [Support] "Quick Watch" via ~args.outputs.debug~.
   ~args.outputs.debug~ allows you to pass in a ~String~ as a primitive. This is helpful for quickly showing the
   value of a variable on the screen. A label with black text and a white background will be created
   for each ~String~ sent in. The labels will be automatically stacked vertically for you.

   Example:

   #+begin_src
     def tick args
       args.state.player ||= { x: 100, y: 100 }
       args.state.player.x += 1
       args.state.player.x = 0 if args.state.player.x > 1280

       # the following string values will generate labels with backgrounds
       # and will auto stack vertically
       args.outputs.debug << "current tick: \#{args.state.tick_count}"
       args.outputs.debug << "player x: \#{args.state.player.x}"
     end
   #+end_src

   If you want something more fully-featured, check out Palantir: https://kfischer-okarin.itch.io/palantir
** [Support] T-Pose placeholders sprites added to zip.
** [Support] Warnings added if file paths contain spaces or capital letters (xplat compatibility guarantees).
** [Support] Nintendo Switch Pro Controller mappings added/are now built-in.
** [Docs] Syntax highlighting added to code examples.
** [Bugfix] Setting your sprite path to ~:solid~ (an alias for ~:pixel~) no longer warns that it must be marked as ~transient!~.
** [Bugfix] Web builds automatically mark all render targets as ~transient!~.
   Non-transient render targets causes durable caching of the texture and is needed if the
   game is resized while running under DirectX. This is not needed for web builds (and may not be
   needed for Mac/Linux builds, but further testing needs to be done).
* 5.11
** [Samples] Added sample app that shows how to implement a clipping of a sprite.
   Sample app location: =samples/07_advanced_rendering/01_render_targets_clip_area=
** [Bugfix] Audio was not looping in web builds. This has been fixed.
** [Bugfix] ~args.inputs.keyboard.key_held.char~ has parity with ~.key_(up|down).char~.
   NOTE: ~args.inputs.keyboard.key_held.char~ will only return the ascii value
         of the last key that was held. Use ~args.inputs.keyboard.key_held.truthy_keys~
         to get an ~Array~ of ~Symbols~ representing all keys being held.

         To get a picture of all key states ~args.inputs.keyboard.keys~ returns a ~Hash~
         with the following keys: ~:down~, ~:held~, ~:down_or_held~, ~:up~.

    This is a demonstration of the behavior (see =./samples/02_input_basics/01_keyboard= for a more detailed example):

    #+begin_src
      def tick args
        # uncomment the line below to see the value changes at a slower rate
        # $gtk.slowmo! 30

        keyboard = args.inputs.keyboard

        args.outputs.labels << { x: 30,
                                 y: 720,
                                 text: "use the J key to test" }

        args.outputs.labels << { x: 30,
                                 y: 720 - 30,
                                 text: "key_down.char: #{keyboard.key_down.char.inspect}" }

        args.outputs.labels << { x: 30,
                                 y: 720 - 60,
                                 text: "key_down.j:    #{keyboard.key_down.j}" }

        args.outputs.labels << { x: 30,
                                 y: 720 - 30,
                                 text: "key_held.char: #{keyboard.key_held.char.inspect}" }

        args.outputs.labels << { x: 30,
                                 y: 720 - 60,
                                 text: "key_held.j:    #{keyboard.key_held.j}" }

        args.outputs.labels << { x: 30,
                                 y: 720 - 30,
                                 text: "key_up.char:   #{keyboard.key_up.char.inspect}" }

        args.outputs.labels << { x: 30,
                                 y: 720 - 60,
                                 text: "key_up.j:      #{keyboard.key_up.j}" }
      end
    #+end_src
* 5.10
** [Support] [Pro] Support added for XCode 15.0, 15.1 Beta and MacOS Sonoma.
   ~$wizards.ios~ enhanced to automatically install a default device if none are present.

   NOTE: If you are creating C Extensions for iOS, take note that a new linker
         has been released with XCode 15.0 and may require you to add the following
         compilation flags for your C Extension to work: ~-Wl,-undefined -Wl,dynamic_lookup~
** [Support] Added ~$wizards.itch.help~ and ~$wizards.ios.help~.
* 5.9
** [Support] [Pro] HD scaling "stretch to fit" has been added.
   There are scenarios where a dev may prefer their HD game to fill the entire screen
   instead of being pixel perfect. To set your game to be scaled to fit (while still
   retaining a 16:9 aspect ratio) set the following properties inside of =./metadata/game_metadata.txt=:

   #+begin_src
     # enables hd mode
     hd=true
     # render the letterbox
     hd_letterbox=true
     # set the scaling to "stretch to fit"
     hd_max_scale=0
   #+end_src

   A full explanation of the hd_max_scale property is available in =./metadata/game_metadata.txt=.
** [Support] [Pro] Added support for texture atlases for games at 1.75 scale (2240x1260)
   See args.grid documentation for details of this new texture atlas.
** [Bugfix] Defining a top level method called ~event~ no longer causes an exception.
** [Support] ~args.layout.rect~ and ~args.layout.debug_primitives~ documentation added.
** [Support] ~args.layout.debug_primitives~ enhanced to provide more layout information.
* 5.8
** [Bugfix] Wasm/web builds with looping audio no longer crash.
* 5.7
** [Support] Added ~Geometry::find_all_intersect_rect_quad_tree~.
   Function is useful if ~Geometry::find_all_intersect_rect~ doesn't meet performance needs
   and if the rectangles that are checked do not change every frame. See docs for usage example.
** [Support] Better handling of full screen mode for web builds.
** [Bugfix] [mRuby] ~Hash~ double-splat format in mRuby 3.0 changed the original Hash. This has been fixed.
   Special thanks to Sophira@discord and Pineapple@discord for finding and bumping the fix of this issue.
*** Repro:
    #+begin_src
      hash   = { }
      result = { **hash, a: 1, b: 2 }
      puts result # { a => 1, b => 2 }
      puts hash   # Wrong: { a => 1, b => 2 } mRuby <3.2 (DragonRuby <5.7)
      puts hash   # Right: { }                mRuby 3.2 (DragonRuby 5.7)
    #end_src
*** Pertinent links that describe the issue in detail:
    - https://github.com/mruby/mruby/commit/12d51807a397545d5cf423d2f9bd9b52b7e4a786
    - https://github.com/mruby/mruby/pull/5640#issuecomment-1032674395
*** Performance of ~Hash~ double-splat vs ~merge~ vs ~merge!~
    #+begin_src
      def tick args
        if args.inputs.keyboard.key_down.r
          args.gtk.console.show
          args.gtk.benchmark iterations: 100000,
                             merge: -> () {
                               a = { x: 0, y: 0, w: 100, h: 100 }
                               b = { r: 255, g: 255, b: 255 }
                               c = { anchor_x: 0.5, anchor_y: 0.5 }
                               d = { path: :pixel }
                               e = a.merge(b).merge(c).merge(d)
                             },
                             merge_bang: -> () {
                               a = { x: 0, y: 0, w: 100, h: 100 }
                               b = { r: 255, g: 255, b: 255 }
                               c = { anchor_x: 0.5, anchor_y: 0.5 }
                               d = { path: :pixel }
                               e = a.merge(b).merge!(c).merge!(d)
                             },
                             splat: -> () {
                               a = { x: 0, y: 0, w: 100, h: 100 }
                               b = { r: 255, g: 255, b: 255 }
                               c = { anchor_x: 0.5, anchor_y: 0.5 }
                               d = { path: :pixel }
                               e = { **a, **b, **c, **d }
                             }
        end
      end

      * BENCHMARK RESULT: splat is fastest
      ** Iterations: 100000
      ** Fastest:    splat
      ** Second:     merge_bang
      ** Margin %:   merge_bang was 465% slower than splat
      ** Margin ms:  merge_bang took 723ms longer than splat (198ms vs 921ms)
      ** Times:
      *** splat: 198ms (0% 0ms).
      *** merge_bang: 921ms (465% 723ms).
      *** merge: 993ms (502% 795ms).
    #+end_src
* 5.6
** [Pro] [Android] Minimum target SDK increased to API 31 to stay in compliance with Google Play App submissions.
** [Pro] [BugFix] Nested required files are correctly loaded if ~compile_ruby=true~ for the game's metadata.
   Thank you to dishcandanty@discord and drewhamlett@discord for repro and testing support.
** [Performance] ~args.geometry.find_all_intersect_rect rect, rects~ is 20% faster.
** [Docs] Docs added for ~args.audio[].(x|y|z)~ usage.
** [Support] ~$gtk.platform? key~ and ~$gtk.platform_mappings~ provides more granularity across platforms.
   Example:
   #+begin_src
     def tick args
       label_style = { x: 640, y: 360, anchor_x: 0.5, anchor_y: 0.5 }
       if    args.gtk.platform? :macos # other options :win, :linux, :web, :android, :ios
         args.outputs.labels << { text: "I am running on MacOS.", **label_style }
       elsif args.gtk.platform? :touch
         args.outputs.labels << { text: "I am running on a device that supports touch (either iOS/Android native or mobile web).", **label_style }
       elsif args.gtk.platform? :steam
         args.outputs.labels << { text: "I am running via steam (covers both desktop and steamdeck).", **label_style }
       elsif args.gtk.platform? :steam_deck
         args.outputs.labels << { text: "I am running via steam on the Steam Deck (not steam desktop).", **label_style }
       elsif args.gtk.platform? :steam_desktop
         args.outputs.labels << { text: "I am running via steam on desktop (not steam deck).", **label_style }
       end
     end
   #+end_src
   For additional details, see docs.
** [Support] ~Numeric#frame_index~ now supports allows for a ~repeat_index:~ option.
   The ~repeat_index~ option is helpful if your sprite animation has start frames that shouldn't be
   considered if a loop of the animation occurs.
   Example:
   #+begin_src
     def tick args
       start_looping_at = 0

       sprite_index =
         start_looping_at.frame_index count: 5,        # sprite animation contains 2 starting frames
                                      hold_for: 4,
                                      repeat: true,
                                      repeat_index: 2, # start from index 2 on repeat
                                      tick_count_override: args.state.tick_count

       sprite_index ||= 0

       args.outputs.sprites << [
         640 - 50,
         360 - 50,
         100,
         100,
         "sprites/dragon-\#{sprite_index}.png"
       ]
     end
   #+end_src
** [Support] Added ~args.inputs.locale~.
   The function returns the ISO 639-1 two-letter language code based on OS preferences (see https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes).
** [BugFix] ~Numeric#idiv~ returns correct values for integer division of negative numbers.
   Thank you to Ceph@discord for pointing out this oversight.
** [Samples] Added sample app that shows how to use the new ~repeat_index~ parameter on ~Numeric#frame_index~.
   Sample app location: =samples/03_rendering_sprites/03_animation_states_intermediate=
* 5.5
** [Bugfix] [Support] Web builds hosted on Itch.io were not working in
   Safari nor Chrome on Android. We found a work around for this.
** [Samples] Added sample app that shows how to use cameras in combination with a tactical grid.
   Location of sample app: =./samples/07_advanced_rendering/16_camera_space_world_space_simple_grid_map=
* 5.4
** [Bugfix] ~outputs.static_primitives~ now correctly renders labels.
* 5.3
** [Support] ~require~ statements are now processed synchronously.
** [Support] ~require_relative~ added.
** [Bugfix] ~outputs.primitives~ checks for ~draw_override~ before looking for ~primitive_marker~.
* 5.2
** [Support] [Pro] [Indie] C Extensions can be used on WASM/Web builds.
   $gtk.dlopen will look for assemblies under =mygame/native/emscripten-wasm=.
   See Emscripten docs for compilation steps [[https://emscripten.org/docs/getting_started/downloads.html]].
** [Samples] Added sample app that shows how to convert from screen space to world space without using matricies.
   Location of sample: =./samples/07_advanced_rendering/16_camera_space_world_space_simple=
   NOTE: This is a **really** good foundation for building games with a camera that pans and zooms.
** [Support] Suppress framerate warnings after a recent =$gtk.reset= or =require= of a file.
** [Bugfix] Passing nested enumerables to ~args.outputs...~ no longer causes a segfault.
* 5.1
** [Support] Added ~Inputs#last_active~.
   This function returns the last active input which will be set to either ~:keyboard~,
   ~:mouse~, or ~:controller~. The function is helpful when you need to present on screen
   instructions based on the input the player chose to play with.

   Example:

   #+begin_src
     def tick args
       if args.inputs.last_active == :controller
         args.outputs.labels << { x: 60, y: 60, text: "Use the D-Pad to move around." }
       else
         args.outputs.labels << { x: 60, y: 60, text: "Use the arrow keys to move around." }
       end
     end
   #+end_src
** [Support] Added ~cvars~ attribute accessor to the ~attr_gtk~ class macro.
   ~args.cvars~ is now accessible at the top level if you mix in ~attr_gtk~.

   Example:

   #+begin_src
     class Game
       attr_gtk

       def tick
         # render a label with the game version
	 # (no need to prefix cvars with args)
         outputs.labels << {
           x: 640,
           y: 360,
           text: args.cvars["game_metadata.version"].value.to_s
         }
       end
     end

     def tick args
       $game ||= Game.new
       $game.args = args
       $game.tick
     end

     def reset
       $game = nil
     end
   #+end_src
** [Bugfix] [Pro] [iOS] Regress in ~$wizards.ios.start env: :sim~ has been resolved.
** [OSS] [Performance] More preformant ~Numeric#map(_with_index)~ and Numeric#each(_with_index).
   Performance improvements made to ~NumericHmap~ and ~Numeric#each~. Special thanks to
   levi@discord for contributing this optimization.
* 5.0
** [IMPORTANT] This release has minor breaking changes. They are trivial to resolve.
   Come to the DragonRuby Discord Server and we'll help you work through any issues.
   [[http://discord.dragonruby.org]].

   Let's start with some good news before we get into breaking changes.
** [Support] Portrait orientation enabled for all license tiers.
   In ~metadata/game_metadata.txt~, you can set ~orientation=portrait~ and DragonRuby will
   be presented with a logical resolution of 720x1280.
*** Limitations/Differences between license tiers.
    - The Standard and Indie tier's portrait resolution is fixed to 720p (with scaling).
    - You will still need a Pro license to:
      - Render your game in HD and High DPI.
      - Leverage auto-discovery of Texture Atlases (Pro tier supports resolutions
        from 720p all the way to 4K).
      - Deploy your portrait (or landscape) game to a mobile device, AppStore, or Google Play.
      - Render outside of the game's safe area (All Screen Mode).

    I hope the ability to prototype a portrait game encourages y'all to upgrade to Pro
    so you can play the games you've built on your phone (even potentially release to the AppStore and Google Play).

    Okay, now for the breaking changes (don't panic).
** [BREAKING] ~Outputs#sounds~ no longer supports looping audio. Migrate to ~Args#audio~.
   If you were passing ~.ogg~ values into ~args.outputs.sounds~, they will no longer loop.
   All sounds passed into this collection will play once. Example:

   #+begin_src
     def tick args
       if args.state.tick_count == 0
         # bg music will not loop after completion
         # use args.audio to create a looping (see below)
         args.outputs.sounds << "sounds/bg-music.ogg"
       end
     end
   #+end_src

   If you want audio to loop, use ~args.audio~:

   #+begin_src
     def tick args
       if args.state.tick_count == 0
         args.audio[:bg_music] = { input: "sounds/bg-music.ogg", looping: true }
       end
     end
   #+end_src

   A warning message will be provided by DrangonRuby if we detect this breaking change.
** [BREAKING] Property based access in ~Hash#(x|x1|y|y1)~ no longer consults ~rect~, ~point~.
   The following behavior of ~Hash#(x|x1|y|y1)~ was experimental and has been removed
   (it's unlikely that you even used this/knew about it).

   This is the ~Hash~ behavior in 4.x:

   #+begin_src
     some_hash = {
       rect: {
         x: 0,
	 y: 0,
	 w: 100,
	 h: 100
       }
     }

     puts some_hash.x      # -> 0
     puts some_hash.x1     # -> 0
     puts some_hash.rect.x # -> 0
   #+end_src

   This is the ~Hash~ behavior in 5.0:

   #+begin_src
     some_hash = {
       rect: {
         x: 0,
	 y: 0,
	 w: 100,
	 h: 100
       }
     }

     puts some_hash.x      # -> nil
     puts some_hash.x1     # -> nil
     puts some_hash.rect.x # -> 0
   #+end_src

   Keep an eye out for null reference exceptions and check to see if
   you have a nested ~Hash~ with ~rect~ or ~point~ defined.

   That's it for breaking changes.
** [Pro] [iOS] Device type can now be specified when deploying to the simulator.
   ~$wizards.ios.start~ now supports a ~:sim_name~ argument. If you don't provide one, your
   game will be deployed to an iPhone 14 Simulator. Example:
   #+begin_src
     # this command is run in DragonRuby's Console
     $wizards.ios.start env: :sim, sim_name: "iPad Pro (12.9-inch)"
   #+end_src

   To get available simulators, you can run the following command in a terminal window:
   #+begin_src
     # run this command in a terminal window (not DragonRuby's Console)
     xcrun simctl list devices
   #+end_src

   A thank you goes out to death.au@discord for adding this capability.
** [Performance] Performance of property based access of ~Hash~ values improved by ~30%.
   Expect to see a small improvement if you use attribute accessors to access
   ~Hash~ values (eg ~some_hash.x~ vs ~some_hash[:x]~).
** [Support] Framerate notification made less obtrusive and can be disabled.
   The FPS warning messages have been modified to not show a wall of text in the console. You
   can toggle the warning message using ~$gtk.(disable|enable)_framerate_warning!~. It is enabled
   by default and will provide instructions on how to disable the warning in case you forget
   this method name.
** [Bugfix] Fixed segfault of http apis if an invalid URL is passed to the function.
   A thank you goes out to thedarkmagi@discord for finding unique was of crashing
   DragonRuby.
** [Bugfix] ~$gtk.reset_and_replay FILE_NAME, speed: SPEED~ no longer ignores the ~speed:~ parameter.
** [Bugfix] Invocation of ~$gtk.calcstringbox~ no longer crashes web builds.
** [Docs] Added more comprehensive docs for ~args.audio~ and fixed code examples for ~gtk.http_post~.
** [OSS] ~attr_gtk~ implementation uses predictable ~iVar~ names.
   A number of people got tripped up in using ~attr_gtk~ because of the ~@__PROP__~ naming
   convention. The naming convention has been changed to the more easily guessable ~@PROP~ format.
** [mRuby] Small performance improvements to ~mrb_obj_value~ have been made.
   The function is used frequently in the virtual machine, so it was worth making a small change
   to this runtime machinery.
** [Note] My sincerest condolences to anyone upset about not having a DR version number of 4.20.
* 4.19
** [Pro] [iOS] Fixed iOS wizard's simulator deployment.
** [Performance] Small performance improvement to value retrieval of ~Hash~ via ~method_missing~.
** [Docs] Docs added/refined for creating a new DR project and deploying/publishing.
** [Bugfix] Web games no longer crash on Safari.
   Note: There is a bug in how Itch.io serves up the COOP COEP headers which needs to be fixed
   before web games will work with Safari (opening up an issue with Itch pending).
* 4.18
** [Pro] C Extensions has access to more mRuby functions.
   You now have access to ~300 mRuby functions. For usage example, see the sample located at
   =samples/12_c_extensions/04_handcrafted_extension_advanced=

   Let us know if there's a function you'd like added that we haven't exposed.
** [Bugfix] ~Module#purge_class~ takes into consideration the module context as opposed to ~Object~.
   This function is useful if you want to reinitialize a class without functions that may have been
   added via hotloading but no longer exist in source code.
** [Docs] New doc pages have been added.
   - Both Docs and Samples are located at =index.(html|txt)=
   - Just Docs are located at =docs.(html|txt)=
   - Just Samples are located at =samples.(html|txt)=
   - Open Sourced files are located at =oss.txt=

   The pages above are located locally within the =./docs= directory and online at [[docs.dragonruby.org]]

   Note:
   This change breaks permalinks to samples. If you have them referenced anywhere, you'll need to update them. :sadpanda:
* 4.17
** [Bugfix] Fixed file persistence accross sessions for webbuilds.
** [Performance] Added caching of symbols used in C (minor performance improvement in load times).
* 4.16
** [Pro] [iOS] Http apis have been re-introduced.
   Http apis have be re-enabled and use standard iOS apis as opposed
   to libcurl. With this change, TestFlight builds will be accepted by
   Apple.
*** IMPORTANT:
    Apple requires all http requests to be over https and will not allow http
    requests to unencrypted urls in production packages (they are allowed in
    dev builds however).
** [Pro] [iOS] Simulator support added.
   You can now run your game with hotloading directly on the simulator. From the
   Console run ~$wizards.ios.start env: :sim~ to do so.
** [Performance] Web builds now use Emscripten multi-threaded capabilities.
   Expect to see performance improvements for web builds with this change.
*** IMPORTANT:
    Web builds now require the use of SharedArrayBuffer which will require you to
    enable the ~SharedArrayBuffer~ support on Itch.io. An error message will be presented
    on your game if we detect that this property has not been set correctly.

    If you are self-hosting your game, your web server must include the COOP and COEP HTTP
    Headers for your game to work. More info here: [[https://web.dev/coop-coep/]].

    Additionally, because of these headers, you will not be able to issue HTTP requests
    within a web build that are Cross Origin.
** [Support] ~Added $gtk.disable_controller_config!~
   DR presents a wizard for unrecognized controllers. If you want to disable this wizard,
   you can use this function to do so.
** [Performance] Label rendering performance improved.
   Expect to see a performance improvement of over 100% for labels rendered as Hashes, Entities, and Classes.
** [Support] ~$gtk.reset~ also resets sprites.
   If you do not want to reset sprites, you can do ~$gtk.reset include_sprites: false~
** [Support] Current DragonRuby version and changelog can be viewed at [[version.dragonruby.org]] and [[changelog.dragonruby.org]].
* 4.15
** [Bug] Removed mouse wheel debug statements.
** [Bug] Fixed regression where ~args.inputs.mouse.click~ no longer returned true for middle and right mouse buttons.
** [Support] Added ~args.inputs.keyboard.active~ and ~args.inputs.controller.active~.
   This property will return true if any of keys or buttons were pressed on the frame. It's useful
   for instructions contextually to which input device the player is using.
* 4.14
** [Pro] [iOS] Fixed iOS binaries so that they will be accepted by TestFlight.
   We have temporarily disabled $gtk.http_* apis until we isolate what part of that machinery
   is causing the binary to be rejected by TestFlight (instead of waiting for Apple to fix the bug
   on their end).
** [Pro] [iOS] Fixed scaling issue for games running on the iPhone 13 mini.
** [Support] ~$gtk.platform~ returns "Steam Deck" if the game is being run on the Steam Deck.
** [Bugfix] Removed mouse wheel debug statements.
** [Docs] Added docs for deploying to a local Steam Deck.
* 4.13
** [Indie] [Pro] [Support] Beta release of Steam distribution.
   ~dragonruby-publish~ now supports the creation of a Steam package containing Linux, Mac, and Windows
   binaries of your game. A "Deploying to Steam" section has been added to documentation (which is available
   locally at http://localhost:9001 while DragonRuby is running, and online at docs.dragonruby.org).

   This is a beta iteration with some rough edges. Let us know if you hit any snags and we'll improve
   on what's there.

   This enhancement to ~dragonruby-publish~ streamlines should prove to be a significant time
   saver over creating payload using the Steamworks toolchain :-)
** [Bugfix] Pixel arrays are now fully unlocked in the Standard license tier. I swear it'll work this time.
** [Performance] Increased the polling interval between simulation ticks for web builds.
   The faster interval helped top end machines, but taxed lower end machines a bit too much. Frame rates
   will be more stable, but a little lower.
** [Bugfix] Mouse wheel input will now be captured during game play recording.
* 4.12
** [Bugfix] Fixed Raspberry Pi and Web Builds.
   Tweetcart updates had an unintended side effect and broke Raspberry Pi and Web. Regression suites
   have been put into place to make sure this doesn't happen again. We are unsure of why only those platforms
   failed, but think it's a bug in mRuby's byte-code compilation (further research needed).

   The Tweetcart source has been mostly reverted except for the addition of the ~pixels!~ and ~color~
   method. We will work with OSS contributers to flesh out a fuller api (and get closer to finding the
   mRuby bug).
* 4.11
** [Support] $args.pixel_arrays unlocked for all license tiers!
   All license tiers can now use both sound synthesis and pixel arrays, woo hoo!
** [OSS] Tweetcart apis enhanced given that everyone can now use pixel arrays.
   Source code for tweetcart.rb is available locally at =./docs/oss.txt= and
   online at https://github.com/DragonRuby/dragonruby-game-toolkit-contrib/blob/main/dragon/tweetcart.rb.

   You can download the GitHub version by invoking the following function in the DragonRuby Console:

   $gtk.download_stb_rb "dragonruby", "dragonruby-game-toolkit-contrib", "tweetcart.rb"

** [Support] A "Close Console" button is available in a production build.
   It's helpful to have this button if you're running the game on a touch device w/o a keyboard.
** [Bugfix] render_targets will no longer be randomly lost on Windows+DirectX when the game window is resized.
   No really. This time we definitely maybe have this resolved.
** [Performance] Performance improvements to web builds.
   Invocation of ~tick~ should happen more frequently bringing web games closer to 60fps.
* 4.10
** [Samples] Added sample app that shows how to orchestrate a sequence of animations (eg cutscenes).
   Sample app location: =./samples/08_tweening_lerping_easing_functions/08_cutscenes=
** [Samples] Added sample app/reference implementation for a casual arcade game.
   Sample app location: =samples/99_genre_arcade/squares=
** [Samples] Added sample that show's how to create an animated button.
   Sample app location: =./samples/08_tweening_lerping_easing_functions/04_pulsing_button=
** [Samples] Added sample app that shows how to do drag and drop (leverages ~$args.inputs.mouse.held~).
   Sample app location: =./samples/02_input_basics/04_mouse_drag_and_drop=
** [Bugfix] dragonruby-publish ignores unrecognized files as opposed to failing (such as .DS_Store).
** [Bugfix] dragonruby-publish gives a better error message if game icons are too large.
** [Bugfix] render_targets will no longer be randomly lost on Windows+DirectX when the game window is resized.
   NOTE: There was a lot of rewiring of outputs and render targets behind the scenes. Please bring up
   issues on the DragonRuby Discord Server's feedback-bug-report channel: https://discord.com/channels/608064116111966245/895482347250655292
** [Support] ~args.outputs.background_color~ now accepts both colors in Hash and Array format.
** [Support] Added ~$gtk.reset_sprites "DIRECTORY"~.
   Function will recursively go through the the directory provided and reset
   all pngs/invalidate DR cached textures.
** [OSS] Open sourced =./runtime/async_require.rb=.
   Source code is under =./docs/oss.txt=
** [Support] Added ~$gtk.download_stb_rb~ and ~$gtk.download_stb_rb_raw~.
   These two functions can help facilitate the integration of external code files. OSS contributors
   are encouraged to create libraries that all fit in one file (lowering the barrier to
   entry for adoption).

   Examples:

   #+begin_src
     def tick args
     end

     # option 1:
     # source code will be downloaded from the specified GitHub url, and saved locally with a
     # predefined folder convension.
     $gtk.download_stb_rb "https://github.com/xenobrain/ruby_vectormath/blob/main/vectormath_2d.rb"

     # option 2:
     # source code will be downloaded from the specified GitHub username, repository, and file.
     # code will be saved locally with a predefined folder convension.
     $gtk.download_stb_rb "xenobrain", "ruby_vectormath", "vectormath_2d.rb"

     # option 3:
     # source code will be downloaded from a direct/raw url and saved to a direct/raw local path.
     $gtk.download_stb_rb_raw "https://raw.githubusercontent.com/xenobrain/ruby_vectormath/main/vectormath_2d.rb",
                              "lib/xenobrain/ruby_vectionmath/vectormath_2d.rb"
   #+end_src
* 4.9
** [Support] ~anchor_x~, ~anchor_y~ has been added to labels, borders, and solids.
   For labels the following lines are equivalent:
   #+begin_src
     def tick args
       args.outputs.labels << {
         x: 640,
	 y: 360,
	 text: "Hello World",
	 alignment_enum: 1,
	 vertical_alignment_enum: 1
       }

       args.outputs.labels << {
         x: 640,
	 y: 360,
	 text: "Hello World",
	 anchor_x: 0.5,
	 anchor_y: 0.5
       }
     end
   #+end

   If ~alignment~ keys and ~anchor~ keys are both provided, the ~anchor~ keys
   will be given precedence (their default value is ~nil~).

   Borders and solids behave like sprites (see notes about ~anchor~ attributes in 4.8 change log).
** [Support] ~args.geometry.intersect_rect?~ and ~args.geometry.inside_rect?~ respect ~anchor_x~ and ~anchor_y~.
   Given the addition of the ~anchor~ properties to rect-based primitives, these geometry functions
   have been updated to take the new properties into consideration when determining collision.

   A special thank you to @Ceph@discord and @Dominic@dicord for making a valid case
   for adding this functionality through the use of a sample app that ships with DR.
** [Support] ~$gtk.set_window_scale(float_value)~ has been added.
   This is a helper method that will resize your game window to a scaled 16:9 (or 9:16 for portrait mode) aspect ratio.

   The float value that is passed into the method will be floored to the closest supported scale:
   0.1 (160x90), 0.25 (320p), 0.5 (540p), 0.75 (960p), 1.0 (720p), 1.25 (HD+), 1.5 (1080p), 2.0 (1440p), 2.5 (1800p), 3.0 (4k), 4.0 (5k)

   Note: This method does not change the logical resolution of 1280x720 (it just resizes your game window).
** [Support] Added ~args.inputs.mouse.held~
   This method compliments ~args.inputs.mouse.(click|down)~ and ~args.inputs.mouse.up~ (about damn time).
** [Samples] Animation states sample app has been updated to leverage the new ~anchor_x~, ~anchor_y~ properties.
   Sample app location: =./samples/03_rendering_sprites/03_animation_states=
** [Samples] Clepto Frog sample app has been rewritten/significantly cleaned up.
   The Clepto Frog platformer reference implementation has been cleaned up significantly.
   The reference implementation covers the following concepts in a non-trivial way:
   - Rendering a camera viewport with pan and zoom using render targets.
   - Saving and loading game data from the file system.
   - In-game map editor.
   - Physics and AABB collision.
** [Samples] Added sample app that covers ~args.layout.rect~ apis in landscape mode.
   Originally this sample app was only written for portrait mode and was only available to
   Pro users.
   Sample app location: =./samples/07_advanced_rendering/18_layouts=

   Note: ~args.layout.rect~ is an extremely valuable bit of machinery when it comes to laying out
   menu systems and ui elements (definitely worth getting familiar with).
** [Samples] Added sample app that shows how to convert from camera space to world space using matrices.
   Sample app location: =./samples/07_advanced_rendering/16_matrix_camera_space_world_space=
** [Bugfix] Minor typo fixes/elaborations added to the docs. Minor updates to purchase matrix on dragonruby.org and itch.io.
* 4.8
** [Bugfix] [Pro] [Android] ~dragonruby-publish~ generates apks and aab's that are compatible with SDK 33+.
** [Support] Replay speed for recordings can be as high as 60x (increased from 7x).
** [Support] ~anchor_x~ and ~anchor_y~ added to Sprites.
   The default value for ~anchor_x~ and ~anchor_y~ is ~nil~. Setting the value to (for example) 0.5, 0.5
   will shift the x left by 50% of the width and y down by 50% of the height.

   Example:
   #+begin_src
     def tick args
       # sprite's bottom left corner will be at 640, 360
       args.outputs.sprites << { x: 640, y: 360, w: 30, h: 30, path: "sprites/square/blue.png" }

       # sprite's bottom left corner will be at 625, 345 (sprite's center will be at 640, 360)
       args.outputs.sprites << { x: 640, y: 360, anchor_x: 0.5, anchor_y: 0.5, w: 30, h: 30, path: "sprites/square/blue.png" }

       # sprite's bottom left corner will be at 625, 360 (sprite is centered on the x axis, and bottom aligned on the y axis)
       args.outputs.sprites << { x: 640, y: 360, anchor_x: 0.5, anchor_y: 0, w: 30, h: 30, path: "sprites/square/blue.png" }
     end
   #+end_src
*** Advanced sprite rendering.
    If you are using classes with ~draw_override~. The ~ffi_draw.draw_sprite_5~ function is available to use:

    The argument order for ffi_draw.draw_sprite_5 is:
    - x, y, w, h,
    - path,
    - angle,
    - alpha, red_saturation, green_saturation, blue_saturation
    - tile_x, tile_y, tile_w, tile_h,
    - flip_horizontally, flip_vertically,
    - angle_anchor_x, angle_anchor_y,
    - source_x, source_y, source_w, source_h,
    - blendmode_enum
    - anchor_x
    - anchor_y

    See =./samples/09_performance/07_static_sprites_as_classes_with_custom_drawing= how to use ~draw_override~.
** [Support] ~size_px~ added to Labels.
   A label's size can be provided via ~size_enum~ or ~size_px~. If both are provided ~size_px~ will
   be used (the default value of ~size_px~ is nil).
   #+begin_src
     def tick args
       # render a label in the center of the screen with a pixel
       # height of 22 pixels
       args.outputs.labels << { x: 360,
                                y: 640,
				text: "Hello World."
				size_px: 22,
				alignment_enum: 1,
				vertical_alignment_enum: 1 }
     end
   #+end_src
** [Samples] Added sample app that shows how to create a camera with multiple targets
   Sample located at: =./samples/07_advanced_rendering/07_simple_camera_multiple_targets=
** [Samples] Added sample app that shows how to do raycasting with textures and projectiles.
   Sample located at: =./samples/99_genre_3d/04_ray_caster_advanced=

   A HUGE thank you to James (@68K@discord) for contributing this sample app and giving
   us a solid starting point for creating a game like Doom.
** [Bugfix] Numeric functions in docs are now categorized correctly.
** [Support] [Pro] Web builds can now be published with HD and High DPI enabled.
** [Support] Added ~args.inputs.key_up.char~
   The property which will be the character that was just released
   (similar to how ~args.inputs.key_down.char~ represents the
   character that was just pressed)
** [OSS] Open sourced the machinery that controls the processing of
   --no-tick and --tick cli arguments under =./runtime/process_argv.rb=
** [Support] [Advanced] Added ~$gtk.disable_nil_punning!~.
   Nil punning in DR allows you to do the following:

   #+begin_src
     def tick args
       args.state.player.loc.x ||= 100
       args.state.player.armor.hp ||= 500
     end
   #+end_src

   While the above syntax provides convenience while rapidly prototyping, it can lead to null
   reference exceptions that are harder to track down as your project becomes more complex (and is
   now less needed given that ~Hash~ can access ~key/value~ pairs as if they were attributes).
   You can use ~$gtk.disable_nil_punning!~ to disable this capability. Doing so will require you
   to specify the intermediary properties before you can set a value on leaf attributes.

   Example:

   #+begin_src
     def tick args
       # the following lines will throw an exception if nil punning is disabled
       # args.state.player.loc.x ||= 100
       # args.state.player.armor.hp ||= 500

       # instead, you must do

       # option 1
       # fully define state using hashes
       args.state.player ||= {
         loc: {
	   x: 100
	 },
	 armor: {
	   hp: 500
	 }
       }

       # OR

       # option 2
       # fully define state using entities
       args.state.player ||= args.state.new_entity(:player)
       args.state.player.loc ||= args.state.new_entity(:loc)
       args.state.player.loc.x ||= 100
       args.state.player.armor ||= args.state.new_entity(:armor, hp: 500)
     end

     # disabling nil punning must be done outside of tick
     $gtk.disable_nil_punning!
   #+end_src
* 4.7
** [Support] Sound Synthesis is now available at all license tiers, including Standard! Woo hoo!
   Check out the sample app located at: =./samples/07_advanced_audio/02_sound_synthesis/=
** [Samples] Sample app added that shows how to make a Fifteen Puzzle game.
   Location of sample app: =./samples/99_genre_board_game/01_fifteen_puzzle=
** [Bugfix] Standard definition rendering of labels was inaccurate for size_enum and window scales > 1280x720. This has been fixed.
** [Bugfix] Documentation export escapes ruby code that would be interpreted as html.
* 4.6
** [Bugfix] Screenshot api ignores alpha transparency if ~a~ is 255.
** [Bugfix] ~set_mouse_grab(2)~ fixed to behave correctly (it was definitely Ryan's fault).
** [OSS] Open sourced ~$gtk.notify~ machinery.
   Source code for ~GTK::Runtime::Notify~ in =./docs/oss.txt=.
** [OSS] Open sourced ~$gtk.process_argsv~
   Source code for ~GTK::Runtime::ProcessARGSV~ in =./docs/oss.txt=.
** [OSS] Updated the following docs based on feedback in #oss-docs-contributions:
   - Sound docs updated.
   - Various typo and phrasing fixes.
   - ~'~ (single tick) is scrubbed when generating href links for documentation export.
   - Screenshot api docs updated.
** [Samples] Sample app added that shows how to create a checkbox.
   Location of sample: =samples/09_ui_controls/01_checkboxes=
** [Samples] Sample app added that shows how create a button + label using a render_target.
   Location of sample: =samples/07_advanced_rendering/06_buttons_as_render_targets=
** [Samples] Sample app added that shows how to create an animation queue.
   An animation queue is useful for visual effects such as a particle system.
   Location of sample: =samples/08_tweening_lerping_easing_functions/06_animation_queues=
** [Samples] Added sample app that shows how to do a very simple AABB collision.
   Location of sample: =./samples/04_physics_and_collisions/01_simple_aabb_collision_with_map_editor=
** [Samples] Added sample app that shows how to do a AABB collisions with a built in map editor.
   Location of sample: =./samples/04_physics_and_collisions/01_simple_aabb_collision=
** [Samples] Added sample app that shows how to create a render target composed of multiple sprites.
   Location of sample: =./samples/07_advanced_rendering/01_render_targets_combining_sprites=
** [Samples] Added sample app that shows ramp collision.
   This sample app uses the new replay capabilities ~$gtk.replay_and_reset "replay.txt", speed: 2~. A replay
   file is included with the sample so you can see how changing parts of the code affects collision.
   Location of sample: =samples/04_physics_and_collisions/12_ramp_collision=
** [Support] Added ~args.state.geometry.find_all_intersect_rect(rect, rects)~
   Similar to ~args.state.geometry.find_intersect_rect~ but returns a collection opposed to just the first collision. This function is implemented in C.
** [Support] Major enhancements to recording and replay functionality.
*** ~$recording.on_replay_tick &block~ and ~$recording.on_recording_tick &block~ functions:
    These functions can be used to execute code while a recording or replay is executing.

    Example:

    #+begin_src
      def tick args
        ....
      end

      $recording.on_replay_tick do |args|
        # code will be called after each tick of a replay
      end

      $recording.on_recording_tick do |args|
        # code will be called after each tick of a recording
      end
    #+end_src
*** ~$gtk.reset_and_replay FILE, speed: (1 - 7)
    Calling this function at the bottom of a file (outside of tick) will automatically reset your game and run the replay.

    Example:

    #+begin_src
      def tick args
        ....
      end

      # record a replay via the Console by using the menu item or running ~$record.start_recording SEED_NUMBER~.
      # after your recording has been saved, you can use the following line to autoplay the recording
      # on file save
      $gtk.reset_and_replay "replay.txt", speed: 2
    #+end_src
*** ~$recording.start_replay~ accepts a ~speed:~ parameter.
    From the console you can run ~$recording.start_replay FILE, speed: 2~. The maximum replay speed is ~7~.
* 4.5
** [Samples] Sample app that shows how to create a thick line using render targets.
   Location of sample: =./samples/07_advanced_rendering/02_render_targets_thick_lines=
** [Samples] Sample app that shows a large number of AABB collisions with gravity and physics.
   Location of sample: =./samples/09_performance/09_collision_limits_aabb=
   Demo video: https://youtu.be/HQTfqaIxSOA
** [Samples] Sample app that shows advanced scene transitions (with global fade-in and fade-out)
   Location of sample: =./samples/08_tweening_lerping_easing_functions/05_scene_transitions=
** [Support] Added docs for ~args.gtk~ functions.
** [Support] Added docs for ~args.geometry~ functions.
** [Support] Added ~Numeric#lerp(to, step)~ and ~Numeric#remap(r1_begin, r1_end, r2_begin, r2_end)~.
** [Support] Added Easing definitions ~smooth_start_(quad|cube|quart|quint)~ and ~smooth_stop_(quad|cube|quart|quint)~
   Example:

   #+begin_src
    def tick args
      args.state.box ||= {
        x: 0,
        y: 0,
        w: 40,
        h: 40
      }

      final_x = 1240
      final_y = 680

      # if space key is pressed, start animation
      if args.inputs.keyboard.key_down.space
        args.state.animation_start_at = args.state.tick_count
      end

      progress_x = 0
      progress_y = 0

      if args.state.animation_start_at
        perc = args.easing.ease args.state.animation_start_at,     # start tick
                                args.state.tick_count,             # current tick
                                60,                                # duration
                                :smooth_stop_quint                 # easing definition

        progress_x = final_x * perc
        progress_y = final_y * perc
      end

      args.outputs.solids << {
        x: progress_x,
        y: progress_y,
        w: 40,
        h: 40
      }
    end
   #+end_src
* 4.4
** [Bugfix] Triangle rendering of solids respects r,g,b values.
* 4.3
** [Bugfix] Simpler encoding of a game's web-build working directory.
* 4.2
** [Bugfix] Update to web-based builds to remove offset within the Itch.io iFrame. This is definitely maybe fixed this time.
** [Bugfix] Ensure that a game's web-build working directory is unique from game to game.
* 4.1
** [Bugfix] Update to web-based builds to remove offset within the Itch.io iFrame.
* 4.0
** [BREAKING] This release contains two very minor breaking changes (don't panic).
   1. ~args.inputs.mouse.point~ no longer returns a ~Tuple/Array~ and
      instead returns a ~Hash~ with ~x~ and ~y~ keys.

      NOTE: You will ONLY see an issue with this change if you were
      destructing the ~Tuple~.

      Lines like the following:

      #+begin_src ruby
        # destructure tuple no longer supported
        x, y = args.inputs.mouse.point
      #+end_src

      Must be changed to:

      #+begin_src ruby
        # Hash is returned (which cannot be destructured)
        point = args.inputs.mouse.point
        x, y = point.x, point.y
      #+end_src

   2. ~args.inputs.directional_vector~ has a similar change and can no longer
       be destructured.

   That's it for the breaking changes!
** [Pro] [iOS] C Extensions fixed so that they are accepted by TestFlight.
   A sample app has been added that shows how to package C Extensions for iOS:
   =/samples/12_c_extensions/05_ios_c_extensions/=
** [Pro] [Android] Android SDK target is now version 33 (android-ndk-r25b).
** [Bugfix] [Pro] ~all_screen_max_scale~ in combination with ~highdpi~ is respected for both
   landscape and portrait games.
** [Samples] Turn based RPG sample app has been added.
   The sample app is located at: =./samples/99_genre_rpg_turn_based/turn_based_battle=.
** [Performance] Performance improvements to ~Entities~.
   Both ~args.state.new_entity~ and ~args.state.new_entity_strict~
   have gotten some performance improvements. The perf boost to
   ~new_entity_strict~ is significant.
** [Performance] [Support] ~args.gtk.warn_array_primitives!~ added.
   While array based primitives are simple to create, they come with a performance
   penalty. If you find that you're having performance issues, you can use ~warn_array_primitives~
   to find places in your code where array primitives are used/created
   (so you can convert them to ~Hashes~):

   #+begin_src ruby
     def tick args
       # add this line to the top of your tick method
       args.gtk.warn_array_primitives!

       # a log entry will be printed to the Console anywhere array primitives are being used

       # example
       args.outputs.labels << [100, 100, "Hello"]

       # example
       rect_1 = [10, 100, 50, 50]
       rect_2 = [10, 100, 50, 50]
       args.geometry.intersect_rect? rect_1, rect_2
     end
   #+end_src
** [Support] Added out of bounds warning for sprite's ~(source|tile)_(x|y|w|h)~ properties.
   You will be warned if a specified source/tile rect goes past the boundaries of a sprite.
* 3.24
** [Pro] [Bugfix] Triangles can now render a partial area of a render target again.
   Bug was introduced in 3.22.
** [Pro] [iOS] Deployment target SDK for dev builds dropped to 11.0 to match production
   A larger number of iOS devices are supported with this change.
** [Pro] [Bugfix] Portrait orientation is respected even when hd=false.
** [Itch.io] Embedded web builds on Itch.io no longer have a weird margin at the top of the game.
   Itch.io's embed logic for web builds looks to have changed,
   =./.dragonruby/stubs/html5/dragonruby-html5-loader.js= no longer sets the canvas size to 100%
   of the width and height of the container. This seems to have resolved the issue with how Itch
   does its iFrame-based presentation.
** [Support] Added more options and documentation to =metadata/cvars.txt=.
** [Support] Fixed memory leaks in dragonruby-httpd.
** [Support] Remote hotload added to all platforms and works on the Steam Deck.
*** Creating a build with remote hotloading enabled.
    1. Run =./dragonruby-publish --package-with-remote-hotload=.
    2. Start dragonruby in dev mode, binaries under the =./builds= directory will look
       for your dev instance on startup. Machines running the hotload
       enabled binary need to be on the same network as your dev machine.
    3. Code updates will be reflected within the running binaries.

    Note: For iOS run the following from the DragonRuby Console:
          ~wizards.ios.start env: :hotload~
*** Steps to hotload to the Steam Deck:
    Demo video here: https://youtu.be/id5RcgynKwc
**** Easy Setup
     1. Restart the Steam Deck in Desktop Mode.
     2. Copy your game binary onto an SD card and open up the game on your Steam Deck.
**** Advanced Setup (if you want to update your game without needing an SD Card)
     1. Restart the Steam Deck in Desktop Mode.
     2. Open up Konsole and set an admin password via ~passwd~.
     3. Disable readonly mode: ~sudo steamos-readonly disable~.
     4. Update pacman ~sudo pacman-key --populate archlinux~.
     5. Update sshd_config =sudo vim /etc/ssh/sshd_config= and
        uncomment the ~PubkeyAuthentication yes~ line.
     6. Enable ssh: ~sudo systemctl enable sshd~.
     7. Start ssh: ~sudo systemctl start sshd~.
     8. With the steps above you can use ~scp~ to copy the game over
        from your dev machine without needing an SD Card:
        ~scp -R ./builds/SOURCE.bin deck@IP_ADDRESS:/home/<USER>/Downloads~

     Note: Steps 2 through 7 need only be done once.

     Note: ~scp~ comes pre-installed on Mac and Linux. You can
           download the tool for Windows from https://winscp.net/eng/index.php
* 3.23
** [Pro] [VR] Headset and controller orientation added.
   A normalized vector with ~x,y,z~ is returned for all orientations.
   - ~args.inputs.controller_one.left_hand.orientation.(x|y|z)
   - ~args.inputs.controller_one.right_hand.orientation.(x|y|z)
   - ~args.inputs.headset.orientation.(x|y|z)
** [Pro] Texture atlases explicitly search for a single directory to avoid long startup times.
   The default directory that will be searched is =sprites=. You can override the default
   directory by specifying ~sprites_directory=DIRECTORY~ in =game_metadata.txt=.
** [Support] Line and border rendering is 140%+ faster for ~Hash~ and ~Class~ based representations.
** =cvars.txt= added with HTTP logging disabled.
   =./mygame/metadata/cvars.txt= has been added to the zip file with the following contents:
   #+begin_src cvars
   log.filter_subsystems=HTTPServer
   #+end_src
** [Support] ~Numeric#vector~ has been deprecated.
   Use ~Numeric.to_vector~ which returns a ~Hash~ containing ~x~ and ~y~ instead of ~Array/Tuple~.
** [Bugfix] ~args.inputs.shift_(left|right)~ report the correct value in web builds.
* 3.22
** [Pro] [Support] Triangles support texture atlases.
** [Bugfix] ~args.inputs.mouse.relative_(x|y)~ report correct values (regression when HD support was added).
** [Bugfix] Console menu buttons are positioned correctly in both portrait and landscape mode.
** [Samples] Sample app showing scene management added.
   Location: =./samples/02_input_basics/07_managing_scenes/=
* 3.21
** [Pro] [Support] Added portrait mode.
   In =metadata/game_metadata.txt= you can specify ~orientation=portrait~ to build a game that has
   a logical canvas of 720x1280 (as opposed to ~landscape~ 1280x720).
** [Pro] [iOS] ~$wizards.ios.start~ creates the correct metadata file for portrait mode games.
** [Pro] [Support] All Screen renders/scales correctly even if the window is smaller than 720p.
** [Pro] [Support] ~args.layout.rect~ expanded to support Portrait mode (and simplified).
** [Pro] [Support] Render targets render fonts in High DPI if ~highdpi=true~.
** [Support] Added a more descriptive error message to ~dragonruby-publish~ if files needed to publish
   are missing.
** [Samples] Added sample app demonstrating ~args.layout.(rect|rect_group|point)~ for ~orientation=portrait~.
   Location: =./samples/07_advanced_rendering_hd/04_layouts_and_portrait_mode=
** [Samples] Ray Casting sample app added.
   A big thank you to =Brenner Little@discord= for granting permission to include this as a sample app.
   Location: =./samples/99_genre_3d/04_ray_caster=
** [OSS] The following PRs have been merged in from https://github.com/DragonRuby/dragonruby-game-toolkit-contrib.
   - Add documentation section about accessing files #133
* 3.20
** [Pro] [Support] ~highdpi=true|false~ can be specified in =./metadata/game_metadata.txt=.
** [Pro] [Bugfix] Texture atlases respect sprite properties ~tile_(x|y|w|h)~ and ~source_(x|y|w|h)~.
** [Pro] [Bugfix] Texture atlases retrieval respects non-standard game directory.
   If your startup time for your game is still long, DM us on our Discord Server so we can troubleshoot.
** [Pro] [Samples] Added sample app that shows how to use ~args.grid.allscreen_*~ properties.
   Located at =./samples/07_advanced_rendering_hd/03_allscreen_properties=.
** [Support] Api usage for mouse grab is ~$gtk.set_mouse_grab(0|1|2)~, NOT ~$gtk.set_mouse_grab(true|false)~.
   - 0: disables mouse grab.
   - 1: enables mouse grab with cursor visible.
   - 2: enables mouse grab with cursor invisible.

   SDL Hints have been added to help mouse stay within the bounds of the window (MacOS may still be flakey with window boundaries though).
** [Bugfix] game_metadata.txt configuration retrieval increased to 1024 lines.
** [Support] Added ~args.events.resize_occurred~ which will return ~true~ if the game window has been resized.
** [Support] ~scale_quality=0|1|2~ can be specified in =./metadata/game_metadata.txt=. This property can be set at all license tiers.
   - 0: nearest neighbor
   - 1: linear
   - 2: anti-aliasing
* 3.19
** [Pro] Updates to DragonRuby Game Toolkit Pro.
   The following updates have been made to the Pro license.
*** [Support] HD rendering capabilities added.
    HD rendering capabilities have been added for the Pro version of DragonRuby Game Toolkit.
    Detailed documentation can be found inside of =./metadata/game_metadata.txt= and in the sample apps.

    Check out the following YouTube Video for a demo of All Screen Mode: [[https://youtu.be/Rnc6z84zaa4]]

    Check out the following YouTube Video for a demo of Texture Atlases: [[https://youtu.be/gH0XiafWQ_c]]

    Clepto Frog and The Little Probe both have HD Configurations that can be enabled. Be sure to play
    around with them.
**** Rending support for 720p to 5K.
     A sample app that shows HD Rendering can be found at =./samples/07_advanced_rendering_hd/01_hd_labels=

     To enable HD Mode set ~hd=true~ inside of =./metadata/game_metadata.txt=. Your game will
     be scaled to best fit the screen size that it's being run on (labels inside of render targets
     are also rendered at native scale).
**** Texture Atlases.
     A sample app that shows how texture atlases work can be found at =./samples/07_advanced_rendering_hd/02_texture_atlases=

     With HD mode enabled. DragonRuby will automatically use HD sprites given the following
     naming convention (assume we are using a sprite called =player.png=):

     | Name  | Resolution | File Naming Convention (enum) |
     |-------+------------+-------------------------------|
     | 720p  |   1280x720 | =player.png=                  |
     | HD+   |   1600x900 | =<EMAIL>=              |
     | 1080p |  1920x1080 | =<EMAIL>=              |
     | 1440p |  2560x1440 | =<EMAIL>=              |
     | 1800p |  3200x1800 | =<EMAIL>=              |
     | 4k    |  3200x2160 | =<EMAIL>=              |
     | 5k    |  6400x2880 | =<EMAIL>=              |

     The current file naming convention DR is trying to use can be accessed
     via ~args.grid.render_scale_enum~ (be sure to take a look at the sample app).
**** All Screen Mode.
     To enable All Screen Mode set ~allscreen=true~ inside of =./metadata/game_metadata.txt=. All
     Screen Mode removes letter boxing around your game allowing you to render outside of the
     1280x720 logical area (unsafe screen area).

     You can also set ~allscreen_max_scale=RENDER_SCALE_ENUM~ which controls how your games scales
     past 720p (giving you more or less unsafe screen area).

     The following properties on ~args.grid~ can assist in rendering off-screen:

     - ~allscreen_left~
     - ~allscreen_right~
     - ~allscreen_width~
     - ~allscreen_height~
     - ~allscreen_width~
     - ~allscreen_height~
     - ~allscreen_offset_x~
     - ~allscreen_offset_y~
     - ~render_width~
     - ~render_height~

     NOTE: All ~allscreen~ values above are given in the logical 1280x720 scale (you will still continue
     to render your game assuming 1280x720).
*** [Pro] [Support] [VR] Simulator, Device Tracking, Z-Depth Buffering
    Some much needed apis have been added to DragonRuby Game Toolkit VR.
**** Z-Depth Buffering
     Sprites are automatically sorted based off of their ~z~ property (you no
     longer have to manually sort primitives within your game). If you see visual
     tearing, it means that two primitives are competing for the same Z depth
     and one has to be moved forward or back.

     Note: Z-depth buffering assumes that your primitives are not transparent. If
     you are using transparent sprites, you must sort by Z values for cascading
     transparencies to render _mostly_ correctly. This is a fundamental limitation
     of OpenGL. You can read more about this limitation at [[https://www.sjbaker.org/steve/omniv/alpha_sorting.html]].
**** Headset and controller tracking added.
     The following device locations are now available.

     - ~args.inputs.headset.position.(x|y|z)~
     - ~args.inputs.controller_one.left_hand.position.(x|y|z)~
     - ~args.inputs.controller_one.right_hand.position.(x|y|z)~

     Things to note about the values above:

     - All location values are floating point numbers representing the relative distance from the
       Boundary's Origin.
     - The floating point values represent the number of meters away from that origin position.
     - 96 pixels maps to 1 meter in VR.
     - DR's VR world is 1280x1280x1280 (try the VR Skybox sample app to see what that scale looks like).
**** VR Simulator
     You can see how your game looks like in VR using ~./dragonruby-vrsim~ (start up that process as opposed
     to ~./dragonruby~).

     Controls:

     - You can use a USB controller to simulate the buttons on the Oculus Quest controllers.
     - Pressing =ctrl+m= will capture mouse and keyboard input. Use =WASD/arrow= keys to move
       around the VR environment. Mouse to look around. and =I= and =K= to fly up and down in the
       VR environment.
     - Press =ctrl+g= to exit mouse capture.

     Note: There's still a bit of work that needs to be done with the simulator. Hope you find what's
     currently there useful.
*** [Support] [iOS] Remote hotloading on iOS devices logs to ~spam~ category instead of ~debug~.
** [Pro] [Indie] [Standard] Updates to all license tiers.
   The following updates are available at all licensing tiers.
*** [Support] Added ~$gtk.set_mouse_grab(true/false)~.
*** [Support] Added ~$gtk.list_files(dir)~, ~$gtk.stat_file(path)~, and ~$gtk.delete_file(path)~.
    You can view documentation for file apis:
    - Under =./docs/docs.txt
    - At [[http://docs.dragonruby.org]] (latest version).
    - Or at [[http://localhost:9001]] while your game is running.
*** [Support] ~log_(spam|debug|info|warn|error|unfiltered)~ functions added to ~Object~.
    The logging apis that were only available via ~$gtk~ are now accessible at the top level.
*** [Support] Performance enhancements to ~GTK::Geometry::intersect_rect?(rect_1, rect_2, tolerance)~
    The ~intersect_rect?~ api has been re-implemented in C which significantly increases performance.
*** [Support] Added ~GTK::Geometry::find_intersect_rect(rect, rects, tolerance)~
    A helper method called ~find_intersect_rect~ has been added.

    If you find yourself doing:
    #+begin_src ruby
      collision = args.state.enemies.find { |e| e.intersect_rect? args.state.player }
    #+end_src

    You can instead do the following for a significant performance boost:
    #+begin_src ruby
      collision = args.geometry.find_intersect_rect(args.state.enemies, args.state.player)
    #+end_src
*** [Samples] Added sample app that shows how to wrap long lines of text.
    Location =./samples/01_rendering_basics/01_labels_text_wrapping=.
*** [Samples] Screen shake sample app added.
    Location =./samples/07_advanced_rendering/07_shake_camera=.
*** [Samples] Physics sample app added that shows interactions between a bouncing ball and arbitrary lines.
    Located at =./samples/04_physics_and_collisions/11_bouncing_ball_with_gravity=.
*** [Samples] Button sample app added that shows how to create a button and wire up click behavior.
    Located at =./samples/05_mouse/05_clicking_buttons=.
*** [Samples] File api sample app has been added that shows how to use all the available file apis.
    Located at =./samples/06_save_load/00_reading_writing_files=.
*** [Bugfix] ~args.audio~ no longer crashes when a hash key is reused.
*** [OSS] The following PRs have been merged in from https://github.com/DragonRuby/dragonruby-game-toolkit-contrib.
    - Add scaling to Tweetcart scene! #131
    - Use begin blocks to improve console eval #125
    - Improve responsivity of Control hotkeys (e.g. Ctrl-g, Ctrl-u, Ctrl-d) #126
    - Add default value to Tweetcart no_clear! #132
    - Bump regex from 1.4.2 to 1.5.5 in /samples/13_rust_extensions/02_intermediate/regex-capi #121
* 3.18
** [Pro] [Bugfix] [iOS] iOS packaging wizard removes =.DS_Store= metadata from codesigned binaries.
** [Bugfix] ~args.inputs.controller_(three|four)~'s ~truthy_keys~ are now cleared at the end of ~tick~.
** [Bugfix] =dragonruby-publish= supports the packaging of larger audio files.
** [Bugfix] =http://localhost:9001/changelog= no longer throws an exception.
** [Bugfix] [OSS] ~Numeric#clamp_wrap~ now includes the minimum value.
   Thanks to Hiro_r_b@discord for the PR.
** [Bugfix] [OSS] ~Tweetcart#no_clear!~ respects the ~render_target_name~ parameter that is passed to it.
   Thanks to Hiro_r_b@discord for the PR.
** [Samples] Path finding sample apps have been updated to user ~Hash~ based primitives.
   Location of Path finding samples: =./samples/13_path_finding_algorithms=
** [OSS] ~args.gtk.(save|load)_state~ has been open sourced.
** [OSS] The Console now uses a scoped class called ~ConsoleEvaluator~.
   - The global variables ~$gtk~, ~$args~, $wizards~ etc are still available,
     but now you can type ~gtk.reset~ instead of ~$gtk.reset~.
   - Console accepts ~search "search term"~ which will search the documentation and return topics that match.
** [OSS] Small styling changes have been made to the Console.
** [Support] Added documentation for ~args.gtk.(mouse|keyboard)_focus~.
** [Support] Added documentation for ~args.gtk.platform?~.
** [Support] Added ~repl_silent~ which suppresses showing the Console (usable in =app/repl.rb=)
   The following when added to =app/repl.rb= will bring up the Console after execution:
   #+begin_src ruby
     repl do
       args.state.player.x = 100
       args.state.player.y = 200
       puts "Player location is #{args.state.player.x}", #{args.state.player.y}"
     end
   #+end_src

   Using ~repl_silent~ will NOT bring up the Console:
   #+begin_src ruby
     repl_silent do
       args.state.player.x = 100
       args.state.player.y = 200
       puts "Player location is #{args.state.player.x}", #{args.state.player.y}"
     end
   #+end_src
* 3.17
** [Bugfix] VSync was set to false as the default. This has been reenabled.
* 3.16
** [Pro] [iOS] ~tick~ given a higher thread priority.
** [Pro] [iOS] background music session category set to Ambient.
   This will keep music volume playing from another app from being lowered.
** [Pro] [Indie] Added ~gtk.get_pixels file_name~ which will return a pixel buffer.
   You can store ~gtk.get_pixels~ into a variable and use it to get the color
   of a sprite at a specific pixel location (eg collision map).
** [Pro] [Indie] Added sample app that shows how to load a ~pixel_array~ from a png.
   Sample app is located at =./samples/07_advanced_rendering/06_pixel_arrays_from_file=.
** [Bugfix] Html builds now persists files across sessions.
** [Support] Touch apis are now available in Standard Edition and work in web builds.
** [Support] DawnBringer color palette added to Tweetcart.
** [Support] Added ~args.inputs.controller_(three|four)~.
** [Support] Added ~gtk.open_game_dir~ which will open the sandbox location for your game.
** [Support] DragonRuby Dev Server (http://localhost:9001) now contains a link to source code backups.
** [Support] Game runs at a faster fps when it doesn't have focus.
   You can configure the framerate via ~renderer.background_sleep~ cvar.
** [Support] ~./dragonruby-publish~ now sets the ~--userversion~ flag on Butler.
** [Support] Added ~gtk.reset_next_tick~ which will invoke ~gtk.reset~ before the next tick is invoked.
   This will keep you from having exceptions caused by invoking ~gtk.reset~ part way through your ~tick~ method.
** [OSS] ~gtk.(save_state|load_state)~ open sourced.
** [OSS] DragonRuby Console now persists local variables (thanks mooff@discord for the contribution).
** [OSS] PR that cleans up ~./dragon/api.rb~ has been merged in (thanks kfischer_okarin@discord for the contribution).
* 3.15
** [Support] [Important] Server Side Request Forgery vulnerability patch for DragonRuby Game Toolkit Web Console (http://localhost:9001)
   All http endpoints that submit data to DragonRuby now only accept ~Content-Type~ with the *exact value* of ~application/json~.
   This change patches a SSRF vulnerability that would allow a malicious website to process request against
   ~http://localhost:9001/dragon/eval/~ and execute arbitrary code on your machine.

   Thanks to sophira@discord for finding a specific edge case where the Content-Type could include both ~multipart/form-data~ and ~application/json~.

   Source code for the Web Console is available at [[https://github.com/DragonRuby/dragonruby-game-toolkit-contrib/blob/master/dragon/api.rb]]
* 3.14
** [Support] Performance improvements to ~Array#map_with_index~.
** [Support] Performance improvements to ~Array#map~.
** [Bugfix] ~Matrix::normalize~ when given a vector that contains all zeros, returns a vector containing all zeros instead of ~nil~.
** [Bugfix] ~Geometry::line_intersect~ correctly handles vertical lines.
** [Bugfix] ~Geometry::ray_test~ reports ~:left~, ~:right~, ~:on~ correctly for lines of negative slope.
** [Samples] See the following sample app for usage of ~Geometry::(line_intersect|ray_test)~
   Location: =./samples/99_genre_platformer/the_little_probe=
** [Samples] New sample app added showing how to create a boss battle.
   Location: =./samples/99_genre_boss_battle/boss_battle_game_jam=
* 3.13
** [Bugfix] [VR] Cube World sample app includes ~MatrixFunctions~ and no longer throws an exception.
** [Bugfix] ~Array#reject~ properly handles invocation when a block isn't provided.
** [Bugfix] ~Matrix.(normalize|normalize!) computes unit vector correctly.
* 3.12
** [Support] Improved performance for processing many to many collisions.
   Sample app has also been updated: =/samples/09_performance/09_collision_limits_many_to_many/=
** [Support] 5% to 15% performance improvement on non-static output rendering.
** [Support] ~Array#(map|reject)~ performance improved ~25%.
** [Bugfix] ~Array#sample~ no longer leaves out the last element from selection.
** [Bugfix] ~$gtk.(save|load)_state~ correctly persists value types with the same value.
* 3.11
** [Bugfix] Fixed regression in C rendering pipeline that caused a degradation in hash rendering performance.
** [Bugfix] ~Hash.(merge|merge!) respects block that is passed into it. Regression was introduced in 2.14.
** [Support] Matrix math apis moved to ~Matrix~ class/module.
   Instead of ~vec3(1, 1, 1)~, you will need to do ~Matrix.vec3(1, 1, 1)~.

   Optionally you can mixin the ~MatrixFunctions~ module into your class to remove the ~Matrix~
   prefix requirement.

   VR and Advanced Rendering sample apps have been updated to reflect this change.

   Option 1:
   #+begin_src ruby
     def tick args
       puts Matrix.vec4(1, 1, 1, 1)
     end
   #+end_src

   Option 2:
   #+begin_src ruby
     include MatrixFunctions

     def tick args
       puts vec4(1, 1, 1, 1)
     end
   #+end_src

* 3.10
** [Pro] [VR] Cubeworld sample app added =./samples/14_vr/08_cubeworld_vr=
** [Pro] [VR] Gimbal Lock sample app added =./samples/14_vr/05_gimbal_lock=
** [Pro] [VR] Let There Be Light sample app entity count increased given C based rendering pipeline.
** [Pro] [VR] Space Invaders Z Rendering reworked to spread out enemies.
** [Standard] Small performance improvements to ~args.geometry.find_collisions~.
** [Standard] ~mul~ performs component-wise multiplication on ~vec~ and ~float~.
** [Standard] ~add~ performs component-wise addition on ~float~.
* 3.9
** [Pro] [VR] Rendering pipeline handles the enumeration of ~args.outputs~ in C.
** [Pro] [VR] Triangle support added.
** [Pro] [VR] [Bugfix] Fixed exception related to rendering lines when a width/height isn't provided.
** [Indie] [Samples] Sample apps added to show matrix apis in conjunction with triangles.
   Three sample apps have been added to demonstrate these capabilities:
   - 2D matrix transforms: =./samples/07_advanced_rendering/15_matrix_and_triangles_2d=
   - 3D matrix transforms: =./samples/07_advanced_rendering/15_matrix_and_triangles_3d=
   - 3D matrix transforms advanced: (with mouse look): =./samples/07_advanced_rendering/15_matrix_cubeworld=
** [Standard] [Support] Hash-based sprite rendering is a tiny bit faster.
** [Standard] The following matrix math apis have been added to ~Object~ (accessible from anywhere):
   The intent of these matrix functions is to mirror GLSL matrix apis.
   - ~vec(2|3|4)~: Creates a ~vec~ with the given dimension. Represented as a ~Hash~
                   with the ~primitive_marker~ set to ~:vec2~, ~:vec3~, or ~vec4~.
   - ~mat(2|3|4)~: Creates a ~mat~ with the given dimension. Represented as a ~Hash~ of ~Hash~es
                   with the ~primitive_marker~ set to ~:mat2~, ~:mat3~, ~:mat4~ at the top level,
                   and ~:vec(2|3|4)~ for each vector component.
   - ~add~: Adds matrices given to the function (returns ~nil~ if a matrix multiplication isn't valid).
   - ~mul~: Multiplies matrices given to the function. Returns ~nil~ if the matrices are incompatible.
   - ~dot~: Performs a scalar dot product for matrices given a list of matrices. Returns ~nil~ if the matrices are incompatible.
   - ~cross~: Performs a cross product for matrices given a list of matrices. Returns ~nil~ if the matrices are incompatible.
   - ~normalize~: Normalizes a vector. Returns the new normalized vector as a new ~Hash~.
   - ~normalize!~: Normalizes a vector. Mutates the vector passed in.
   - ~distance~: Returns the distance between to vectors. Returns ~nil~ if the vectors are incompatible.
   - ~Hash#+~: Delegates to ~add~ for matrix multiplication. Returns ~nil~ if the matrices are
               incompatible or if ~primitive_marker~ isn't set on the ~Hash~.
   - ~Hash#*~: Delegates to ~mul~ for matrix multiplication. Returns ~nil~ if the matrices are
               incompatible or if ~primitive_marker~ isn't set on the ~Hash~.
** [Standard] Added ~Numeric#from_(bottom|left)~ which will take into consideration the origin of ~args.grid~.
** [Standard] Added ~Keyboard#(left|right|up|down)_arrow~ which excludes WASD keys.
** [Standard] Log statements added when a controller is connected.
** [OSS] Synced changes to =api.rb= from open source repo.
* 3.8
** [Pro] [iOS] Fixed issue related to app tombstoning and the simulation loop attempting to catch up when brought back to the foreground.
** [Pro] [iOS] iOS wizard reports CLI errors from ~sh~ commands.
** [Pro] [Android] Better organization of =./.dragonruby/stubs/android/= template files.
** [Support] 3Dconnexion SpaceNavigator device ignored as a controller.
** [OSS] [Support] PR merged: ~Enumerable#sum~ .
** [OSS] [Support] PR merged: Bug fix in reporting test failures accurately .
** [OSS] [Support] PR merged: ~Tweetcart::scene!~ now accepts ~w~ and ~h~ as optional parameters.
** [OSS] [Support] PR merged: ~Tweetcart::tc~ alias to ~$args.state.tick_count~.
** [Support] Performance improvements to rendering.
   1. You should see a 10% to 30% overall increase in rendering performance across all primitives.
   2. You should see a 50% to 200% increase in rendering ~sprites~ represented as ~Hash~.
   3. Game Toolkit's render loop no longer defines/calls ~GTK::Runtime#primitives(pass)~. The rendering pipeline
      handles the enumeration of ~args.outputs~ in C.

      You can define ~GTK::Runtime#primitives(pass)~ if you want to
      override this core rendering behavior. A sample app has been added to demonstrate this:
      =./samples/07_advanced_rendering/15_override_core_rendering=

      It is STRONGLY recommended that you do NOT override core rendering
      behavior unless you know what you're doing.
** [Bugfix] ~Hash#(left|right|top|bottom)~ reports the correct value if any one of those properties is assigned a value.
* 3.7
** [Pro] [Android] Added support for Google Play App Bundles.
** [Pro] [Android] SDK compilation target updated to API 30.
** [Support] ~args.outputs.sprites~ will render the ~:pixel~ render target if ~path~ is not provided.
   Example:
   #+begin_src ruby
     def tick args
       # renders a sprite
       args.outputs.sprites << { x: 0, y: 0, w: 100, h: 100, path: "sprites/square/blue.png" }

       # renders a checker-box
       args.outputs.sprites << { x: 100, y: 100, w: 100, h: 100, path: "sprites/square/does-not-exist.png" }

       # renders a white square
       args.outputs.sprites << { x: 200, y: 200, w: 100, h: 100 }

       # equivalent to
       args.outputs.sprites << { x: 200, y: 200, w: 100, h: 100, path: :pixel }

       # renders a green square
       args.outputs.sprites << { x: 300, y: 200, w: 100, h: 100, r: 0, g: 255, b: 0 }

       # renders a green square
       args.outputs.sprites << { x: 300, y: 200, w: 100, h: 100, r: 0, g: 255, b: 0, path: :pixel }
     end
   #+end_src
** [Bugfix] Added missing assets for triangle sample app.
* 3.6
** [Pro]
   The following updates are available Pro license holders.
*** [VR] VR capabilities are now incorporated into the Pro DragonRuby License and no longer requires a separate download.
*** [VR] [Bugfix] ~args.controller_one.left_analog_y_perc~ reports the correct direction.
*** [iOS] iOS icons assets updated to pass latest submission requirements.
*** [iOS] [Bugfix] iOS production distribution now reports the correct value for ~$gtk.production~.
*** [Android] Android ~minSdkTarget~ lowered to version 23 from 26 to increase support more Android devices.
*** [VR] [Samples] Citadel sample app added that shows how to render 3d columns composed of rotated sprites.
    Location: =./samples/14_vr/citadels=
*** [Bugfix] ~./dragonruby-publish~ no longer fails to publish to Itch (iOS and Android bundles are ignored).
** [Indie]
   The following updates are available to Pro and Indie license holders.
*** [Support] [C Extensions] C extensions can now directly access the underlying runtime and extend it.
    Example:
    #+begin_src c
      DRB_FFI_EXPORT
      void drb_register_c_extensions_with_api(mrb_state *state, struct drb_api_t *api) {
        drb_api = api;
        struct RClass *FFI = drb_api->mrb_module_get(state, "FFI");
        struct RClass *module = drb_api->mrb_define_module_under(state, FFI, "CExt");
        struct RClass *base = state->object_class;
        struct RClass *Adder = drb_api->mrb_define_class_under(state, module, "Adder", base);
        drb_api->mrb_define_method(state, Adder, "add_all", add_all, MRB_ARGS_REQ(1));
      }
    #+end_src
*** [Support] ~args.outputs.solids~ and ~args.outputs.sprites~ can now render triangles.
    Example:

    #+begin_src ruby
      def tick args
        args.outputs.solids << {
          x:  640 - 64, y:  400,
          x2: 640,      y2: 400 + 100,
          x3: 640 + 64, y3: 400
        }

        args.outputs.sprites << {
          x:  640 - 64, y:  400,
          x2: 640,      y2: 400 + 100,
          x3: 640 + 64, y3: 400
          path: 'dragonruby.png',
          source_x:  0,   source_y:  0,
          source_x2: 128, source_y2: 100,
          source_x3: 128, source_y3: 0
        }
      end
    #+end_src
*** [Samples] Added sample app that shows how to hand craft a Ruby object in C.
    Location: =./samples/12_c_extensions/04_handcrafted_extension=
*** [Samples] Added sample app that shows how to render triangles.
    Location: =./samples/07_advanced_rendering/13_triangles=
*** [Samples] Added sample app that shows how to render a polygon using triangles.
    Location: =./samples/07_advanced_rendering/14_triangles_trapezoid=
** [Standard]
   The following updates are available to all license holders.
*** [Support] Emscripten (HTML builds) upgraded to 3.1.3.
*** [Support] libSDL upgraded to 2.0.20 (rendering performance increases anywhere from 5% to 20%).
*** [Support] ~attr_sprite~ incorporates properties needed to render a triangle for api compatibility.
*** [Support] Added ~args.geometry.find_collisions rects~ to do fast many-to-many collision checks.
    A ~Hash~ is returned where the key is the reference to each rect that has a collision.
*** [Support] Added ~args.controller_(one|two).connected~ which returns ~true~ if a controller is present.
*** [Samples] Added many to many collisions sample app.
    Location: =./samples/09_performance/09_collision_limits_many_to_many=
*** [Samples] Added cool platforming sample app.
    Location: =./samples/99_genre_platformer/shadows=
*** [Bugfix] ~Numeric#frame_index~ now respects ~tick_count_override~ if the parameter is provided.
*** [Bugfix] ~Float#(sin|cos|sin_d|cos_d|sin_r|cos_r) added.
*** [Bugfix] DragonRuby gracefully loads even if one of your ruby files has a syntax error.
*** [Bugfix] ~args.audio~ properties correctly convert ~Integer~ values supplied to it.
*** [Legal] =eula.txt= added. Warning, it contains legal speak.

* 3.5
** [Support] stb_vobis upgraded to 1.22.
** [Bugfix] Ogg Vorbis deterioration issue fixed for x86 MacOS.

* 3.4
** [Pro] [Support] Added ~args.inputs.finger_(left|right)~.
** [Support] Added ~Numeric#(sin|cos)_d~ (an alias to ~Numeric#(sin|cos)~). The Numeric is assumed to be in degrees.
** [Support] Added ~Numeric#(sin|cos)_r~. The ~Numeric~ is assumed to be in radians.
** [Support] [Tweetcart] Added ~cos~ (assumes degrees), ~cos_d~, ~cos_r~ (for radians), ~sin~ (assumes degrees), ~sin_d~ ~sin_r~ (for radians) to Tweetcart's top level api.
** [Support] [Tweetcart] Added ~no_clear!(render_target_name)~ to Tweetcart's top level api which invokes ~args.outputs[render_target_name].clear_before_render = false~.
** [Support] Added documentation for render order, starting a new project, ~gtk.calcspritebox~.
** [Support] Added ~putz~ which is an alias to ~puts~ so it's less annoying to search and remove random ~puts~ statements you've sprinkled in your code.
** [Support] [Pro] [iOS] Better comments related to ~devcert~ ~prodcert~ specification in ~ios_metadata.txt~.
** [Samples] Added sample app that shows how to do dynamic lighting using ~blendmode_enum~ and ~render_targets~.
   Location of sample app is =./samples/07_advanced_rendering/12_lighting=

* 3.3
** [Bugfix] ~fn.pretty_(print|format)~ correctly prints ~Arrays~ nested within a ~Hash~.
** [Support] Added docs for ~args.easing.ease~.
** [Pro] [Bugfix] iOS hotloading no longer continuously polls for a remote server if one isn't present on startup.
** [Pro] [Bugfix] iOS production builds correctly sign binaries.
** [Pro] [Support] Icons for iPad Pro 12.9 added.
** [Pro] [Support] Log output redirects to iOS Device's internal console.
** [Samples] Added sample app that simulates falling sand.
   Location of sample app is =./samples/99_genre_simulation/sand_simulation=.

* 3.2
** [Bugfix] ~args.gtk.write_file~ now writes to the game directory in dev mode.
** [Support] Added ~args.gtk.set_cursor path, dx, dy~ which can be used to set the in game cursor to a sprite.
** [Support] In-game http server is now available in Pro, Indie, AND Standard tiers.
   Take a look at the samples apps under =./samples/11_http=
** [OSS] DragonRuby's dev server api has been open sourced.
   All source code that has been open sourced is available locally under =./docs/docs.txt=.
   You can also access documentation at [http://localhost:9001] while your game is running.
** [Support] Added ~.mp3~ support to ~args.gtk.audio~.
   Audio api sample is located at =/samples/07_advanced_audio/01_audio_mixer/=.

* 3.1
** [Bugfix] Fixed ~gtk.http_post_body~ fixed for MacOS Monterey (and any other OS's that are using more current versions of libcurl).
** [Bugfix] Fixed ~FrozenStringException~ when invoking ~puts nil~.

* 3.0
** [Summary]
*** Major Release
    This is the RTM (release to market) version of DragonRuby Game Toolkit 3.0.
*** Oculus Quest/VR Support added to Pro License
    You can watch the demo video at: [[DragonRuby VR][https://www.youtube.com/watch?v=I_s_kyOUgzM&ab_channel=RyanC.Gordon]]

    DragonRuby Game Toolkit Pro license holders now have access to DragonRuby Game Toolkit - VR. Go to http://dragonruby.org to
    download. The price of the Pro license has been increased to $128 per year. Anyone who currently has a Pro subscription will be
    grand-fathered in with their current subscription price and will *NOT* see a price increase *EVER*.
*** Indie Subscription Tier
    With the addition of VR capabilities to the Pro tier. A new Indie tier has been
    created (which is cheaper than a Pro license). Here is the breakdown of
    features between each Tier:

    | Standard           | Indie                 | Pro + VR                                  |
    |--------------------+-----------------------+-------------------------------------------|
    | OSX                | OSX                   | OSX                                       |
    | Windows            | Windows               | Windows                                   |
    | Linux              | Linux                 | Linux                                     |
    | Raspberry Pi       | Raspberry Pi          | Raspberry Pi                              |
    | Web Assembly       | Web Assembly          | Web Assembly                              |
    | Web-based REPL     | Web-based REPL        | Web-based REPL                            |
    | Itch.io Automation | Itch.io Automation    | Itch.io Automation                        |
    | -                  | C Extensions          | C Extensions                              |
    | -                  | Sound Synthesis       | Sound Synthesis                           |
    | -                  | In-game Web Server    | In-game Web server                        |
    | -                  | Byte-code Compilation | Byte-code Compilation                     |
    | -                  | -                     | iOS                                       |
    | -                  | -                     | Android                                   |
    | -                  | -                     | Oculus Quest / VR Capabilities            |
    | -                  | -                     | MP4 Replay Export                         |
    | -                  | -                     | Coming Soon: Triangle Primitive Rendering |
    | -                  | -                     | Coming Soon: HD Mode                      |
    | -                  | -                     | Coming Soon: All Screen Mode              |
    | -                  | -                     | Coming Soon: Portrait Orientation         |

*** Sample Apps
    Quite a few sample apps have been added which cover Mario style
    platforming, Oculus Quest VR samples, 3D math concepts, advanced logging concepts, and
    rendering labels inside of ~render_targets~.

** [MAJOR] This is a major release of DragonRuby Game Toolkit
   This is a major release of DragonRuby Game Toolkit. It supports Ruby 3.0 language features
   and contains apis that are anywhere from 30% to 200% faster.

   Even though there are breaking changes, the exceptions that may occur
   should be fairly straightforward to fix. If you need help, come to the DragonRuby
   Discord Server: http://discord.dragonruby.org and we'll help you upgrade.
** [Samples]
*** New 3D sample app has been added that shows how to perform ~Matrix~ transforms.
    Location: =./samples/99_genre_3d/03_yaw_pitch_roll=.
*** New Performance based sample app has been added that shows how to use ~Struct~.
    Location: =./samples/09_performance/01_sprites_as_struct=.
*** New ~render_target~ sample app has been added that shows how to rotate a label using a ~render_target~.
    Location: =./samples/07_advanced_rendering/00_rotating_label=
*** New Topdown RPG sample app has been added that replicates the casino room in Legend of Zelda.
    Location: =./samples/99_genre_rpg_topdown/topdown_casino=
*** New Platforming sample app has been added that breaks down jumping mechanics like in Super Mario Bros.
    Location: =./samples/99_genre_mario/01_jumping=
*** New Platforming sample app has been added that breaks down collision mechanics like in Super Mario Bros.
    Location: =./samples/99_genre_mario/01_jumping_and_collisions=
*** New Advanced Debugging sample app has been added that shows off DR's new logging capabilities.
    Location: =./samples/10_advanced_debugging/00_logging/app/main.rb=
** [Support]
*** [Pro] Embedded http server now supports IPv6.
*** [Pro] Ruby array C Extension generation.
*** [Performance] ~Hash~ value assignment is 16x faster. Rendering using ~Hashes~ is 10x faster.
    This deficiency exists in mRuby and has been optimized in DragonRuby.

    Because of this change, the following code is 16x faster:
    #+begin_src ruby
      some_hash = {}
      some_hash[:a] ||= :some_value
    #+end_src

    In the event of a missing key, the execution speed is also 16x faster:
    #+begin_src ruby
      some_hash = { a: 10 }
      some_hash[:b] # nil, 16x faster
      some_hash[:a] # no speed change/already fast
    #+end_src
*** Labels can support larger font sizes.
*** Structs can be used with ~args.outputs~.
*** ~gtk.benchmark~ is a bit more accurate.
*** Added ~gtk.docs_benchmark~.
*** Moved =./exceptions= and  =./console_history.txt= under =./logs=.
*** Rendering performance improved for ~OpenEntity~.
*** New logging apis have been added.
*** Added ~args.gtk.version_indie?~ for the new Indie tier.
** [Bugfix]
*** Labels no longer shift vertically when the screen is resized.
*** ~args.inputs.mouse.wheel~ reports the correct direction on non Mac machines.
*** ~dragonruby-httpd~ works with Chrome.
*** Crashes related to unsupported Korean keyboard events on MacOS have been resolved. Thank you podo@discord for troubleshooting.
*** Fixed malformed http content for ~args.gtk.http_post_body~.
*** Crashes related to headless unit tests have been fixed.
** [MAJOR]
*** [SAFE] Ruby 3.0 language features are now available. The new syntax should be compatible with Ruby 2.0.
*** [SAFE] Except to see anywhere from 20% to 200% boost in performance overall.
*** [SAFE] [DIVERGENT] DragonRuby's Runtime returns a ~Float~ for integer division (retaining mRuby 2.x behavior).
    In mRuby 3.0, ~1/2~ would return ~0 (Int)~. In the DragonRuby Runtime, ~1/2~ returns ~0.5 (Float)~. Use ~Numeric#idiv~ for
    integer division.
*** [SAFE] [DIVERGENT] DragonRuby's Runtime retained ~rand~ generation algorithms from mRuby 2.0.
    Incorporating 3.0's RNG algorithm would have resulted in unnecessary breaking changes that would have invalidated replays
    of existing games.
*** [SAFE] [DIVERGENT] ~Hash~ in most cases will not discriminate between ~Float~ and ~Int~ keys (where the number after the decimal point is ~0~).
    This was existing mRuby 2.0 behavior and was retained within this major release to avoid unnecessary breaking changes related to key lookup.
*** [BREAKING] Mutation of a ~frozen~ instance with ~include~ or ~extend~ throws an exception (in mRuby 2.0 this behavior did not occur).
    Helpful error messages have been added where this type of mutation is most likely to happen.
*** [BREAKING] Arithmetic and comparison operations no longer attempt to coerce/infer types.
    You will receive exceptions because of this. General troubleshooting tips for these exceptions:
    1. Carefully read the ~backtrace~ for ~Exceptions~ that are thrown (come to our Discord if you need help).
    2. Make sure all references to ~args.state~ have been initialized to a default value.
    3. Audit ~Int/Float~ operations to make sure they are operating on variables
       that have been initialized with a default value.
    4. Audit ~String~ operations to make sure they are operating on variables
       that have been initialized with a default value.
    5. Audit ~Enumerable~ operations to make sure they are operating on variables
       that have been initialized to a default value.
    Special thanks to erquint@discord, leviondiscord@discord, and danhealy@discord for continuing to raise
    debugging concerns with respect to arithmetic/implicit coercion.
*** [BREAKING] DragonRuby's "Level 1" Runtime has been updated to mRuby 3.0.
    This is a major release of DragonRuby Game Toolkit. The breaking changes should be minimal, but unfortunately do exist.
    Please report issues in our Discord and we will get them resolved right away: http://discord.dragonruby.org (mention @amirrajan).

    A special thank you to podo@discord, hiro_r_b@discord, kfischer_okarin@discord, and leviondiscord@discord for troubleshooting.

    A full explanation of DragonRuby's Multilevel Runtime Architecture can be found here: http://docs.dragonruby.org/#----what-is-dragonruby-
    Please read through it so you have a better understanding of how DragonRuby is different than other Ruby runtimes.
    It's VERY IMPORTANT to educate yourself on these differentiators. Especially if you find yourself being asked:
    #+begin_quote
      Why are you using Ruby to build games? Ruby is slow.
    #+end_quote
*** [SOFT BREAK] In a small subset of situations (such as with frozen objects), ~Hash~ keys *will* differentiate between ~Float~ and ~Int~ keys.
    This is new behavior in mRuby 3.0, so be cognizant of this subtlety if you receive an exception.
    The following works in mRuby 2.0:
    #+begin_src ruby
      def tick args
        sample_hash = { 3.0 => "float value resolved", 4 => "int value resolved" }
        puts sample_hash[3.0] # float value resolved
        puts sample_hash[3]   # float value resolved

        puts sample_hash[4.0] # int value resolved
        puts sample_hash[4]   # int value resolved
      end
    #+end_src
    But in some instances, it may not work in 3.0:
    #+begin_src ruby
      def tick args
        sample_hash = { 3.0 => "float value resolved", 4 => "int value resolved" }
        puts sample_hash[3.0] # float value resolved
        puts sample_hash[3]   # might return nil

        puts sample_hash[4.0] # might return nil
        puts sample_hash[4]   # int value resolved
      end
    #+end_src
    If you know ahead of time if your keys are ~Int~s. Be sure to invoke ~.to_i~ on any keys that may have been
    converted into a ~Float~ (especially when deserializing data from disk).
** [Pro] [Bugfix]
*** [Pro] Fixed Android bundling targeting Dalvik.
*** [Pro] iOS Wizard failed to stage correctly on a brand new project. This has been resolved.
*** [Pro] Auto discovering iOS development/distribution certificates was problematic and must now be explicitly defined.
    =mygame/metadata/ios_metadata.txt= now requires the ~devcert~ and ~prodcert~ config values. Here is an example template:
    #+begin_src txt
      # ios_metadata.txt is used by the Pro version of DragonRuby Game Toolkit to create iOS apps.
      # Information about the Pro version can be found at: http://dragonruby.org/toolkit/game#purchase

      # teamname needs to be set to your assigned team id which can be found at https://developer.apple.com/account/#/membership/L7H57V9CRD
      teamid=
      # appid needs to be set to your application identifier which can be found at https://developer.apple.com/account/resources/identifiers/list
      appid=
      # appname is the name you want to show up underneath the app icon on the device
      appname=

      # devcert is the certificate to use for development/deploying to your local device
      devcert=
      # prodcert is the certificate to use for distribution to the app store
      prodcert=
    #+end_src
* 2.26
** [Pro] [Bugfix] [Android] Fixed crashes related to setting the package name with a non =org.*= prefix.
** [Bugfix] ~GTK::Runtime#serialize_state~ no longer persists data related to failed method invocations ~:__trash_count__~.
** [Bugfix] [Support] Exception is now thrown if a default value cannot be inferred for an entity's property.
** [Bugfix] Starting value for seeding ~GTK::Entity#entity_id~ is reset when ~GTK::Runtime#reset~ is invoked.
   An empty ~OpenEntity~ will not be silently created (better stacktrace/explanation is provided instead).
** [Support] Framerate drops will no longer present an "Important Notification Occurred" status (it wasn't very useful).
** [Support] Added small delay in processing Console input if ~args.inputs.keyboard.key_down.enter~ is true.
   This will keep the console from being dismissed because of ~$gtk.reset~ being pre-populated when an exception occurs.
** [API] Pre-defined ~:pixel~ render target now available.
   Before ~(boot|tick)~ are invoked, a white solid with a size of 1280x1280
   is added as a render target. You can use this predefined render target to
   create solids and get ~args.outputs.sprites~ related capabilities.
   Example:
   This is an example showing how to render a red box using ~args.outputs.solids~:
   #+begin_src ruby
     def tick args
       args.outputs.solids << {
         x:  0, y:  0,
         w: 64, h: 64,
         r: 100, g: 0, b: 0
       }
     end
   #+end_src
   This will render the same red box, but leverages ~args.outputs.sprites~:
   #+begin_src ruby
     def tick args
       args.outputs.sprites << {
         x:  0, y:  0,
         w: 64, h: 64,
         r: 100, g: 0, b: 0
         path: :pixel,
         angle: 0
       }
     end
   #+end_src
** [API] Initial exploratory cut of a =Tweetcart= API has been created.
   The source code is available in the Contrib repo and also available locally under the =./docs/= directory.
   Example:
   The following code renders 10 ~Solids~ of various colors:
   #+begin_src ruby
     wht  = [255] * 3
     red  = [255, 0, 0]
     blu  = [0, 130, 255]
     purp = [150, 80, 255]

     TICK {
       bg! 0

       slds << [0, 0, 3, 3, 0, 255, 0, 255]

       sld!     10, 10
       sld!     20, 20, 3, 2
       sld!     30, 30, 2, 2, red
       sld!     35, 35, blu

       slds!    40, 40

       slds!   [50, 50],
               [60, 60, purp],
               [70, 70, 10, 10, wht],
               [80, 80, 4, 4, 255, 0, 255]
     }
   #+end_src
** [Samples] Added TeenyTiny MiniGameJam sample app: =/Users/<USER>/projects/dragonruby/samples/99_genre_teenytiny/=.
** [OSS] ~GTK::Recording~ and ~GTK::Replay~ have been open sourced.
** [OSS] OSS Contrib synced:
   #+begin_src sh
     commit aa8d3ac4bdccf522b8082a7fa7d595be2bd54b7d (origin/master, origin/HEAD)
     Author: Bartosz Podrygajlo <<EMAIL>>
     Date:   Wed Aug 4 11:19:06 2021 +0200

         Samples: Use to_radians for deg-to-rad conversion

         Replace the local implementation with the build-in Numeric.to_radians

     commit 26f20690be4da8f5091c720b1a25e32742c8c5fa
     Author: Pedro Brighenti <<EMAIL>>
     Date:   Sun Aug 22 16:09:47 2021 +0100

         Fixed typo in 02_input_basics/02_mouse

     commit dae203d736b9d6db00c10a75208f44a3a937442f
     Author: leviongit <<EMAIL>>
     Date:   Wed Aug 25 20:28:03 2021 +0200

         fix: console raising error at autocomplete request
   #+end_src
* 2.25
** [Support] [Pro] For Android releases, ~packageid~ can be specified in =./mygame/game_metadata.txt=.
   To verify the package name is correct for your Android build, you can use the following command:
   #+begin_src sh
     aapt dump badging PATH_TO_APK | grep package:\ name
   #+end_src
** [API] Added ~Outputs#clear_before_render~ which tells render targets to clear what's currently there.
** [Samples] Demonstration of ~clear_before_render~ is shown in =./samples/07_advanced_rendering/11_render_target_noclear/app/main.rb=
   Pressing the spacebar will clear the render target by setting ~clear_before_render~ equal to true.
** [Samples] Fixed tunneling issue in =./samples/04_physics_and_collisions/06_box_collision_3/app/main.rb=
   Thanks to @magiondiscord@discord for finding the edgecase (no pun intended).
** [C Extensions] [Pro] Binding generation now type checks and throws an exception in the event of a mismatch.
   An additional flag can be passed when generating bindings ~--no-typecheck~. For full documentation
   see: =./samples/12_c_extensions/README.md =
* 2.24
** [Bugfix] Labels within render targets scale correctly when windows are resized.
** [Bugfix] [Pro] Better error handling in the iOS wizard if app metadata fails to retrieve.
** [API] Added ~Numeric.mid? n1, n2~ which is similar to ~Numeric.between?~ except the numbers supplied to ~mid~ do not have to be in strictly increasing order.
** [OSS] Contributions from [[http://github.com/dragonruby/dragonruby-game-toolkit-contrib]] have been synchronized.
* 2.23
** [Bugfix] ~Hash#include?~ had invalid logic which has now been removed and should operate to core spec.
** [Bugfix] ~Hash#anchor_rect~ respects the anchor parameter passed to it as opposed to anchoring to 0.5, 0.5 only.
** [Bugfix] ~args.gtk.reset~ now clears ~args.audio~.
** [Bugfix] ~Numeric#(+|-|/|*) returns ~self~ instead of ~nil~ if arithmetic operation is performed against it.
** [Bugfix] ~Numeric#(+|-|/|*) can now be used with ~Array#inject~.
   NOTE: A special thank you to leviondiscord@discord for persistence in talking about this issue and
   taking the time to try to understand nil punning and the underlying reasoning for the original behavior (which proved
   to be a unnecessarily inconsistency in the case of arithmetic).
** [Bugfix] ~args.layout.debug_primitives~ are now cached. The grid overlay will render more quickly.
** [Bugfix] Inner exceptions from ~GTK::Geometry~ class methods are now retained and reported within the error message.
** [Bugfix] ~args.grid~ responds to ~x~, ~y~ which allows it to be used with geometric functions.
   You can use ~args.grid~ or continue to use ~args.grid.rect~.
** [Bugfix] Font glyph cache is invalided if the Window size changes.
** [API] ~args.audio~ now supports the ability to seek the audio to a specific location.
   Set ~args.audio[:some_audio][:playtime]~ to seconds (float) representing the point in time you
   want the audio to seek to.
   Take a look at the audio_mixer sample app for full api usage.
** [Samples] Audio Mixer sample app got a solid facelift. =./samples/07_advanced_audio/01_audio_mixer/app/main.rb=
** [Samples] Performance based sample apps are better labeled and contain documentation/fixes for new override methods.
** [Support] The sprites under ~mygame/sprites/(square|circle|isometric|hexagon)~ have been redone (a lot nicer looking).
** [Support] ~args.outputs.lines~ can now accept rectangular primitives ~(x|y|w|h)~.
   If the primitive does not respond to ~(x2|y2)~, then ~(w|h)~ will be used to derive ~(x2|y2)~. This
   change makes the definition of horizontal and vertical lines more intuitive.

   Instead of defining a horizontal line like this:
   #+begin_src ruby
     args.outputs.lines << { x:   0, y: 360, x2: 1280, y2: 360 }
     args.outputs.lines << { x: 640, y:   0, x2:  640, y2: 720 }
   #+end_src

   You can instead do this:
   #+begin_src ruby
     args.outputs.lines << { x:   0, y: 360, w: 1280 }
     args.outputs.lines << { x: 640, y:   0, h:  720 }
   #+end_src
** [Support] Console font decreased slightly to give more real estate.
** [Support] Console will apply color/formatting to comment blocks (making code blocks easier to read).
** [Support] Slight performance improvements to ~OpenEntity~.
** [Support] ~Hash#method_missing~ expanded to allow access to methods that collide with core apis.
   The ~Hash#method_missing~ capability allows you to access ~some_hash[:some_key]~, by using ~some_hash.some_key~ instead.
   For keys that conflict with core ~Hash~ api's, you can now suffix the method with an ~underscore~. Example: You
   can access ~some_hash[:length]~ using ~some_hash.length_~.

   Using the ~dot~ operator over ~Hash#[]~ isn't required, but it is encouraged (sample apps will opt to use the ~dot~
   operator for accessing keys in a ~Hash~, using the ~underscore~ suffix as needed).
** [Deprecation] [Soft] ~Hash#(solid|sprite|label|line|border)~ methods log a deprecation warning and suggest new methods usage.
*** New methods
   NOTE: Given how long these methods have been around, it's unlikely they will be removed for *very* long time.
   Here are the new method options:
   - ~Hash#(solid!|to_solid)~
   - ~Hash#(sprite!|to_sprite)~
   - ~Hash#(label!|to_label)~
   - ~Hash#(line!|to_line)~
   - ~Hash#(border!|to_border)~
*** Sample Usage
    These new methods accept a ~Hash~ as an additional parameter that will perform a ~Hash#merge!~
    with the object it's called on. They also better communicate that these primitive
    methods cause side effects. The existing methods did not communicate this which is why they are
    being deprecated.

    This is how ~Hash#border~ is currently being used:
    #+begin_src ruby
      def tick args
        color = { r: 255, g: 0, b: 0 }
        cell  = {
          x:   0,
          y:   0,
          w: 100,
          h: 100
        }
        args.outputs.primitives << cell.merge!(color).border
      end
    #+end_src

    You'll want to use ~Hash#border!~ instead:
    #+begin_src ruby
      def tick args
        color = { r: 255, g: 0, b: 0 }
        cell  = {
          x:   0,
          y:   0,
          w: 100,
          h: 100
        }
        args.outputs.primitives << cell.border!(color)
        # args.outputs.primitives << cell.merge(color).border! # this is also valid
      end
    #+end_src

    Use ~Hash#to_border~ if you *don't* want to mutate the original ~Hash~:
    #+begin_src ruby
      def tick args
        color = { r: 255, g: 0, b: 0 }
        cell  = {
          x:   0,
          y:   0,
          w: 100,
          h: 100
        }
        args.outputs.primitives << {
          x: 0,
          y: 0,
          w: 100,
          h: 100
        }.to_border(color)
      end
    #+end_src
* 2.22
** [Pro] [iOS] [Bugfix] Added missing icon files for ios and missing ios_metadata.txt template file.
** [API] ~GTK::Runtime#http_post_body url:string, body:string, headers:array[string]~
   ~args.gtk.http_post_body~ can be used to post raw data (with the responsibility of encoding left to the dev).
   Example:
   #+begin_src ruby
     def tick args
       if args.inputs.keyboard.key_down.enter
         json = "{ \"userId\": \"1000\", \"name\": \"Artorias of the Abyss\" }"
         args.gtk.http_post_body "http://tempuri.com/updated-profile",
                                 json,
                                 ["Content-Type: application/json"
                                  "Content-Length: #{json.length}"]
       end
     end
   #+end_src
** [Support] ~GTK::Runtime#notify_extended!~ now accepts ~overwrite~ as an option.
   Example:
   #+begin_src ruby
     def tick args
       if args.inputs.keyboard.key_down.enter
         args.gtk.notify_extended! message: "message to show", # message to show
                                   durations: 300,             # how many ticks to show the message
                                   env:       :prod,           # by default, notifications only happened in :dev
                                   overwrite: true             # if this value is true, the notification will
                                                               # shown even if another one is currently in progress
       end
     end
   #+end_src
** [Support] Better method missing exception formatting if the object's inspect is very long.

* 2.21
** [Samples] Added farming simulator starting point: =./samples/99_genre_crafting/farming_game_starting_point/=
** [Pro] [iOS] Updated iOS Wizard to consult metadata/ios_metadata.txt for iOS Specific configurations.
** [Support] Added a more streamlined means to get autocomplete suggestions: http://localhost:9001/dragon/autocomplete/
** [Support] DragonRuby Console prints backtrace for exceptions if helpful information is contained in it.

* 2.20
** [Bugfix] ~GTK::Runtime#benchmark~ reports completion time if only a single lambda is provided.
** [OSS] ~GTK::Runtime#benchmark~ has been open sourced.
** [API] Added ~args.layout.rect_group~ which allows you to layout a group of primitives
   Entries of the group can be offset by a delta row/col value. This is useful if you want to
   position primitives as if they were in a table. Here's an example of presenting a collection
   of labels each offset by a delta row ~drow~:
   #+begin_src ruby
     def tick args
       args.state.player.x ||= 640
       args.state.player.y ||= 360
       args.state.player.x  += 10
       args.state.player.y  -= 10

       debug_labels = args.layout
                          .rect_group row:  0,   # starting row
                                      drow: 0.5, # row amount to offset set each item by (delta row)
                                      col:  0,   # starting col
                                      group: [   # items representing the group
                                        { text: "player.x: #{args.state.player.x}" },
                                        { text: "player.y: #{args.state.player.y}" },
                                      ]
       args.outputs.labels << debug_labels
     end
   #+end_src
** [Samples] New sample app: =./samples/99_genre_fighting/01_special_move_inputs/app/main.rb=
   Sample app added that shows how to capture "special moves" like in a fighting game.
** [Bugfix] Ensure custom console buttons show up in both dev and production builds.

* 2.19
** [Bugfix] Exception no longer thrown when requiring a brand new file.
** [Support] [Important] Server Side Request Forgery vulnerability patch for DragonRuby Game Toolkit Web Console (http://localhost:9001)
   All http endpoints that submit data to DragonRuby now only accept ~application/json~ payloads (~x-www-form-urlencoded~ is no longer allowed).
   This change patches a SSRF vulnerability that would allow a malicious website to process request against
   ~http://localhost:9001/dragon/eval/~ and execute arbitrary code on your machine.

   Special thanks to @mooff@discord for bringing up this concern and @kfischer_okarin@discord for verifying it.

   The new api interaction is as follows:
*** From curl:
   #+begin_src sh
     curl -H "Content-Type: application/json" --data '{ "code": "$result = $args.state" }' -X POST http://localhost:9001/dragon/eval/
   #+end_src
*** From DragonRuby Game Toolkit
   #+begin_src ruby
     def tick args
       if args.inputs.keyboard.key_down.r
         json = <<-S
   {
     "code": "
   log 'received request from $gtk.http_post'
   $gtk.console.show
   $result = 1 + 2
   "
   }
   S
         args.state
             .http_eval_request = args.gtk
                                      .http_post "http://localhost:9001/dragon/eval/",
                                                 { "body" => json },
                                                 [ "Content-Type: application/json" ]
       end
       if (args.state.tick_count.zmod? 60) && args.state.http_eval_request && args.state.http_eval_request.complete
         puts args.state.http_eval_request.response_data
       end
     end
   #+end_src
* 2.18
** [API] Added ~args.inputs.controller_(one|two).(key_down|key_held|key_up).(back|select)~.
** [API] Added ~args.inputs.controller_(one|two).(key_down|key_held|key_up).(home|guide)~.
** [API] Primitives now support ~blendmode_enum~ which can be used to inform how transparency should be blended.
   You may have encountered render artifacts where opacity of a sprite would take on the background color of a render target,
   or text of a label had quality degradation when included in a render target.
   This can now be resolved by setting the ~blendmode_enum~ to ~0~. Example:
   #+begin_src ruby
     def tick args
       # render target has transparent background
       args.render_target(:camera).background_color = [0, 0, 0, 0]

       # set blendmode_enum to 0 for label to retain quality
       args.render_target(:camera).labels <<  { x: 700, y: 300, text: 'Hello World', blendmode_enum: 0 }

       # set blendmode_enum to 0 for sprite so that background_color isn't blended in
       args.render_target(:camera).sprites << { x: 700,
                                                y: 300,
                                                w: 300,
                                                h: 300,
                                                path: 'sprites/some-sprite-with-feathering.png',
                                                blendmode_enum: 0 }

       args.outputs.sprites << { x: 0, y: 0, w: 1280, h: 720, path: :camera }
     end
   #+end_src
   The following blend modes are supported: 0 (none), 1 (alpha), 2 (add), 3 (mod), 4 (multiply).
** [Samples] Sample app demonstrating ~blendmode_enum~ has been added: =./samples/07_advanced_rendering/10_blend_modes/=.
** [Bugfix] Emscripten/web builds will now allocate as much memory as needed to run the game.
   The compilation of wasm games now set ~ALLOW_MEMORY_GROWTH~. If you had issues with large sound files
   not loading within a web build, this change should resolve that issue.
** [Support] Added ~GTK::Runtime#benchmark~ which can be used to gauge performance.
   Example usage:
   #+begin_src ruby
     def tick args
       # press r to run benchmark
       if args.inputs.keyboard.key_down.r
         args.gtk.console.show
         args.gtk.benchmark iterations: 1000, # number of iterations
                            # label for experiment
                            using_numeric_map: -> () {
                              # experiment body
                              v = 100.map do |i|
                                i * 100
                              end
                            },
                            # label for experiment
                            using_numeric_times: -> () {
                              # experiment body
                              v = []
                              100.times do |i|
                                v << i * 100
                              end
                            }
       end
     end
#+end_src
** [Support] Better error message/exception for method missing on value types.
** [Support] ~args.layout.debug_primitives~ will draw the cells on the screen to help with placement.
   ~args.layout.rect(row:, col:, w:, h:)~ is a great way to layout controls. The ~debug_primitives~ function
   will render an overlay to help you with placement. Example:
   #+begin_src ruby
     def tick args
       args.outputs.debug << args.layout.debug_primitives
     end
   #+end_src
** [Bugfix] ~args.inputs.http_requests { |req| req.body }~ will now exclude the "Content-Disposition" envelope.
   Performing an ~args.gtk.http_post url, form_fields~ with populated form_fields creates a Content-Disposition envelope
   in the http request. The embedded web server's ~request.body~ now excludes that part of the payload to make parsing
   the body easier. ~request.raw_body~ has been added to show the unaltered http payload if you need it.
** [Support] [Experimental] ~http://localhost:9001/dragon/eval/~ supports requests from ~args.gtk.http_post~.
   Here is an example of how to request an eval of code over http via ~gtk.http_post~
   #+begin_src ruby
     def tick args
       if args.inputs.keyboard.key_down.r
         code_to_eval = <<-S
   log 'received request from $gtk.http_post'
   $gtk.console.show
   $result = 1 + 2
   S
         args.state
             .http_eval_request = args.gtk
                                      .http_post "http://localhost:9001/dragon/eval/",
                                                 { "body" => code_to_eval }
       end
       if (args.state.tick_count.zmod? 60) && args.state.http_eval_request && args.state.http_eval_request.complete
         puts args.state.http_eval_request.response_data
       end
     end
   #+end_src
** [Support] Rendering should be 10% to 15% faster.
   Enumeration of primitives uses a new module ~Object#fn#each_send~ which is faster than
   using a while loop. Here is an example of how to use ~fn.each_send~:
   #+begin_src ruby
     def process_item i

     end

     def process_very_large_array
       very_large_array = ...

       fn.each_send very_large_array, # array to process
                    self,             # object containing method to invoke
                    :process_item     # name of method to invoke for each item
     end
   #+end_src

* 2.17
** [Bugfix] Fixed another reload edgecase related to: Files loaded on startup will not be loaded again on frame 57 (hotloading is deferred until after the game has been fully initialized).

* 2.16
** [Bugfix] [Pro] Android 32-bit deployment should now work.
** [Support] [Pro] Android deployment are (for now) always production builds. Instructions for http server:
   If you want to have access to your app over http, you will need to put the following line in ~app/main.rb~
   before deploying:

   #+begin_src ruby
     def tick args
       args.gtk.start_server! port: 9001, enable_in_prod: true
       ....
     end
   #+end_src
** [API] ~Runtime#calcspritebox~ added.
   Calling ~args.gtk.calcspritebox PATH~ will return the ~w~ and ~h~ of the sprite as an array/tuple.
** [Support] Better error messages in the event of a ~uninitialized constant~ Exception when invoking ~require~.
** [Support] Better coloring of codeblocks begin/end within the Console.
** [Bugfix] Added additional Raspberry Pi configurations (arm32). DRGTK running on Raspberry Pi's should work now.
** [Bugfix] Files loaded on startup will not be loaded again on frame 57 (hotloading is deferred until after the game has been fully initialized).
** [Bugfix] Allow startup even if audio isn't available.
** [API] [Bugfix] ~Geometry::inside_rect?~ will return ~nil~ instead of throwing an exception if either argument passed into it are ~nil~.

* 2.15
** [Bugfix] [Pro] ~dragonruby-publish~ no longer fails with ~ios-device~ error.
** [API] ~Numeric#clamp~ now accepts a single parameter (representing ~min~ value).
** [Support] Better Console visuals/information in the event of a ~method_missing~ exception.
** [Support] Better formatting of "Did you mean?" in the event of a ~method_missing~ exception.
** [Support] Added help text in Console stating that ~ctrl+g~ can be used to clear the prompt.
** [Bugfix] Console automatically closes in the event of a syntax error being fixed.

* 2.14
** [Samples] Added sample app that shows how to create a very simple camera =./samples/07_advanced_rendering/07_simple_camera/=.
** [Pro] [Support] Compiler optimizations for iOS builds. Your iOS apps should be quite a bit faster now.
** [API] Better error handling missing methods on ~args.inputs.keyboard.KEYBOARD_KEY~.
** [API] Added ~args.gtk.version_pro?~ which will return ~true~ for the Pro version of DragonRuby Game Toolkit.

* 2.13
** [Samples] Added sample app that you can use as a starting point for Classic Jam: Guantlet Edition =./samples/99_genre_dungeon_crawl/classics_jam/=
** [API] ~Geometry~ apis no longer rely on ~left~, ~right~, ~top~, and ~bottom~ allowing you to use anything that ~respond_to~ ~x~, ~y~, ~w~, ~h~.
** [API] Added ~args.inputs.directional_angle~ which will return nil, 0, 45, 90 ... 360 depending on if ~up~, ~down~, ~left~, ~right~ are pressed.
** [API] ~OpenEntity~ and ~StrictEntity~ include the ~Geometry~ module.
** [API] Added ~OpenEntity#merge~ which invokes ~(OpenEntity#as_hash).merge~.

* 2.12
** [Support] Better call stack for exceptions.
** [Support] Added ~args.fn.pretty_print OBJECT~ and ~args.fn.pretty_print_verbose OBJECT~.
   These printing apis are still a work in progress/might not be perfect.
** [Bugfix] Fixed Raspberry Pi executable segfaulting (it was totally @icculus's fault).
** [Bugfix] Fixed ~GTK#http_(get|post) for Mojave, while at the same time not breaking Catalina/Big Sur (it was totally Apple's fault).
** [Bugfix] [Pro] Fixed unhandled exception caused by missing headers when responding to http requests.
** [API] Added ~Geometry#rotate_point(target_point, degrees_to_rotate, pivot_point = [0, 0])~.
** [API] Added ~FFI::Draw#draw_label_2~ which accepts ~vertical_alignment_enum~ as a new parameter.
   ~FFI::Draw#draw_label~ defaults ~vertical_alignment_enum~ to ~2~ for backwards compatibility.
   ** [API] ~Label~ primitive now supports ~vertical_alignment_enum~.
   Example:

   #+begin_src ruby
     def tick args
       args.outputs.labels << {
         # x position of the label's origin
         x: 640,

         # y position of the label's origin
         y: 360,

         # text of the label
         text: "Sample Label",

         # red color of the label
         r: 0,

         # green color of the label
         g: 0,

         # blue color of the label
         b: 0,

         # alpha of the label
         a: 255,

         # default value of size_enum is 0 this virutal value of zero represents
         # the recommended font size that would be legible on devices large and small
         # this value can be negative to go smaller (but with the risk of being hard to read
         # on mobile or handheld screens)
         size_enum: 0

         # this represents the horizontal alignment of the label, which is calculated using
         # the far left value of the string (~x~) and its render width.
         # 0 (default value) means ~left~ aligned, 1 means ~center~ aligned, 2 means ~right~ aligned
         alignment_enum: 0,

         # this represents the vertical alignment of the label, which is calculated using
         # the asension value of the string (~h~) and the bottom location of the string (~y~).
         # 0 means ~bottom~ aligned, 1 means ~center~ aligned, 2 (default value) means ~top~ aligned
         # IMPORTANT: To reiterate, the default value of this enum is 2 for backwards compatibility.
         vertical_alignment_enum: 0,

         # the font for the label
         font: "font.ttf"
       }
     end
   #+end_src

* 2.11
** [Sample] Added a very simple sample app that shows how to move a sprite using the keyboard =samples/02_input_basics/01_moving_a_sprite=.
** [API] ~attr_gtk~ adds ~new_entity~ to the class that invokes this class macro.
** [API] Added ~args.temp_state~. All values are set to ~nil~ on this object after ~tick~ is processed.
** [API] ~Hash#merge(!)~ accepts both ~Entities~ and ~Hashes~.
** [API] ~Hash~ responds to method missing and will forward to ~[key](=)~.
** [API] ~Layout#rect~ accepts ~d(x|y)_ratio~ which alters the final rectangle by the ratio specified.
** [API] ~Layout#rect~ accepts ~merge~ which will add the hash specified into the resulting rectangle.
** [API] Added ~Runtime#platform?(symbol)~ which consults ~Runtime#platform_mappings~ to make it easier to determine the platform your game is running on.
** [Support] ~$wizards.itch.start~ performs a the publishing process for you, but no longer attempts to deploy to Itch.io using Butler (Butler is too unstable).
** [Support] [Pro] ~$wizards.ios.start~ now asks for App's version number.
** [Support] Performance improvements to ~Numeric#==~.

* 2.10
** [Sample] [Bugfix] Fixed naming of roguelike sample apps (they were wrong).
** [Pro] Ahead of time/bytecode compilation added.
   In the manifest file, add ~compile_ruby=true~. ~./dragonruby-publish~ will do the work.
** [Pro] [Bugfix] Fixed issue where game would not boot if main.rb was compiled (AOT will totally work this time).
** [Pro] [Bugfix] ~dragonruby-publish~ will compile ruby files outside of =mygame/app= (such as mygame/lib).

* 2.9
** [Sample] [Bugfix] Fixed exception in breadth first search sample app =samples/13_path_finding_algorithms/01_breadth_first_search=.
** [Pro] [Bugfix] Fixed premature ~free~ in AOT which caused it to fail loading.

* 2.8
** [Pro] [Support] ~args.inputs.http_requests~ has been added so that you can prototype multiplayer games over http.
   Here is an example usage:
   #+begin_src ruby
   def tick args
     args.gtk.start_server! port: 9001,  enabled_in_prod: true
     args.inputs.http_request.each do |req|
       puts req
       req.respond 200, "hello from DragonRuby", { 'Content-Type' => 'text/plain }
     end
   end
   #+end_src
** [Pro] [Sample] Added sample app that shows how to process http request =./samples/11_http/02_web_server=.
** [Pro] [iOS] Added the ability to create a distribution build of an iOS app.
   To deploy to your device as a dev build, open up the Console and type ~$wizards.ios.start env: :dev~.
   To create a package that can be uploaded to Apple, open up the Console and type ~$wizards.ios.start env: :prod~.
** [Pro] [Bugfix] [iOS] Remote hot loading of a game deployed to your iOS device is stable and will not perform unnecessary reloads.
** [Pro] Ahead of time/bytecode compilation added.
   In the manifest file, add ~compile_ruby=true~. ~./dragonruby-publish~ will do the work.
** [Perf] Minor improvements made to arithmetic operations.
** [Samples] Created a much simpler sample app that shows how to play sounds.
** [OSS] Hot loading machinery has been open sourced.
** [Sample] Added sample app that shows a z-targeting game mechanic =./samples/07_advanced_rendering/08_z_targeting_camera=.
** [Support] DragonRuby GTK when in development mode spins up an http endpoint that exposes capabilities of the Console as a webpage.
   The default port for the http endpoint is a port number 9001 (it's a port number over 9000). After
   starting up DragonRuby, go to http://localhost:9001 to see the what options you have available (these options will expand over time).
** [Sample] Added a Tower Defense sample app =samples/13_path_finding_algorithms/09_tower_defense=.

* 2.7
** [Bugfix] Html5 exception when trying to find a missing metadata file no longer occurs.
** [API] ~attr_rect~, ~attr_sprite~ class macros updated so that they can be mixed into ~Entities~.
   #+begin_src
     def tick args
       args.state.player ||= args.state.new_entity :player, x: 0, y: 0, w: 100, h: 100 do |e|
         e.attr_rect
       end

       puts60 (args.state.player.intersect_rect? [10, 10, 10, 10])
     end
   #+end_src
** [Docs] Better themeing of table of contents.
** [Docs] Added docs for easing functions and string functions. Added ~args.easing~ and ~args.string~ so you have access to class functions like ~args.geometry~.
** [Bugfix] ~args.inputs.directional_vector~ now returns the correct normalized values for diagonals.
** [Bugfix] ~args.layout.rect~ now respects ~args.grid.origin_center!~.
** [API] Hash responds to ~pos~ and ~z~ so you can access those properties via the dot operator instead of using indexed symbols.
** [Samples] Misnamed sample app has been renamed to =bullet_hell= to reflect what the sample shows.

* 2.6
** [Samples] Fixed \r\n syntax error in animation creator sample app that only affected Window (grr).

* 2.5
** [Samples] [Pro] Fixed exception in synth sample app (no really this time).

* 2.4
** [OSS] Contributions from [[http://github.com/dragonruby/dragonruby-game-toolkit-contrib]] have been synchronized.
** [OSS] Remote hot load client has been open sourced.
** [Samples] Added starter template from Nokia Jam 3 under =./samples/99_lowrez=.
** [Samples] [Pro] Fixed exception in synth sample app.
** [Support] Initial work to include line number and file in the event on an exception.

* 2.3
** [Bugfix]  Ensure that all requires are processed before running unit tests.
** [Samples] =./samples/99_genre_3d/02_wireframe=: Sample app added to show how to create a wireframe using ~.off~ (Object File Format) geometry files.
** [Samples] =./samples/13_path_finding_algorithms= Sample apps added that show various types of pathfinding.
** [Samples] =./samples/(08_bouncing_on_collision|09_arbitrary_collision|10_collision_with_object_removal) Sample apps added that show collision of arbitrary polygons.
** [Samples] =./samples/99_genre_rpg_tactical= Sample app added that show how to do player/enemy turns in a tactical RPG.
** [API] Added a means to get autocompletion suggestions from the Runtime via ~GTK::Runtime::suggest_autocomplete~. You can use this to create fancy autocompletion plugins for your preferred editor.
   Example usage:
   #+begin_src ruby
     # API: $gtk.suggest_autocompletion index: STRING_INDEX_REPRESENTING_CURSOR_LOCATION, text: SOURCE_CODE

     # Write something like the following to app/mailbox.rb
     # get the autocomplete suggestions for the source code with the cursor at index 21
     suggestions = $gtk.suggest_autocompletion index: 21, text: <<-SOURCE_CODE
       def tick args
         args.
       end
     SOURCE_CODE

     # write autocomplete suggestions to a file for reading later
     $gtk.write_file 'autocomplete.txt', (suggestions.join "\n")
   #+end_src
** [API] The ~attr_gtk~ class macro now adds ~layout~ as a member variable.
** [API] Added ~Kernel#puts(6|60|600). Using ~puts6~ will print to the console ever 6 ticks, ~puts60~ will print every second, ~puts600~ will print to the console every 10 seconds.
** [API] Added ~Mouse#inside_rect?~.
** [API] Added ~Geometry#center_inside_rect_(x|y)~ which only centers rects horizonally (x) or vertically (y).
** [API] ~GTK::Layout#rect~ now accepts ~d(x|y|w|h)~ parameters.
   Example usage:
   #+begin_src ruby
     # note: the layout is composed of 12 rows and 24 columns, rows go from top to bottom and columns go from left to right.
     #       ~args.layout.rect~ returns a rectangle represented as a ~Hash~ with ~:x~, ~:y~, ~:w~, and ~:h~.
     # render a border at row 0, column 0, with a width 1 and height of 1
     def tick args
       args.outputs.borders << (args.layout.rect row: 0, # you have access to 12 rows (float values are allowed)
                                                 col: 0, # you have access to 24 columns (float values are allowed)
                                                 w:   1, # w and h are represented in cell units (not pixels)
                                                 h:   1)
     end
   #+end_src

   Example usage with added parameters
   #+begin_src ruby
     # render a border at row 0, column 0, with a width 1 and height of 1, but shift the rectangle by dx, dy (represented in pixels),
     # and increase/decrease by dw, and dh (reprsented in pixels)
     def tick args
     def tick args
       args.outputs.borders << (args.layout.rect row:   0, # you have access to 12 rows (float values are allowed)
                                                 col:   0, # you have access to 24 columns (float values are allowed)
                                                 w:     1, # w and h are represented in cell units (not pixels)
                                                 h:     1,
                                                 dx:  -10, # after calculating, shift x by -10 pixels (left)
                                                 dy:   10, # after calculating, shift y by 10 pixels (up)
                                                 dw:  100, # after calculating, increase the w by 100 pixels
                                                 dh:  200) # after calculating, increase height by 200 pixels
     end
   #+end_src
** [OSS] ~GTK::Runtime::Autocomplete~ (the logic for suggesting autocompletions) has been open sourced.
** [OSS] ~GTK::Runtime::Framerate~ (the logic for calculating framerate) has been open sourced.
** [OSS] ~GTK::Layout~ (the logic to calculate aspect-ratio-aware rectangles within a 12x24 grid) has been open sourced.

* 2.2
** [Bugfix] Better support for requires for paths in nested directories.
** [Bugfix] Better support for requires for paths outside off the app directory.
** [Support] Added ~--test~ command line argument that will automatically start tests withouth explicitly having to call ~$tests.start~.
   Take a look at ~samples/10_advanced_debugging/03_unit_tests~ for samples.

* 2.1
** [Bugfix] Better support for nested require statements ~require~ and top level ~require~ statements that depend on ~tick_count == 0~.
** [Bugfix] Ensure that screen remains black while nested and top level requires are being processed.

* 2.0
** [IMPORTANT] This version does NOT have any breaking changes.
** [Pro] [API] [Audio] Sound synthesis sample app now includes square, triangle, and sawtooth wave generation.
** [Pro] [Bugfix] [Android] Android deployment issues related to the latest SDKs have been fixed.
** [Support] Apple Silicon publishing added.
** [API] ~$gtk.version~ has been added and will return a ~String~ (in this case "2.0"). Assume the version is "1.0" if ~$gtk.respond_to? :version~ returns ~false~.
** [Bugfix] ~require~ parsing improved and facilities put in place to ensure all files are required before ~tick~ is invoked.
** [Bugfix] Flashing of background color when your game starts has been fixed. The default starting color is black.
** [Bugfix] [Http] $gtk.http_post now works correctly with form collections.
** [Samples] New sample app ~samples/04_physics_and_collisions/06_box_collision_3~ added that shows auto-tiling concepts with dynamically changing collisions.

* 1.27
** [Pro] [API] [Audio] Sound synthesis capabilities added.
** [Pro] [Samples] [Audio] Added a new sample app samples/01_rendering_basics/07_sound_synthesis/ that shows how to create sine waves.
** [Pro] [OSS] [iOS] iOS Wizard open sourced.
** [Bugfix] Scaling issues fixed with HTML5 games.
** [API] Added ~GTK::Runtime#openurl~ ~args.gtk.openurl(some_url)~.
** [API] Added ~GTK::Runtime#set_window_fullscreen~ ~args.gtk.set_window_fullscreen(true|false)~.
** [Bugfix] Sprite angles now accept float values instead of just ints.
** [Bugfix] More accurate results for ~args.gtk.calcstringbox~.
** [Support] Better cursor rendering in the Console.
** [Experimental] Added component layout api (open sourced), take a look at samples/01_rendering_basics/07_sound_synthesis/ to see how it's used to layout buttons.
** [Support] Emscripten dependencies updated (hopefully yielding better HTML5 games).
** [OSS] [iOS] Itch Wizard open sourced.

* 1.26
** [Pro] [Samples] [C Extensions] New sample app added showing how to incorporate your own regex library: 12_c_extensions/02_intermediate
** [Pro] [Samples] [C Extensions] New sample app added showing how to manipulate single pixels: 12_c_extensions/03_native_pixel_arrays
** [Samples] [API] [Experimental] Added a new sample app 01_rendering/06_audio_mixer that gives you more control over sounds.
   Check out the YouTube video at [[https://www.youtube.com/watch?v=b1pL4pEWymI&ab_channel=AmirRajan]]
** [Docs] Docs added for ~outputs.screenshots~ (thank you kfischer_okarin@discord).
** [Support] ~trace!~ now provides timing information (thank you danhealy@discord).
** [Support] Console now supports left and right movement of the cursor (thank you kfischer_okarin@discord).
** [Bugfix] ~args.state~ serialization and deserialization now takes into consideration hashes that were stored by reference.
** [API] Added ~attr~ class method that is an alias for ~attr_accessor~.
** [API] Re-added ~args.gtk.save_state~ and ~args.gtk.load_state~ as convenience methods.
   This is what the convenience methods do:
   #+begin_src ruby
     def save_state
       serialize_state "game_state_#{Time.now.to_i}.txt", @args.state
       serialize_state "game_state.txt", @args.state
     end

     def load_state
       @args.state = deserialize_state 'game_state.txt'
     end
   #+end_src
** [OSS] Implementation of draw order and the underlying FFI methods have been open sourced.
** [API] ~GTK::Runtime#console_button_primitive~
   The ~args.gtk.console_button_primitive~ is a function that returns and handles the click of a button that will render in the top right
   corner of the screen. If this button is clicked, then the console will be shown. This is useful for when you want have the console
   readily available on Desktop or if you need to bring up the console on mobile devices (mobile is available in DragonRuby Game
   Toolkit Pro only). To use this, add the following to your ~tick~ method:
   #+begin_src ruby
     def tick args
       args.outputs.reserved << args.gtk.console_button_primitive
     end
   #+end_src
   The source code for this function is:
   #+begin_src ruby
   module GTK
     class Runtime
       def console_button_primitive
         return nil if $gtk.production
         return nil if @console.visible?

         @console_button ||= { x: 55.from_right, y: 50.from_top, w: 50, h: 50, path: 'console-logo.png', a: 80 }.sprite
         if @args.inputs.mouse.click && (@args.inputs.mouse.inside_rect? @console_button)
           @args.inputs.mouse.click = nil
           @console.show
         end
         @console_button
       end
     end
   end
   #+end_src
** [Bugfix] HTML game will now be uploaded to Itch.io along with Mac, Linux, PC, and Raspberry Pi binaries.
** [Support] Emscripten compiler has been updated.
* 1.25
** [Bugfix] Hash#anchor_rect wasn't working like Array#anchor_rect (now it is).
** [API] Added Mouse#inside_circle? so you don't have to do ~args.inputs.mouse.point.inside_circle?~.
** [Bugfix] Mac app will no longer ask to monitor keyboard input when starting.
* 1.24
** [Bugfix] ~GTK::Runtime#(write|append)_file~
   ~$gtk.(write|append)_file in production mode will no long write to the
   app directory. You can get the write location of the file using
   ~gtk.get_game_dir~.
** [Wizard] [Experimental] Itch.io Wizard
   An Itch.io Wizard has been added to the Console as a menu button. you
   can also invoke the wizard via ~$wizards.itch.start~.
* 1.23
** [API] ~Numeric#randomize~
   ~Numeric#randomize~ can now accept the symbol ~:int~.
   ~(100.randomize :int)~ will return a random whole number between ~0~
   and ~100~ inclusive. Here are some other uses of ~Numeric#randomize~:
   #+begin_src ruby
     100.randomize :ratio        # returns a float between 0 and 100
     100.randomize :ratio, :sign # returns a float between 0 and 100, but negative or positive
   #+end_src
** [Bugfix] ~Label#size_enum~
   Fixed bug where a label's size_enum is larger than the maximum font
   size of the OS.
** [API] ~GTK::Runtime#(write|append)_file~ ~GTK::Runtime#(write|append)_file_root~
   This is kind of a breaking change, but should actually yield a nice
   development experience. ~$gtk.(write|append)_file~ will now write to
   your game directory as opposed to writing to the process execution
   directory. To write to the process execution directory, use
   ~gtk.(write|append)_file_root~.
** [Bugfix] ~def tick args~
   The simulation will run more consistently in the event a slow ~tick~
   occurs and won't try to "catchup" (which causes the game to run very
   fast for a few ticks).
* 1.22
** [Samples] Console Customization
   Sample app added that shows how to add custom buttons to the
   Console. The new sample app is located at ~./samples/99_genre_dev_tools/add_buttons_to_console~
* 1.21
** [Bugfix] Sprite Color Saturation
   There was a bug with rgb saturations on sprites. This bug was
   terminated with extreme prejudice.
* 1.20
** [Bugfix] Line Colors
   The green and blue values for lines and screenshots was flipped. This
   bug was terminated with extreme prejudice.
* 1.19
** [Samples] Sprite Rendering Performance
   Better sample apps showing a spectrum of ways to render a lot of
   sprites. All sprite_limits samples apps were reworked.
** [Support] Override Draw Call For Sprite
   Performance enhancements to draw calls and a means to override them
   completely if you really need to (take a look at the sprite limit
   sample apps).
** [Support] Troubleshooting Framerate
   Added better diagnostics for performance issues. Try
   ~$gtk.framerate_diagnostics~ from the console. This diagnostics
   information has been open sourced.
** [Samples] Sample Apps Reorganized
** [Bugfix] ~Outputs#background_color~
   Setting ~Output#background_color~ to an invalid value no longer segfaults.
** [Docs] Searching Sample Code
   The file docs/docs.html now contains all code from the sample apps so you only have to search in one place.
** [Samples] There are over 12,000 lines of sample code now :-)
* 1.18
** [Docs] Added the "Continuity of Design" to the "DragonRuby's Philosophy" section.
** [Docs] Added all source code that has been open sourced to the docs.html.
** [Support] ~$gtk.reset~ Enhancements
   ~$gtk.reset~ now resets the grids origin back to bottom left by calling the ~args.grid.origin_bottom_left!~ method.
** [API] ~Numeric#from_top~
   Added ~Numeric#from_top~ so that it's easier to position primitives:
   #+begin_src ruby
     args.outputs.labels << [10, 10.from_top, "hello world"]
   #+end_src
** [API] ~Object::attr_gtk~, ~Object::attr_sprite~, ~Object::attr_rect~ as instance methods.
** [API] ~Mouse#inside_rect?~
   Added ~Mouse#inside_rect?~ (~args.inputs.mouse.inside_rect?~) which
   delegates to ~args.inputs.mouse.point.inside_rect?~.
** [Support] Added a collection of menu buttons for common tasks to DragonRuby Console.
* 1.17
** [MacOS] Updated minimum OS support to include MacOS 10.9+ (really I swear this time it's fixed definitely, maybe).
* 1.16
** [Bugfix] Fixed bug in HTML5 template that would cause an exception when releasing to Itch.
* 1.15
** [Support] Rendering Enhancements
   Rendering and simulation have been decoupled which will allow fps to
   be unbounded. ~tick~ will still run at 60 hz (we just render as fast
   as the platform allows).
* 1.14
** [Support] HTML5 Template Updated
   Better HTML5 template. Additional JS events added to handle loss of
   keyboard input within an iframe.
** [Bugfix] ~args.outputs.screenshots~ regression fixed.
** [Docs] Added documentation for a few more ~Numeric~ methods.
** [Samples] Animation Creator
   Brand new advanced sample app: 99_sample_sprite_animation_creator. The
   sample app uses ~args.outputs.screenshots~ and ~render_targets~
   heavily along with in memory queues as a means to consolidate events
   coming from different parts of the app.
* 1.13
** [API] Sprite angle now accepts fractional degrees.
** [Samples] Better font added to LOWREZJAM 2020 template.
** [API] ~args.outputs[RENDER_TARGET_NAME]~
   Added ~args.outputs[RENDER_TARGET_NAME]~ as an alias to
   ~args.render_target(RENDER_TARGET_NAME)~. Either of the following will
   work:
   #+begin_src ruby
     def tick args
       if args.state.tick_count == 1
          args.render_target(:camera).width  = 100
          args.render_target(:camera).height = 100
          args.render_target(:camera).solids << [0, 0, 50, 50, 255, 0, 0]
       end

       if args.state.tick_count > 0
         args.outputs.sprites << { x: 0,
                                   y: 0,
                                   w: 500,
                                   h: 500,
                                   source_x: 0,
                                   source_y: 0,
                                   source_w: 50,
                                   source_h: 50,
                                   path: :camera }
       end
     end

     $gtk.reset
   #+end_src

   Is the same as:

   #+begin_src ruby
     def tick args
       if args.state.tick_count == 1
          args.outputs[:camera].width  = 100
          args.outputs[:camera].height = 100
          args.outputs[:camera].solids << [0, 0, 50, 50, 255, 0, 0]
       end

       if args.state.tick_count > 0
         args.outputs.sprites << { x: 0,
                                   y: 0,
                                   w: 500,
                                   h: 500,
                                   source_x: 0,
                                   source_y: 0,
                                   source_w: 50,
                                   source_h: 50,
                                   path: :camera }
       end
     end

     $gtk.reset
   #+end_src
* 1.12
** [Samples] New LOWREZ Sample
   LOWREZ Jam sample app reworked in preparation for LOWREZ Jam 2020
   (starts on August 1st so hurry and join).
** [Docs] ~GTK::Mouse.docs~
   Docs added for GTK::Mouse, you can access them via the Console by typing ~GTK::Mouse.docs~ or ~$gtk.args.inputs.mouse.docs~.
** [MacOS] Updated minimum OS support to include MacOS 10.9+.
* 1.11
** [Bugfix] Fixed error in ~docs_search "TERM"~.
* 1.10
** [Support] Documentation infrastructure added (take a look at docs/docs.html).
   Bring up the DragonRuby Console.
   To search docs you can type ~docs_search "SEARCH TERM"~
   If you want to get fancy you can provide a ~lambda~ to filter documentation:
   #+begin_src ruby
      docs_search { |entry| (entry.include? "Array") && (!entry.include? "Enumerable") }
   #+end_src
** [Bugfix] ~Sprite#source_(x|y|w|h)
   Fixed sprite rendering issues with source_(x|y|w|h) properties on
   sprites.
** [Support] Removed Double Buffering
   Removed double buffering of game if framerate drops below 60
   fps. There have been quite a few performance enhancements that make
   this method unneeded (plus there will be future enhancments to
   rendering).
** [Support] Console Scroll Wheel Support
   Console now supports mouse wheel scrolling.
** [Support] One Time Notification Clean Up
   One time notifications have less noise/easier to read.
** [Bugfix] Orphaned ~app~ Directory
   Rogue ~app/main.rb~ directory will no longer be created if you run a sample app.
* 1.9
** [Bugfix] HTTP on windows should now work, for real this time.
** [Bugfix] Non-720p render targets now use correct coordinate system.
* 1.8
** [Bugfix] HTTP on windows should now work.
** [Bugfix] ~even?~ and ~odd?~ return the correct result for Fixnum.
** [Bugfix] ~args.intputs.mouse_wheel~ now reports the delta change in x and y correctly.
** [Bugfix] Improved analog joystick accuracy when converting to percentages.
** [Support] Console Tab Completion
   Incorporated pull request from https://github.com/kfischer-okarin that adds autocompletion to the Console.
   This is the PR:
     - https://github.com/DragonRuby/dragonruby-game-toolkit-contrib/commit/da0fdcfbd2bd9739fe056eb646920df79a32954c
     - https://github.com/DragonRuby/dragonruby-game-toolkit-contrib/commit/99305ca79118fa0704c8681f4019738b8c7a500d
* 1.7
** [BREAKING] ~args.inputs.mouse.wheel.point~
   ~args.inputs.mouse.wheel.point~ is gone. Use args.inputs.mouse.x and
   .y if you need cursor position.
** [BREAKING] ~args.inputs.mouse.wheel.(x|y)~
   ~args.inputs.mouse.wheel.(x|y)~ now represent the amount the
   mousewheel/trackpad has moved since the last tick and not the mouse
   cursor position. Use args.inputs.mouse.x and .y if you need cursor
   position.
* 1.6
** [API] ~Sprite#source_(x|y|w|h)~
   ~Sprite~ now supports source_(x|y|w|h). These properties are
   consistent with the origin being in the bottom left. The existing
   properties tile_(x|y|w|h) assumes that origin 0, 0 is in the top
   left. The code below will render the same sprite (in their respective
   coordinate systems):
   #+begin_src ruby
     # using tile_(x|y|w|h) properties
     args.outputs.sprites << { x: 0,
                               y: 0,
                               w: 1280,
                               h: 100,
                               path: :block,
                               tile_x: 0,
                               tile_y: 720 - 100,
                               tile_w: 1280,
                               tile_h: 100 }
   #+end_src

   is equivalent to:

   #+begin_src ruby
     # using source_(x|y|w|h) properties
     args.outputs.sprites << { x: 0,
                               y: 0,
                               w: 1280,
                               h: 100,
                               path: :block,
                               source_x: 0,
                               source_y: 0,
                               source_w: 1280,
                               source_h: 100 }
   #+end_src
   Note: if you provide both tile_(x|y|w|h) and source_(x|y|w|h). The
   values of tile_ will "win" so as not to break existing code out
   there.
** [Bugfix] Duplicate Requires
   Updated require to remove duplicate requires of the same file (or
   files that have recently been required).
** [Bugfix] Strict Entities Serialization
   Strict entities of different types/names serialize and deserialize
   correctly.
** [Samples] Render Targets with Alphas
   Updated render targets sample app to show two render targets with
   transparencies. No really, render targets now have a transparent
   background and respect opacity.
* 1.5
** [API] ~$gtk.(show|hide)_cursor~
   Added ~$gtk.show_cursor~ and ~$gtk.hide_cursor~ to show and hide the mouse
   cursor. The function only needs to be called once. EG:
   #+begin_src ruby
     def tick args
       args.gtk.hide_cursor if args.state.tick_count == 0
     end
   #+end_src
   The Jam Craft 2020 sample app updated to have more comments and demonstrate a custom mouse cursor.
* 1.4
** [Bugfix] ~$gtk.reset~
   Adding $gtk.reset at the bottom of main.rb will no longer cause an infinite loop.
** [Samples] Jam Craft 2020.
* 1.3
** [Samples] Added Better Help Text/Explanations
* 1.2
** [Bugfix] Top-level require statements within main.rb will load before invoking the rest of the code in main.rb.
** [Samples] Better keyboard input sample app.
** [Samples] New sample app that shows how to use Numeric#ease_spline.
** [Bugfix] Fixed "FFI::Draw cannot be serialized" error message.
* 1.1
** [Bugfix] Fixed exception associated with providing serialization related help.
** [Bugfix] Fixed comments on how to run tests from CLI.
** [Support] More helpful global variables added. Here's a list:
     - $gtk
     - $console
     - $args
     - $state
     - $tests
     - $record
     - $replay
** [API] inputs.keyboard.key_(down|held|up).any? and inputs.keyboard.key_(down|held|up).all? added.
** [Support] Recording gameplay and replaying streamlined a bit more. GIVE THE REPLAY FEATURE A SHOT! IT'S AWESOME!! Bring up the console and run: $record.start SEED_NUMBER.
** [Support] Bringing up the console will stop a replay if one is running.
* 1.0
** [News] DragonRuby Game Toolkit turns 1.0. Happy birthday!
** [Bugfix] args.state.new_entity_strict serializes and deserializes correctly now.
** [BREAKING] Entity#hash has been renamed to Entity#as_hash so as not to redefine Object#hash. This is a "private" method so you probably don't have to worry about anything breaking on your end.
** [BREAKING] gtk.save_state and gtk.load_state have been replaced with gtk.serialize_state and gtk.deserialize_state (helpful error messages have been added).
** [Support] Console can now render sprites (this is still in its early stages). Try $gtk.console.addsprite(w: 50, h: 50, path: "some_path.png").
** [API] Render targets now have a transparent background and respect opacity.
** [API] Render targets can be cached/programatically created once and reused.
** [Samples] A new render target sample app has been created to show how to cache them.
** [Samples] Easing sample app reworked/simplified.
** [Support] GTK will keep a backup of your source file changes under the tmp directory. One day this feature will save your ass.