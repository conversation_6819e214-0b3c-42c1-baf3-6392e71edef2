replay_version 2.0
stopped_at 1274
seed 100
recorded_at Sun Sep 29 21:40:29 2019
[:mouse_move, 961, 28, 2, 1, 44]
[:mouse_move, 948, 31, 2, 2, 45]
[:mouse_move, 932, 36, 2, 3, 46]
[:mouse_move, 891, 48, 2, 4, 47]
[:mouse_move, 868, 55, 2, 5, 48]
[:mouse_move, 779, 79, 2, 6, 49]
[:mouse_move, 713, 96, 2, 7, 50]
[:mouse_move, 568, 120, 2, 8, 51]
[:mouse_move, 448, 132, 2, 9, 52]
[:mouse_move, 332, 134, 2, 10, 53]
[:mouse_move, 305, 134, 2, 11, 54]
[:mouse_move, 272, 132, 2, 12, 55]
[:mouse_move, 262, 131, 2, 13, 56]
[:mouse_move, 233, 127, 2, 14, 57]
[:mouse_move, 225, 126, 2, 15, 58]
[:mouse_move, 208, 123, 2, 16, 59]
[:mouse_move, 205, 123, 2, 17, 72]
[:mouse_move, 191, 131, 2, 18, 73]
[:mouse_move, 183, 135, 2, 19, 74]
[:mouse_move, 176, 138, 2, 20, 75]
[:mouse_move, 165, 144, 2, 21, 76]
[:mouse_move, 160, 146, 2, 22, 77]
[:mouse_move, 153, 149, 2, 23, 78]
[:mouse_move, 152, 149, 2, 24, 79]
[:mouse_move, 150, 150, 2, 25, 80]
[:mouse_move, 150, 149, 2, 26, 82]
[:mouse_move, 151, 148, 2, 27, 83]
[:mouse_move, 152, 146, 2, 28, 84]
[:mouse_move, 154, 144, 2, 29, 85]
[:mouse_move, 154, 143, 2, 30, 86]
[:mouse_move, 155, 143, 2, 31, 87]
[:mouse_move, 155, 142, 2, 32, 88]
[:mouse_move, 155, 141, 2, 33, 93]
[:mouse_move, 155, 140, 2, 34, 97]
[:mouse_move, 155, 138, 2, 35, 98]
[:mouse_move, 155, 136, 2, 36, 99]
[:mouse_move, 156, 135, 2, 37, 100]
[:mouse_move, 156, 133, 2, 38, 101]
[:mouse_move, 156, 132, 2, 39, 103]
[:mouse_button_pressed, 1, 0, 1, 40, 119]
[:mouse_button_up, 1, 0, 1, 41, 125]
[:mouse_move, 156, 133, 2, 42, 129]
[:mouse_move, 156, 138, 2, 43, 130]
[:mouse_move, 156, 147, 2, 44, 131]
[:mouse_move, 156, 163, 2, 45, 132]
[:mouse_move, 156, 174, 2, 46, 133]
[:mouse_move, 156, 182, 2, 47, 134]
[:mouse_move, 156, 192, 2, 48, 135]
[:mouse_move, 156, 194, 2, 49, 136]
[:mouse_move, 156, 195, 2, 50, 137]
[:mouse_move, 156, 196, 2, 51, 138]
[:mouse_button_pressed, 1, 0, 1, 52, 138]
[:mouse_button_up, 1, 0, 1, 53, 148]
[:mouse_move, 156, 205, 2, 54, 154]
[:mouse_move, 156, 215, 2, 55, 155]
[:mouse_move, 156, 220, 2, 56, 156]
[:mouse_move, 156, 226, 2, 57, 157]
[:mouse_move, 156, 229, 2, 58, 158]
[:mouse_move, 156, 236, 2, 59, 159]
[:mouse_move, 156, 238, 2, 60, 160]
[:mouse_move, 156, 242, 2, 61, 161]
[:mouse_move, 156, 246, 2, 62, 162]
[:mouse_move, 156, 271, 2, 63, 163]
[:mouse_move, 157, 288, 2, 64, 164]
[:mouse_move, 161, 322, 2, 65, 165]
[:mouse_move, 165, 343, 2, 66, 166]
[:mouse_move, 178, 405, 2, 67, 167]
[:mouse_move, 187, 432, 2, 68, 168]
[:mouse_button_pressed, 1, 0, 1, 69, 181]
[:mouse_move, 187, 440, 2, 70, 191]
[:mouse_move, 188, 466, 2, 71, 192]
[:mouse_move, 188, 476, 2, 72, 193]
[:mouse_move, 188, 486, 2, 73, 194]
[:mouse_move, 188, 501, 2, 74, 195]
[:mouse_move, 189, 512, 2, 75, 196]
[:mouse_move, 190, 516, 2, 76, 197]
[:mouse_move, 190, 517, 2, 77, 198]
[:mouse_move, 190, 518, 2, 78, 199]
[:mouse_move, 190, 519, 2, 79, 200]
[:mouse_move, 190, 520, 2, 80, 202]
[:mouse_move, 191, 520, 2, 81, 206]
[:mouse_move, 191, 521, 2, 82, 207]
[:mouse_move, 193, 523, 2, 83, 208]
[:mouse_move, 193, 524, 2, 84, 209]
[:mouse_move, 194, 526, 2, 85, 210]
[:mouse_move, 194, 527, 2, 86, 211]
[:mouse_move, 195, 528, 2, 87, 212]
[:mouse_move, 195, 529, 2, 88, 221]
[:mouse_move, 195, 528, 2, 89, 224]
[:mouse_button_up, 1, 0, 1, 90, 227]
[:mouse_button_pressed, 1, 0, 1, 91, 245]
[:mouse_button_up, 1, 0, 1, 92, 246]
[:mouse_button_pressed, 1, 0, 1, 93, 255]
[:mouse_button_up, 1, 0, 1, 94, 255]
[:mouse_button_pressed, 1, 0, 1, 95, 260]
[:mouse_button_up, 1, 0, 1, 96, 262]
[:mouse_button_pressed, 1, 0, 1, 97, 268]
[:mouse_button_up, 1, 0, 1, 98, 271]
[:mouse_button_pressed, 1, 0, 1, 99, 281]
[:mouse_button_up, 1, 0, 1, 100, 285]
[:mouse_button_pressed, 1, 0, 1, 101, 288]
[:mouse_button_up, 1, 0, 1, 102, 294]
[:mouse_move, 195, 527, 2, 103, 310]
[:mouse_move, 196, 522, 2, 104, 311]
[:mouse_move, 208, 493, 2, 105, 312]
[:mouse_move, 213, 482, 2, 106, 313]
[:mouse_move, 227, 457, 2, 107, 314]
[:mouse_move, 236, 444, 2, 108, 315]
[:mouse_move, 250, 425, 2, 109, 316]
[:mouse_move, 256, 418, 2, 110, 317]
[:mouse_move, 266, 410, 2, 111, 318]
[:mouse_move, 270, 408, 2, 112, 319]
[:mouse_move, 278, 406, 2, 113, 320]
[:mouse_move, 280, 406, 2, 114, 321]
[:mouse_move, 285, 405, 2, 115, 322]
[:mouse_move, 286, 405, 2, 116, 323]
[:mouse_move, 288, 405, 2, 117, 324]
[:mouse_move, 288, 404, 2, 118, 325]
[:mouse_move, 289, 404, 2, 119, 326]
[:mouse_button_pressed, 1, 0, 1, 120, 330]
[:mouse_button_up, 1, 0, 1, 121, 339]
[:mouse_move, 291, 402, 2, 122, 342]
[:mouse_move, 297, 391, 2, 123, 343]
[:mouse_move, 308, 375, 2, 124, 344]
[:mouse_move, 324, 357, 2, 125, 345]
[:mouse_move, 346, 337, 2, 126, 346]
[:mouse_move, 353, 334, 2, 127, 347]
[:mouse_move, 379, 321, 2, 128, 348]
[:mouse_move, 384, 321, 2, 129, 349]
[:mouse_move, 397, 320, 2, 130, 350]
[:mouse_move, 406, 320, 2, 131, 351]
[:mouse_move, 408, 320, 2, 132, 352]
[:mouse_move, 410, 320, 2, 133, 353]
[:mouse_button_pressed, 1, 0, 1, 134, 353]
[:mouse_move, 411, 320, 2, 135, 353]
[:mouse_button_up, 1, 0, 1, 136, 362]
[:mouse_move, 410, 320, 2, 137, 364]
[:mouse_move, 410, 321, 2, 138, 374]
[:mouse_move, 410, 323, 2, 139, 375]
[:mouse_move, 409, 324, 2, 140, 376]
[:mouse_move, 409, 325, 2, 141, 377]
[:mouse_move, 425, 314, 2, 142, 381]
[:mouse_move, 441, 305, 2, 143, 382]
[:mouse_move, 470, 293, 2, 144, 383]
[:mouse_move, 530, 278, 2, 145, 384]
[:mouse_move, 557, 278, 2, 146, 385]
[:mouse_move, 585, 278, 2, 147, 386]
[:mouse_move, 619, 287, 2, 148, 387]
[:mouse_move, 638, 292, 2, 149, 388]
[:mouse_move, 658, 298, 2, 150, 389]
[:mouse_move, 670, 303, 2, 151, 390]
[:mouse_move, 686, 307, 2, 152, 391]
[:mouse_move, 692, 309, 2, 153, 392]
[:mouse_move, 698, 309, 2, 154, 393]
[:mouse_move, 701, 310, 2, 155, 394]
[:mouse_move, 705, 311, 2, 156, 395]
[:mouse_move, 706, 312, 2, 157, 396]
[:mouse_move, 708, 313, 2, 158, 397]
[:mouse_move, 708, 303, 2, 159, 401]
[:mouse_move, 708, 297, 2, 160, 402]
[:mouse_move, 708, 290, 2, 161, 403]
[:mouse_move, 708, 286, 2, 162, 404]
[:mouse_move, 705, 280, 2, 163, 405]
[:mouse_move, 701, 281, 2, 164, 406]
[:mouse_move, 687, 294, 2, 165, 407]
[:mouse_move, 678, 303, 2, 166, 408]
[:mouse_move, 669, 311, 2, 167, 409]
[:mouse_move, 635, 331, 2, 168, 410]
[:mouse_move, 628, 333, 2, 169, 411]
[:mouse_move, 617, 335, 2, 170, 412]
[:mouse_move, 610, 336, 2, 171, 413]
[:mouse_move, 601, 338, 2, 172, 414]
[:mouse_move, 599, 339, 2, 173, 415]
[:mouse_button_pressed, 1, 0, 1, 174, 415]
[:mouse_move, 598, 339, 2, 175, 415]
[:mouse_button_up, 1, 0, 1, 176, 425]
[:mouse_move, 598, 337, 2, 177, 425]
[:mouse_move, 600, 334, 2, 178, 425]
[:mouse_move, 603, 329, 2, 179, 426]
[:mouse_move, 608, 325, 2, 180, 427]
[:mouse_move, 621, 316, 2, 181, 428]
[:mouse_move, 639, 307, 2, 182, 429]
[:mouse_move, 669, 296, 2, 183, 430]
[:mouse_move, 690, 290, 2, 184, 431]
[:mouse_move, 720, 278, 2, 185, 432]
[:mouse_move, 738, 271, 2, 186, 433]
[:mouse_move, 754, 265, 2, 187, 434]
[:mouse_move, 770, 261, 2, 188, 435]
[:mouse_move, 772, 261, 2, 189, 436]
[:mouse_move, 782, 259, 2, 190, 437]
[:mouse_move, 786, 259, 2, 191, 438]
[:mouse_move, 787, 259, 2, 192, 440]
[:mouse_button_pressed, 1, 0, 1, 193, 440]
[:mouse_button_up, 1, 0, 1, 194, 451]
[:mouse_move, 785, 259, 2, 195, 454]
[:mouse_move, 780, 259, 2, 196, 455]
[:mouse_move, 753, 270, 2, 197, 456]
[:mouse_move, 695, 302, 2, 198, 457]
[:mouse_move, 665, 322, 2, 199, 458]
[:mouse_move, 631, 349, 2, 200, 459]
[:mouse_move, 553, 433, 2, 201, 460]
[:mouse_move, 513, 485, 2, 202, 461]
[:mouse_move, 438, 608, 2, 203, 462]
[:mouse_move, 381, 719, 2, 204, 463]
[:mouse_move, 383, 695, 2, 205, 484]
[:mouse_move, 386, 690, 2, 206, 485]
[:mouse_move, 402, 671, 2, 207, 486]
[:mouse_move, 409, 665, 2, 208, 487]
[:mouse_move, 415, 658, 2, 209, 488]
[:mouse_move, 418, 655, 2, 210, 489]
[:mouse_move, 420, 654, 2, 211, 490]
[:mouse_move, 422, 653, 2, 212, 490]
[:mouse_move, 423, 652, 2, 213, 491]
[:mouse_move, 424, 650, 2, 214, 492]
[:mouse_move, 425, 648, 2, 215, 492]
[:mouse_move, 426, 646, 2, 216, 493]
[:mouse_move, 428, 638, 2, 217, 494]
[:mouse_move, 430, 633, 2, 218, 495]
[:mouse_move, 433, 627, 2, 219, 496]
[:mouse_move, 436, 615, 2, 220, 497]
[:mouse_move, 438, 609, 2, 221, 498]
[:mouse_move, 439, 604, 2, 222, 498]
[:mouse_move, 439, 598, 2, 223, 499]
[:mouse_move, 440, 593, 2, 224, 500]
[:mouse_move, 440, 586, 2, 225, 501]
[:mouse_move, 440, 582, 2, 226, 502]
[:mouse_move, 440, 579, 2, 227, 502]
[:mouse_move, 440, 577, 2, 228, 503]
[:mouse_move, 440, 576, 2, 229, 504]
[:mouse_move, 440, 575, 2, 230, 504]
[:mouse_move, 440, 574, 2, 231, 505]
[:mouse_move, 440, 572, 2, 232, 507]
[:mouse_move, 440, 571, 2, 233, 508]
[:mouse_move, 440, 570, 2, 234, 508]
[:mouse_move, 440, 569, 2, 235, 509]
[:mouse_move, 440, 568, 2, 236, 510]
[:mouse_move, 440, 567, 2, 237, 511]
[:mouse_move, 440, 566, 2, 238, 511]
[:mouse_button_pressed, 1, 0, 1, 239, 512]
[:mouse_move, 441, 566, 2, 240, 512]
[:mouse_button_up, 1, 0, 1, 241, 520]
[:mouse_move, 444, 566, 2, 242, 533]
[:mouse_move, 466, 576, 2, 243, 534]
[:mouse_move, 480, 582, 2, 244, 535]
[:mouse_move, 559, 606, 2, 245, 536]
[:mouse_move, 651, 624, 2, 246, 537]
[:mouse_move, 685, 628, 2, 247, 538]
[:mouse_move, 770, 634, 2, 248, 539]
[:mouse_move, 821, 634, 2, 249, 540]
[:mouse_move, 830, 634, 2, 250, 541]
[:mouse_move, 850, 634, 2, 251, 542]
[:mouse_move, 859, 634, 2, 252, 543]
[:mouse_move, 864, 634, 2, 253, 544]
[:mouse_move, 869, 634, 2, 254, 545]
[:mouse_move, 870, 634, 2, 255, 546]
[:mouse_move, 871, 634, 2, 256, 547]
[:mouse_move, 872, 634, 2, 257, 549]
[:mouse_move, 873, 633, 2, 258, 550]
[:mouse_move, 875, 633, 2, 259, 551]
[:mouse_move, 875, 632, 2, 260, 553]
[:mouse_button_pressed, 1, 0, 1, 261, 553]
[:mouse_move, 876, 632, 2, 262, 553]
[:mouse_button_up, 1, 0, 1, 263, 562]
[:mouse_move, 877, 631, 2, 264, 565]
[:mouse_move, 885, 625, 2, 265, 566]
[:mouse_move, 898, 616, 2, 266, 567]
[:mouse_move, 926, 597, 2, 267, 568]
[:mouse_move, 943, 585, 2, 268, 569]
[:mouse_move, 965, 573, 2, 269, 570]
[:mouse_move, 971, 571, 2, 270, 571]
[:mouse_move, 992, 560, 2, 271, 572]
[:mouse_move, 999, 557, 2, 272, 573]
[:mouse_move, 1005, 555, 2, 273, 574]
[:mouse_move, 1008, 553, 2, 274, 575]
[:mouse_move, 1012, 553, 2, 275, 576]
[:mouse_move, 1013, 553, 2, 276, 577]
[:mouse_move, 1015, 552, 2, 277, 578]
[:mouse_move, 1016, 551, 2, 278, 580]
[:mouse_button_pressed, 1, 0, 1, 279, 581]
[:mouse_button_up, 1, 0, 1, 280, 590]
[:mouse_move, 1013, 551, 2, 281, 601]
[:mouse_move, 1012, 551, 2, 282, 602]
[:mouse_move, 1010, 551, 2, 283, 603]
[:mouse_move, 1009, 550, 2, 284, 604]
[:mouse_move, 1008, 547, 2, 285, 605]
[:mouse_move, 1008, 546, 2, 286, 606]
[:mouse_move, 1007, 543, 2, 287, 607]
[:mouse_move, 1006, 541, 2, 288, 608]
[:mouse_move, 1006, 537, 2, 289, 609]
[:mouse_move, 1006, 534, 2, 290, 610]
[:mouse_move, 1006, 524, 2, 291, 611]
[:mouse_move, 1006, 517, 2, 292, 612]
[:mouse_move, 1010, 495, 2, 293, 613]
[:mouse_move, 1014, 482, 2, 294, 614]
[:mouse_move, 1023, 456, 2, 295, 615]
[:mouse_move, 1026, 449, 2, 296, 616]
[:mouse_move, 1031, 437, 2, 297, 617]
[:mouse_move, 1034, 431, 2, 298, 618]
[:mouse_move, 1038, 423, 2, 299, 619]
[:mouse_move, 1039, 421, 2, 300, 620]
[:mouse_move, 1040, 419, 2, 301, 621]
[:mouse_move, 1041, 418, 2, 302, 622]
[:mouse_move, 1041, 415, 2, 303, 635]
[:mouse_move, 1040, 408, 2, 304, 636]
[:mouse_move, 1039, 403, 2, 305, 637]
[:mouse_move, 1035, 393, 2, 306, 638]
[:mouse_move, 1032, 389, 2, 307, 639]
[:mouse_move, 1028, 385, 2, 308, 640]
[:mouse_move, 1027, 383, 2, 309, 641]
[:mouse_move, 1024, 380, 2, 310, 642]
[:mouse_move, 1023, 379, 2, 311, 643]
[:mouse_button_pressed, 1, 0, 1, 312, 644]
[:mouse_move, 1023, 378, 2, 313, 644]
[:mouse_move, 1017, 378, 2, 314, 654]
[:mouse_button_up, 1, 0, 1, 315, 655]
[:mouse_move, 1008, 377, 2, 316, 655]
[:mouse_move, 998, 376, 2, 317, 656]
[:mouse_move, 965, 371, 2, 318, 657]
[:mouse_move, 918, 364, 2, 319, 658]
[:mouse_move, 895, 357, 2, 320, 659]
[:mouse_move, 885, 355, 2, 321, 660]
[:mouse_move, 860, 346, 2, 322, 661]
[:mouse_move, 848, 342, 2, 323, 662]
[:mouse_move, 833, 334, 2, 324, 663]
[:mouse_move, 831, 333, 2, 325, 664]
[:mouse_move, 826, 328, 2, 326, 665]
[:mouse_move, 825, 327, 2, 327, 666]
[:mouse_move, 823, 324, 2, 328, 667]
[:mouse_move, 821, 322, 2, 329, 669]
[:mouse_move, 821, 321, 2, 330, 670]
[:mouse_move, 814, 321, 2, 331, 671]
[:mouse_move, 802, 327, 2, 332, 672]
[:mouse_move, 758, 366, 2, 333, 673]
[:mouse_move, 724, 393, 2, 334, 674]
[:mouse_move, 684, 422, 2, 335, 675]
[:mouse_move, 625, 453, 2, 336, 676]
[:mouse_move, 583, 471, 2, 337, 677]
[:mouse_move, 559, 479, 2, 338, 678]
[:mouse_move, 539, 485, 2, 339, 679]
[:mouse_move, 503, 488, 2, 340, 680]
[:mouse_move, 484, 488, 2, 341, 681]
[:mouse_move, 450, 482, 2, 342, 682]
[:mouse_move, 432, 476, 2, 343, 683]
[:mouse_move, 397, 457, 2, 344, 684]
[:mouse_move, 391, 457, 2, 345, 700]
[:mouse_move, 366, 457, 2, 346, 701]
[:mouse_move, 301, 457, 2, 347, 702]
[:mouse_move, 265, 457, 2, 348, 703]
[:mouse_move, 231, 457, 2, 349, 704]
[:mouse_move, 99, 457, 2, 350, 705]
[:mouse_move, 74, 456, 2, 351, 706]
[:mouse_move, 42, 454, 2, 352, 707]
[:mouse_move, 34, 453, 2, 353, 708]
[:mouse_move, 6, 449, 2, 354, 709]
[:mouse_move, 0, 447, 2, 355, 710]
[:mouse_move, 0, 446, 2, 356, 711]
[:mouse_move, 0, 445, 2, 357, 717]
[:mouse_move, 0, 446, 2, 358, 720]
[:mouse_move, 1, 446, 2, 359, 723]
[:mouse_move, 12, 446, 2, 360, 844]
[:mouse_move, 34, 438, 2, 361, 845]
[:mouse_move, 58, 428, 2, 362, 845]
[:mouse_move, 113, 406, 2, 363, 846]
[:mouse_move, 211, 360, 2, 364, 847]
[:mouse_move, 364, 270, 2, 365, 848]
[:mouse_move, 398, 247, 2, 366, 849]
[:mouse_move, 448, 214, 2, 367, 850]
[:mouse_move, 462, 204, 2, 368, 851]
[:mouse_move, 497, 180, 2, 369, 852]
[:mouse_move, 510, 171, 2, 370, 853]
[:mouse_move, 518, 164, 2, 371, 853]
[:mouse_move, 523, 160, 2, 372, 854]
[:mouse_move, 527, 155, 2, 373, 855]
[:mouse_move, 531, 145, 2, 374, 856]
[:mouse_move, 533, 138, 2, 375, 857]
[:mouse_move, 537, 127, 2, 376, 858]
[:mouse_move, 540, 119, 2, 377, 859]
[:mouse_move, 548, 105, 2, 378, 860]
[:mouse_move, 561, 105, 2, 379, 874]
[:mouse_move, 587, 105, 2, 380, 874]
[:mouse_move, 626, 105, 2, 381, 875]
[:mouse_move, 677, 106, 2, 382, 876]
[:mouse_move, 837, 122, 2, 383, 876]
[:mouse_move, 910, 133, 2, 384, 877]
[:mouse_move, 1038, 154, 2, 385, 878]
[:mouse_move, 1135, 169, 2, 386, 879]
[:mouse_move, 1165, 173, 2, 387, 880]
[:mouse_move, 1237, 185, 2, 388, 881]
[:mouse_move, 1230, 185, 2, 389, 891]
[:mouse_move, 1221, 185, 2, 390, 892]
[:mouse_move, 1211, 183, 2, 391, 893]
[:mouse_move, 1196, 179, 2, 392, 893]
[:mouse_move, 1166, 172, 2, 393, 894]
[:mouse_move, 1149, 168, 2, 394, 895]
[:mouse_move, 1117, 161, 2, 395, 895]
[:mouse_move, 1084, 156, 2, 396, 896]
[:mouse_move, 1071, 155, 2, 397, 897]
[:mouse_move, 1046, 152, 2, 398, 897]
[:mouse_move, 1025, 151, 2, 399, 898]
[:mouse_move, 1005, 151, 2, 400, 899]
[:mouse_move, 986, 151, 2, 401, 899]
[:mouse_move, 970, 151, 2, 402, 900]
[:mouse_move, 964, 151, 2, 403, 901]
[:mouse_move, 959, 151, 2, 404, 901]
[:mouse_move, 952, 151, 2, 405, 902]
[:mouse_move, 946, 151, 2, 406, 903]
[:mouse_move, 942, 151, 2, 407, 903]
[:mouse_move, 940, 151, 2, 408, 904]
[:mouse_move, 939, 151, 2, 409, 905]
[:mouse_move, 937, 151, 2, 410, 906]
[:mouse_move, 937, 150, 2, 411, 908]
[:mouse_move, 937, 149, 2, 412, 910]
[:mouse_move, 938, 149, 2, 413, 912]
[:mouse_move, 938, 148, 2, 414, 913]
[:mouse_move, 939, 148, 2, 415, 914]
[:mouse_move, 941, 147, 2, 416, 915]
[:mouse_move, 943, 145, 2, 417, 916]
[:mouse_move, 944, 144, 2, 418, 917]
[:mouse_move, 945, 143, 2, 419, 918]
[:mouse_move, 945, 142, 2, 420, 918]
[:mouse_move, 946, 142, 2, 421, 919]
[:mouse_move, 946, 141, 2, 422, 920]
[:mouse_move, 947, 141, 2, 423, 921]
[:mouse_move, 948, 141, 2, 424, 929]
[:mouse_move, 949, 141, 2, 425, 931]
[:mouse_move, 951, 141, 2, 426, 932]
[:mouse_move, 952, 141, 2, 427, 933]
[:mouse_move, 954, 141, 2, 428, 934]
[:mouse_move, 959, 141, 2, 429, 935]
[:mouse_move, 960, 140, 2, 430, 936]
[:mouse_move, 964, 140, 2, 431, 937]
[:mouse_move, 965, 140, 2, 432, 938]
[:mouse_move, 968, 139, 2, 433, 939]
[:mouse_move, 969, 139, 2, 434, 940]
[:mouse_move, 971, 139, 2, 435, 941]
[:mouse_move, 972, 139, 2, 436, 942]
[:mouse_move, 975, 138, 2, 437, 943]
[:mouse_move, 977, 138, 2, 438, 944]
[:mouse_move, 979, 138, 2, 439, 945]
[:mouse_move, 982, 137, 2, 440, 945]
[:mouse_move, 985, 137, 2, 441, 946]
[:mouse_move, 988, 137, 2, 442, 947]
[:mouse_move, 989, 137, 2, 443, 947]
[:mouse_move, 992, 137, 2, 444, 948]
[:mouse_move, 994, 137, 2, 445, 949]
[:mouse_move, 996, 137, 2, 446, 949]
[:mouse_move, 997, 136, 2, 447, 950]
[:mouse_move, 999, 136, 2, 448, 951]
[:mouse_move, 1000, 136, 2, 449, 951]
[:mouse_move, 1001, 136, 2, 450, 952]
[:mouse_move, 1002, 135, 2, 451, 953]
[:mouse_move, 1003, 135, 2, 452, 953]
[:mouse_move, 1004, 134, 2, 453, 954]
[:mouse_move, 1005, 133, 2, 454, 955]
[:mouse_move, 1005, 132, 2, 455, 956]
[:mouse_move, 1006, 132, 2, 456, 957]
[:mouse_move, 1006, 131, 2, 457, 957]
[:mouse_move, 1007, 130, 2, 458, 958]
[:mouse_move, 1007, 128, 2, 459, 960]
[:mouse_move, 1007, 127, 2, 460, 961]
[:mouse_move, 1007, 126, 2, 461, 962]
[:mouse_move, 1007, 125, 2, 462, 963]
[:mouse_move, 1005, 123, 2, 463, 964]
[:mouse_move, 1004, 122, 2, 464, 965]
[:mouse_move, 1001, 120, 2, 465, 966]
[:mouse_move, 999, 118, 2, 466, 967]
[:mouse_move, 993, 116, 2, 467, 968]
[:mouse_move, 990, 115, 2, 468, 969]
[:mouse_move, 984, 114, 2, 469, 970]
[:mouse_move, 981, 113, 2, 470, 971]
[:mouse_move, 976, 112, 2, 471, 972]
[:mouse_move, 975, 112, 2, 472, 973]
[:mouse_move, 972, 112, 2, 473, 974]
[:mouse_move, 971, 111, 2, 474, 974]
[:mouse_move, 969, 111, 2, 475, 975]
[:mouse_move, 966, 111, 2, 476, 976]
[:mouse_move, 963, 111, 2, 477, 976]
[:mouse_move, 961, 111, 2, 478, 977]
[:mouse_move, 959, 111, 2, 479, 978]
[:mouse_move, 956, 111, 2, 480, 978]
[:mouse_move, 954, 111, 2, 481, 979]
[:mouse_move, 952, 112, 2, 482, 980]
[:mouse_move, 950, 112, 2, 483, 980]
[:mouse_move, 948, 112, 2, 484, 981]
[:mouse_move, 947, 113, 2, 485, 982]
[:mouse_move, 946, 113, 2, 486, 982]
[:mouse_move, 945, 113, 2, 487, 983]
[:mouse_move, 945, 114, 2, 488, 984]
[:mouse_move, 944, 115, 2, 489, 984]
[:mouse_move, 943, 115, 2, 490, 985]
[:mouse_move, 942, 116, 2, 491, 986]
[:mouse_move, 942, 117, 2, 492, 986]
[:mouse_move, 942, 118, 2, 493, 988]
[:mouse_move, 941, 119, 2, 494, 989]
[:mouse_move, 941, 121, 2, 495, 991]
[:mouse_move, 941, 122, 2, 496, 992]
[:mouse_move, 941, 123, 2, 497, 993]
[:mouse_move, 941, 124, 2, 498, 994]
[:mouse_move, 941, 127, 2, 499, 995]
[:mouse_move, 941, 129, 2, 500, 996]
[:mouse_move, 941, 132, 2, 501, 997]
[:mouse_move, 942, 134, 2, 502, 998]
[:mouse_move, 944, 136, 2, 503, 999]
[:mouse_move, 945, 138, 2, 504, 1000]
[:mouse_move, 947, 138, 2, 505, 1001]
[:mouse_move, 948, 139, 2, 506, 1001]
[:mouse_move, 949, 140, 2, 507, 1002]
[:mouse_move, 950, 140, 2, 508, 1003]
[:mouse_move, 952, 141, 2, 509, 1003]
[:mouse_move, 954, 141, 2, 510, 1004]
[:mouse_move, 956, 141, 2, 511, 1005]
[:mouse_move, 958, 141, 2, 512, 1005]
[:mouse_move, 960, 141, 2, 513, 1006]
[:mouse_move, 962, 141, 2, 514, 1007]
[:mouse_move, 964, 141, 2, 515, 1007]
[:mouse_move, 969, 141, 2, 516, 1008]
[:mouse_move, 972, 140, 2, 517, 1009]
[:mouse_move, 975, 140, 2, 518, 1009]
[:mouse_move, 978, 139, 2, 519, 1010]
[:mouse_move, 982, 138, 2, 520, 1011]
[:mouse_move, 987, 136, 2, 521, 1012]
[:mouse_move, 991, 135, 2, 522, 1013]
[:mouse_move, 996, 133, 2, 523, 1014]
[:mouse_move, 999, 132, 2, 524, 1015]
[:mouse_move, 1003, 130, 2, 525, 1016]
[:mouse_move, 1005, 128, 2, 526, 1018]
[:mouse_move, 1006, 126, 2, 527, 1020]
[:mouse_move, 1006, 125, 2, 528, 1021]
[:mouse_move, 1006, 124, 2, 529, 1022]
[:mouse_move, 1006, 123, 2, 530, 1023]
[:mouse_move, 1004, 122, 2, 531, 1024]
[:mouse_move, 1002, 121, 2, 532, 1025]
[:mouse_move, 995, 119, 2, 533, 1026]
[:mouse_move, 990, 118, 2, 534, 1027]
[:mouse_move, 985, 117, 2, 535, 1028]
[:mouse_move, 981, 116, 2, 536, 1028]
[:mouse_move, 979, 116, 2, 537, 1029]
[:mouse_move, 975, 115, 2, 538, 1030]
[:mouse_move, 972, 115, 2, 539, 1030]
[:mouse_move, 969, 115, 2, 540, 1031]
[:mouse_move, 966, 115, 2, 541, 1032]
[:mouse_move, 964, 115, 2, 542, 1032]
[:mouse_move, 962, 115, 2, 543, 1033]
[:mouse_move, 961, 116, 2, 544, 1034]
[:mouse_move, 960, 116, 2, 545, 1034]
[:mouse_move, 959, 117, 2, 546, 1035]
[:mouse_move, 958, 118, 2, 547, 1036]
[:mouse_move, 956, 120, 2, 548, 1036]
[:mouse_move, 955, 122, 2, 549, 1037]
[:mouse_move, 955, 125, 2, 550, 1038]
[:mouse_move, 953, 128, 2, 551, 1038]
[:mouse_move, 952, 132, 2, 552, 1039]
[:mouse_move, 951, 134, 2, 553, 1040]
[:mouse_move, 950, 137, 2, 554, 1040]
[:mouse_move, 948, 138, 2, 555, 1041]
[:mouse_move, 947, 140, 2, 556, 1042]
[:mouse_move, 946, 142, 2, 557, 1043]
[:mouse_move, 945, 142, 2, 558, 1044]
[:mouse_move, 944, 143, 2, 559, 1045]
[:mouse_move, 943, 144, 2, 560, 1050]
[:mouse_move, 943, 145, 2, 561, 1052]
[:mouse_move, 943, 146, 2, 562, 1055]
[:mouse_move, 944, 146, 2, 563, 1059]
[:mouse_move, 945, 146, 2, 564, 1061]
[:mouse_move, 947, 146, 2, 565, 1063]
[:mouse_move, 948, 146, 2, 566, 1064]
[:mouse_move, 949, 147, 2, 567, 1065]
[:mouse_move, 950, 147, 2, 568, 1065]
[:mouse_move, 953, 147, 2, 569, 1066]
[:mouse_move, 954, 147, 2, 570, 1067]
[:mouse_move, 958, 147, 2, 571, 1068]
[:mouse_move, 960, 147, 2, 572, 1069]
[:mouse_move, 963, 147, 2, 573, 1070]
[:mouse_move, 966, 147, 2, 574, 1072]
[:mouse_move, 968, 147, 2, 575, 1074]
[:mouse_move, 969, 147, 2, 576, 1075]
[:mouse_move, 971, 147, 2, 577, 1076]
[:mouse_move, 972, 147, 2, 578, 1077]
[:mouse_move, 974, 147, 2, 579, 1078]
[:mouse_move, 976, 147, 2, 580, 1079]
[:mouse_move, 977, 147, 2, 581, 1080]
[:mouse_move, 978, 147, 2, 582, 1080]
[:mouse_move, 980, 147, 2, 583, 1081]
[:mouse_move, 981, 147, 2, 584, 1082]
[:mouse_move, 983, 147, 2, 585, 1082]
[:mouse_move, 984, 147, 2, 586, 1083]
[:mouse_move, 986, 147, 2, 587, 1084]
[:mouse_move, 987, 147, 2, 588, 1084]
[:mouse_move, 988, 147, 2, 589, 1085]
[:mouse_move, 990, 147, 2, 590, 1086]
[:mouse_move, 992, 147, 2, 591, 1086]
[:mouse_move, 993, 147, 2, 592, 1087]
[:mouse_move, 994, 147, 2, 593, 1088]
[:mouse_move, 996, 147, 2, 594, 1088]
[:mouse_move, 997, 147, 2, 595, 1089]
[:mouse_move, 998, 147, 2, 596, 1090]
[:mouse_move, 999, 147, 2, 597, 1090]
[:mouse_move, 1000, 147, 2, 598, 1091]
[:mouse_move, 1001, 147, 2, 599, 1092]
[:mouse_move, 1002, 147, 2, 600, 1092]
[:mouse_move, 1003, 147, 2, 601, 1093]
[:mouse_move, 1004, 147, 2, 602, 1094]
[:mouse_move, 1005, 147, 2, 603, 1094]
[:mouse_move, 1006, 147, 2, 604, 1095]
[:mouse_move, 1008, 147, 2, 605, 1097]
[:mouse_move, 1009, 147, 2, 606, 1098]
[:mouse_move, 1010, 147, 2, 607, 1099]
[:mouse_move, 1011, 147, 2, 608, 1100]
[:mouse_move, 1013, 147, 2, 609, 1101]
[:mouse_move, 1014, 147, 2, 610, 1102]
[:mouse_move, 1016, 147, 2, 611, 1103]
[:mouse_move, 1018, 147, 2, 612, 1105]
[:mouse_move, 1019, 147, 2, 613, 1106]
[:mouse_move, 1020, 147, 2, 614, 1107]
[:mouse_move, 1021, 147, 2, 615, 1109]
[:mouse_move, 1022, 147, 2, 616, 1110]
[:mouse_move, 1023, 147, 2, 617, 1111]
[:mouse_move, 1024, 147, 2, 618, 1113]
[:mouse_move, 1025, 147, 2, 619, 1115]
[:mouse_move, 1026, 147, 2, 620, 1116]
[:mouse_move, 1027, 147, 2, 621, 1117]
[:mouse_move, 1028, 147, 2, 622, 1117]
[:mouse_move, 1029, 147, 2, 623, 1118]
[:mouse_move, 1030, 147, 2, 624, 1119]
[:mouse_move, 1031, 147, 2, 625, 1119]
[:mouse_move, 1032, 147, 2, 626, 1120]
[:mouse_move, 1033, 147, 2, 627, 1121]
[:mouse_move, 1034, 147, 2, 628, 1122]
[:mouse_move, 1036, 147, 2, 629, 1123]
[:mouse_move, 1037, 147, 2, 630, 1124]
[:mouse_move, 1038, 147, 2, 631, 1125]
[:mouse_move, 1040, 147, 2, 632, 1126]
[:mouse_move, 1042, 147, 2, 633, 1128]
[:mouse_move, 1043, 147, 2, 634, 1129]
[:mouse_move, 1044, 147, 2, 635, 1130]
[:mouse_move, 1045, 147, 2, 636, 1131]
[:mouse_move, 1046, 147, 2, 637, 1132]
[:mouse_move, 1047, 147, 2, 638, 1133]
[:mouse_move, 1048, 147, 2, 639, 1134]
[:mouse_move, 1049, 147, 2, 640, 1136]
[:mouse_move, 1049, 146, 2, 641, 1137]
[:mouse_move, 1050, 146, 2, 642, 1138]
[:mouse_move, 1051, 146, 2, 643, 1139]
[:mouse_move, 1052, 146, 2, 644, 1141]
[:mouse_move, 1053, 146, 2, 645, 1142]
[:mouse_move, 1054, 146, 2, 646, 1144]
[:mouse_move, 1055, 146, 2, 647, 1145]
[:mouse_move, 1056, 146, 2, 648, 1146]
[:mouse_move, 1057, 145, 2, 649, 1147]
[:mouse_move, 1059, 145, 2, 650, 1149]
[:mouse_move, 1060, 145, 2, 651, 1151]
[:mouse_move, 1061, 145, 2, 652, 1152]
[:mouse_move, 1062, 145, 2, 653, 1153]
[:mouse_move, 1062, 144, 2, 654, 1154]
[:mouse_move, 1063, 144, 2, 655, 1155]
[:mouse_move, 1064, 144, 2, 656, 1156]
[:mouse_move, 1065, 144, 2, 657, 1157]
[:mouse_move, 1066, 144, 2, 658, 1158]
[:mouse_move, 1067, 144, 2, 659, 1159]
[:mouse_move, 1068, 144, 2, 660, 1160]
[:mouse_move, 1069, 144, 2, 661, 1161]
[:mouse_move, 1070, 144, 2, 662, 1162]
[:mouse_move, 1071, 144, 2, 663, 1163]
[:mouse_move, 1072, 144, 2, 664, 1165]
[:mouse_move, 1073, 144, 2, 665, 1166]
[:mouse_move, 1074, 144, 2, 666, 1168]
[:mouse_move, 1075, 144, 2, 667, 1169]
[:mouse_move, 1076, 144, 2, 668, 1171]
[:mouse_move, 1077, 144, 2, 669, 1171]
[:mouse_move, 1078, 144, 2, 670, 1172]
[:mouse_move, 1079, 144, 2, 671, 1173]
[:mouse_move, 1080, 144, 2, 672, 1173]
[:mouse_move, 1081, 144, 2, 673, 1174]
[:mouse_move, 1082, 144, 2, 674, 1175]
[:mouse_move, 1083, 144, 2, 675, 1176]
[:mouse_move, 1084, 144, 2, 676, 1177]
[:key_down_raw, 1073742051, 1024, 2, 677, 1272]
[:key_down_raw, 113, 1024, 2, 678, 1273]
