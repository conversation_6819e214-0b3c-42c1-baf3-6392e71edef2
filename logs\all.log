
* WARNING: ~Hash#sprite~ is deprecated becuase it's poorly named. Use ~Hash#sprite!~ instead.
This method will stay here for backwards compatibility. But, consider using one of the following methods:
** Option 1: ~:sprite! &OPTIONAL additional_keys_to_merge~
This will mutate the current hash in-place. It's identical to the current method you're using,
but better signifies that a side effect is occuring (because of the ~!~ at the end).
*** Example
Here is an example of how your code may change.
**** Before
#+begin_src ruby
  def tick args
    args.state.primitive_style  ||= { r: 255, g: 255, b: 255 }

    # here ~Hash#merge!~ and ~Hash#border~ are used in combination
    args.state.some_primitive ||= { x: 8, y: 8, w: 32, h: 32 }.merge!(primitive_style).sprite

    args.outputs.primitives << args.state.some_primitive
  end
#+end_src
**** After
#+begin_src ruby
  def tick args
    args.state.primitive_style  ||= { r: 255, g: 255, b: 255 }

    # if you use ~Hash#sprite!~ you can combine the
    # ~Hash#merge!~ and ~Hash#sprite~ method calls
    args.state.some_sprite ||= { x: 8, y: 8, w: 32, h: 32 }.sprite! primitive_style

    args.outputs.primitives << args.state.button_sprite
  end
#+end_src
** Option 2: ~:to_sprite &OPTIONAL additional_keys_to_merge~
This will return a new ~Hash~ leaving the original *untouched*.
** Caller:
If you decide to fix this error now, you can run ~Log.reset~ to invalidate this warning
and be notified of other places you're using this function.

[Message ID: [:consider_sprite!]]


* WARNING: ~Hash#sprite~ is deprecated becuase it's poorly named. Use ~Hash#sprite!~ instead.
This method will stay here for backwards compatibility. But, consider using one of the following methods:
** Option 1: ~:sprite! &OPTIONAL additional_keys_to_merge~
This will mutate the current hash in-place. It's identical to the current method you're using,
but better signifies that a side effect is occuring (because of the ~!~ at the end).
*** Example
Here is an example of how your code may change.
**** Before
#+begin_src ruby
  def tick args
    args.state.primitive_style  ||= { r: 255, g: 255, b: 255 }

    # here ~Hash#merge!~ and ~Hash#border~ are used in combination
    args.state.some_primitive ||= { x: 8, y: 8, w: 32, h: 32 }.merge!(primitive_style).sprite

    args.outputs.primitives << args.state.some_primitive
  end
#+end_src
**** After
#+begin_src ruby
  def tick args
    args.state.primitive_style  ||= { r: 255, g: 255, b: 255 }

    # if you use ~Hash#sprite!~ you can combine the
    # ~Hash#merge!~ and ~Hash#sprite~ method calls
    args.state.some_sprite ||= { x: 8, y: 8, w: 32, h: 32 }.sprite! primitive_style

    args.outputs.primitives << args.state.button_sprite
  end
#+end_src
** Option 2: ~:to_sprite &OPTIONAL additional_keys_to_merge~
This will return a new ~Hash~ leaving the original *untouched*.
** Caller:
If you decide to fix this error now, you can run ~Log.reset~ to invalidate this warning
and be notified of other places you're using this function.

[Message ID: [:consider_sprite!]]


* WARNING: ~Hash#sprite~ is deprecated becuase it's poorly named. Use ~Hash#sprite!~ instead.
This method will stay here for backwards compatibility. But, consider using one of the following methods:
** Option 1: ~:sprite! &OPTIONAL additional_keys_to_merge~
This will mutate the current hash in-place. It's identical to the current method you're using,
but better signifies that a side effect is occuring (because of the ~!~ at the end).
*** Example
Here is an example of how your code may change.
**** Before
#+begin_src ruby
  def tick args
    args.state.primitive_style  ||= { r: 255, g: 255, b: 255 }

    # here ~Hash#merge!~ and ~Hash#border~ are used in combination
    args.state.some_primitive ||= { x: 8, y: 8, w: 32, h: 32 }.merge!(primitive_style).sprite

    args.outputs.primitives << args.state.some_primitive
  end
#+end_src
**** After
#+begin_src ruby
  def tick args
    args.state.primitive_style  ||= { r: 255, g: 255, b: 255 }

    # if you use ~Hash#sprite!~ you can combine the
    # ~Hash#merge!~ and ~Hash#sprite~ method calls
    args.state.some_sprite ||= { x: 8, y: 8, w: 32, h: 32 }.sprite! primitive_style

    args.outputs.primitives << args.state.button_sprite
  end
#+end_src
** Option 2: ~:to_sprite &OPTIONAL additional_keys_to_merge~
This will return a new ~Hash~ leaving the original *untouched*.
** Caller:
If you decide to fix this error now, you can run ~Log.reset~ to invalidate this warning
and be notified of other places you're using this function.

[Message ID: [:consider_sprite!]]


* WARNING: ~Hash#sprite~ is deprecated becuase it's poorly named. Use ~Hash#sprite!~ instead.
This method will stay here for backwards compatibility. But, consider using one of the following methods:
** Option 1: ~:sprite! &OPTIONAL additional_keys_to_merge~
This will mutate the current hash in-place. It's identical to the current method you're using,
but better signifies that a side effect is occuring (because of the ~!~ at the end).
*** Example
Here is an example of how your code may change.
**** Before
#+begin_src ruby
  def tick args
    args.state.primitive_style  ||= { r: 255, g: 255, b: 255 }

    # here ~Hash#merge!~ and ~Hash#border~ are used in combination
    args.state.some_primitive ||= { x: 8, y: 8, w: 32, h: 32 }.merge!(primitive_style).sprite

    args.outputs.primitives << args.state.some_primitive
  end
#+end_src
**** After
#+begin_src ruby
  def tick args
    args.state.primitive_style  ||= { r: 255, g: 255, b: 255 }

    # if you use ~Hash#sprite!~ you can combine the
    # ~Hash#merge!~ and ~Hash#sprite~ method calls
    args.state.some_sprite ||= { x: 8, y: 8, w: 32, h: 32 }.sprite! primitive_style

    args.outputs.primitives << args.state.button_sprite
  end
#+end_src
** Option 2: ~:to_sprite &OPTIONAL additional_keys_to_merge~
This will return a new ~Hash~ leaving the original *untouched*.
** Caller:
If you decide to fix this error now, you can run ~Log.reset~ to invalidate this warning
and be notified of other places you're using this function.

[Message ID: [:consider_sprite!]]


* WARNING: ~Hash#sprite~ is deprecated becuase it's poorly named. Use ~Hash#sprite!~ instead.
This method will stay here for backwards compatibility. But, consider using one of the following methods:
** Option 1: ~:sprite! &OPTIONAL additional_keys_to_merge~
This will mutate the current hash in-place. It's identical to the current method you're using,
but better signifies that a side effect is occuring (because of the ~!~ at the end).
*** Example
Here is an example of how your code may change.
**** Before
#+begin_src ruby
  def tick args
    args.state.primitive_style  ||= { r: 255, g: 255, b: 255 }

    # here ~Hash#merge!~ and ~Hash#border~ are used in combination
    args.state.some_primitive ||= { x: 8, y: 8, w: 32, h: 32 }.merge!(primitive_style).sprite

    args.outputs.primitives << args.state.some_primitive
  end
#+end_src
**** After
#+begin_src ruby
  def tick args
    args.state.primitive_style  ||= { r: 255, g: 255, b: 255 }

    # if you use ~Hash#sprite!~ you can combine the
    # ~Hash#merge!~ and ~Hash#sprite~ method calls
    args.state.some_sprite ||= { x: 8, y: 8, w: 32, h: 32 }.sprite! primitive_style

    args.outputs.primitives << args.state.button_sprite
  end
#+end_src
** Option 2: ~:to_sprite &OPTIONAL additional_keys_to_merge~
This will return a new ~Hash~ leaving the original *untouched*.
** Caller:
If you decide to fix this error now, you can run ~Log.reset~ to invalidate this warning
and be notified of other places you're using this function.

[Message ID: [:consider_sprite!]]


* WARNING: ~Hash#sprite~ is deprecated becuase it's poorly named. Use ~Hash#sprite!~ instead.
This method will stay here for backwards compatibility. But, consider using one of the following methods:
** Option 1: ~:sprite! &OPTIONAL additional_keys_to_merge~
This will mutate the current hash in-place. It's identical to the current method you're using,
but better signifies that a side effect is occuring (because of the ~!~ at the end).
*** Example
Here is an example of how your code may change.
**** Before
#+begin_src ruby
  def tick args
    args.state.primitive_style  ||= { r: 255, g: 255, b: 255 }

    # here ~Hash#merge!~ and ~Hash#border~ are used in combination
    args.state.some_primitive ||= { x: 8, y: 8, w: 32, h: 32 }.merge!(primitive_style).sprite

    args.outputs.primitives << args.state.some_primitive
  end
#+end_src
**** After
#+begin_src ruby
  def tick args
    args.state.primitive_style  ||= { r: 255, g: 255, b: 255 }

    # if you use ~Hash#sprite!~ you can combine the
    # ~Hash#merge!~ and ~Hash#sprite~ method calls
    args.state.some_sprite ||= { x: 8, y: 8, w: 32, h: 32 }.sprite! primitive_style

    args.outputs.primitives << args.state.button_sprite
  end
#+end_src
** Option 2: ~:to_sprite &OPTIONAL additional_keys_to_merge~
This will return a new ~Hash~ leaving the original *untouched*.
** Caller:
If you decide to fix this error now, you can run ~Log.reset~ to invalidate this warning
and be notified of other places you're using this function.

[Message ID: [:consider_sprite!]]


* WARNING: ~Hash#sprite~ is deprecated becuase it's poorly named. Use ~Hash#sprite!~ instead.
This method will stay here for backwards compatibility. But, consider using one of the following methods:
** Option 1: ~:sprite! &OPTIONAL additional_keys_to_merge~
This will mutate the current hash in-place. It's identical to the current method you're using,
but better signifies that a side effect is occuring (because of the ~!~ at the end).
*** Example
Here is an example of how your code may change.
**** Before
#+begin_src ruby
  def tick args
    args.state.primitive_style  ||= { r: 255, g: 255, b: 255 }

    # here ~Hash#merge!~ and ~Hash#border~ are used in combination
    args.state.some_primitive ||= { x: 8, y: 8, w: 32, h: 32 }.merge!(primitive_style).sprite

    args.outputs.primitives << args.state.some_primitive
  end
#+end_src
**** After
#+begin_src ruby
  def tick args
    args.state.primitive_style  ||= { r: 255, g: 255, b: 255 }

    # if you use ~Hash#sprite!~ you can combine the
    # ~Hash#merge!~ and ~Hash#sprite~ method calls
    args.state.some_sprite ||= { x: 8, y: 8, w: 32, h: 32 }.sprite! primitive_style

    args.outputs.primitives << args.state.button_sprite
  end
#+end_src
** Option 2: ~:to_sprite &OPTIONAL additional_keys_to_merge~
This will return a new ~Hash~ leaving the original *untouched*.
** Caller:
If you decide to fix this error now, you can run ~Log.reset~ to invalidate this warning
and be notified of other places you're using this function.

[Message ID: [:consider_sprite!]]

