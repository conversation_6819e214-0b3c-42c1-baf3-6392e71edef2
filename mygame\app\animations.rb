class Animation
  attr_reader :done

  def initialize(loop: false, repeat: 1, &block)
    @steps = []
    @pc = 0
    @wait = 0
    @done = false
    @loop = loop
    @repeat = repeat
    @finished_repeats = 0
    @on_finish = []
    @dsl = AnimationDSL.new(@steps)
    @dsl.instance_eval(&block)
  end

  def on_finish(&block)
    @on_finish << block
  end

  def tick(args)
    return if @done

    if @wait > 0
      @wait -= 1
      return
    end

    if @pc >= @steps.length
      @finished_repeats += 1
      if @loop || @finished_repeats < @repeat
        @pc = 0
        return
      else
        @done = true
        @on_finish.each { |cb| cb.call(args) }
        return
      end
    end

    step = @steps[@pc]
    @pc += 1

    case step
    when Hash
      @wait = step[:wait] if step[:wait]
    when Proc
      step.call(args)
    when Array
      step.each { |s| s.tick(args) }
    end
  end
end

class AnimationDSL
  def initialize(steps)
    @steps = steps
  end

  def wait(ticks)
    @steps << { wait: ticks }
  end

  def set(prop, value)
    @steps << ->(args) { args.state.send("#{prop}=", value) }
  end

  def tween(prop, to:, duration:, easing: nil)
    from = nil
    easing ||= ->(t) { t }
    @steps << ->(args) do
      from ||= args.state.send(prop) || 0
      progress = (args.state.tick_count % duration).to_f / duration
      args.state.send("#{prop}=", from + (to - from) * easing.call(progress))
    end
    wait duration
  end

  def play_sound(sound)
    @steps << ->(args) { args.outputs.sounds << sound }
  end

  def spawn_effect(effect_name, x:, y:)
    @steps << ->(args) do
      args.state.effects ||= []
      args.state.effects << { name: effect_name, x: x, y: y, duration: 30 }
    end
  end

  def show_message(text, duration: 60)
    @steps << ->(args) do
      args.state.messages ||= []
      args.state.messages << { text: text, duration: duration }
    end
  end

  def call(&block)
    @steps << block
  end

  def parallel(&block)
    substeps = []
    dsl = AnimationDSL.new(substeps)
    dsl.instance_eval(&block)
    @steps << substeps
  end
end
