<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>DragonRuby Docs</title>
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
  <meta name="description" content="Description">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0">

  <!-- Themes (light + dark) -->
  <link rel="stylesheet" media="(prefers-color-scheme: dark)" href="/public/theme-simple-dark.css">
  <link rel="stylesheet" media="(prefers-color-scheme: light)" href="/public/theme-simple.css">

  <!-- Prism JS (light + dark) -->
  <link rel="stylesheet" media="(prefers-color-scheme: dark)" href="/public/prism-one-dark.min.css">
  <link rel="stylesheet" media="(prefers-color-scheme: light)" href="/public/prism-one-light.min.css">
  <style>
   :root {
     --code-font-size: 14px;
     --content-max-width: 61em;
   }
  </style>
</head>
<body>
  <div id="app">
    <div style="width: 600px; margin-left: auto; margin-right: auto">
      <div style="text-align: center">
        <p>Loading Docs...</p>
        <a href="static/docs.html" style="color: white; visited: white; text-decoration: underline;">Click Here for Static Docs</a>
      </div>
   <div>
  </div>
  <!-- Docsify config -->
  <script>
   window.$docsify = {
     name: 'DragonRuby Docs',
     logo: 'public/logo.png',
     homepage: 'index.md',
     search: {
       paths: 'auto',
       depth: 6
     },
     loadSidebar: true,
     subMaxLevel: 4,
     auto2top: true,
     relativePath: true,
     plugins: [
       function pageFooter(hook, _vm) {
         var footer = [
           '<hr/>',
           '<footer>',
           '<span>© 2012 - 2023, DragonRuby LLP.</span>',
           '</footer>',
         ].join('');

         hook.afterEach(function (html) {
           return html + footer;
         });
       }
     ]
   }
  </script>
  <!-- Docsify v4 -->
  <script src="/public/docsify.min.js"></script>
  <script src="/public/docsify-themeable.min.js"></script>

  <!-- Prism Ruby highlight -->
  <script src="/public/prism-ruby.min.js"></script>
  <script src="/public/prism-clike.min.js"></script>
  <script src="/public/prism-c.min.js"></script>
  <script src="/public/prism-glsl.min.js"></script>
  <script src="/public/prism-autoloader.min.js"></script>

  <!-- Other Plugins -->
  <script src="/public/zoom-image.min.js"></script>
  <script src="/public/search.min.js"></script>
  <script src="/public/docsify-copy-code.min.js"></script>

  <script src="/public/version.js"></script>
</body>
</html>
