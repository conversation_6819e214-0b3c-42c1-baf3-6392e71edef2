<html lang="en">
  <head>
    <meta charset="utf-8">
    <title>DragonRuby Game Toolkit Documentation</title>
    <script type="text/javascript">
/*
  Highlight.js 10.0.3 (a4b1bd2d)
  License: BSD-3-Clause
  Copyright (c) 2006-2020, <PERSON>
*/
var hljs=function(){"use strict";function e(n){Object.freeze(n);var t="function"==typeof n;return Object.getOwnPropertyNames(n).forEach((function(r){!n.hasOwnProperty(r)||null===n[r]||"object"!=typeof n[r]&&"function"!=typeof n[r]||t&&("caller"===r||"callee"===r||"arguments"===r)||Object.isFrozen(n[r])||e(n[r])})),n}function n(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}function t(e){var n,t={},r=Array.prototype.slice.call(arguments,1);for(n in e)t[n]=e[n];return r.forEach((function(e){for(n in e)t[n]=e[n]})),t}function r(e){return e.nodeName.toLowerCase()}var a=Object.freeze({__proto__:null,escapeHTML:n,inherit:t,nodeStream:function(e){var n=[];return function e(t,a){for(var i=t.firstChild;i;i=i.nextSibling)3===i.nodeType?a+=i.nodeValue.length:1===i.nodeType&&(n.push({event:"start",offset:a,node:i}),a=e(i,a),r(i).match(/br|hr|img|input/)||n.push({event:"stop",offset:a,node:i}));return a}(e,0),n},mergeStreams:function(e,t,a){var i=0,s="",o=[];function l(){return e.length&&t.length?e[0].offset!==t[0].offset?e[0].offset<t[0].offset?e:t:"start"===t[0].event?e:t:e.length?e:t}function c(e){s+="<"+r(e)+[].map.call(e.attributes,(function(e){return" "+e.nodeName+'="'+n(e.value).replace(/"/g,"&quot;")+'"'})).join("")+">"}function u(e){s+="</"+r(e)+">"}function d(e){("start"===e.event?c:u)(e.node)}for(;e.length||t.length;){var g=l();if(s+=n(a.substring(i,g[0].offset)),i=g[0].offset,g===e){o.reverse().forEach(u);do{d(g.splice(0,1)[0]),g=l()}while(g===e&&g.length&&g[0].offset===i);o.reverse().forEach(c)}else"start"===g[0].event?o.push(g[0].node):o.pop(),d(g.splice(0,1)[0])}return s+n(a.substr(i))}});const i="</span>",s=e=>!!e.kind;class o{constructor(e,n){this.buffer="",this.classPrefix=n.classPrefix,e.walk(this)}addText(e){this.buffer+=n(e)}openNode(e){if(!s(e))return;let n=e.kind;e.sublanguage||(n=`${this.classPrefix}${n}`),this.span(n)}closeNode(e){s(e)&&(this.buffer+=i)}span(e){this.buffer+=`<span class="${e}">`}value(){return this.buffer}}class l{constructor(){this.rootNode={children:[]},this.stack=[this.rootNode]}get top(){return this.stack[this.stack.length-1]}get root(){return this.rootNode}add(e){this.top.children.push(e)}openNode(e){let n={kind:e,children:[]};this.add(n),this.stack.push(n)}closeNode(){if(this.stack.length>1)return this.stack.pop()}closeAllNodes(){for(;this.closeNode(););}toJSON(){return JSON.stringify(this.rootNode,null,4)}walk(e){return this.constructor._walk(e,this.rootNode)}static _walk(e,n){return"string"==typeof n?e.addText(n):n.children&&(e.openNode(n),n.children.forEach(n=>this._walk(e,n)),e.closeNode(n)),e}static _collapse(e){e.children&&(e.children.every(e=>"string"==typeof e)?(e.text=e.children.join(""),delete e.children):e.children.forEach(e=>{"string"!=typeof e&&l._collapse(e)}))}}class c extends l{constructor(e){super(),this.options=e}addKeyword(e,n){""!==e&&(this.openNode(n),this.addText(e),this.closeNode())}addText(e){""!==e&&this.add(e)}addSublanguage(e,n){let t=e.root;t.kind=n,t.sublanguage=!0,this.add(t)}toHTML(){return new o(this,this.options).value()}finalize(){}}function u(e){return e&&e.source||e}const d="(-?)(\\b0[xX][a-fA-F0-9]+|(\\b\\d+(\\.\\d*)?|\\.\\d+)([eE][-+]?\\d+)?)",g={begin:"\\\\[\\s\\S]",relevance:0},h={className:"string",begin:"'",end:"'",illegal:"\\n",contains:[g]},f={className:"string",begin:'"',end:'"',illegal:"\\n",contains:[g]},p={begin:/\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\b/},m=function(e,n,r){var a=t({className:"comment",begin:e,end:n,contains:[]},r||{});return a.contains.push(p),a.contains.push({className:"doctag",begin:"(?:TODO|FIXME|NOTE|BUG|XXX):",relevance:0}),a},b=m("//","$"),v=m("/\\*","\\*/"),x=m("#","$");var _=Object.freeze({__proto__:null,IDENT_RE:"[a-zA-Z]\\w*",UNDERSCORE_IDENT_RE:"[a-zA-Z_]\\w*",NUMBER_RE:"\\b\\d+(\\.\\d+)?",C_NUMBER_RE:d,BINARY_NUMBER_RE:"\\b(0b[01]+)",RE_STARTERS_RE:"!|!=|!==|%|%=|&|&&|&=|\\*|\\*=|\\+|\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\?|\\[|\\{|\\(|\\^|\\^=|\\||\\|=|\\|\\||~",BACKSLASH_ESCAPE:g,APOS_STRING_MODE:h,QUOTE_STRING_MODE:f,PHRASAL_WORDS_MODE:p,COMMENT:m,C_LINE_COMMENT_MODE:b,C_BLOCK_COMMENT_MODE:v,HASH_COMMENT_MODE:x,NUMBER_MODE:{className:"number",begin:"\\b\\d+(\\.\\d+)?",relevance:0},C_NUMBER_MODE:{className:"number",begin:d,relevance:0},BINARY_NUMBER_MODE:{className:"number",begin:"\\b(0b[01]+)",relevance:0},CSS_NUMBER_MODE:{className:"number",begin:"\\b\\d+(\\.\\d+)?(%|em|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc|px|deg|grad|rad|turn|s|ms|Hz|kHz|dpi|dpcm|dppx)?",relevance:0},REGEXP_MODE:{begin:/(?=\/[^\/\n]*\/)/,contains:[{className:"regexp",begin:/\//,end:/\/[gimuy]*/,illegal:/\n/,contains:[g,{begin:/\[/,end:/\]/,relevance:0,contains:[g]}]}]},TITLE_MODE:{className:"title",begin:"[a-zA-Z]\\w*",relevance:0},UNDERSCORE_TITLE_MODE:{className:"title",begin:"[a-zA-Z_]\\w*",relevance:0},METHOD_GUARD:{begin:"\\.\\s*[a-zA-Z_]\\w*",relevance:0}}),E="of and for in not or if then".split(" ");function R(e,n){return n?+n:(t=e,E.includes(t.toLowerCase())?0:1);var t}const N=n,w=t,{nodeStream:y,mergeStreams:O}=a;return function(n){var r=[],a={},i={},s=[],o=!0,l=/((^(<[^>]+>|\t|)+|(?:\n)))/gm,d="Could not find the language '{}', did you forget to load/include a language module?",g={noHighlightRe:/^(no-?highlight)$/i,languageDetectRe:/\blang(?:uage)?-([\w-]+)\b/i,classPrefix:"hljs-",tabReplace:null,useBR:!1,languages:void 0,__emitter:c};function h(e){return g.noHighlightRe.test(e)}function f(e,n,t,r){var a={code:n,language:e};T("before:highlight",a);var i=a.result?a.result:p(a.language,a.code,t,r);return i.code=a.code,T("after:highlight",i),i}function p(e,n,r,i){var s=n;function l(e,n){var t=v.case_insensitive?n[0].toLowerCase():n[0];return e.keywords.hasOwnProperty(t)&&e.keywords[t]}function c(){null!=_.subLanguage?function(){if(""!==k){var e="string"==typeof _.subLanguage;if(!e||a[_.subLanguage]){var n=e?p(_.subLanguage,k,!0,E[_.subLanguage]):m(k,_.subLanguage.length?_.subLanguage:void 0);_.relevance>0&&(T+=n.relevance),e&&(E[_.subLanguage]=n.top),w.addSublanguage(n.emitter,n.language)}else w.addText(k)}}():function(){var e,n,t,r;if(_.keywords){for(n=0,_.lexemesRe.lastIndex=0,t=_.lexemesRe.exec(k),r="";t;){r+=k.substring(n,t.index);var a=null;(e=l(_,t))?(w.addText(r),r="",T+=e[1],a=e[0],w.addKeyword(t[0],a)):r+=t[0],n=_.lexemesRe.lastIndex,t=_.lexemesRe.exec(k)}r+=k.substr(n),w.addText(r)}else w.addText(k)}(),k=""}function h(e){e.className&&w.openNode(e.className),_=Object.create(e,{parent:{value:_}})}var f={};function b(n,t){var a,i=t&&t[0];if(k+=n,null==i)return c(),0;if("begin"==f.type&&"end"==t.type&&f.index==t.index&&""===i){if(k+=s.slice(t.index,t.index+1),!o)throw(a=Error("0 width match regex")).languageName=e,a.badRule=f.rule,a;return 1}if(f=t,"begin"===t.type)return function(e){var n=e[0],t=e.rule;return t.__onBegin&&(t.__onBegin(e)||{}).ignoreMatch?function(e){return 0===_.matcher.regexIndex?(k+=e[0],1):(B=!0,0)}(n):(t&&t.endSameAsBegin&&(t.endRe=RegExp(n.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&"),"m")),t.skip?k+=n:(t.excludeBegin&&(k+=n),c(),t.returnBegin||t.excludeBegin||(k=n)),h(t),t.returnBegin?0:n.length)}(t);if("illegal"===t.type&&!r)throw(a=Error('Illegal lexeme "'+i+'" for mode "'+(_.className||"<unnamed>")+'"')).mode=_,a;if("end"===t.type){var l=function(e){var n=e[0],t=s.substr(e.index),r=function e(n,t){if(function(e,n){var t=e&&e.exec(n);return t&&0===t.index}(n.endRe,t)){for(;n.endsParent&&n.parent;)n=n.parent;return n}if(n.endsWithParent)return e(n.parent,t)}(_,t);if(r){var a=_;a.skip?k+=n:(a.returnEnd||a.excludeEnd||(k+=n),c(),a.excludeEnd&&(k=n));do{_.className&&w.closeNode(),_.skip||_.subLanguage||(T+=_.relevance),_=_.parent}while(_!==r.parent);return r.starts&&(r.endSameAsBegin&&(r.starts.endRe=r.endRe),h(r.starts)),a.returnEnd?0:n.length}}(t);if(null!=l)return l}if("illegal"===t.type&&""===i)return 1;if(A>1e5&&A>3*t.index)throw Error("potential infinite loop, way more iterations than matches");return k+=i,i.length}var v=M(e);if(!v)throw console.error(d.replace("{}",e)),Error('Unknown language: "'+e+'"');!function(e){function n(n,t){return RegExp(u(n),"m"+(e.case_insensitive?"i":"")+(t?"g":""))}class r{constructor(){this.matchIndexes={},this.regexes=[],this.matchAt=1,this.position=0}addRule(e,n){n.position=this.position++,this.matchIndexes[this.matchAt]=n,this.regexes.push([n,e]),this.matchAt+=function(e){return RegExp(e.toString()+"|").exec("").length-1}(e)+1}compile(){0===this.regexes.length&&(this.exec=()=>null);let e=this.regexes.map(e=>e[1]);this.matcherRe=n(function(e,n){for(var t=/\[(?:[^\\\]]|\\.)*\]|\(\??|\\([1-9][0-9]*)|\\./,r=0,a="",i=0;i<e.length;i++){var s=r+=1,o=u(e[i]);for(i>0&&(a+="|"),a+="(";o.length>0;){var l=t.exec(o);if(null==l){a+=o;break}a+=o.substring(0,l.index),o=o.substring(l.index+l[0].length),"\\"==l[0][0]&&l[1]?a+="\\"+(+l[1]+s):(a+=l[0],"("==l[0]&&r++)}a+=")"}return a}(e),!0),this.lastIndex=0}exec(e){this.matcherRe.lastIndex=this.lastIndex;let n=this.matcherRe.exec(e);if(!n)return null;let t=n.findIndex((e,n)=>n>0&&null!=e),r=this.matchIndexes[t];return Object.assign(n,r)}}class a{constructor(){this.rules=[],this.multiRegexes=[],this.count=0,this.lastIndex=0,this.regexIndex=0}getMatcher(e){if(this.multiRegexes[e])return this.multiRegexes[e];let n=new r;return this.rules.slice(e).forEach(([e,t])=>n.addRule(e,t)),n.compile(),this.multiRegexes[e]=n,n}considerAll(){this.regexIndex=0}addRule(e,n){this.rules.push([e,n]),"begin"===n.type&&this.count++}exec(e){let n=this.getMatcher(this.regexIndex);n.lastIndex=this.lastIndex;let t=n.exec(e);return t&&(this.regexIndex+=t.position+1,this.regexIndex===this.count&&(this.regexIndex=0)),t}}function i(e){let n=e.input[e.index-1],t=e.input[e.index+e[0].length];if("."===n||"."===t)return{ignoreMatch:!0}}if(e.contains&&e.contains.includes("self"))throw Error("ERR: contains `self` is not supported at the top-level of a language.  See documentation.");!function r(s,o){s.compiled||(s.compiled=!0,s.__onBegin=null,s.keywords=s.keywords||s.beginKeywords,s.keywords&&(s.keywords=function(e,n){var t={};return"string"==typeof e?r("keyword",e):Object.keys(e).forEach((function(n){r(n,e[n])})),t;function r(e,r){n&&(r=r.toLowerCase()),r.split(" ").forEach((function(n){var r=n.split("|");t[r[0]]=[e,R(r[0],r[1])]}))}}(s.keywords,e.case_insensitive)),s.lexemesRe=n(s.lexemes||/\w+/,!0),o&&(s.beginKeywords&&(s.begin="\\b("+s.beginKeywords.split(" ").join("|")+")(?=\\b|\\s)",s.__onBegin=i),s.begin||(s.begin=/\B|\b/),s.beginRe=n(s.begin),s.endSameAsBegin&&(s.end=s.begin),s.end||s.endsWithParent||(s.end=/\B|\b/),s.end&&(s.endRe=n(s.end)),s.terminator_end=u(s.end)||"",s.endsWithParent&&o.terminator_end&&(s.terminator_end+=(s.end?"|":"")+o.terminator_end)),s.illegal&&(s.illegalRe=n(s.illegal)),null==s.relevance&&(s.relevance=1),s.contains||(s.contains=[]),s.contains=[].concat(...s.contains.map((function(e){return function(e){return e.variants&&!e.cached_variants&&(e.cached_variants=e.variants.map((function(n){return t(e,{variants:null},n)}))),e.cached_variants?e.cached_variants:function e(n){return!!n&&(n.endsWithParent||e(n.starts))}(e)?t(e,{starts:e.starts?t(e.starts):null}):Object.isFrozen(e)?t(e):e}("self"===e?s:e)}))),s.contains.forEach((function(e){r(e,s)})),s.starts&&r(s.starts,o),s.matcher=function(e){let n=new a;return e.contains.forEach(e=>n.addRule(e.begin,{rule:e,type:"begin"})),e.terminator_end&&n.addRule(e.terminator_end,{type:"end"}),e.illegal&&n.addRule(e.illegal,{type:"illegal"}),n}(s))}(e)}(v);var x,_=i||v,E={},w=new g.__emitter(g);!function(){for(var e=[],n=_;n!==v;n=n.parent)n.className&&e.unshift(n.className);e.forEach(e=>w.openNode(e))}();var y,O,k="",T=0,L=0,A=0,B=!1;try{for(_.matcher.considerAll();A++,B?B=!1:(_.matcher.lastIndex=L,_.matcher.considerAll()),y=_.matcher.exec(s);)O=b(s.substring(L,y.index),y),L=y.index+O;return b(s.substr(L)),w.closeAllNodes(),w.finalize(),x=w.toHTML(),{relevance:T,value:x,language:e,illegal:!1,emitter:w,top:_}}catch(n){if(n.message&&n.message.includes("Illegal"))return{illegal:!0,illegalBy:{msg:n.message,context:s.slice(L-100,L+100),mode:n.mode},sofar:x,relevance:0,value:N(s),emitter:w};if(o)return{relevance:0,value:N(s),emitter:w,language:e,top:_,errorRaised:n};throw n}}function m(e,n){n=n||g.languages||Object.keys(a);var t=function(e){const n={relevance:0,emitter:new g.__emitter(g),value:N(e),illegal:!1,top:E};return n.emitter.addText(e),n}(e),r=t;return n.filter(M).filter(k).forEach((function(n){var a=p(n,e,!1);a.language=n,a.relevance>r.relevance&&(r=a),a.relevance>t.relevance&&(r=t,t=a)})),r.language&&(t.second_best=r),t}function b(e){return g.tabReplace||g.useBR?e.replace(l,(function(e,n){return g.useBR&&"\n"===e?"<br>":g.tabReplace?n.replace(/\t/g,g.tabReplace):""})):e}function v(e){var n,t,r,a,s,o=function(e){var n,t=e.className+" ";if(t+=e.parentNode?e.parentNode.className:"",n=g.languageDetectRe.exec(t)){var r=M(n[1]);return r||(console.warn(d.replace("{}",n[1])),console.warn("Falling back to no-highlight mode for this block.",e)),r?n[1]:"no-highlight"}return t.split(/\s+/).find(e=>h(e)||M(e))}(e);h(o)||(T("before:highlightBlock",{block:e,language:o}),g.useBR?(n=document.createElement("div")).innerHTML=e.innerHTML.replace(/\n/g,"").replace(/<br[ \/]*>/g,"\n"):n=e,s=n.textContent,r=o?f(o,s,!0):m(s),(t=y(n)).length&&((a=document.createElement("div")).innerHTML=r.value,r.value=O(t,y(a),s)),r.value=b(r.value),T("after:highlightBlock",{block:e,result:r}),e.innerHTML=r.value,e.className=function(e,n,t){var r=n?i[n]:t,a=[e.trim()];return e.match(/\bhljs\b/)||a.push("hljs"),e.includes(r)||a.push(r),a.join(" ").trim()}(e.className,o,r.language),e.result={language:r.language,re:r.relevance},r.second_best&&(e.second_best={language:r.second_best.language,re:r.second_best.relevance}))}function x(){if(!x.called){x.called=!0;var e=document.querySelectorAll("pre code");r.forEach.call(e,v)}}const E={disableAutodetect:!0,name:"Plain text"};function M(e){return e=(e||"").toLowerCase(),a[e]||a[i[e]]}function k(e){var n=M(e);return n&&!n.disableAutodetect}function T(e,n){var t=e;s.forEach((function(e){e[t]&&e[t](n)}))}Object.assign(n,{highlight:f,highlightAuto:m,fixMarkup:b,highlightBlock:v,configure:function(e){g=w(g,e)},initHighlighting:x,initHighlightingOnLoad:function(){window.addEventListener("DOMContentLoaded",x,!1)},registerLanguage:function(e,t){var r;try{r=t(n)}catch(n){if(console.error("Language definition for '{}' could not be registered.".replace("{}",e)),!o)throw n;console.error(n),r=E}r.name||(r.name=e),a[e]=r,r.rawDefinition=t.bind(null,n),r.aliases&&r.aliases.forEach((function(n){i[n]=e}))},listLanguages:function(){return Object.keys(a)},getLanguage:M,requireLanguage:function(e){var n=M(e);if(n)return n;throw Error("The '{}' language is required, but not loaded.".replace("{}",e))},autoDetection:k,inherit:w,addPlugin:function(e,n){s.push(e)}}),n.debugMode=function(){o=!1},n.safeMode=function(){o=!0},n.versionString="10.0.3";for(const n in _)"object"==typeof _[n]&&e(_[n]);return Object.assign(n,_),n}({})}();"object"==typeof exports&&"undefined"!=typeof module&&(module.exports=hljs);
hljs.registerLanguage("apache",function(){"use strict";return function(e){var n={className:"number",begin:"\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}(:\\d{1,5})?"};return{name:"Apache config",aliases:["apacheconf"],case_insensitive:!0,contains:[e.HASH_COMMENT_MODE,{className:"section",begin:"</?",end:">",contains:[n,{className:"number",begin:":\\d{1,5}"},e.inherit(e.QUOTE_STRING_MODE,{relevance:0})]},{className:"attribute",begin:/\w+/,relevance:0,keywords:{nomarkup:"order deny allow setenv rewriterule rewriteengine rewritecond documentroot sethandler errordocument loadmodule options header listen serverroot servername"},starts:{end:/$/,relevance:0,keywords:{literal:"on off all deny allow"},contains:[{className:"meta",begin:"\\s\\[",end:"\\]$"},{className:"variable",begin:"[\\$%]\\{",end:"\\}",contains:["self",{className:"number",begin:"[\\$%]\\d+"}]},n,{className:"number",begin:"\\d+"},e.QUOTE_STRING_MODE]}}],illegal:/\S/}}}());
hljs.registerLanguage("bash",function(){"use strict";return function(e){const s={};Object.assign(s,{className:"variable",variants:[{begin:/\$[\w\d#@][\w\d_]*/},{begin:/\$\{/,end:/\}/,contains:[{begin:/:-/,contains:[s]}]}]});const n={className:"subst",begin:/\$\(/,end:/\)/,contains:[e.BACKSLASH_ESCAPE]},t={className:"string",begin:/"/,end:/"/,contains:[e.BACKSLASH_ESCAPE,s,n]};n.contains.push(t);const a={begin:/\$\(\(/,end:/\)\)/,contains:[{begin:/\d+#[0-9a-f]+/,className:"number"},e.NUMBER_MODE,s]};return{name:"Bash",aliases:["sh","zsh"],lexemes:/\b-?[a-z\._]+\b/,keywords:{keyword:"if then else elif fi for while in do done case esac function",literal:"true false",built_in:"break cd continue eval exec exit export getopts hash pwd readonly return shift test times trap umask unset alias bind builtin caller command declare echo enable help let local logout mapfile printf read readarray source type typeset ulimit unalias set shopt autoload bg bindkey bye cap chdir clone comparguments compcall compctl compdescribe compfiles compgroups compquote comptags comptry compvalues dirs disable disown echotc echoti emulate fc fg float functions getcap getln history integer jobs kill limit log noglob popd print pushd pushln rehash sched setcap setopt stat suspend ttyctl unfunction unhash unlimit unsetopt vared wait whence where which zcompile zformat zftp zle zmodload zparseopts zprof zpty zregexparse zsocket zstyle ztcp",_:"-ne -eq -lt -gt -f -d -e -s -l -a"},contains:[{className:"meta",begin:/^#![^\n]+sh\s*$/,relevance:10},{className:"function",begin:/\w[\w\d_]*\s*\(\s*\)\s*\{/,returnBegin:!0,contains:[e.inherit(e.TITLE_MODE,{begin:/\w[\w\d_]*/})],relevance:0},a,e.HASH_COMMENT_MODE,t,{className:"",begin:/\\"/},{className:"string",begin:/'/,end:/'/},s]}}}());
hljs.registerLanguage("c-like",function(){"use strict";return function(e){function t(e){return"(?:"+e+")?"}var n="(decltype\\(auto\\)|"+t("[a-zA-Z_]\\w*::")+"[a-zA-Z_]\\w*"+t("<.*?>")+")",r={className:"keyword",begin:"\\b[a-z\\d_]*_t\\b"},a={className:"string",variants:[{begin:'(u8?|U|L)?"',end:'"',illegal:"\\n",contains:[e.BACKSLASH_ESCAPE]},{begin:"(u8?|U|L)?'(\\\\(x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4,8}|[0-7]{3}|\\S)|.)",end:"'",illegal:"."},{begin:/(?:u8?|U|L)?R"([^()\\ ]{0,16})\((?:.|\n)*?\)\1"/}]},s={className:"number",variants:[{begin:"\\b(0b[01']+)"},{begin:"(-?)\\b([\\d']+(\\.[\\d']*)?|\\.[\\d']+)(u|U|l|L|ul|UL|f|F|b|B)"},{begin:"(-?)(\\b0[xX][a-fA-F0-9']+|(\\b[\\d']+(\\.[\\d']*)?|\\.[\\d']+)([eE][-+]?[\\d']+)?)"}],relevance:0},i={className:"meta",begin:/#\s*[a-z]+\b/,end:/$/,keywords:{"meta-keyword":"if else elif endif define undef warning error line pragma _Pragma ifdef ifndef include"},contains:[{begin:/\\\n/,relevance:0},e.inherit(a,{className:"meta-string"}),{className:"meta-string",begin:/<.*?>/,end:/$/,illegal:"\\n"},e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE]},c={className:"title",begin:t("[a-zA-Z_]\\w*::")+e.IDENT_RE,relevance:0},o=t("[a-zA-Z_]\\w*::")+e.IDENT_RE+"\\s*\\(",l={keyword:"int float while private char char8_t char16_t char32_t catch import module export virtual operator sizeof dynamic_cast|10 typedef const_cast|10 const for static_cast|10 union namespace unsigned long volatile static protected bool template mutable if public friend do goto auto void enum else break extern using asm case typeid wchar_t short reinterpret_cast|10 default double register explicit signed typename try this switch continue inline delete alignas alignof constexpr consteval constinit decltype concept co_await co_return co_yield requires noexcept static_assert thread_local restrict final override atomic_bool atomic_char atomic_schar atomic_uchar atomic_short atomic_ushort atomic_int atomic_uint atomic_long atomic_ulong atomic_llong atomic_ullong new throw return and and_eq bitand bitor compl not not_eq or or_eq xor xor_eq",built_in:"std string wstring cin cout cerr clog stdin stdout stderr stringstream istringstream ostringstream auto_ptr deque list queue stack vector map set bitset multiset multimap unordered_set unordered_map unordered_multiset unordered_multimap array shared_ptr abort terminate abs acos asin atan2 atan calloc ceil cosh cos exit exp fabs floor fmod fprintf fputs free frexp fscanf future isalnum isalpha iscntrl isdigit isgraph islower isprint ispunct isspace isupper isxdigit tolower toupper labs ldexp log10 log malloc realloc memchr memcmp memcpy memset modf pow printf putchar puts scanf sinh sin snprintf sprintf sqrt sscanf strcat strchr strcmp strcpy strcspn strlen strncat strncmp strncpy strpbrk strrchr strspn strstr tanh tan vfprintf vprintf vsprintf endl initializer_list unique_ptr _Bool complex _Complex imaginary _Imaginary",literal:"true false nullptr NULL"},d=[r,e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE,s,a],_={variants:[{begin:/=/,end:/;/},{begin:/\(/,end:/\)/},{beginKeywords:"new throw return else",end:/;/}],keywords:l,contains:d.concat([{begin:/\(/,end:/\)/,keywords:l,contains:d.concat(["self"]),relevance:0}]),relevance:0},u={className:"function",begin:"("+n+"[\\*&\\s]+)+"+o,returnBegin:!0,end:/[{;=]/,excludeEnd:!0,keywords:l,illegal:/[^\w\s\*&:<>]/,contains:[{begin:"decltype\\(auto\\)",keywords:l,relevance:0},{begin:o,returnBegin:!0,contains:[c],relevance:0},{className:"params",begin:/\(/,end:/\)/,keywords:l,relevance:0,contains:[e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE,a,s,r,{begin:/\(/,end:/\)/,keywords:l,relevance:0,contains:["self",e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE,a,s,r]}]},r,e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE,i]};return{aliases:["c","cc","h","c++","h++","hpp","hh","hxx","cxx"],keywords:l,disableAutodetect:!0,illegal:"</",contains:[].concat(_,u,d,[i,{begin:"\\b(deque|list|queue|stack|vector|map|set|bitset|multiset|multimap|unordered_map|unordered_set|unordered_multiset|unordered_multimap|array)\\s*<",end:">",keywords:l,contains:["self",r]},{begin:e.IDENT_RE+"::",keywords:l},{className:"class",beginKeywords:"class struct",end:/[{;:]/,contains:[{begin:/</,end:/>/,contains:["self"]},e.TITLE_MODE]}]),exports:{preprocessor:i,strings:a,keywords:l}}}}());
hljs.registerLanguage("c",function(){"use strict";return function(e){var n=e.getLanguage("c-like").rawDefinition();return n.name="C",n.aliases=["c","h"],n}}());
hljs.registerLanguage("coffeescript",function(){"use strict";return function(e){var n={keyword:"in if for while finally new do return else break catch instanceof throw try this switch continue typeof delete debugger super yield import export from as default await then unless until loop of by when and or is isnt not",literal:"true false null undefined yes no on off",built_in:"npm require console print module global window document"},i="[A-Za-z$_][0-9A-Za-z$_]*",s={className:"subst",begin:/#\{/,end:/}/,keywords:n},a=[e.BINARY_NUMBER_MODE,e.inherit(e.C_NUMBER_MODE,{starts:{end:"(\\s*/)?",relevance:0}}),{className:"string",variants:[{begin:/'''/,end:/'''/,contains:[e.BACKSLASH_ESCAPE]},{begin:/'/,end:/'/,contains:[e.BACKSLASH_ESCAPE]},{begin:/"""/,end:/"""/,contains:[e.BACKSLASH_ESCAPE,s]},{begin:/"/,end:/"/,contains:[e.BACKSLASH_ESCAPE,s]}]},{className:"regexp",variants:[{begin:"///",end:"///",contains:[s,e.HASH_COMMENT_MODE]},{begin:"//[gim]{0,3}(?=\\W)",relevance:0},{begin:/\/(?![ *]).*?(?![\\]).\/[gim]{0,3}(?=\W)/}]},{begin:"@"+i},{subLanguage:"javascript",excludeBegin:!0,excludeEnd:!0,variants:[{begin:"```",end:"```"},{begin:"`",end:"`"}]}];s.contains=a;var t=e.inherit(e.TITLE_MODE,{begin:i}),r={className:"params",begin:"\\([^\\(]",returnBegin:!0,contains:[{begin:/\(/,end:/\)/,keywords:n,contains:["self"].concat(a)}]};return{name:"CoffeeScript",aliases:["coffee","cson","iced"],keywords:n,illegal:/\/\*/,contains:a.concat([e.COMMENT("###","###"),e.HASH_COMMENT_MODE,{className:"function",begin:"^\\s*"+i+"\\s*=\\s*(\\(.*\\))?\\s*\\B[-=]>",end:"[-=]>",returnBegin:!0,contains:[t,r]},{begin:/[:\(,=]\s*/,relevance:0,contains:[{className:"function",begin:"(\\(.*\\))?\\s*\\B[-=]>",end:"[-=]>",returnBegin:!0,contains:[r]}]},{className:"class",beginKeywords:"class",end:"$",illegal:/[:="\[\]]/,contains:[{beginKeywords:"extends",endsWithParent:!0,illegal:/[:="\[\]]/,contains:[t]},t]},{begin:i+":",end:":",returnBegin:!0,returnEnd:!0,relevance:0}])}}}());
hljs.registerLanguage("cpp",function(){"use strict";return function(e){var t=e.getLanguage("c-like").rawDefinition();return t.disableAutodetect=!1,t.name="C++",t.aliases=["cc","c++","h++","hpp","hh","hxx","cxx"],t}}());
hljs.registerLanguage("csharp",function(){"use strict";return function(e){var n={keyword:"abstract as base bool break byte case catch char checked const continue decimal default delegate do double enum event explicit extern finally fixed float for foreach goto if implicit in int interface internal is lock long object operator out override params private protected public readonly ref sbyte sealed short sizeof stackalloc static string struct switch this try typeof uint ulong unchecked unsafe ushort using virtual void volatile while add alias ascending async await by descending dynamic equals from get global group into join let nameof on orderby partial remove select set value var when where yield",literal:"null false true"},i=e.inherit(e.TITLE_MODE,{begin:"[a-zA-Z](\\.?\\w)*"}),a={className:"number",variants:[{begin:"\\b(0b[01']+)"},{begin:"(-?)\\b([\\d']+(\\.[\\d']*)?|\\.[\\d']+)(u|U|l|L|ul|UL|f|F|b|B)"},{begin:"(-?)(\\b0[xX][a-fA-F0-9']+|(\\b[\\d']+(\\.[\\d']*)?|\\.[\\d']+)([eE][-+]?[\\d']+)?)"}],relevance:0},s={className:"string",begin:'@"',end:'"',contains:[{begin:'""'}]},t=e.inherit(s,{illegal:/\n/}),l={className:"subst",begin:"{",end:"}",keywords:n},r=e.inherit(l,{illegal:/\n/}),c={className:"string",begin:/\$"/,end:'"',illegal:/\n/,contains:[{begin:"{{"},{begin:"}}"},e.BACKSLASH_ESCAPE,r]},o={className:"string",begin:/\$@"/,end:'"',contains:[{begin:"{{"},{begin:"}}"},{begin:'""'},l]},g=e.inherit(o,{illegal:/\n/,contains:[{begin:"{{"},{begin:"}}"},{begin:'""'},r]});l.contains=[o,c,s,e.APOS_STRING_MODE,e.QUOTE_STRING_MODE,a,e.C_BLOCK_COMMENT_MODE],r.contains=[g,c,t,e.APOS_STRING_MODE,e.QUOTE_STRING_MODE,a,e.inherit(e.C_BLOCK_COMMENT_MODE,{illegal:/\n/})];var d={variants:[o,c,s,e.APOS_STRING_MODE,e.QUOTE_STRING_MODE]},E=e.IDENT_RE+"(<"+e.IDENT_RE+"(\\s*,\\s*"+e.IDENT_RE+")*>)?(\\[\\])?",_={begin:"@"+e.IDENT_RE,relevance:0};return{name:"C#",aliases:["cs","c#"],keywords:n,illegal:/::/,contains:[e.COMMENT("///","$",{returnBegin:!0,contains:[{className:"doctag",variants:[{begin:"///",relevance:0},{begin:"\x3c!--|--\x3e"},{begin:"</?",end:">"}]}]}),e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE,{className:"meta",begin:"#",end:"$",keywords:{"meta-keyword":"if else elif endif define undef warning error line region endregion pragma checksum"}},d,a,{beginKeywords:"class interface",end:/[{;=]/,illegal:/[^\s:,]/,contains:[{beginKeywords:"where class"},i,{begin:"<",end:">",keywords:"in out"},e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE]},{beginKeywords:"namespace",end:/[{;=]/,illegal:/[^\s:]/,contains:[i,e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE]},{className:"meta",begin:"^\\s*\\[",excludeBegin:!0,end:"\\]",excludeEnd:!0,contains:[{className:"meta-string",begin:/"/,end:/"/}]},{beginKeywords:"new return throw await else",relevance:0},{className:"function",begin:"("+E+"\\s+)+"+e.IDENT_RE+"\\s*\\(",returnBegin:!0,end:/\s*[{;=]/,excludeEnd:!0,keywords:n,contains:[{begin:e.IDENT_RE+"\\s*\\(",returnBegin:!0,contains:[e.TITLE_MODE],relevance:0},{className:"params",begin:/\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:n,relevance:0,contains:[d,a,e.C_BLOCK_COMMENT_MODE]},e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE]},_]}}}());
hljs.registerLanguage("css",function(){"use strict";return function(e){var n={begin:/(?:[A-Z\_\.\-]+|--[a-zA-Z0-9_-]+)\s*:/,returnBegin:!0,end:";",endsWithParent:!0,contains:[{className:"attribute",begin:/\S/,end:":",excludeEnd:!0,starts:{endsWithParent:!0,excludeEnd:!0,contains:[{begin:/[\w-]+\(/,returnBegin:!0,contains:[{className:"built_in",begin:/[\w-]+/},{begin:/\(/,end:/\)/,contains:[e.APOS_STRING_MODE,e.QUOTE_STRING_MODE,e.CSS_NUMBER_MODE]}]},e.CSS_NUMBER_MODE,e.QUOTE_STRING_MODE,e.APOS_STRING_MODE,e.C_BLOCK_COMMENT_MODE,{className:"number",begin:"#[0-9A-Fa-f]+"},{className:"meta",begin:"!important"}]}}]};return{name:"CSS",case_insensitive:!0,illegal:/[=\/|'\$]/,contains:[e.C_BLOCK_COMMENT_MODE,{className:"selector-id",begin:/#[A-Za-z0-9_-]+/},{className:"selector-class",begin:/\.[A-Za-z0-9_-]+/},{className:"selector-attr",begin:/\[/,end:/\]/,illegal:"$",contains:[e.APOS_STRING_MODE,e.QUOTE_STRING_MODE]},{className:"selector-pseudo",begin:/:(:)?[a-zA-Z0-9\_\-\+\(\)"'.]+/},{begin:"@(page|font-face)",lexemes:"@[a-z-]+",keywords:"@page @font-face"},{begin:"@",end:"[{;]",illegal:/:/,returnBegin:!0,contains:[{className:"keyword",begin:/@\-?\w[\w]*(\-\w+)*/},{begin:/\s/,endsWithParent:!0,excludeEnd:!0,relevance:0,keywords:"and or not only",contains:[{begin:/[a-z-]+:/,className:"attribute"},e.APOS_STRING_MODE,e.QUOTE_STRING_MODE,e.CSS_NUMBER_MODE]}]},{className:"selector-tag",begin:"[a-zA-Z-][a-zA-Z0-9_-]*",relevance:0},{begin:"{",end:"}",illegal:/\S/,contains:[e.C_BLOCK_COMMENT_MODE,n]}]}}}());
hljs.registerLanguage("diff",function(){"use strict";return function(e){return{name:"Diff",aliases:["patch"],contains:[{className:"meta",relevance:10,variants:[{begin:/^@@ +\-\d+,\d+ +\+\d+,\d+ +@@$/},{begin:/^\*\*\* +\d+,\d+ +\*\*\*\*$/},{begin:/^\-\-\- +\d+,\d+ +\-\-\-\-$/}]},{className:"comment",variants:[{begin:/Index: /,end:/$/},{begin:/={3,}/,end:/$/},{begin:/^\-{3}/,end:/$/},{begin:/^\*{3} /,end:/$/},{begin:/^\+{3}/,end:/$/},{begin:/^\*{15}$/}]},{className:"addition",begin:"^\\+",end:"$"},{className:"deletion",begin:"^\\-",end:"$"},{className:"addition",begin:"^\\!",end:"$"}]}}}());
hljs.registerLanguage("go",function(){"use strict";return function(e){var n={keyword:"break default func interface select case map struct chan else goto package switch const fallthrough if range type continue for import return var go defer bool byte complex64 complex128 float32 float64 int8 int16 int32 int64 string uint8 uint16 uint32 uint64 int uint uintptr rune",literal:"true false iota nil",built_in:"append cap close complex copy imag len make new panic print println real recover delete"};return{name:"Go",aliases:["golang"],keywords:n,illegal:"</",contains:[e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE,{className:"string",variants:[e.QUOTE_STRING_MODE,e.APOS_STRING_MODE,{begin:"`",end:"`"}]},{className:"number",variants:[{begin:e.C_NUMBER_RE+"[i]",relevance:1},e.C_NUMBER_MODE]},{begin:/:=/},{className:"function",beginKeywords:"func",end:"\\s*(\\{|$)",excludeEnd:!0,contains:[e.TITLE_MODE,{className:"params",begin:/\(/,end:/\)/,keywords:n,illegal:/["']/}]}]}}}());
hljs.registerLanguage("http",function(){"use strict";return function(e){var n="HTTP/[0-9\\.]+";return{name:"HTTP",aliases:["https"],illegal:"\\S",contains:[{begin:"^"+n,end:"$",contains:[{className:"number",begin:"\\b\\d{3}\\b"}]},{begin:"^[A-Z]+ (.*?) "+n+"$",returnBegin:!0,end:"$",contains:[{className:"string",begin:" ",end:" ",excludeBegin:!0,excludeEnd:!0},{begin:n},{className:"keyword",begin:"[A-Z]+"}]},{className:"attribute",begin:"^\\w",end:": ",excludeEnd:!0,illegal:"\\n|\\s|=",starts:{end:"$",relevance:0}},{begin:"\\n\\n",starts:{subLanguage:[],endsWithParent:!0}}]}}}());
hljs.registerLanguage("ini",function(){"use strict";return function(e){var n={className:"number",relevance:0,variants:[{begin:/([\+\-]+)?[\d]+_[\d_]+/},{begin:e.NUMBER_RE}]},a=e.COMMENT();a.variants=[{begin:/;/,end:/$/},{begin:/#/,end:/$/}];var s={className:"variable",variants:[{begin:/\$[\w\d"][\w\d_]*/},{begin:/\$\{(.*?)}/}]},i={className:"literal",begin:/\bon|off|true|false|yes|no\b/},t={className:"string",contains:[e.BACKSLASH_ESCAPE],variants:[{begin:"'''",end:"'''",relevance:10},{begin:'"""',end:'"""',relevance:10},{begin:'"',end:'"'},{begin:"'",end:"'"}]};return{name:"TOML, also INI",aliases:["toml"],case_insensitive:!0,illegal:/\S/,contains:[a,{className:"section",begin:/\[+/,end:/\]+/},{begin:/^[a-z0-9\[\]_\.-]+(?=\s*=\s*)/,className:"attr",starts:{end:/$/,contains:[a,{begin:/\[/,end:/\]/,contains:[a,i,s,t,n,"self"],relevance:0},i,s,t,n]}}]}}}());
hljs.registerLanguage("java",function(){"use strict";return function(e){var a="false synchronized int abstract float private char boolean var static null if const for true while long strictfp finally protected import native final void enum else break transient catch instanceof byte super volatile case assert short package default double public try this switch continue throws protected public private module requires exports do",n={className:"meta",begin:"@[À-ʸa-zA-Z_$][À-ʸa-zA-Z_$0-9]*",contains:[{begin:/\(/,end:/\)/,contains:["self"]}]};return{name:"Java",aliases:["jsp"],keywords:a,illegal:/<\/|#/,contains:[e.COMMENT("/\\*\\*","\\*/",{relevance:0,contains:[{begin:/\w+@/,relevance:0},{className:"doctag",begin:"@[A-Za-z]+"}]}),e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE,e.APOS_STRING_MODE,e.QUOTE_STRING_MODE,{className:"class",beginKeywords:"class interface",end:/[{;=]/,excludeEnd:!0,keywords:"class interface",illegal:/[:"\[\]]/,contains:[{beginKeywords:"extends implements"},e.UNDERSCORE_TITLE_MODE]},{beginKeywords:"new throw return else",relevance:0},{className:"function",begin:"([À-ʸa-zA-Z_$][À-ʸa-zA-Z_$0-9]*(<[À-ʸa-zA-Z_$][À-ʸa-zA-Z_$0-9]*(\\s*,\\s*[À-ʸa-zA-Z_$][À-ʸa-zA-Z_$0-9]*)*>)?\\s+)+"+e.UNDERSCORE_IDENT_RE+"\\s*\\(",returnBegin:!0,end:/[{;=]/,excludeEnd:!0,keywords:a,contains:[{begin:e.UNDERSCORE_IDENT_RE+"\\s*\\(",returnBegin:!0,relevance:0,contains:[e.UNDERSCORE_TITLE_MODE]},{className:"params",begin:/\(/,end:/\)/,keywords:a,relevance:0,contains:[n,e.APOS_STRING_MODE,e.QUOTE_STRING_MODE,e.C_NUMBER_MODE,e.C_BLOCK_COMMENT_MODE]},e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE]},{className:"number",begin:"\\b(0[bB]([01]+[01_]+[01]+|[01]+)|0[xX]([a-fA-F0-9]+[a-fA-F0-9_]+[a-fA-F0-9]+|[a-fA-F0-9]+)|(([\\d]+[\\d_]+[\\d]+|[\\d]+)(\\.([\\d]+[\\d_]+[\\d]+|[\\d]+))?|\\.([\\d]+[\\d_]+[\\d]+|[\\d]+))([eE][-+]?\\d+)?)[lLfF]?",relevance:0},n]}}}());
hljs.registerLanguage("javascript",function(){"use strict";return function(e){var n={begin:/<[A-Za-z0-9\\._:-]+/,end:/\/[A-Za-z0-9\\._:-]+>|\/>/},a="[A-Za-z$_][0-9A-Za-z$_]*",s={keyword:"in of if for while finally var new function do return void else break catch instanceof with throw case default try this switch continue typeof delete let yield const export super debugger as async await static import from as",literal:"true false null undefined NaN Infinity",built_in:"eval isFinite isNaN parseFloat parseInt decodeURI decodeURIComponent encodeURI encodeURIComponent escape unescape Object Function Boolean Error EvalError InternalError RangeError ReferenceError StopIteration SyntaxError TypeError URIError Number Math Date String RegExp Array Float32Array Float64Array Int16Array Int32Array Int8Array Uint16Array Uint32Array Uint8Array Uint8ClampedArray ArrayBuffer DataView JSON Intl arguments require module console window document Symbol Set Map WeakSet WeakMap Proxy Reflect Promise"},r={className:"number",variants:[{begin:"\\b(0[bB][01]+)n?"},{begin:"\\b(0[oO][0-7]+)n?"},{begin:e.C_NUMBER_RE+"n?"}],relevance:0},i={className:"subst",begin:"\\$\\{",end:"\\}",keywords:s,contains:[]},t={begin:"html`",end:"",starts:{end:"`",returnEnd:!1,contains:[e.BACKSLASH_ESCAPE,i],subLanguage:"xml"}},c={begin:"css`",end:"",starts:{end:"`",returnEnd:!1,contains:[e.BACKSLASH_ESCAPE,i],subLanguage:"css"}},o={className:"string",begin:"`",end:"`",contains:[e.BACKSLASH_ESCAPE,i]};i.contains=[e.APOS_STRING_MODE,e.QUOTE_STRING_MODE,t,c,o,r,e.REGEXP_MODE];var l=i.contains.concat([e.C_BLOCK_COMMENT_MODE,e.C_LINE_COMMENT_MODE]),d={className:"params",begin:/\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,contains:l};return{name:"JavaScript",aliases:["js","jsx","mjs","cjs"],keywords:s,contains:[{className:"meta",relevance:10,begin:/^\s*['"]use (strict|asm)['"]/},{className:"meta",begin:/^#!/,end:/$/},e.APOS_STRING_MODE,e.QUOTE_STRING_MODE,t,c,o,e.C_LINE_COMMENT_MODE,e.COMMENT("/\\*\\*","\\*/",{relevance:0,contains:[{className:"doctag",begin:"@[A-Za-z]+",contains:[{className:"type",begin:"\\{",end:"\\}",relevance:0},{className:"variable",begin:a+"(?=\\s*(-)|$)",endsParent:!0,relevance:0},{begin:/(?=[^\n])\s/,relevance:0}]}]}),e.C_BLOCK_COMMENT_MODE,r,{begin:/[{,\n]\s*/,relevance:0,contains:[{begin:a+"\\s*:",returnBegin:!0,relevance:0,contains:[{className:"attr",begin:a,relevance:0}]}]},{begin:"("+e.RE_STARTERS_RE+"|\\b(case|return|throw)\\b)\\s*",keywords:"return throw case",contains:[e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE,e.REGEXP_MODE,{className:"function",begin:"(\\(.*?\\)|"+a+")\\s*=>",returnBegin:!0,end:"\\s*=>",contains:[{className:"params",variants:[{begin:a},{begin:/\(\s*\)/},{begin:/\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:s,contains:l}]}]},{begin:/,/,relevance:0},{className:"",begin:/\s/,end:/\s*/,skip:!0},{variants:[{begin:"<>",end:"</>"},{begin:n.begin,end:n.end}],subLanguage:"xml",contains:[{begin:n.begin,end:n.end,skip:!0,contains:["self"]}]}],relevance:0},{className:"function",beginKeywords:"function",end:/\{/,excludeEnd:!0,contains:[e.inherit(e.TITLE_MODE,{begin:a}),d],illegal:/\[|%/},{begin:/\$[(.]/},e.METHOD_GUARD,{className:"class",beginKeywords:"class",end:/[{;=]/,excludeEnd:!0,illegal:/[:"\[\]]/,contains:[{beginKeywords:"extends"},e.UNDERSCORE_TITLE_MODE]},{beginKeywords:"constructor",end:/\{/,excludeEnd:!0},{begin:"(get|set)\\s+(?="+a+"\\()",end:/{/,keywords:"get set",contains:[e.inherit(e.TITLE_MODE,{begin:a}),{begin:/\(\)/},d]}],illegal:/#(?!!)/}}}());
hljs.registerLanguage("json",function(){"use strict";return function(n){var e={literal:"true false null"},i=[n.C_LINE_COMMENT_MODE,n.C_BLOCK_COMMENT_MODE],t=[n.QUOTE_STRING_MODE,n.C_NUMBER_MODE],a={end:",",endsWithParent:!0,excludeEnd:!0,contains:t,keywords:e},l={begin:"{",end:"}",contains:[{className:"attr",begin:/"/,end:/"/,contains:[n.BACKSLASH_ESCAPE],illegal:"\\n"},n.inherit(a,{begin:/:/})].concat(i),illegal:"\\S"},s={begin:"\\[",end:"\\]",contains:[n.inherit(a)],illegal:"\\S"};return t.push(l,s),i.forEach((function(n){t.push(n)})),{name:"JSON",contains:t,keywords:e,illegal:"\\S"}}}());
hljs.registerLanguage("kotlin",function(){"use strict";return function(e){var n={keyword:"abstract as val var vararg get set class object open private protected public noinline crossinline dynamic final enum if else do while for when throw try catch finally import package is in fun override companion reified inline lateinit init interface annotation data sealed internal infix operator out by constructor super tailrec where const inner suspend typealias external expect actual trait volatile transient native default",built_in:"Byte Short Char Int Long Boolean Float Double Void Unit Nothing",literal:"true false null"},a={className:"symbol",begin:e.UNDERSCORE_IDENT_RE+"@"},i={className:"subst",begin:"\\${",end:"}",contains:[e.C_NUMBER_MODE]},s={className:"variable",begin:"\\$"+e.UNDERSCORE_IDENT_RE},t={className:"string",variants:[{begin:'"""',end:'"""(?=[^"])',contains:[s,i]},{begin:"'",end:"'",illegal:/\n/,contains:[e.BACKSLASH_ESCAPE]},{begin:'"',end:'"',illegal:/\n/,contains:[e.BACKSLASH_ESCAPE,s,i]}]};i.contains.push(t);var r={className:"meta",begin:"@(?:file|property|field|get|set|receiver|param|setparam|delegate)\\s*:(?:\\s*"+e.UNDERSCORE_IDENT_RE+")?"},l={className:"meta",begin:"@"+e.UNDERSCORE_IDENT_RE,contains:[{begin:/\(/,end:/\)/,contains:[e.inherit(t,{className:"meta-string"})]}]},c=e.COMMENT("/\\*","\\*/",{contains:[e.C_BLOCK_COMMENT_MODE]}),o={variants:[{className:"type",begin:e.UNDERSCORE_IDENT_RE},{begin:/\(/,end:/\)/,contains:[]}]},d=o;return d.variants[1].contains=[o],o.variants[1].contains=[d],{name:"Kotlin",aliases:["kt"],keywords:n,contains:[e.COMMENT("/\\*\\*","\\*/",{relevance:0,contains:[{className:"doctag",begin:"@[A-Za-z]+"}]}),e.C_LINE_COMMENT_MODE,c,{className:"keyword",begin:/\b(break|continue|return|this)\b/,starts:{contains:[{className:"symbol",begin:/@\w+/}]}},a,r,l,{className:"function",beginKeywords:"fun",end:"[(]|$",returnBegin:!0,excludeEnd:!0,keywords:n,illegal:/fun\s+(<.*>)?[^\s\(]+(\s+[^\s\(]+)\s*=/,relevance:5,contains:[{begin:e.UNDERSCORE_IDENT_RE+"\\s*\\(",returnBegin:!0,relevance:0,contains:[e.UNDERSCORE_TITLE_MODE]},{className:"type",begin:/</,end:/>/,keywords:"reified",relevance:0},{className:"params",begin:/\(/,end:/\)/,endsParent:!0,keywords:n,relevance:0,contains:[{begin:/:/,end:/[=,\/]/,endsWithParent:!0,contains:[o,e.C_LINE_COMMENT_MODE,c],relevance:0},e.C_LINE_COMMENT_MODE,c,r,l,t,e.C_NUMBER_MODE]},c]},{className:"class",beginKeywords:"class interface trait",end:/[:\{(]|$/,excludeEnd:!0,illegal:"extends implements",contains:[{beginKeywords:"public protected internal private constructor"},e.UNDERSCORE_TITLE_MODE,{className:"type",begin:/</,end:/>/,excludeBegin:!0,excludeEnd:!0,relevance:0},{className:"type",begin:/[,:]\s*/,end:/[<\(,]|$/,excludeBegin:!0,returnEnd:!0},r,l]},t,{className:"meta",begin:"^#!/usr/bin/env",end:"$",illegal:"\n"},{className:"number",begin:"\\b(0[bB]([01]+[01_]+[01]+|[01]+)|0[xX]([a-fA-F0-9]+[a-fA-F0-9_]+[a-fA-F0-9]+|[a-fA-F0-9]+)|(([\\d]+[\\d_]+[\\d]+|[\\d]+)(\\.([\\d]+[\\d_]+[\\d]+|[\\d]+))?|\\.([\\d]+[\\d_]+[\\d]+|[\\d]+))([eE][-+]?\\d+)?)[lLfF]?",relevance:0}]}}}());
hljs.registerLanguage("less",function(){"use strict";return function(e){var n="([\\w-]+|@{[\\w-]+})",a=[],s=[],t=function(e){return{className:"string",begin:"~?"+e+".*?"+e}},r=function(e,n,a){return{className:e,begin:n,relevance:a}},i={begin:"\\(",end:"\\)",contains:s,relevance:0};s.push(e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE,t("'"),t('"'),e.CSS_NUMBER_MODE,{begin:"(url|data-uri)\\(",starts:{className:"string",end:"[\\)\\n]",excludeEnd:!0}},r("number","#[0-9A-Fa-f]+\\b"),i,r("variable","@@?[\\w-]+",10),r("variable","@{[\\w-]+}"),r("built_in","~?`[^`]*?`"),{className:"attribute",begin:"[\\w-]+\\s*:",end:":",returnBegin:!0,excludeEnd:!0},{className:"meta",begin:"!important"});var c=s.concat({begin:"{",end:"}",contains:a}),l={beginKeywords:"when",endsWithParent:!0,contains:[{beginKeywords:"and not"}].concat(s)},o={begin:n+"\\s*:",returnBegin:!0,end:"[;}]",relevance:0,contains:[{className:"attribute",begin:n,end:":",excludeEnd:!0,starts:{endsWithParent:!0,illegal:"[<=$]",relevance:0,contains:s}}]},g={className:"keyword",begin:"@(import|media|charset|font-face|(-[a-z]+-)?keyframes|supports|document|namespace|page|viewport|host)\\b",starts:{end:"[;{}]",returnEnd:!0,contains:s,relevance:0}},d={className:"variable",variants:[{begin:"@[\\w-]+\\s*:",relevance:15},{begin:"@[\\w-]+"}],starts:{end:"[;}]",returnEnd:!0,contains:c}},b={variants:[{begin:"[\\.#:&\\[>]",end:"[;{}]"},{begin:n,end:"{"}],returnBegin:!0,returnEnd:!0,illegal:"[<='$\"]",relevance:0,contains:[e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE,l,r("keyword","all\\b"),r("variable","@{[\\w-]+}"),r("selector-tag",n+"%?",0),r("selector-id","#"+n),r("selector-class","\\."+n,0),r("selector-tag","&",0),{className:"selector-attr",begin:"\\[",end:"\\]"},{className:"selector-pseudo",begin:/:(:)?[a-zA-Z0-9\_\-\+\(\)"'.]+/},{begin:"\\(",end:"\\)",contains:c},{begin:"!important"}]};return a.push(e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE,g,d,o,b),{name:"Less",case_insensitive:!0,illegal:"[=>'/<($\"]",contains:a}}}());
hljs.registerLanguage("lua",function(){"use strict";return function(e){var t={begin:"\\[=*\\[",end:"\\]=*\\]",contains:["self"]},a=[e.COMMENT("--(?!\\[=*\\[)","$"),e.COMMENT("--\\[=*\\[","\\]=*\\]",{contains:[t],relevance:10})];return{name:"Lua",lexemes:e.UNDERSCORE_IDENT_RE,keywords:{literal:"true false nil",keyword:"and break do else elseif end for goto if in local not or repeat return then until while",built_in:"_G _ENV _VERSION __index __newindex __mode __call __metatable __tostring __len __gc __add __sub __mul __div __mod __pow __concat __unm __eq __lt __le assert collectgarbage dofile error getfenv getmetatable ipairs load loadfile loadstring module next pairs pcall print rawequal rawget rawset require select setfenv setmetatable tonumber tostring type unpack xpcall arg self coroutine resume yield status wrap create running debug getupvalue debug sethook getmetatable gethook setmetatable setlocal traceback setfenv getinfo setupvalue getlocal getregistry getfenv io lines write close flush open output type read stderr stdin input stdout popen tmpfile math log max acos huge ldexp pi cos tanh pow deg tan cosh sinh random randomseed frexp ceil floor rad abs sqrt modf asin min mod fmod log10 atan2 exp sin atan os exit setlocale date getenv difftime remove time clock tmpname rename execute package preload loadlib loaded loaders cpath config path seeall string sub upper len gfind rep find match char dump gmatch reverse byte format gsub lower table setn insert getn foreachi maxn foreach concat sort remove"},contains:a.concat([{className:"function",beginKeywords:"function",end:"\\)",contains:[e.inherit(e.TITLE_MODE,{begin:"([_a-zA-Z]\\w*\\.)*([_a-zA-Z]\\w*:)?[_a-zA-Z]\\w*"}),{className:"params",begin:"\\(",endsWithParent:!0,contains:a}].concat(a)},e.C_NUMBER_MODE,e.APOS_STRING_MODE,e.QUOTE_STRING_MODE,{className:"string",begin:"\\[=*\\[",end:"\\]=*\\]",contains:[t],relevance:5}])}}}());
hljs.registerLanguage("makefile",function(){"use strict";return function(e){var i={className:"variable",variants:[{begin:"\\$\\("+e.UNDERSCORE_IDENT_RE+"\\)",contains:[e.BACKSLASH_ESCAPE]},{begin:/\$[@%<?\^\+\*]/}]},n={className:"string",begin:/"/,end:/"/,contains:[e.BACKSLASH_ESCAPE,i]},a={className:"variable",begin:/\$\([\w-]+\s/,end:/\)/,keywords:{built_in:"subst patsubst strip findstring filter filter-out sort word wordlist firstword lastword dir notdir suffix basename addsuffix addprefix join wildcard realpath abspath error warning shell origin flavor foreach if or and call eval file value"},contains:[i]},s={begin:"^"+e.UNDERSCORE_IDENT_RE+"\\s*(?=[:+?]?=)"},r={className:"section",begin:/^[^\s]+:/,end:/$/,contains:[i]};return{name:"Makefile",aliases:["mk","mak"],keywords:"define endef undefine ifdef ifndef ifeq ifneq else endif include -include sinclude override export unexport private vpath",lexemes:/[\w-]+/,contains:[e.HASH_COMMENT_MODE,i,n,a,s,{className:"meta",begin:/^\.PHONY:/,end:/$/,keywords:{"meta-keyword":".PHONY"},lexemes:/[\.\w]+/},r]}}}());
hljs.registerLanguage("xml",function(){"use strict";return function(e){var n={className:"symbol",begin:"&[a-z]+;|&#[0-9]+;|&#x[a-f0-9]+;"},a={begin:"\\s",contains:[{className:"meta-keyword",begin:"#?[a-z_][a-z1-9_-]+",illegal:"\\n"}]},s=e.inherit(a,{begin:"\\(",end:"\\)"}),t=e.inherit(e.APOS_STRING_MODE,{className:"meta-string"}),i=e.inherit(e.QUOTE_STRING_MODE,{className:"meta-string"}),c={endsWithParent:!0,illegal:/</,relevance:0,contains:[{className:"attr",begin:"[A-Za-z0-9\\._:-]+",relevance:0},{begin:/=\s*/,relevance:0,contains:[{className:"string",endsParent:!0,variants:[{begin:/"/,end:/"/,contains:[n]},{begin:/'/,end:/'/,contains:[n]},{begin:/[^\s"'=<>`]+/}]}]}]};return{name:"HTML, XML",aliases:["html","xhtml","rss","atom","xjb","xsd","xsl","plist","wsf","svg"],case_insensitive:!0,contains:[{className:"meta",begin:"<![a-z]",end:">",relevance:10,contains:[a,i,t,s,{begin:"\\[",end:"\\]",contains:[{className:"meta",begin:"<![a-z]",end:">",contains:[a,s,i,t]}]}]},e.COMMENT("\x3c!--","--\x3e",{relevance:10}),{begin:"<\\!\\[CDATA\\[",end:"\\]\\]>",relevance:10},n,{className:"meta",begin:/<\?xml/,end:/\?>/,relevance:10},{className:"tag",begin:"<style(?=\\s|>)",end:">",keywords:{name:"style"},contains:[c],starts:{end:"</style>",returnEnd:!0,subLanguage:["css","xml"]}},{className:"tag",begin:"<script(?=\\s|>)",end:">",keywords:{name:"script"},contains:[c],starts:{end:"<\/script>",returnEnd:!0,subLanguage:["javascript","handlebars","xml"]}},{className:"tag",begin:"</?",end:"/?>",contains:[{className:"name",begin:/[^\/><\s]+/,relevance:0},c]}]}}}());
hljs.registerLanguage("markdown",function(){"use strict";return function(n){const e={begin:"<",end:">",subLanguage:"xml",relevance:0},a={begin:"\\[.+?\\][\\(\\[].*?[\\)\\]]",returnBegin:!0,contains:[{className:"string",begin:"\\[",end:"\\]",excludeBegin:!0,returnEnd:!0,relevance:0},{className:"link",begin:"\\]\\(",end:"\\)",excludeBegin:!0,excludeEnd:!0},{className:"symbol",begin:"\\]\\[",end:"\\]",excludeBegin:!0,excludeEnd:!0}],relevance:10},i={className:"strong",contains:[],variants:[{begin:/_{2}/,end:/_{2}/},{begin:/\*{2}/,end:/\*{2}/}]},s={className:"emphasis",contains:[],variants:[{begin:/\*(?!\*)/,end:/\*/},{begin:/_(?!_)/,end:/_/,relevance:0}]};i.contains.push(s),s.contains.push(i);var c=[e,a];return i.contains=i.contains.concat(c),s.contains=s.contains.concat(c),{name:"Markdown",aliases:["md","mkdown","mkd"],contains:[{className:"section",variants:[{begin:"^#{1,6}",end:"$",contains:c=c.concat(i,s)},{begin:"(?=^.+?\\n[=-]{2,}$)",contains:[{begin:"^[=-]*$"},{begin:"^",end:"\\n",contains:c}]}]},e,{className:"bullet",begin:"^[ \t]*([*+-]|(\\d+\\.))(?=\\s+)",end:"\\s+",excludeEnd:!0},i,s,{className:"quote",begin:"^>\\s+",contains:c,end:"$"},{className:"code",variants:[{begin:"(`{3,})(.|\\n)*?\\1`*[ ]*"},{begin:"(~{3,})(.|\\n)*?\\1~*[ ]*"},{begin:"```",end:"```+[ ]*$"},{begin:"~~~",end:"~~~+[ ]*$"},{begin:"`.+?`"},{begin:"(?=^( {4}|\\t))",contains:[{begin:"^( {4}|\\t)",end:"(\\n)$"}],relevance:0}]},{begin:"^[-\\*]{3,}",end:"$"},a,{begin:/^\[[^\n]+\]:/,returnBegin:!0,contains:[{className:"symbol",begin:/\[/,end:/\]/,excludeBegin:!0,excludeEnd:!0},{className:"link",begin:/:\s*/,end:/$/,excludeBegin:!0}]}]}}}());
hljs.registerLanguage("nginx",function(){"use strict";return function(e){var n={className:"variable",variants:[{begin:/\$\d+/},{begin:/\$\{/,end:/}/},{begin:"[\\$\\@]"+e.UNDERSCORE_IDENT_RE}]},a={endsWithParent:!0,lexemes:"[a-z/_]+",keywords:{literal:"on off yes no true false none blocked debug info notice warn error crit select break last permanent redirect kqueue rtsig epoll poll /dev/poll"},relevance:0,illegal:"=>",contains:[e.HASH_COMMENT_MODE,{className:"string",contains:[e.BACKSLASH_ESCAPE,n],variants:[{begin:/"/,end:/"/},{begin:/'/,end:/'/}]},{begin:"([a-z]+):/",end:"\\s",endsWithParent:!0,excludeEnd:!0,contains:[n]},{className:"regexp",contains:[e.BACKSLASH_ESCAPE,n],variants:[{begin:"\\s\\^",end:"\\s|{|;",returnEnd:!0},{begin:"~\\*?\\s+",end:"\\s|{|;",returnEnd:!0},{begin:"\\*(\\.[a-z\\-]+)+"},{begin:"([a-z\\-]+\\.)+\\*"}]},{className:"number",begin:"\\b\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}(:\\d{1,5})?\\b"},{className:"number",begin:"\\b\\d+[kKmMgGdshdwy]*\\b",relevance:0},n]};return{name:"Nginx config",aliases:["nginxconf"],contains:[e.HASH_COMMENT_MODE,{begin:e.UNDERSCORE_IDENT_RE+"\\s+{",returnBegin:!0,end:"{",contains:[{className:"section",begin:e.UNDERSCORE_IDENT_RE}],relevance:0},{begin:e.UNDERSCORE_IDENT_RE+"\\s",end:";|{",returnBegin:!0,contains:[{className:"attribute",begin:e.UNDERSCORE_IDENT_RE,starts:a}],relevance:0}],illegal:"[^\\s\\}]"}}}());
hljs.registerLanguage("objectivec",function(){"use strict";return function(e){var n=/[a-zA-Z@][a-zA-Z0-9_]*/,_="@interface @class @protocol @implementation";return{name:"Objective-C",aliases:["mm","objc","obj-c"],keywords:{keyword:"int float while char export sizeof typedef const struct for union unsigned long volatile static bool mutable if do return goto void enum else break extern asm case short default double register explicit signed typename this switch continue wchar_t inline readonly assign readwrite self @synchronized id typeof nonatomic super unichar IBOutlet IBAction strong weak copy in out inout bycopy byref oneway __strong __weak __block __autoreleasing @private @protected @public @try @property @end @throw @catch @finally @autoreleasepool @synthesize @dynamic @selector @optional @required @encode @package @import @defs @compatibility_alias __bridge __bridge_transfer __bridge_retained __bridge_retain __covariant __contravariant __kindof _Nonnull _Nullable _Null_unspecified __FUNCTION__ __PRETTY_FUNCTION__ __attribute__ getter setter retain unsafe_unretained nonnull nullable null_unspecified null_resettable class instancetype NS_DESIGNATED_INITIALIZER NS_UNAVAILABLE NS_REQUIRES_SUPER NS_RETURNS_INNER_POINTER NS_INLINE NS_AVAILABLE NS_DEPRECATED NS_ENUM NS_OPTIONS NS_SWIFT_UNAVAILABLE NS_ASSUME_NONNULL_BEGIN NS_ASSUME_NONNULL_END NS_REFINED_FOR_SWIFT NS_SWIFT_NAME NS_SWIFT_NOTHROW NS_DURING NS_HANDLER NS_ENDHANDLER NS_VALUERETURN NS_VOIDRETURN",literal:"false true FALSE TRUE nil YES NO NULL",built_in:"BOOL dispatch_once_t dispatch_queue_t dispatch_sync dispatch_async dispatch_once"},lexemes:n,illegal:"</",contains:[{className:"built_in",begin:"\\b(AV|CA|CF|CG|CI|CL|CM|CN|CT|MK|MP|MTK|MTL|NS|SCN|SK|UI|WK|XC)\\w+"},e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE,e.C_NUMBER_MODE,e.QUOTE_STRING_MODE,e.APOS_STRING_MODE,{className:"string",variants:[{begin:'@"',end:'"',illegal:"\\n",contains:[e.BACKSLASH_ESCAPE]}]},{className:"meta",begin:/#\s*[a-z]+\b/,end:/$/,keywords:{"meta-keyword":"if else elif endif define undef warning error line pragma ifdef ifndef include"},contains:[{begin:/\\\n/,relevance:0},e.inherit(e.QUOTE_STRING_MODE,{className:"meta-string"}),{className:"meta-string",begin:/<.*?>/,end:/$/,illegal:"\\n"},e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE]},{className:"class",begin:"("+_.split(" ").join("|")+")\\b",end:"({|$)",excludeEnd:!0,keywords:_,lexemes:n,contains:[e.UNDERSCORE_TITLE_MODE]},{begin:"\\."+e.UNDERSCORE_IDENT_RE,relevance:0}]}}}());
hljs.registerLanguage("perl",function(){"use strict";return function(e){var n="getpwent getservent quotemeta msgrcv scalar kill dbmclose undef lc ma syswrite tr send umask sysopen shmwrite vec qx utime local oct semctl localtime readpipe do return format read sprintf dbmopen pop getpgrp not getpwnam rewinddir qq fileno qw endprotoent wait sethostent bless s|0 opendir continue each sleep endgrent shutdown dump chomp connect getsockname die socketpair close flock exists index shmget sub for endpwent redo lstat msgctl setpgrp abs exit select print ref gethostbyaddr unshift fcntl syscall goto getnetbyaddr join gmtime symlink semget splice x|0 getpeername recv log setsockopt cos last reverse gethostbyname getgrnam study formline endhostent times chop length gethostent getnetent pack getprotoent getservbyname rand mkdir pos chmod y|0 substr endnetent printf next open msgsnd readdir use unlink getsockopt getpriority rindex wantarray hex system getservbyport endservent int chr untie rmdir prototype tell listen fork shmread ucfirst setprotoent else sysseek link getgrgid shmctl waitpid unpack getnetbyname reset chdir grep split require caller lcfirst until warn while values shift telldir getpwuid my getprotobynumber delete and sort uc defined srand accept package seekdir getprotobyname semop our rename seek if q|0 chroot sysread setpwent no crypt getc chown sqrt write setnetent setpriority foreach tie sin msgget map stat getlogin unless elsif truncate exec keys glob tied closedir ioctl socket readlink eval xor readline binmode setservent eof ord bind alarm pipe atan2 getgrent exp time push setgrent gt lt or ne m|0 break given say state when",t={className:"subst",begin:"[$@]\\{",end:"\\}",keywords:n},s={begin:"->{",end:"}"},r={variants:[{begin:/\$\d/},{begin:/[\$%@](\^\w\b|#\w+(::\w+)*|{\w+}|\w+(::\w*)*)/},{begin:/[\$%@][^\s\w{]/,relevance:0}]},i=[e.BACKSLASH_ESCAPE,t,r],a=[r,e.HASH_COMMENT_MODE,e.COMMENT("^\\=\\w","\\=cut",{endsWithParent:!0}),s,{className:"string",contains:i,variants:[{begin:"q[qwxr]?\\s*\\(",end:"\\)",relevance:5},{begin:"q[qwxr]?\\s*\\[",end:"\\]",relevance:5},{begin:"q[qwxr]?\\s*\\{",end:"\\}",relevance:5},{begin:"q[qwxr]?\\s*\\|",end:"\\|",relevance:5},{begin:"q[qwxr]?\\s*\\<",end:"\\>",relevance:5},{begin:"qw\\s+q",end:"q",relevance:5},{begin:"'",end:"'",contains:[e.BACKSLASH_ESCAPE]},{begin:'"',end:'"'},{begin:"`",end:"`",contains:[e.BACKSLASH_ESCAPE]},{begin:"{\\w+}",contains:[],relevance:0},{begin:"-?\\w+\\s*\\=\\>",contains:[],relevance:0}]},{className:"number",begin:"(\\b0[0-7_]+)|(\\b0x[0-9a-fA-F_]+)|(\\b[1-9][0-9_]*(\\.[0-9_]+)?)|[0_]\\b",relevance:0},{begin:"(\\/\\/|"+e.RE_STARTERS_RE+"|\\b(split|return|print|reverse|grep)\\b)\\s*",keywords:"split return print reverse grep",relevance:0,contains:[e.HASH_COMMENT_MODE,{className:"regexp",begin:"(s|tr|y)/(\\\\.|[^/])*/(\\\\.|[^/])*/[a-z]*",relevance:10},{className:"regexp",begin:"(m|qr)?/",end:"/[a-z]*",contains:[e.BACKSLASH_ESCAPE],relevance:0}]},{className:"function",beginKeywords:"sub",end:"(\\s*\\(.*?\\))?[;{]",excludeEnd:!0,relevance:5,contains:[e.TITLE_MODE]},{begin:"-\\w\\b",relevance:0},{begin:"^__DATA__$",end:"^__END__$",subLanguage:"mojolicious",contains:[{begin:"^@@.*",end:"$",className:"comment"}]}];return t.contains=a,s.contains=a,{name:"Perl",aliases:["pl","pm"],lexemes:/[\w\.]+/,keywords:n,contains:a}}}());
hljs.registerLanguage("php",function(){"use strict";return function(e){var r={begin:"\\$+[a-zA-Z_-ÿ][a-zA-Z0-9_-ÿ]*"},t={className:"meta",variants:[{begin:/<\?php/,relevance:10},{begin:/<\?[=]?/},{begin:/\?>/}]},a={className:"string",contains:[e.BACKSLASH_ESCAPE,t],variants:[{begin:'b"',end:'"'},{begin:"b'",end:"'"},e.inherit(e.APOS_STRING_MODE,{illegal:null}),e.inherit(e.QUOTE_STRING_MODE,{illegal:null})]},n={variants:[e.BINARY_NUMBER_MODE,e.C_NUMBER_MODE]},i={keyword:"__CLASS__ __DIR__ __FILE__ __FUNCTION__ __LINE__ __METHOD__ __NAMESPACE__ __TRAIT__ die echo exit include include_once print require require_once array abstract and as binary bool boolean break callable case catch class clone const continue declare default do double else elseif empty enddeclare endfor endforeach endif endswitch endwhile eval extends final finally float for foreach from global goto if implements instanceof insteadof int integer interface isset iterable list new object or private protected public real return string switch throw trait try unset use var void while xor yield",literal:"false null true",built_in:"Error|0 AppendIterator ArgumentCountError ArithmeticError ArrayIterator ArrayObject AssertionError BadFunctionCallException BadMethodCallException CachingIterator CallbackFilterIterator CompileError Countable DirectoryIterator DivisionByZeroError DomainException EmptyIterator ErrorException Exception FilesystemIterator FilterIterator GlobIterator InfiniteIterator InvalidArgumentException IteratorIterator LengthException LimitIterator LogicException MultipleIterator NoRewindIterator OutOfBoundsException OutOfRangeException OuterIterator OverflowException ParentIterator ParseError RangeException RecursiveArrayIterator RecursiveCachingIterator RecursiveCallbackFilterIterator RecursiveDirectoryIterator RecursiveFilterIterator RecursiveIterator RecursiveIteratorIterator RecursiveRegexIterator RecursiveTreeIterator RegexIterator RuntimeException SeekableIterator SplDoublyLinkedList SplFileInfo SplFileObject SplFixedArray SplHeap SplMaxHeap SplMinHeap SplObjectStorage SplObserver SplObserver SplPriorityQueue SplQueue SplStack SplSubject SplSubject SplTempFileObject TypeError UnderflowException UnexpectedValueException ArrayAccess Closure Generator Iterator IteratorAggregate Serializable Throwable Traversable WeakReference Directory __PHP_Incomplete_Class parent php_user_filter self static stdClass"};return{aliases:["php","php3","php4","php5","php6","php7"],case_insensitive:!0,keywords:i,contains:[e.HASH_COMMENT_MODE,e.COMMENT("//","$",{contains:[t]}),e.COMMENT("/\\*","\\*/",{contains:[{className:"doctag",begin:"@[A-Za-z]+"}]}),e.COMMENT("__halt_compiler.+?;",!1,{endsWithParent:!0,keywords:"__halt_compiler",lexemes:e.UNDERSCORE_IDENT_RE}),{className:"string",begin:/<<<['"]?\w+['"]?$/,end:/^\w+;?$/,contains:[e.BACKSLASH_ESCAPE,{className:"subst",variants:[{begin:/\$\w+/},{begin:/\{\$/,end:/\}/}]}]},t,{className:"keyword",begin:/\$this\b/},r,{begin:/(::|->)+[a-zA-Z_\x7f-\xff][a-zA-Z0-9_\x7f-\xff]*/},{className:"function",beginKeywords:"fn function",end:/[;{]/,excludeEnd:!0,illegal:"[$%\\[]",contains:[e.UNDERSCORE_TITLE_MODE,{className:"params",begin:"\\(",end:"\\)",excludeBegin:!0,excludeEnd:!0,keywords:i,contains:["self",r,e.C_BLOCK_COMMENT_MODE,a,n]}]},{className:"class",beginKeywords:"class interface",end:"{",excludeEnd:!0,illegal:/[:\(\$"]/,contains:[{beginKeywords:"extends implements"},e.UNDERSCORE_TITLE_MODE]},{beginKeywords:"namespace",end:";",illegal:/[\.']/,contains:[e.UNDERSCORE_TITLE_MODE]},{beginKeywords:"use",end:";",contains:[e.UNDERSCORE_TITLE_MODE]},{begin:"=>"},a,n]}}}());
hljs.registerLanguage("php-template",function(){"use strict";return function(n){return{name:"PHP template",subLanguage:"xml",contains:[{begin:/<\?(php|=)?/,end:/\?>/,subLanguage:"php",contains:[{begin:"/\\*",end:"\\*/",skip:!0},{begin:'b"',end:'"',skip:!0},{begin:"b'",end:"'",skip:!0},n.inherit(n.APOS_STRING_MODE,{illegal:null,className:null,contains:null,skip:!0}),n.inherit(n.QUOTE_STRING_MODE,{illegal:null,className:null,contains:null,skip:!0})]}]}}}());
hljs.registerLanguage("plaintext",function(){"use strict";return function(t){return{name:"Plain text",aliases:["text","txt"],disableAutodetect:!0}}}());
hljs.registerLanguage("properties",function(){"use strict";return function(e){var n="[ \\t\\f]*",t="("+n+"[:=]"+n+"|[ \\t\\f]+)",a="([^\\\\:= \\t\\f\\n]|\\\\.)+",s={end:t,relevance:0,starts:{className:"string",end:/$/,relevance:0,contains:[{begin:"\\\\\\n"}]}};return{name:".properties",case_insensitive:!0,illegal:/\S/,contains:[e.COMMENT("^\\s*[!#]","$"),{begin:"([^\\\\\\W:= \\t\\f\\n]|\\\\.)+"+t,returnBegin:!0,contains:[{className:"attr",begin:"([^\\\\\\W:= \\t\\f\\n]|\\\\.)+",endsParent:!0,relevance:0}],starts:s},{begin:a+t,returnBegin:!0,relevance:0,contains:[{className:"meta",begin:a,endsParent:!0,relevance:0}],starts:s},{className:"attr",relevance:0,begin:a+n+"$"}]}}}());
hljs.registerLanguage("python",function(){"use strict";return function(e){var n={keyword:"and elif is global as in if from raise for except finally print import pass return exec else break not with class assert yield try while continue del or def lambda async await nonlocal|10",built_in:"Ellipsis NotImplemented",literal:"False None True"},a={className:"meta",begin:/^(>>>|\.\.\.) /},i={className:"subst",begin:/\{/,end:/\}/,keywords:n,illegal:/#/},s={begin:/\{\{/,relevance:0},r={className:"string",contains:[e.BACKSLASH_ESCAPE],variants:[{begin:/(u|b)?r?'''/,end:/'''/,contains:[e.BACKSLASH_ESCAPE,a],relevance:10},{begin:/(u|b)?r?"""/,end:/"""/,contains:[e.BACKSLASH_ESCAPE,a],relevance:10},{begin:/(fr|rf|f)'''/,end:/'''/,contains:[e.BACKSLASH_ESCAPE,a,s,i]},{begin:/(fr|rf|f)"""/,end:/"""/,contains:[e.BACKSLASH_ESCAPE,a,s,i]},{begin:/(u|r|ur)'/,end:/'/,relevance:10},{begin:/(u|r|ur)"/,end:/"/,relevance:10},{begin:/(b|br)'/,end:/'/},{begin:/(b|br)"/,end:/"/},{begin:/(fr|rf|f)'/,end:/'/,contains:[e.BACKSLASH_ESCAPE,s,i]},{begin:/(fr|rf|f)"/,end:/"/,contains:[e.BACKSLASH_ESCAPE,s,i]},e.APOS_STRING_MODE,e.QUOTE_STRING_MODE]},l={className:"number",relevance:0,variants:[{begin:e.BINARY_NUMBER_RE+"[lLjJ]?"},{begin:"\\b(0o[0-7]+)[lLjJ]?"},{begin:e.C_NUMBER_RE+"[lLjJ]?"}]},t={className:"params",variants:[{begin:/\(\s*\)/,skip:!0,className:null},{begin:/\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,contains:["self",a,l,r,e.HASH_COMMENT_MODE]}]};return i.contains=[r,l,a],{name:"Python",aliases:["py","gyp","ipython"],keywords:n,illegal:/(<\/|->|\?)|=>/,contains:[a,l,{beginKeywords:"if",relevance:0},r,e.HASH_COMMENT_MODE,{variants:[{className:"function",beginKeywords:"def"},{className:"class",beginKeywords:"class"}],end:/:/,illegal:/[${=;\n,]/,contains:[e.UNDERSCORE_TITLE_MODE,t,{begin:/->/,endsWithParent:!0,keywords:"None"}]},{className:"meta",begin:/^[\t ]*@/,end:/$/},{begin:/\b(print|exec)\(/}]}}}());
hljs.registerLanguage("python-repl",function(){"use strict";return function(n){return{aliases:["pycon"],contains:[{className:"meta",starts:{end:/ |$/,starts:{end:"$",subLanguage:"python"}},variants:[{begin:/^>>>(?=[ ]|$)/},{begin:/^\.\.\.(?=[ ]|$)/}]}]}}}());
hljs.registerLanguage("ruby",function(){"use strict";return function(e){var n="[a-zA-Z_]\\w*[!?=]?|[-+~]\\@|<<|>>|=~|===?|<=>|[<>]=?|\\*\\*|[-/+%^&*~`|]|\\[\\]=?",a={keyword:"and then defined module in return redo if BEGIN retry end for self when next until do begin unless END rescue else break undef not super class case require yield alias while ensure elsif or include attr_reader attr_writer attr_accessor",literal:"true false nil"},s={className:"doctag",begin:"@[A-Za-z]+"},i={begin:"#<",end:">"},r=[e.COMMENT("#","$",{contains:[s]}),e.COMMENT("^\\=begin","^\\=end",{contains:[s],relevance:10}),e.COMMENT("^__END__","\\n$")],c={className:"subst",begin:"#\\{",end:"}",keywords:a},t={className:"string",contains:[e.BACKSLASH_ESCAPE,c],variants:[{begin:/'/,end:/'/},{begin:/"/,end:/"/},{begin:/`/,end:/`/},{begin:"%[qQwWx]?\\(",end:"\\)"},{begin:"%[qQwWx]?\\[",end:"\\]"},{begin:"%[qQwWx]?{",end:"}"},{begin:"%[qQwWx]?<",end:">"},{begin:"%[qQwWx]?/",end:"/"},{begin:"%[qQwWx]?%",end:"%"},{begin:"%[qQwWx]?-",end:"-"},{begin:"%[qQwWx]?\\|",end:"\\|"},{begin:/\B\?(\\\d{1,3}|\\x[A-Fa-f0-9]{1,2}|\\u[A-Fa-f0-9]{4}|\\?\S)\b/},{begin:/<<[-~]?'?(\w+)(?:.|\n)*?\n\s*\1\b/,returnBegin:!0,contains:[{begin:/<<[-~]?'?/},{begin:/\w+/,endSameAsBegin:!0,contains:[e.BACKSLASH_ESCAPE,c]}]}]},b={className:"params",begin:"\\(",end:"\\)",endsParent:!0,keywords:a},d=[t,i,{className:"class",beginKeywords:"class module",end:"$|;",illegal:/=/,contains:[e.inherit(e.TITLE_MODE,{begin:"[A-Za-z_]\\w*(::\\w+)*(\\?|\\!)?"}),{begin:"<\\s*",contains:[{begin:"("+e.IDENT_RE+"::)?"+e.IDENT_RE}]}].concat(r)},{className:"function",beginKeywords:"def",end:"$|;",contains:[e.inherit(e.TITLE_MODE,{begin:n}),b].concat(r)},{begin:e.IDENT_RE+"::"},{className:"symbol",begin:e.UNDERSCORE_IDENT_RE+"(\\!|\\?)?:",relevance:0},{className:"symbol",begin:":(?!\\s)",contains:[t,{begin:n}],relevance:0},{className:"number",begin:"(\\b0[0-7_]+)|(\\b0x[0-9a-fA-F_]+)|(\\b[1-9][0-9_]*(\\.[0-9_]+)?)|[0_]\\b",relevance:0},{begin:"(\\$\\W)|((\\$|\\@\\@?)(\\w+))"},{className:"params",begin:/\|/,end:/\|/,keywords:a},{begin:"("+e.RE_STARTERS_RE+"|unless)\\s*",keywords:"unless",contains:[i,{className:"regexp",contains:[e.BACKSLASH_ESCAPE,c],illegal:/\n/,variants:[{begin:"/",end:"/[a-z]*"},{begin:"%r{",end:"}[a-z]*"},{begin:"%r\\(",end:"\\)[a-z]*"},{begin:"%r!",end:"![a-z]*"},{begin:"%r\\[",end:"\\][a-z]*"}]}].concat(r),relevance:0}].concat(r);c.contains=d,b.contains=d;var g=[{begin:/^\s*=>/,starts:{end:"$",contains:d}},{className:"meta",begin:"^([>?]>|[\\w#]+\\(\\w+\\):\\d+:\\d+>|(\\w+-)?\\d+\\.\\d+\\.\\d(p\\d+)?[^>]+>)",starts:{end:"$",contains:d}}];return{name:"Ruby",aliases:["rb","gemspec","podspec","thor","irb"],keywords:a,illegal:/\/\*/,contains:r.concat(g).concat(d)}}}());
hljs.registerLanguage("rust",function(){"use strict";return function(e){var n="([ui](8|16|32|64|128|size)|f(32|64))?",t="drop i8 i16 i32 i64 i128 isize u8 u16 u32 u64 u128 usize f32 f64 str char bool Box Option Result String Vec Copy Send Sized Sync Drop Fn FnMut FnOnce ToOwned Clone Debug PartialEq PartialOrd Eq Ord AsRef AsMut Into From Default Iterator Extend IntoIterator DoubleEndedIterator ExactSizeIterator SliceConcatExt ToString assert! assert_eq! bitflags! bytes! cfg! col! concat! concat_idents! debug_assert! debug_assert_eq! env! panic! file! format! format_args! include_bin! include_str! line! local_data_key! module_path! option_env! print! println! select! stringify! try! unimplemented! unreachable! vec! write! writeln! macro_rules! assert_ne! debug_assert_ne!";return{name:"Rust",aliases:["rs"],keywords:{keyword:"abstract as async await become box break const continue crate do dyn else enum extern false final fn for if impl in let loop macro match mod move mut override priv pub ref return self Self static struct super trait true try type typeof unsafe unsized use virtual where while yield",literal:"true false Some None Ok Err",built_in:t},lexemes:e.IDENT_RE+"!?",illegal:"</",contains:[e.C_LINE_COMMENT_MODE,e.COMMENT("/\\*","\\*/",{contains:["self"]}),e.inherit(e.QUOTE_STRING_MODE,{begin:/b?"/,illegal:null}),{className:"string",variants:[{begin:/r(#*)"(.|\n)*?"\1(?!#)/},{begin:/b?'\\?(x\w{2}|u\w{4}|U\w{8}|.)'/}]},{className:"symbol",begin:/'[a-zA-Z_][a-zA-Z0-9_]*/},{className:"number",variants:[{begin:"\\b0b([01_]+)"+n},{begin:"\\b0o([0-7_]+)"+n},{begin:"\\b0x([A-Fa-f0-9_]+)"+n},{begin:"\\b(\\d[\\d_]*(\\.[0-9_]+)?([eE][+-]?[0-9_]+)?)"+n}],relevance:0},{className:"function",beginKeywords:"fn",end:"(\\(|<)",excludeEnd:!0,contains:[e.UNDERSCORE_TITLE_MODE]},{className:"meta",begin:"#\\!?\\[",end:"\\]",contains:[{className:"meta-string",begin:/"/,end:/"/}]},{className:"class",beginKeywords:"type",end:";",contains:[e.inherit(e.UNDERSCORE_TITLE_MODE,{endsParent:!0})],illegal:"\\S"},{className:"class",beginKeywords:"trait enum struct union",end:"{",contains:[e.inherit(e.UNDERSCORE_TITLE_MODE,{endsParent:!0})],illegal:"[\\w\\d]"},{begin:e.IDENT_RE+"::",keywords:{built_in:t}},{begin:"->"}]}}}());
hljs.registerLanguage("scss",function(){"use strict";return function(e){var t={className:"variable",begin:"(\\$[a-zA-Z-][a-zA-Z0-9_-]*)\\b"},i={className:"number",begin:"#[0-9A-Fa-f]+"};return e.CSS_NUMBER_MODE,e.QUOTE_STRING_MODE,e.APOS_STRING_MODE,e.C_BLOCK_COMMENT_MODE,{name:"SCSS",case_insensitive:!0,illegal:"[=/|']",contains:[e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE,{className:"selector-id",begin:"\\#[A-Za-z0-9_-]+",relevance:0},{className:"selector-class",begin:"\\.[A-Za-z0-9_-]+",relevance:0},{className:"selector-attr",begin:"\\[",end:"\\]",illegal:"$"},{className:"selector-tag",begin:"\\b(a|abbr|acronym|address|area|article|aside|audio|b|base|big|blockquote|body|br|button|canvas|caption|cite|code|col|colgroup|command|datalist|dd|del|details|dfn|div|dl|dt|em|embed|fieldset|figcaption|figure|footer|form|frame|frameset|(h[1-6])|head|header|hgroup|hr|html|i|iframe|img|input|ins|kbd|keygen|label|legend|li|link|map|mark|meta|meter|nav|noframes|noscript|object|ol|optgroup|option|output|p|param|pre|progress|q|rp|rt|ruby|samp|script|section|select|small|span|strike|strong|style|sub|sup|table|tbody|td|textarea|tfoot|th|thead|time|title|tr|tt|ul|var|video)\\b",relevance:0},{className:"selector-pseudo",begin:":(visited|valid|root|right|required|read-write|read-only|out-range|optional|only-of-type|only-child|nth-of-type|nth-last-of-type|nth-last-child|nth-child|not|link|left|last-of-type|last-child|lang|invalid|indeterminate|in-range|hover|focus|first-of-type|first-line|first-letter|first-child|first|enabled|empty|disabled|default|checked|before|after|active)"},{className:"selector-pseudo",begin:"::(after|before|choices|first-letter|first-line|repeat-index|repeat-item|selection|value)"},t,{className:"attribute",begin:"\\b(src|z-index|word-wrap|word-spacing|word-break|width|widows|white-space|visibility|vertical-align|unicode-bidi|transition-timing-function|transition-property|transition-duration|transition-delay|transition|transform-style|transform-origin|transform|top|text-underline-position|text-transform|text-shadow|text-rendering|text-overflow|text-indent|text-decoration-style|text-decoration-line|text-decoration-color|text-decoration|text-align-last|text-align|tab-size|table-layout|right|resize|quotes|position|pointer-events|perspective-origin|perspective|page-break-inside|page-break-before|page-break-after|padding-top|padding-right|padding-left|padding-bottom|padding|overflow-y|overflow-x|overflow-wrap|overflow|outline-width|outline-style|outline-offset|outline-color|outline|orphans|order|opacity|object-position|object-fit|normal|none|nav-up|nav-right|nav-left|nav-index|nav-down|min-width|min-height|max-width|max-height|mask|marks|margin-top|margin-right|margin-left|margin-bottom|margin|list-style-type|list-style-position|list-style-image|list-style|line-height|letter-spacing|left|justify-content|initial|inherit|ime-mode|image-orientation|image-resolution|image-rendering|icon|hyphens|height|font-weight|font-variant-ligatures|font-variant|font-style|font-stretch|font-size-adjust|font-size|font-language-override|font-kerning|font-feature-settings|font-family|font|float|flex-wrap|flex-shrink|flex-grow|flex-flow|flex-direction|flex-basis|flex|filter|empty-cells|display|direction|cursor|counter-reset|counter-increment|content|column-width|column-span|column-rule-width|column-rule-style|column-rule-color|column-rule|column-gap|column-fill|column-count|columns|color|clip-path|clip|clear|caption-side|break-inside|break-before|break-after|box-sizing|box-shadow|box-decoration-break|bottom|border-width|border-top-width|border-top-style|border-top-right-radius|border-top-left-radius|border-top-color|border-top|border-style|border-spacing|border-right-width|border-right-style|border-right-color|border-right|border-radius|border-left-width|border-left-style|border-left-color|border-left|border-image-width|border-image-source|border-image-slice|border-image-repeat|border-image-outset|border-image|border-color|border-collapse|border-bottom-width|border-bottom-style|border-bottom-right-radius|border-bottom-left-radius|border-bottom-color|border-bottom|border|background-size|background-repeat|background-position|background-origin|background-image|background-color|background-clip|background-attachment|background-blend-mode|background|backface-visibility|auto|animation-timing-function|animation-play-state|animation-name|animation-iteration-count|animation-fill-mode|animation-duration|animation-direction|animation-delay|animation|align-self|align-items|align-content)\\b",illegal:"[^\\s]"},{begin:"\\b(whitespace|wait|w-resize|visible|vertical-text|vertical-ideographic|uppercase|upper-roman|upper-alpha|underline|transparent|top|thin|thick|text|text-top|text-bottom|tb-rl|table-header-group|table-footer-group|sw-resize|super|strict|static|square|solid|small-caps|separate|se-resize|scroll|s-resize|rtl|row-resize|ridge|right|repeat|repeat-y|repeat-x|relative|progress|pointer|overline|outside|outset|oblique|nowrap|not-allowed|normal|none|nw-resize|no-repeat|no-drop|newspaper|ne-resize|n-resize|move|middle|medium|ltr|lr-tb|lowercase|lower-roman|lower-alpha|loose|list-item|line|line-through|line-edge|lighter|left|keep-all|justify|italic|inter-word|inter-ideograph|inside|inset|inline|inline-block|inherit|inactive|ideograph-space|ideograph-parenthesis|ideograph-numeric|ideograph-alpha|horizontal|hidden|help|hand|groove|fixed|ellipsis|e-resize|double|dotted|distribute|distribute-space|distribute-letter|distribute-all-lines|disc|disabled|default|decimal|dashed|crosshair|collapse|col-resize|circle|char|center|capitalize|break-word|break-all|bottom|both|bolder|bold|block|bidi-override|below|baseline|auto|always|all-scroll|absolute|table|table-cell)\\b"},{begin:":",end:";",contains:[t,i,e.CSS_NUMBER_MODE,e.QUOTE_STRING_MODE,e.APOS_STRING_MODE,{className:"meta",begin:"!important"}]},{begin:"@(page|font-face)",lexemes:"@[a-z-]+",keywords:"@page @font-face"},{begin:"@",end:"[{;]",returnBegin:!0,keywords:"and or not only",contains:[{begin:"@[a-z-]+",className:"keyword"},t,e.QUOTE_STRING_MODE,e.APOS_STRING_MODE,i,e.CSS_NUMBER_MODE]}]}}}());
hljs.registerLanguage("shell",function(){"use strict";return function(s){return{name:"Shell Session",aliases:["console"],contains:[{className:"meta",begin:"^\\s{0,3}[/\\w\\d\\[\\]()@-]*[>%$#]",starts:{end:"$",subLanguage:"bash"}}]}}}());
hljs.registerLanguage("sql",function(){"use strict";return function(e){var t=e.COMMENT("--","$");return{name:"SQL",case_insensitive:!0,illegal:/[<>{}*]/,contains:[{beginKeywords:"begin end start commit rollback savepoint lock alter create drop rename call delete do handler insert load replace select truncate update set show pragma grant merge describe use explain help declare prepare execute deallocate release unlock purge reset change stop analyze cache flush optimize repair kill install uninstall checksum restore check backup revoke comment values with",end:/;/,endsWithParent:!0,lexemes:/[\w\.]+/,keywords:{keyword:"as abort abs absolute acc acce accep accept access accessed accessible account acos action activate add addtime admin administer advanced advise aes_decrypt aes_encrypt after agent aggregate ali alia alias all allocate allow alter always analyze ancillary and anti any anydata anydataset anyschema anytype apply archive archived archivelog are as asc ascii asin assembly assertion associate asynchronous at atan atn2 attr attri attrib attribu attribut attribute attributes audit authenticated authentication authid authors auto autoallocate autodblink autoextend automatic availability avg backup badfile basicfile before begin beginning benchmark between bfile bfile_base big bigfile bin binary_double binary_float binlog bit_and bit_count bit_length bit_or bit_xor bitmap blob_base block blocksize body both bound bucket buffer_cache buffer_pool build bulk by byte byteordermark bytes cache caching call calling cancel capacity cascade cascaded case cast catalog category ceil ceiling chain change changed char_base char_length character_length characters characterset charindex charset charsetform charsetid check checksum checksum_agg child choose chr chunk class cleanup clear client clob clob_base clone close cluster_id cluster_probability cluster_set clustering coalesce coercibility col collate collation collect colu colum column column_value columns columns_updated comment commit compact compatibility compiled complete composite_limit compound compress compute concat concat_ws concurrent confirm conn connec connect connect_by_iscycle connect_by_isleaf connect_by_root connect_time connection consider consistent constant constraint constraints constructor container content contents context contributors controlfile conv convert convert_tz corr corr_k corr_s corresponding corruption cos cost count count_big counted covar_pop covar_samp cpu_per_call cpu_per_session crc32 create creation critical cross cube cume_dist curdate current current_date current_time current_timestamp current_user cursor curtime customdatum cycle data database databases datafile datafiles datalength date_add date_cache date_format date_sub dateadd datediff datefromparts datename datepart datetime2fromparts day day_to_second dayname dayofmonth dayofweek dayofyear days db_role_change dbtimezone ddl deallocate declare decode decompose decrement decrypt deduplicate def defa defau defaul default defaults deferred defi defin define degrees delayed delegate delete delete_all delimited demand dense_rank depth dequeue des_decrypt des_encrypt des_key_file desc descr descri describ describe descriptor deterministic diagnostics difference dimension direct_load directory disable disable_all disallow disassociate discardfile disconnect diskgroup distinct distinctrow distribute distributed div do document domain dotnet double downgrade drop dumpfile duplicate duration each edition editionable editions element ellipsis else elsif elt empty enable enable_all enclosed encode encoding encrypt end end-exec endian enforced engine engines enqueue enterprise entityescaping eomonth error errors escaped evalname evaluate event eventdata events except exception exceptions exchange exclude excluding execu execut execute exempt exists exit exp expire explain explode export export_set extended extent external external_1 external_2 externally extract failed failed_login_attempts failover failure far fast feature_set feature_value fetch field fields file file_name_convert filesystem_like_logging final finish first first_value fixed flash_cache flashback floor flush following follows for forall force foreign form forma format found found_rows freelist freelists freepools fresh from from_base64 from_days ftp full function general generated get get_format get_lock getdate getutcdate global global_name globally go goto grant grants greatest group group_concat group_id grouping grouping_id groups gtid_subtract guarantee guard handler hash hashkeys having hea head headi headin heading heap help hex hierarchy high high_priority hosts hour hours http id ident_current ident_incr ident_seed identified identity idle_time if ifnull ignore iif ilike ilm immediate import in include including increment index indexes indexing indextype indicator indices inet6_aton inet6_ntoa inet_aton inet_ntoa infile initial initialized initially initrans inmemory inner innodb input insert install instance instantiable instr interface interleaved intersect into invalidate invisible is is_free_lock is_ipv4 is_ipv4_compat is_not is_not_null is_used_lock isdate isnull isolation iterate java join json json_exists keep keep_duplicates key keys kill language large last last_day last_insert_id last_value lateral lax lcase lead leading least leaves left len lenght length less level levels library like like2 like4 likec limit lines link list listagg little ln load load_file lob lobs local localtime localtimestamp locate locator lock locked log log10 log2 logfile logfiles logging logical logical_reads_per_call logoff logon logs long loop low low_priority lower lpad lrtrim ltrim main make_set makedate maketime managed management manual map mapping mask master master_pos_wait match matched materialized max maxextents maximize maxinstances maxlen maxlogfiles maxloghistory maxlogmembers maxsize maxtrans md5 measures median medium member memcompress memory merge microsecond mid migration min minextents minimum mining minus minute minutes minvalue missing mod mode model modification modify module monitoring month months mount move movement multiset mutex name name_const names nan national native natural nav nchar nclob nested never new newline next nextval no no_write_to_binlog noarchivelog noaudit nobadfile nocheck nocompress nocopy nocycle nodelay nodiscardfile noentityescaping noguarantee nokeep nologfile nomapping nomaxvalue nominimize nominvalue nomonitoring none noneditionable nonschema noorder nopr nopro noprom nopromp noprompt norely noresetlogs noreverse normal norowdependencies noschemacheck noswitch not nothing notice notnull notrim novalidate now nowait nth_value nullif nulls num numb numbe nvarchar nvarchar2 object ocicoll ocidate ocidatetime ociduration ociinterval ociloblocator ocinumber ociref ocirefcursor ocirowid ocistring ocitype oct octet_length of off offline offset oid oidindex old on online only opaque open operations operator optimal optimize option optionally or oracle oracle_date oradata ord ordaudio orddicom orddoc order ordimage ordinality ordvideo organization orlany orlvary out outer outfile outline output over overflow overriding package pad parallel parallel_enable parameters parent parse partial partition partitions pascal passing password password_grace_time password_lock_time password_reuse_max password_reuse_time password_verify_function patch path patindex pctincrease pctthreshold pctused pctversion percent percent_rank percentile_cont percentile_disc performance period period_add period_diff permanent physical pi pipe pipelined pivot pluggable plugin policy position post_transaction pow power pragma prebuilt precedes preceding precision prediction prediction_cost prediction_details prediction_probability prediction_set prepare present preserve prior priority private private_sga privileges procedural procedure procedure_analyze processlist profiles project prompt protection public publishingservername purge quarter query quick quiesce quota quotename radians raise rand range rank raw read reads readsize rebuild record records recover recovery recursive recycle redo reduced ref reference referenced references referencing refresh regexp_like register regr_avgx regr_avgy regr_count regr_intercept regr_r2 regr_slope regr_sxx regr_sxy reject rekey relational relative relaylog release release_lock relies_on relocate rely rem remainder rename repair repeat replace replicate replication required reset resetlogs resize resource respect restore restricted result result_cache resumable resume retention return returning returns reuse reverse revoke right rlike role roles rollback rolling rollup round row row_count rowdependencies rowid rownum rows rtrim rules safe salt sample save savepoint sb1 sb2 sb4 scan schema schemacheck scn scope scroll sdo_georaster sdo_topo_geometry search sec_to_time second seconds section securefile security seed segment select self semi sequence sequential serializable server servererror session session_user sessions_per_user set sets settings sha sha1 sha2 share shared shared_pool short show shrink shutdown si_averagecolor si_colorhistogram si_featurelist si_positionalcolor si_stillimage si_texture siblings sid sign sin size size_t sizes skip slave sleep smalldatetimefromparts smallfile snapshot some soname sort soundex source space sparse spfile split sql sql_big_result sql_buffer_result sql_cache sql_calc_found_rows sql_small_result sql_variant_property sqlcode sqldata sqlerror sqlname sqlstate sqrt square standalone standby start starting startup statement static statistics stats_binomial_test stats_crosstab stats_ks_test stats_mode stats_mw_test stats_one_way_anova stats_t_test_ stats_t_test_indep stats_t_test_one stats_t_test_paired stats_wsr_test status std stddev stddev_pop stddev_samp stdev stop storage store stored str str_to_date straight_join strcmp strict string struct stuff style subdate subpartition subpartitions substitutable substr substring subtime subtring_index subtype success sum suspend switch switchoffset switchover sync synchronous synonym sys sys_xmlagg sysasm sysaux sysdate sysdatetimeoffset sysdba sysoper system system_user sysutcdatetime table tables tablespace tablesample tan tdo template temporary terminated tertiary_weights test than then thread through tier ties time time_format time_zone timediff timefromparts timeout timestamp timestampadd timestampdiff timezone_abbr timezone_minute timezone_region to to_base64 to_date to_days to_seconds todatetimeoffset trace tracking transaction transactional translate translation treat trigger trigger_nestlevel triggers trim truncate try_cast try_convert try_parse type ub1 ub2 ub4 ucase unarchived unbounded uncompress under undo unhex unicode uniform uninstall union unique unix_timestamp unknown unlimited unlock unnest unpivot unrecoverable unsafe unsigned until untrusted unusable unused update updated upgrade upped upper upsert url urowid usable usage use use_stored_outlines user user_data user_resources users using utc_date utc_timestamp uuid uuid_short validate validate_password_strength validation valist value values var var_samp varcharc vari varia variab variabl variable variables variance varp varraw varrawc varray verify version versions view virtual visible void wait wallet warning warnings week weekday weekofyear wellformed when whene whenev wheneve whenever where while whitespace window with within without work wrapped xdb xml xmlagg xmlattributes xmlcast xmlcolattval xmlelement xmlexists xmlforest xmlindex xmlnamespaces xmlpi xmlquery xmlroot xmlschema xmlserialize xmltable xmltype xor year year_to_month years yearweek",literal:"true false null unknown",built_in:"array bigint binary bit blob bool boolean char character date dec decimal float int int8 integer interval number numeric real record serial serial8 smallint text time timestamp tinyint varchar varchar2 varying void"},contains:[{className:"string",begin:"'",end:"'",contains:[{begin:"''"}]},{className:"string",begin:'"',end:'"',contains:[{begin:'""'}]},{className:"string",begin:"`",end:"`"},e.C_NUMBER_MODE,e.C_BLOCK_COMMENT_MODE,t,e.HASH_COMMENT_MODE]},e.C_BLOCK_COMMENT_MODE,t,e.HASH_COMMENT_MODE]}}}());
hljs.registerLanguage("swift",function(){"use strict";return function(e){var i={keyword:"#available #colorLiteral #column #else #elseif #endif #file #fileLiteral #function #if #imageLiteral #line #selector #sourceLocation _ __COLUMN__ __FILE__ __FUNCTION__ __LINE__ Any as as! as? associatedtype associativity break case catch class continue convenience default defer deinit didSet do dynamic dynamicType else enum extension fallthrough false fileprivate final for func get guard if import in indirect infix init inout internal is lazy left let mutating nil none nonmutating open operator optional override postfix precedence prefix private protocol Protocol public repeat required rethrows return right self Self set static struct subscript super switch throw throws true try try! try? Type typealias unowned var weak where while willSet",literal:"true false nil",built_in:"abs advance alignof alignofValue anyGenerator assert assertionFailure bridgeFromObjectiveC bridgeFromObjectiveCUnconditional bridgeToObjectiveC bridgeToObjectiveCUnconditional c compactMap contains count countElements countLeadingZeros debugPrint debugPrintln distance dropFirst dropLast dump encodeBitsAsWords enumerate equal fatalError filter find getBridgedObjectiveCType getVaList indices insertionSort isBridgedToObjectiveC isBridgedVerbatimToObjectiveC isUniquelyReferenced isUniquelyReferencedNonObjC join lazy lexicographicalCompare map max maxElement min minElement numericCast overlaps partition posix precondition preconditionFailure print println quickSort readLine reduce reflect reinterpretCast reverse roundUpToAlignment sizeof sizeofValue sort split startsWith stride strideof strideofValue swap toString transcode underestimateCount unsafeAddressOf unsafeBitCast unsafeDowncast unsafeUnwrap unsafeReflect withExtendedLifetime withObjectAtPlusZero withUnsafePointer withUnsafePointerToObject withUnsafeMutablePointer withUnsafeMutablePointers withUnsafePointer withUnsafePointers withVaList zip"},n=e.COMMENT("/\\*","\\*/",{contains:["self"]}),t={className:"subst",begin:/\\\(/,end:"\\)",keywords:i,contains:[]},a={className:"string",contains:[e.BACKSLASH_ESCAPE,t],variants:[{begin:/"""/,end:/"""/},{begin:/"/,end:/"/}]},r={className:"number",begin:"\\b([\\d_]+(\\.[\\deE_]+)?|0x[a-fA-F0-9_]+(\\.[a-fA-F0-9p_]+)?|0b[01_]+|0o[0-7_]+)\\b",relevance:0};return t.contains=[r],{name:"Swift",keywords:i,contains:[a,e.C_LINE_COMMENT_MODE,n,{className:"type",begin:"\\b[A-Z][\\wÀ-ʸ']*[!?]"},{className:"type",begin:"\\b[A-Z][\\wÀ-ʸ']*",relevance:0},r,{className:"function",beginKeywords:"func",end:"{",excludeEnd:!0,contains:[e.inherit(e.TITLE_MODE,{begin:/[A-Za-z$_][0-9A-Za-z$_]*/}),{begin:/</,end:/>/},{className:"params",begin:/\(/,end:/\)/,endsParent:!0,keywords:i,contains:["self",r,a,e.C_BLOCK_COMMENT_MODE,{begin:":"}],illegal:/["']/}],illegal:/\[|%/},{className:"class",beginKeywords:"struct protocol class extension enum",keywords:i,end:"\\{",excludeEnd:!0,contains:[e.inherit(e.TITLE_MODE,{begin:/[A-Za-z$_][\u00C0-\u02B80-9A-Za-z$_]*/})]},{className:"meta",begin:"(@discardableResult|@warn_unused_result|@exported|@lazy|@noescape|@NSCopying|@NSManaged|@objc|@objcMembers|@convention|@required|@noreturn|@IBAction|@IBDesignable|@IBInspectable|@IBOutlet|@infix|@prefix|@postfix|@autoclosure|@testable|@available|@nonobjc|@NSApplicationMain|@UIApplicationMain|@dynamicMemberLookup|@propertyWrapper)"},{beginKeywords:"import",end:/$/,contains:[e.C_LINE_COMMENT_MODE,n]}]}}}());
hljs.registerLanguage("typescript",function(){"use strict";return function(e){var n={keyword:"in if for while finally var new function do return void else break catch instanceof with throw case default try this switch continue typeof delete let yield const class public private protected get set super static implements enum export import declare type namespace abstract as from extends async await",literal:"true false null undefined NaN Infinity",built_in:"eval isFinite isNaN parseFloat parseInt decodeURI decodeURIComponent encodeURI encodeURIComponent escape unescape Object Function Boolean Error EvalError InternalError RangeError ReferenceError StopIteration SyntaxError TypeError URIError Number Math Date String RegExp Array Float32Array Float64Array Int16Array Int32Array Int8Array Uint16Array Uint32Array Uint8Array Uint8ClampedArray ArrayBuffer DataView JSON Intl arguments require module console window document any number boolean string void Promise"},r={className:"meta",begin:"@[A-Za-z$_][0-9A-Za-z$_]*"},a={begin:"\\(",end:/\)/,keywords:n,contains:["self",e.QUOTE_STRING_MODE,e.APOS_STRING_MODE,e.NUMBER_MODE]},t={className:"params",begin:/\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:n,contains:[e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE,r,a]},s={className:"number",variants:[{begin:"\\b(0[bB][01]+)n?"},{begin:"\\b(0[oO][0-7]+)n?"},{begin:e.C_NUMBER_RE+"n?"}],relevance:0},i={className:"subst",begin:"\\$\\{",end:"\\}",keywords:n,contains:[]},o={begin:"html`",end:"",starts:{end:"`",returnEnd:!1,contains:[e.BACKSLASH_ESCAPE,i],subLanguage:"xml"}},c={begin:"css`",end:"",starts:{end:"`",returnEnd:!1,contains:[e.BACKSLASH_ESCAPE,i],subLanguage:"css"}},E={className:"string",begin:"`",end:"`",contains:[e.BACKSLASH_ESCAPE,i]};return i.contains=[e.APOS_STRING_MODE,e.QUOTE_STRING_MODE,o,c,E,s,e.REGEXP_MODE],{name:"TypeScript",aliases:["ts"],keywords:n,contains:[{className:"meta",begin:/^\s*['"]use strict['"]/},e.APOS_STRING_MODE,e.QUOTE_STRING_MODE,o,c,E,e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE,s,{begin:"("+e.RE_STARTERS_RE+"|\\b(case|return|throw)\\b)\\s*",keywords:"return throw case",contains:[e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE,e.REGEXP_MODE,{className:"function",begin:"(\\(.*?\\)|"+e.IDENT_RE+")\\s*=>",returnBegin:!0,end:"\\s*=>",contains:[{className:"params",variants:[{begin:e.IDENT_RE},{begin:/\(\s*\)/},{begin:/\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:n,contains:["self",e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE]}]}]}],relevance:0},{className:"function",beginKeywords:"function",end:/[\{;]/,excludeEnd:!0,keywords:n,contains:["self",e.inherit(e.TITLE_MODE,{begin:"[A-Za-z$_][0-9A-Za-z$_]*"}),t],illegal:/%/,relevance:0},{beginKeywords:"constructor",end:/[\{;]/,excludeEnd:!0,contains:["self",t]},{begin:/module\./,keywords:{built_in:"module"},relevance:0},{beginKeywords:"module",end:/\{/,excludeEnd:!0},{beginKeywords:"interface",end:/\{/,excludeEnd:!0,keywords:"interface extends"},{begin:/\$[(.]/},{begin:"\\."+e.IDENT_RE,relevance:0},r,a]}}}());
hljs.registerLanguage("yaml",function(){"use strict";return function(e){var n={className:"string",relevance:0,variants:[{begin:/'/,end:/'/},{begin:/"/,end:/"/},{begin:/\S+/}],contains:[e.BACKSLASH_ESCAPE,{className:"template-variable",variants:[{begin:"{{",end:"}}"},{begin:"%{",end:"}"}]}]};return{name:"YAML",case_insensitive:!0,aliases:["yml","YAML"],contains:[{className:"attr",variants:[{begin:"\\w[\\w :\\/.-]*:(?=[ \t]|$)"},{begin:'"\\w[\\w :\\/.-]*":(?=[ \t]|$)'},{begin:"'\\w[\\w :\\/.-]*':(?=[ \t]|$)"}]},{className:"meta",begin:"^---s*$",relevance:10},{className:"string",begin:"[\\|>]([0-9]?[+-])?[ ]*\\n( *)[\\S ]+\\n(\\2[\\S ]+\\n?)*"},{begin:"<%[%=-]?",end:"[%-]?%>",subLanguage:"ruby",excludeBegin:!0,excludeEnd:!0,relevance:0},{className:"type",begin:"!"+e.UNDERSCORE_IDENT_RE},{className:"type",begin:"!!"+e.UNDERSCORE_IDENT_RE},{className:"meta",begin:"&"+e.UNDERSCORE_IDENT_RE+"$"},{className:"meta",begin:"\\*"+e.UNDERSCORE_IDENT_RE+"$"},{className:"bullet",begin:"\\-(?=[ ]|$)",relevance:0},e.HASH_COMMENT_MODE,{beginKeywords:"true false yes no null",keywords:{literal:"true false yes no null"}},{className:"number",begin:"\\b[0-9]{4}(-[0-9][0-9]){0,2}([Tt \\t][0-9][0-9]?(:[0-9][0-9]){2})?(\\.[0-9]*)?([ \\t])*(Z|[-+][0-9][0-9]?(:[0-9][0-9])?)?\\b"},{className:"number",begin:e.C_NUMBER_RE+"\\b"},n]}}}());

    </script>
    <link href="docs.css?ver=1756876866" rel="stylesheet" type="text/css" media="all">
    <script type="text/javascript">
var styleElement = document.createElement('style');
document.getElementsByTagName("head")[0].appendChild(styleElement);

document.addEventListener('load', () => {
  hljs.getLanguage('ruby').keywords += ' args tick ';
})

document.addEventListener("animationstart", e => {
  if (e.animationName == "node-ready") {
    hljs.highlightBlock(e.target);
    e.target.classList.add("fade-in");
    // get the first href within the visible hrefs
  }
});
 </script>
 </head>
  <body>
    <div id='table-of-contents'>
      <li><a class='header-1' href='docs.html'>Docs</a></li>
      <li><a class='header-1' href='samples.html'>Samples</a></li>
<h1>Table Of Contents</h1>
<ul>
<li><a class='header-1' href='#--getting-started'>Getting Started</a></li>
<ul><li><a class='header-2' href='#---introduction'>Introduction</a></li></ul><ul><li><a class='header-2' href='#---prerequisites'>Prerequisites</a></li></ul><ul><li><a class='header-2' href='#---the-game-loop'>The Game Loop</a></li></ul><ul><li><a class='header-2' href='#---breakdown-of-the-tick-method'>Breakdown Of The tick Method</a></li></ul><ul><li><a class='header-2' href='#---rendering-a-sprite'>Rendering A Sprite</a></li></ul><ul><li><a class='header-2' href='#---coordinate-system-and-virtual-canvas'>Coordinate System and Virtual Canvas</a></li></ul><ul><li><a class='header-2' href='#---game-state'>Game State</a></li></ul><ul><li><a class='header-2' href='#---there-is-no-delta-time'>There Is No Delta Time</a></li></ul><ul><li><a class='header-2' href='#---handling-user-input'>Handling User Input</a></li></ul><ul><li><a class='header-2' href='#---coding-on-a-raspberry-pi'>Coding On A Raspberry Pi</a></li></ul><ul><li><a class='header-2' href='#---conclusion'>Conclusion</a></li></ul><li><a class='header-1' href='#--starting-a-new-dragonruby-project'>Starting a New DragonRuby Project</a></li>
<ul><li><a class='header-2' href='#---public-repos'>Public Repos</a></li></ul><ul><ul><li><a class='header-3' href='#----option-1--recommended-'>Option 1 (Recommended)</a></li></ul></ul><ul><ul><li><a class='header-3' href='#----option-2--restrictions-apply-'>Option 2 (Restrictions Apply)</a></li></ul></ul><ul><li><a class='header-2' href='#---private-repos-/-commercial-games'>Private Repos / Commercial Games</a></li></ul><li><a class='header-1' href='#--deploying-to-itch-io'>Deploying To Itch.io</a></li>
<ul><li><a class='header-2' href='#---creating-your-game-landing-page'>Creating Your Game Landing Page</a></li></ul><ul><li><a class='header-2' href='#---update-your-game-s-metadata'>Update Your Game's Metadata</a></li></ul><ul><li><a class='header-2' href='#---building-your-game-for-distribution'>Building Your Game For Distribution</a></li></ul><ul><ul><li><a class='header-3' href='#----browser-game-settings'>Browser Game Settings</a></li></ul></ul><ul><ul><li><a class='header-3' href='#----consider-adding-pause-when-game-is-in-background'>Consider Adding Pause When Game is In Background</a></li></ul></ul><ul><ul><li><a class='header-3' href='#----consider-adding-a-request-to-review-your-game-in-game'>Consider Adding a Request to Review Your Game In-Game</a></li></ul></ul><li><a class='header-1' href='#--deploying-to-mobile-devices'>Deploying To Mobile Devices</a></li>
<ul><li><a class='header-2' href='#---deploying-to-ios'>Deploying to iOS</a></li></ul><ul><li><a class='header-2' href='#---deploying-to-android'>Deploying to Android</a></li></ul><li><a class='header-1' href='#--dragonruby-s-philosophy'>DragonRuby's Philosophy</a></li>
<ul><li><a class='header-2' href='#---challenge-the-status-quo'>Challenge The Status Quo</a></li></ul><ul><li><a class='header-2' href='#---continuity-of-design'>Continuity of Design</a></li></ul><ul><li><a class='header-2' href='#---release-early-and-often'>Release Early and Often</a></li></ul><ul><li><a class='header-2' href='#---sustainable-and-ethical-monetization'>Sustainable And Ethical Monetization</a></li></ul><ul><li><a class='header-2' href='#---sustainable-and-ethical-open-source'>Sustainable And Ethical Open Source</a></li></ul><ul><li><a class='header-2' href='#---people-over-entities'>People Over Entities</a></li></ul><ul><li><a class='header-2' href='#---building-a-game-should-be-fun-and-bring-happiness'>Building A Game Should Be Fun And Bring Happiness</a></li></ul><ul><li><a class='header-2' href='#---real-world-application-drives-features'>Real World Application Drives Features</a></li></ul><li><a class='header-1' href='#--frequently-asked-questions--comments--and-concerns'>Frequently Asked Questions, Comments, and Concerns</a></li>
<ul><li><a class='header-2' href='#---frequently-asked-questions'>Frequently Asked Questions</a></li></ul><ul><ul><li><a class='header-3' href='#----what-is-dragonruby-llp-'>What is DragonRuby LLP?</a></li></ul></ul><ul><ul><li><a class='header-3' href='#----what-is-dragonruby-'>What is DragonRuby?</a></li></ul></ul><ul><ul><li><a class='header-3' href='#----what-does-multilevel-cross-platform-mean-'>What does Multilevel Cross-platform mean?</a></li></ul></ul><ul><ul><li><a class='header-3' href='#----how-is-dragonruby-different-than-mri-'>How is DragonRuby different than MRI?</a></li></ul></ul><ul><ul><li><a class='header-3' href='#----does-dragonruby-support-gems-'>Does DragonRuby support Gems?</a></li></ul></ul><ul><ul><li><a class='header-3' href='#----does-dragonruby-have-a-repl/irb-'>Does DragonRuby have a REPL/IRB?</a></li></ul></ul><ul><ul><li><a class='header-3' href='#----does-dragonruby-support-pry-or-have-any-other-debugging-facilities-'>Does DragonRuby support pry or have any other debugging facilities?</a></li></ul></ul><ul><li><a class='header-2' href='#---frequent-comments-about-ruby-as-a-language-choice'>Frequent Comments About Ruby as a Language Choice</a></li></ul><ul><ul><li><a class='header-3' href='#----but-ruby-is-dead-'>But Ruby is dead.</a></li></ul></ul><ul><ul><li><a class='header-3' href='#----but-ruby-is-slow-'>But Ruby is slow.</a></li></ul></ul><ul><ul><li><a class='header-3' href='#----dynamic-languages-are-slow-'>Dynamic languages are slow.</a></li></ul></ul><ul><li><a class='header-2' href='#---frequent-concerns'>Frequent Concerns</a></li></ul><ul><ul><li><a class='header-3' href='#----dragonruby-is-not-open-source--that-s-not-right-'>DragonRuby is not open source. That's not right.</a></li></ul></ul><ul><ul><li><a class='header-3' href='#----dragonruby-is-for-pay--you-should-offer-a-free-version-'>DragonRuby is for pay. You should offer a free version.</a></li></ul></ul><ul><ul><li><a class='header-3' href='#----what-if-i-build-something-with-dragonruby--but-dragonruby-llp-becomes-insolvent-'>What if I build something with DragonRuby, but DragonRuby LLP becomes insolvent.</a></li></ul></ul><ul><li><a class='header-2' href='#---troubleshoot-performance'>Troubleshoot Performance</a></li></ul><ul><li><a class='header-2' href='#---accessing-files'>Accessing files</a></li></ul><ul><li><a class='header-2' href='#---using--args-state--to-store-your-game-state'>Using <code>args.state</code> To Store Your Game State</a></li></ul><li><a class='header-1' href='#--recipies-'>RECIPIES:</a></li>
<ul><li><a class='header-2' href='#---how-to-determine-what-frame-you-are-on'>How To Determine What Frame You Are On</a></li></ul><ul><li><a class='header-2' href='#---how-to-get-current-framerate'>How To Get Current Framerate</a></li></ul><ul><li><a class='header-2' href='#---how-to-render-a-sprite-using-an-array'>How To Render A Sprite Using An Array</a></li></ul><ul><li><a class='header-2' href='#---rendering-a-sprite-using-a--hash-'>Rendering a Sprite Using a <code>Hash</code></a></li></ul><ul><li><a class='header-2' href='#---how-to-render-a-label'>How To Render A Label</a></li></ul><ul><li><a class='header-2' href='#---a-colored-label'>A Colored Label</a></li></ul><ul><li><a class='header-2' href='#---extended-label-properties'>Extended Label Properties</a></li></ul><ul><li><a class='header-2' href='#---rendering-a-label-as-a--hash-'>Rendering A Label As A <code>Hash</code></a></li></ul><ul><li><a class='header-2' href='#---getting-the-size-of-a-piece-of-text'>Getting The Size Of A Piece Of Text</a></li></ul><ul><li><a class='header-2' href='#---rendering-labels-with-new-line-characters-and-wrapping'>Rendering Labels With New Line Characters And Wrapping</a></li></ul><ul><li><a class='header-2' href='#---how-to-play-a-sound'>How To Play A Sound</a></li></ul><li><a class='header-1' href='#--outputs---args-outputs--'>Outputs (<code>args.outputs</code>)</a></li>
<ul><li><a class='header-2' href='#---collection-render-orders'>Collection Render Orders</a></li></ul><ul><li><a class='header-2' href='#----primitives-'><code>primitives</code></a></li></ul><ul><li><a class='header-2' href='#----debug-'><code>debug</code></a></li></ul><ul><ul><li><a class='header-3' href='#----string-primitives'>String Primitives</a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----watch-'><code>watch</code></a></li></ul></ul><ul><li><a class='header-2' href='#----solids-'><code>solids</code></a></li></ul><ul><ul><li><a class='header-3' href='#----array-render-primitive'>Array Render Primitive</a></li></ul></ul><ul><ul><li><a class='header-3' href='#----hash-render-primitive'>Hash Render Primitive</a></li></ul></ul><ul><ul><li><a class='header-3' href='#----class-render-primitive'>Class Render Primitive</a></li></ul></ul><ul><li><a class='header-2' href='#----borders-'><code>borders</code></a></li></ul><ul><li><a class='header-2' href='#----sprites-'><code>sprites</code></a></li></ul><ul><ul><li><a class='header-3' href='#----properties'>Properties</a></li></ul></ul><ul><ul><li><a class='header-3' href='#----array-render-primitive'>Array Render Primitive</a></li></ul></ul><ul><ul><li><a class='header-3' href='#----hash-render-primitive'>Hash Render Primitive</a></li></ul></ul><ul><ul><li><a class='header-3' href='#----class-render-primitive'>Class Render Primitive</a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----attr_sprite-'><code>attr_sprite</code></a></li></ul></ul><ul><li><a class='header-2' href='#----lines-'><code>lines</code></a></li></ul><ul><ul><li><a class='header-3' href='#----array-render-primitive'>Array Render Primitive</a></li></ul></ul><ul><ul><li><a class='header-3' href='#----hash-render-primitive'>Hash Render Primitive</a></li></ul></ul><ul><ul><li><a class='header-3' href='#----class-render-primitive'>Class Render Primitive</a></li></ul></ul><ul><li><a class='header-2' href='#----labels-'><code>labels</code></a></li></ul><ul><ul><li><a class='header-3' href='#----array-render-primitive'>Array Render Primitive</a></li></ul></ul><ul><ul><li><a class='header-3' href='#----hash-render-primitive'>Hash Render Primitive</a></li></ul></ul><ul><ul><li><a class='header-3' href='#----class-render-primitive'>Class Render Primitive</a></li></ul></ul><ul><li><a class='header-2' href='#---render-targets-------operator-'>Render Targets (<code>[]</code> operator)</a></li></ul><ul><li><a class='header-2' href='#----screenshots-'><code>screenshots</code></a></li></ul><ul><ul><li><a class='header-3' href='#----chroma-key--making-a-color-transparent-'>Chroma key (Making a color transparent)</a></li></ul></ul><ul><li><a class='header-2' href='#---shaders'>Shaders</a></li></ul><ul><ul><li><a class='header-3' href='#-----shader_path-'><code>shader_path</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----shader_uniforms-'><code>shader_uniforms</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----shader_tex-1-15--'><code>shader_tex(1-15)</code></a></li></ul></ul><li><a class='header-1' href='#--inputs---args-inputs--'>Inputs (<code>args.inputs</code>)</a></li>
<ul><li><a class='header-2' href='#----last_active-'><code>last_active</code></a></li></ul><ul><li><a class='header-2' href='#----last_active_at-'><code>last_active_at</code></a></li></ul><ul><li><a class='header-2' href='#----last_active_global_at-'><code>last_active_global_at</code></a></li></ul><ul><li><a class='header-2' href='#----locale-'><code>locale</code></a></li></ul><ul><li><a class='header-2' href='#----up-'><code>up</code></a></li></ul><ul><li><a class='header-2' href='#----down-'><code>down</code></a></li></ul><ul><li><a class='header-2' href='#----left-'><code>left</code></a></li></ul><ul><li><a class='header-2' href='#----right-'><code>right</code></a></li></ul><ul><li><a class='header-2' href='#----left_right-'><code>left_right</code></a></li></ul><ul><li><a class='header-2' href='#----left_right_perc-'><code>left_right_perc</code></a></li></ul><ul><li><a class='header-2' href='#----left_right_directional-'><code>left_right_directional</code></a></li></ul><ul><li><a class='header-2' href='#----left_right_directional_perc-'><code>left_right_directional_perc</code></a></li></ul><ul><li><a class='header-2' href='#----up_down-'><code>up_down</code></a></li></ul><ul><li><a class='header-2' href='#----up_down_directional-'><code>up_down_directional</code></a></li></ul><ul><li><a class='header-2' href='#----up_down_perc-'><code>up_down_perc</code></a></li></ul><ul><li><a class='header-2' href='#----text-'><code>text</code></a></li></ul><ul><li><a class='header-2' href='#---mouse---args-inputs-mouse--'>Mouse (<code>args.inputs.mouse</code>)</a></li></ul><ul><ul><li><a class='header-3' href='#-----has_focus-'><code>has_focus</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----x-'><code>x</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----y-'><code>y</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----previous_x-'><code>previous_x</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----previous_y-'><code>previous_y</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----relative_x-'><code>relative_x</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----relative_y-'><code>relative_y</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----inside_rect--rect-'><code>inside_rect? rect</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----inside_circle--center_point--radius-'><code>inside_circle? center_point, radius</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----moved-'><code>moved</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----button_left-'><code>button_left</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----button_middle-'><code>button_middle</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----button_right-'><code>button_right</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----button_bits-'><code>button_bits</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----wheel-'><code>wheel</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----click--or--down----previous_click----up-'><code>click</code> OR <code>down</code>, <code>previous_click</code>, <code>up</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----key_down-'><code>key_down</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----key_held-'><code>key_held</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----key_up-'><code>key_up</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----buttons-'><code>buttons</code></a></li></ul></ul><ul><li><a class='header-2' href='#---touch'>Touch</a></li></ul><ul><ul><li><a class='header-3' href='#-----args-inputs-touch-'><code>args.inputs.touch</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----args-inputs-finger_left-'><code>args.inputs.finger_left</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----args-inputs-finger_right-'><code>args.inputs.finger_right</code></a></li></ul></ul><ul><li><a class='header-2' href='#---controller---args-inputs-controller_-one-four---'>Controller (<code>args.inputs.controller_(one-four)</code>)</a></li></ul><ul><ul><li><a class='header-3' href='#-----connected-'><code>connected</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----name-'><code>name</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----active-'><code>active</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----up-'><code>up</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----down-'><code>down</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----left-'><code>left</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----right-'><code>right</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----left_right-'><code>left_right</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----up_down-'><code>up_down</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#------left-right-_analog_x_raw-'><code>(left|right)_analog_x_raw</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#------left-right-_analog_y_raw-'><code>(left|right)_analog_y_raw</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#------left-right-_analog_x_perc-'><code>(left|right)_analog_x_perc</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#------left-right-_analog_y_perc-'><code>(left|right)_analog_y_perc</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----dpad_up----directional_up-'><code>dpad_up</code>, <code>directional_up</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----dpad_down----directional_down-'><code>dpad_down</code>, <code>directional_down</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----dpad_left----directional_left-'><code>dpad_left</code>, <code>directional_left</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----dpad_right----directional_right-'><code>dpad_right</code>, <code>directional_right</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#------a-b-x-y-l1-r1-l2-r2-l3-r3-start-select--'><code>(a|b|x|y|l1|r1|l2|r2|l3|r3|start|select)</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----truthy_keys-'><code>truthy_keys</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----key_down-'><code>key_down</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----key_held-'><code>key_held</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----key_up-'><code>key_up</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----left_analog_active--threshold_raw---threshold_perc---'><code>left_analog_active?(threshold_raw:, threshold_perc:)</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----right_analog_active--threshold_raw---threshold_perc---'><code>right_analog_active?(threshold_raw:, threshold_perc:)</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#------left-right-_analog_angle-'><code>(left|right)_analog_angle</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----analog_dead_zone-'><code>analog_dead_zone</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----key_state--key--'><code>key_STATE?(key)</code></a></li></ul></ul><ul><li><a class='header-2' href='#---keyboard-or-controller-on---args-inputs-key_-down-up-held---'>Keyboard or Controller On (<code>args.inputs.key_(down|up|held)</code>)</a></li></ul><ul><li><a class='header-2' href='#---keyboard---args-inputs-keyboard--'>Keyboard (<code>args.inputs.keyboard</code>)</a></li></ul><ul><ul><li><a class='header-3' href='#-----active-'><code>active</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----has_focus-'><code>has_focus</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----up-'><code>up</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----down-'><code>down</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----left-'><code>left</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----right-'><code>right</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----left_right-'><code>left_right</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----up_down-'><code>up_down</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#----keyboard-properties'>Keyboard properties</a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----keycodes-'><code>keycodes</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----char-'><code>char</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----key_state--key--'><code>key_STATE?(key)</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----keys-'><code>keys</code></a></li></ul></ul><li><a class='header-1' href='#--runtime---args-gtk--'>Runtime (<code>args.gtk</code>)</a></li>
<ul><li><a class='header-2' href='#---class-macros'>Class Macros</a></li></ul><ul><ul><li><a class='header-3' href='#-----attr-'><code>attr</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----attr_gtk-'><code>attr_gtk</code></a></li></ul></ul><ul><li><a class='header-2' href='#---indie-and-pro-functions'>Indie and Pro Functions</a></li></ul><ul><ul><li><a class='header-3' href='#-----get_pixels-'><code>get_pixels</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----dlopen-'><code>dlopen</code></a></li></ul></ul><ul><li><a class='header-2' href='#---environment-and-utility-functions'>Environment and Utility Functions</a></li></ul><ul><ul><li><a class='header-3' href='#-----calcstringbox-'><code>calcstringbox</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----request_quit-'><code>request_quit</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----quit_requested--'><code>quit_requested?</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----set_window_fullscreen-'><code>set_window_fullscreen</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----window_fullscreen--'><code>window_fullscreen?</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----set_window_scale-'><code>set_window_scale</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----set_window_title-'><code>set_window_title</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----platform--'><code>platform?</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----production--'><code>production?</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----platform_mappings-'><code>platform_mappings</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----system-'><code>system</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----exec-'><code>exec</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----show_cursor-'><code>show_cursor</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----hide_cursor-'><code>hide_cursor</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----cursor_shown--'><code>cursor_shown?</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----set_mouse_grab-'><code>set_mouse_grab</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----set_system_cursor-'><code>set_system_cursor</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----set_cursor-'><code>set_cursor</code></a></li></ul></ul><ul><li><a class='header-2' href='#---file-io-functions'>File IO Functions</a></li></ul><ul><ul><li><a class='header-3' href='#-----list_files-'><code>list_files</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----stat_file-'><code>stat_file</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----read_file-'><code>read_file</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----write_file-'><code>write_file</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----append_file-'><code>append_file</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----delete_file-'><code>delete_file</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----parse_json-'><code>parse_json</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----parse_json_file-'><code>parse_json_file</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----parse_xml-'><code>parse_xml</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----parse_xml_file-'><code>parse_xml_file</code></a></li></ul></ul><ul><li><a class='header-2' href='#---network-io-functions'>Network IO Functions</a></li></ul><ul><ul><li><a class='header-3' href='#-----http_get-'><code>http_get</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----http_post-'><code>http_post</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----http_post_body-'><code>http_post_body</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----start_server--'><code>start_server!</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----http_get-'><code>http_get</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----http_post-'><code>http_post</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----http_post_body-'><code>http_post_body</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----start_server--'><code>start_server!</code></a></li></ul></ul><ul><li><a class='header-2' href='#---developer-support-functions'>Developer Support Functions</a></li></ul><ul><ul><li><a class='header-3' href='#-----version-'><code>version</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----version_pro--'><code>version_pro?</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----game_version-'><code>game_version</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----reset-'><code>reset</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----reset_next_tick-'><code>reset_next_tick</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----reset_sprite-'><code>reset_sprite</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----reset_sprites-'><code>reset_sprites</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----calcspritebox-'><code>calcspritebox</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----current_framerate-'><code>current_framerate</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----framerate_diagnostics_primitives-'><code>framerate_diagnostics_primitives</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----warn_array_primitives--'><code>warn_array_primitives!</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----benchmark-'><code>benchmark</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----notify--'><code>notify!</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----notify_extended--'><code>notify_extended!</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----slowmo--'><code>slowmo!</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----show_console-'><code>show_console</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----hide_console-'><code>hide_console</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----enable_console-'><code>enable_console</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----disable_console-'><code>disable_console</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----disable_reset_via_ctrl_r-'><code>disable_reset_via_ctrl_r</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----disable_controller_config-'><code>disable_controller_config</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----enable_controller_config-'><code>enable_controller_config</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----start_recording-'><code>start_recording</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----stop_recording-'><code>stop_recording</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----cancel_recording-'><code>cancel_recording</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----start_replay-'><code>start_replay</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----stop_replay-'><code>stop_replay</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----get_base_dir-'><code>get_base_dir</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----get_game_dir-'><code>get_game_dir</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----get_game_dir_url-'><code>get_game_dir_url</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----open_game_dir-'><code>open_game_dir</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----write_file_root-'><code>write_file_root</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----append_file_root-'><code>append_file_root</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----argv-'><code>argv</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----cli_arguments-'><code>cli_arguments</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----download_stb_rb-_raw--'><code>download_stb_rb(_raw)</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----reload_history-'><code>reload_history</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----reload_history_pending-'><code>reload_history_pending</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----reload_if_needed-'><code>reload_if_needed</code></a></li></ul></ul><li><a class='header-1' href='#--state---args-state--'>State (<code>args.state</code>)</a></li>
<ul><li><a class='header-2' href='#----entity_id-'><code>entity_id</code></a></li></ul><ul><li><a class='header-2' href='#----entity_type-'><code>entity_type</code></a></li></ul><ul><li><a class='header-2' href='#----created_at-'><code>created_at</code></a></li></ul><ul><li><a class='header-2' href='#----created_at_elapsed-'><code>created_at_elapsed</code></a></li></ul><ul><li><a class='header-2' href='#----global_created_at-'><code>global_created_at</code></a></li></ul><ul><li><a class='header-2' href='#----global_created_at_elapsed-'><code>global_created_at_elapsed</code></a></li></ul><ul><li><a class='header-2' href='#----as_hash-'><code>as_hash</code></a></li></ul><ul><li><a class='header-2' href='#----tick_count-'><code>tick_count</code></a></li></ul><li><a class='header-1' href='#--geometry'>Geometry</a></li>
<li><a class='header-1' href='#--cvars-/-configuration-/-game-metadata---args-cvars--'>CVars / Configuration / Game Metadata (<code>args.cvars</code>)</a></li>
<ul><li><a class='header-2' href='#---available-configuration'>Available Configuration</a></li></ul><li><a class='header-1' href='#--layout'>Layout</a></li>
<ul><li><a class='header-2' href='#----rect-'><code>rect</code></a></li></ul><ul><li><a class='header-2' href='#----debug_primitives-'><code>debug_primitives</code></a></li></ul><li><a class='header-1' href='#--grid'>Grid</a></li>
<ul><li><a class='header-2' href='#----orientation-'><code>orientation</code></a></li></ul><ul><li><a class='header-2' href='#----orientation_changed--'><code>orientation_changed?</code></a></li></ul><ul><li><a class='header-2' href='#----origin_name-'><code>origin_name</code></a></li></ul><ul><li><a class='header-2' href='#----origin_bottom_left--'><code>origin_bottom_left!</code></a></li></ul><ul><li><a class='header-2' href='#----origin_center--'><code>origin_center!</code></a></li></ul><ul><li><a class='header-2' href='#----portrait--'><code>portrait?</code></a></li></ul><ul><li><a class='header-2' href='#----landscape--'><code>landscape?</code></a></li></ul><ul><li><a class='header-2' href='#---grid-property-categorizations'>Grid Property Categorizations</a></li></ul><ul><li><a class='header-2' href='#----bottom-'><code>bottom</code></a></li></ul><ul><li><a class='header-2' href='#----top-'><code>top</code></a></li></ul><ul><li><a class='header-2' href='#----left-'><code>left</code></a></li></ul><ul><li><a class='header-2' href='#----right-'><code>right</code></a></li></ul><ul><li><a class='header-2' href='#----rect-'><code>rect</code></a></li></ul><ul><li><a class='header-2' href='#----w-'><code>w</code></a></li></ul><ul><li><a class='header-2' href='#----h-'><code>h</code></a></li></ul><ul><li><a class='header-2' href='#----aspect_ratio_w-'><code>aspect_ratio_w</code></a></li></ul><ul><li><a class='header-2' href='#----aspect_ratio_h-'><code>aspect_ratio_h</code></a></li></ul><ul><li><a class='header-2' href='#---hd--highdpi--and-all-screen-modes'>HD, HighDPI, and All Screen Modes</a></li></ul><ul><ul><li><a class='header-3' href='#----all-screen-properties'>All Screen Properties</a></li></ul></ul><ul><ul><li><a class='header-3' href='#----texture-atlases'>Texture Atlases</a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----native_scale-'><code>native_scale</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----render_scale-'><code>render_scale</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----texture_scale-'><code>texture_scale</code></a></li></ul></ul><ul><ul><li><a class='header-3' href='#-----texture_scale_enum-'><code>texture_scale_enum</code></a></li></ul></ul><li><a class='header-1' href='#--array'>Array</a></li>
<ul><li><a class='header-2' href='#----map_2d-'><code>map_2d</code></a></li></ul><ul><li><a class='header-2' href='#----include_any--'><code>include_any?</code></a></li></ul><ul><li><a class='header-2' href='#----any_intersect_rect--'><code>any_intersect_rect?</code></a></li></ul><ul><li><a class='header-2' href='#----map_2d-'><code>map_2d</code></a></li></ul><ul><li><a class='header-2' href='#----each-'><code>each</code></a></li></ul><ul><li><a class='header-2' href='#----reject_nil-'><code>reject_nil</code></a></li></ul><ul><li><a class='header-2' href='#----reject_false-'><code>reject_false</code></a></li></ul><ul><li><a class='header-2' href='#----product-'><code>product</code></a></li></ul><li><a class='header-1' href='#---numeric-'><code>Numeric</code></a></li>
<ul><li><a class='header-2' href='#----frame_index-'><code>frame_index</code></a></li></ul><ul><li><a class='header-2' href='#----elapsed_time-'><code>elapsed_time</code></a></li></ul><ul><li><a class='header-2' href='#----elapsed--'><code>elapsed?</code></a></li></ul><ul><li><a class='header-2' href='#----to_sf-'><code>to_sf</code></a></li></ul><ul><li><a class='header-2' href='#----to_si-'><code>to_si</code></a></li></ul><ul><li><a class='header-2' href='#----new--'><code>new?</code></a></li></ul><li><a class='header-1' href='#---kernel-'><code>Kernel</code></a></li>
<ul><li><a class='header-2' href='#----tick_count-'><code>tick_count</code></a></li></ul><ul><li><a class='header-2' href='#----global_tick_count-'><code>global_tick_count</code></a></li></ul></ul>    </div>
    <div id='content'>
<h1 id='--getting-started'>Getting Started <a style='font-size: small; float: right;' href='#--getting-started'>link</a></h1> 
<p>
This is a tutorial written by Ryan C Gordon (a Juggernaut in the industry who has contracted to Valve, Epic, Activision, and EA... check out his Wikipedia page: https://en.wikipedia.org/wiki/Ryan_C._Gordon).
</p>
<h2 id='---introduction'>Introduction <a style='font-size: small; float: right;' href='#---introduction'>link</a></h2> 
<p>
Welcome!
</p>
<p>
Here's just a little push to get you started if you're new to programming or game development.
</p>
<p>
If you want to write a game, it's no different than writing any other program for any other framework: there are a few simple rules that might be new to you, but more or less programming is programming no matter what you are building.
</p>
<p>
Did you not know that? Did you think you couldn't write a game because you're a "web guy" or you're writing Java at a desk job? Stop letting people tell you that you can't, because you already have everything you need.
</p>
<p>
Here, we're going to be programming in a language called "Ruby." In the interest of full disclosure, I (Ryan Gordon) wrote the C parts of this toolkit and Ruby looks a little strange to me (Amir Rajan wrote the Ruby parts, discounting the parts I mangled), but I'm going to walk you through the basics because we're all learning together, and if you mostly think of yourself as someone that writes C (or C++, C#, Objective-C), PHP, or Java, then you're only a step behind me right now.
</p>
<h2 id='---prerequisites'>Prerequisites <a style='font-size: small; float: right;' href='#---prerequisites'>link</a></h2> 
<p>
Here's the most important thing you should know: Ruby lets you do some complicated things really easily, and you can learn that stuff later. I'm going to show you one or two cool tricks, but that's all.
</p>
<p>
Do you know what an if statement is? A for-loop? An array? That's all you'll need to start.
</p>
<h2 id='---the-game-loop'>The Game Loop <a style='font-size: small; float: right;' href='#---the-game-loop'>link</a></h2> 
<p>
Ok, here are few rules with regards to game development with GTK:
</p>
<ul>
<li>Your game is all going to happen under one function ...</li>
<li>that runs 60 times a second ...</li>
<li>and has to tell the computer what to draw each time.</li>
</ul>
<p>
That's an entire video game in one run-on sentence.
</p>
<p>
Here's that function. You're going to want to put this in mygame/app/main.rb, because that's where we'll look for it by default. Load it up in your favorite text editor.
</p>
<pre><code class="ruby">def tick args
  args.outputs.labels &lt;&lt; [580, 400, 'Hello World!']
end
</code></pre>
<p>
Now run dragonruby ...did you get a window with "Hello World!" written in it? Good, you're officially a game developer!
</p>
<h2 id='---breakdown-of-the-tick-method'>Breakdown Of The tick Method <a style='font-size: small; float: right;' href='#---breakdown-of-the-tick-method'>link</a></h2> 
<p>
<code>mygame/app/main.rb</code>, is where the Ruby source code is located. This looks a little strange, so I'll break it down line by line. In Ruby, a '#' character starts a single-line comment, so I'll talk about this inline.
</p>
<pre><code class="ruby"># This "def"ines a function, named "tick," which takes a single argument
# named "args". DragonRuby looks for this function and calls it every
# frame, 60 times a second. "args" is a magic structure with lots of
# information in it. You can set variables in there for your own game state,
# and every frame it will updated if keys are pressed, joysticks moved,
# mice clicked, etc.
def tick args

  # One of the things in "args" is the "outputs" object that your game uses
  # to draw things. Afraid of rendering APIs? No problem. In DragonRuby,
  # you use arrays to draw things and we figure out the details.
  # If you want to draw text on the screen, you give it an array (the thing
  # in the [ brackets ]), with an X and Y coordinate and the text to draw.
  # The "&lt;&lt;" thing says "append this array onto the list of them at
  # args.outputs.labels)
  args.outputs.labels &lt;&lt; [580, 400, 'Hello World!']
end
</code></pre>
<p>
Once your <code>tick</code> function finishes, we look at all the arrays you made and figure out how to draw it. You don't need to know about graphics APIs. You're just setting up some arrays! DragonRuby clears out these arrays every frame, so you just need to add what you need _right now_ each time.
</p>
<h2 id='---rendering-a-sprite'>Rendering A Sprite <a style='font-size: small; float: right;' href='#---rendering-a-sprite'>link</a></h2> 
<p>
Now let's spice this up a little.
</p>
<p>
We're going to add some graphics. Each 2D image in DragonRuby is called a "sprite," and to use them, you just make sure they exist in a reasonable file format (png, jpg, gif, bmp, etc) and specify them by filename. The first time you use one, DragonRuby will load it and keep it in video memory for fast access in the future. If you use a filename that doesn't exist, you get a fun checkerboard pattern!
</p>
<p>
There's a "dragonruby.png" file included, just to get you started. Let's have it draw every frame with our text:
</p>
<pre><code class="ruby">def tick args
  args.outputs.labels  &lt;&lt; [580, 400, 'Hello World!']
  args.outputs.sprites &lt;&lt; [576, 100, 128, 101, 'dragonruby.png']
end
</code></pre>
<p>
NOTE:
</p>
<p>
 Pro Tip: you don't have to restart DragonRuby to test your changes; when you save main.rb, DragonRuby will notice and reload your program.
</p>
<p>
That <code>.sprites</code> line says "add a sprite to the list of sprites we're drawing, and draw it at position (576, 100) at a size of 128x101 pixels". You can find the image to draw at dragonruby.png.
</p>
<h2 id='---coordinate-system-and-virtual-canvas'>Coordinate System and Virtual Canvas <a style='font-size: small; float: right;' href='#---coordinate-system-and-virtual-canvas'>link</a></h2> 
<p>
Quick note about coordinates: (0, 0) is the bottom left corner of the screen, and positive numbers go up and to the right. This is more "geometrically correct," even if it's not how you remember doing 2D graphics, but we chose this for a simpler reason: when you're making Super Mario Brothers and you want Mario to jump, you should be able to add to Mario's y position as he goes up and subtract as he falls. It makes things easier to understand.
</p>
<p>
Also: your game screen is _always_ 1280x720 pixels. If you resize the window, we will scale and letterbox everything appropriately, so you never have to worry about different resolutions.
</p>
<p>
Ok, now we have an image on the screen, let's animate it:
</p>
<pre><code class="ruby">def tick args
  args.state.rotation  ||= 0
  args.outputs.labels  &lt;&lt; [580, 400, 'Hello World!' ]
  args.outputs.sprites &lt;&lt; [576, 100, 128, 101, 'dragonruby.png', args.state.rotation]
  args.state.rotation  -= 1
end
</code></pre>
<p>
Now you can see that this function is getting called a lot!
</p>
<h2 id='---game-state'>Game State <a style='font-size: small; float: right;' href='#---game-state'>link</a></h2> 
<p>
Here's a fun Ruby thing: <code>args.state.rotation ||= 0</code> is shorthand for "if args.state.rotation isn't initialized, set it to zero." It's a nice way to embed your initialization code right next to where you need the variable.
</p>
<p>
<code>args.state</code> is a place you can hang your own data. It's an open data structure that allows you to define properties that are arbitrarily nested. You don't need to define any kind of class.
</p>
<p>
In this case, the current rotation of our sprite, which is happily spinning at 60 frames per second. If you don't specify rotation (or alpha, or color modulation, or a source rectangle, etc), DragonRuby picks a reasonable default, and the array is ordered by the most likely things you need to tell us: position, size, name.
</p>
<h2 id='---there-is-no-delta-time'>There Is No Delta Time <a style='font-size: small; float: right;' href='#---there-is-no-delta-time'>link</a></h2> 
<p>
One thing we decided to do in DragonRuby is not make you worry about delta time: your function runs at 60 frames per second (about 16 milliseconds) and that's that. Having to worry about framerate is something massive triple-AAA games do, but for fun little 2D games? You'd have to work really hard to not hit 60fps. All your drawing is happening on a GPU designed to run Fortnite quickly; it can definitely handle this.
</p>
<p>
Since we didn't make you worry about delta time, you can just move the rotation by 1 every time and it works without you having to keep track of time and math. Want it to move faster? Subtract 2.
</p>
<h2 id='---handling-user-input'>Handling User Input <a style='font-size: small; float: right;' href='#---handling-user-input'>link</a></h2> 
<p>
Now, let's move that image around.
</p>
<pre><code class="ruby">def tick args
  args.state.rotation ||= 0
  args.state.x ||= 576
  args.state.y ||= 100

  if args.inputs.mouse.click
    args.state.x = args.inputs.mouse.click.point.x - 64
    args.state.y = args.inputs.mouse.click.point.y - 50
  end

  args.outputs.labels  &lt;&lt; [580, 400, 'Hello World!']
  args.outputs.sprites &lt;&lt; [args.state.x,
                           args.state.y,
                           128,
                           101,
                           'dragonruby.png',
                           args.state.rotation]

  args.state.rotation -= 1
end
</code></pre>
<p>
Everywhere you click your mouse, the image moves there. We set a default location for it with <code>args.state.x ||= 576</code>, and then we change those variables when we see the mouse button in action. You can get at the keyboard and game controllers in similar ways.
</p>
<h2 id='---coding-on-a-raspberry-pi'>Coding On A Raspberry Pi <a style='font-size: small; float: right;' href='#---coding-on-a-raspberry-pi'>link</a></h2> 
<p>
We have only tested DragonRuby on a Raspberry Pi 3, Models B and B+, but we believe it _should_ work on any model with comparable specs.
</p>
<p>
If you're running DragonRuby Game Toolkit on a Raspberry Pi, or trying to run a game made with the Toolkit on a Raspberry Pi, and it's really really slow-- like one frame every few seconds--then there's likely a simple fix.
</p>
<p>
You're probably running a desktop environment: menus, apps, web browsers, etc. This is okay! Launch the terminal app and type:
</p>
<pre><code class="bash">do raspi-config
</code></pre>
<p>
It'll ask you for your password (if you don't know, try "raspberry"), and then give you a menu of options. Find your way to "Advanced Options", then "GL Driver", and change this to "GL (Full KMS)" ... not "fake KMS," which is also listed there. Save and reboot. In theory, this should fix the problem.
</p>
<p>
If you're _still_ having problems and have a Raspberry Pi 2 or better, go back to raspi-config and head over to "Advanced Options", "Memory split," and give the GPU 256 megabytes. You might be able to avoid this for simple games, as this takes RAM away from the system and reserves it for graphics. You can also try 128 megabytes as a gentler option.
</p>
<p>
Note that you can also run DragonRuby without X11 at all: if you run it from a virtual terminal it will render fullscreen and won't need the "Full KMS" option. This might be attractive if you want to use it as a game console sort of thing, or develop over ssh, or launch it from RetroPie, etc.
</p>
<h2 id='---conclusion'>Conclusion <a style='font-size: small; float: right;' href='#---conclusion'>link</a></h2> 
<p>
There is a lot more you can do with DragonRuby, but now you've already got just about everything you need to make a simple game. After all, even the most fancy games are just creating objects and moving them around. Experiment a little. Add a few more things and have them interact in small ways. Want something to go away? Just don't add it to <code>args.output</code> anymore.
</p>
<h1 id='--starting-a-new-dragonruby-project'>Starting a New DragonRuby Project <a style='font-size: small; float: right;' href='#--starting-a-new-dragonruby-project'>link</a></h1> 
<p>
The DragonRuby zip that contains the engine is a complete, self contained project structure. To create a new project, unzip the zip file again in its entirety and use that as a starting point for another game. This is the recommended approach to starting a new project.
</p>
<p>
NOTE:
</p>
<p>
 It's strongly recommended that you do NOT keep DragonRuby Game Toolkit in a shared location and instead unzip a clean copy for every game (and commit everything to source control).
</p>
<p>
File access functions are sandoxed and assume that the <code>dragonruby</code> binary lives alongside the game you are building. Do not expect file access functions to return correct values if you are attempting to run the <code>dragonruby</code> binary from a shared location. It's recommended that the directory structure contained in the zip is not altered and games are built using that starting directory structure.
</p>
<h2 id='---public-repos'>Public Repos <a style='font-size: small; float: right;' href='#---public-repos'>link</a></h2> 
<h3 id='----option-1--recommended-'>Option 1 (Recommended) <a style='font-size: small; float: right;' href='#----option-1--recommended-'>link</a></h3> 
<p>
Your public repository needs only to contain the contents of <code>./mygame</code>. This approach is the cleanest and doesn't require your <code>.gitignore</code> to be polluted with DragonRuby specific files.
</p>
<h3 id='----option-2--restrictions-apply-'>Option 2 (Restrictions Apply) <a style='font-size: small; float: right;' href='#----option-2--restrictions-apply-'>link</a></h3> 
<p>
NOTE:
</p>
<p>
 Do NOT commit <code>dragonruby-publish(.exe)</code>, or <code>dragonruby-bind(.exe)</code>.
</p>
<pre><code class="ruby">dragonruby
dragonruby.exe
dragonruby-publish
dragonruby-publish.exe
dragonruby-bind
dragonruby-bind.exe
/tmp/
/builds/
/logs/
/samples/
/docs/
/.dragonruby/
</code></pre>
<p>
If you'd like people who do not own a DragonRuby license to run your game, you may include the <code>dragonruby(.exe)</code> binary within the repo. This permission is granted in good-faith and can be revoked if abused.
</p>
<h2 id='---private-repos-/-commercial-games'>Private Repos / Commercial Games <a style='font-size: small; float: right;' href='#---private-repos-/-commercial-games'>link</a></h2> 
<p>
The following <code>.gitignore</code> should be used for private repositories (commercial games).
</p>
<pre><code class="ruby">/tmp/
/logs/
</code></pre>
<p>
You'll notice that everything else is committed to source control (even the <code>./samples</code>, <code>./docs</code>, and <code>./builds</code> directory).
</p>
<p>
NOTE:
</p>
<p>
 The DragonRuby binary/package is designed to be committed in its entirety with your source code (it’s why we keep it small). This protects the “shelf life” for commercial games. 3 years from now, we might be on a vastly different version of the engine. But you know that the code you’ve written will definitely work with the version that was committed to source control.
</p>
<h1 id='--deploying-to-itch-io'>Deploying To Itch.io <a style='font-size: small; float: right;' href='#--deploying-to-itch-io'>link</a></h1> 
<p>
NOTE:
</p>
<p>
 It's strongly recommended that you do NOT keep DragonRuby Game Toolkit in a shared location and instead unzip a clean copy for every game (and commit everything to source control).
</p>
<p>
File access functions are sandoxed and assume that the <code>dragonruby</code> binary lives alongside the game you are building. Do not expect file access functions to return correct values if you are attempting to run the <code>dragonruby</code> binary from a shared location. It's recommended that the directory structure contained in the zip is not altered and games are built using that starting directory structure.
</p>
<p>
Once you've built your game, you're all set to deploy! Good luck in your game dev journey and if you get stuck, come to the Discord channel!
</p>
<h2 id='---creating-your-game-landing-page'>Creating Your Game Landing Page <a style='font-size: small; float: right;' href='#---creating-your-game-landing-page'>link</a></h2> 
<p>
Log into Itch.io and go to &lt;https://itch.io/game/new&gt;.
</p>
<ul>
<li>  Title: Give your game a Title. This value represents your \<code>gametitle\</code>.</li>
<li>  Project URL: Set your project url. This value represents your \<code>gameid\</code>.</li>
<li>  Classification: Keep this as Game.</li>
<li>  Kind of Project: Select HTML from the drop down list. Don't worry,
    the HTML project type &lt;span class="underline"&gt;also supports binary downloads&lt;/span&gt;.</li>
<li>  Uploads: Skip this section for now.</li>
</ul>
<p>
You can fill out all the other options later.
</p>
<h2 id='---update-your-game-s-metadata'>Update Your Game's Metadata <a style='font-size: small; float: right;' href='#---update-your-game-s-metadata'>link</a></h2> 
<p>
Point your text editor at <code>mygame/metadata/game_metadata.txt</code> and make it look like this:
</p>
<p>
NOTE: Remove the <code>#</code> at the beginning of each line.
</p>
<pre><code class="txt">devid=bob
devtitle=Bob The Game Developer
gameid=mygame
gametitle=My Game
version=0.1
</code></pre>
<p>
The <code>devid</code> property is the username you use to log into Itch.io. The <code>devtitle</code> is your name or company name (it can contain spaces). The <code>gameid</code> is the Project URL value. The <code>gametitle</code> is the name of your game (it can contain spaces). The <code>version</code> can be any <code>major.minor</code> number format.
</p>
<h2 id='---building-your-game-for-distribution'>Building Your Game For Distribution <a style='font-size: small; float: right;' href='#---building-your-game-for-distribution'>link</a></h2> 
<p>
Open up the terminal and run this from the command line:
</p>
<pre><code class="sh">./dragonruby-publish --package mygame
</code></pre>
<p>
NOTE:
</p>
<p>
 If you're on Windows, don't put the "./" on the front. That's a Mac and Linux thing.
</p>
<p>
A directory called <code>./build</code> will be created that contains your binaries. You can upload this to Itch.io manually.
</p>
<h3 id='----browser-game-settings'>Browser Game Settings <a style='font-size: small; float: right;' href='#----browser-game-settings'>link</a></h3> 
<p>
For the HTML version of your game, the following configuration is required for your game to run correctly:
</p>
<ul>
<li>  Check the checkbox labeled <code>This file will be played in the browser</code> for the html version of your game (it's one of the zip files you'll upload).</li>
<li>  Ensure that <code>Embed options -&gt; SharedArrayBuffer support</code> is checked.</li>
<li>  Be sure to set the <code>Viewport dimensions</code> to <code>1280x720</code> for landscape games or your game will not be positioned correctly on your Itch.io page.</li>
<li>  Be sure to set the <code>Viewport dimensions</code> to <code>540x960</code> for portrait games or your game will not be positioned correctly on your Itch.io page.</li>
</ul>
<p>
For subsequent updates you can use an automated deployment to Itch.io:
</p>
<pre><code class="sh">./dragonruby-publish mygame
</code></pre>
<p>
DragonRuby will package &lt;span class="underline"&gt;and publish&lt;/span&gt; your game to itch.io! Tell your friends to go to your game's very own webpage and buy it!
</p>
<p>
If you make changes to your game, just re-run dragonruby-publish and it'll update the downloads for you.
</p>
<h3 id='----consider-adding-pause-when-game-is-in-background'>Consider Adding Pause When Game is In Background <a style='font-size: small; float: right;' href='#----consider-adding-pause-when-game-is-in-background'>link</a></h3> 
<p>
It's a good idea to pause the game if it doesn't have focus. Here's an example of how to do that
</p>
<pre><code class="ruby">def tick args
  # if the keyboard doesn't have focus, and the game is in production mode, and it isn't the first tick
  if (!args.inputs.keyboard.has_focus &amp;&amp;
      args.gtk.production &amp;&amp;
      Kernel.tick_count != 0)
    args.outputs.background_color = [0, 0, 0]
    args.outputs.labels &lt;&lt; { x: 640,
                             y: 360,
                             text: "Game Paused (click to resume).",
                             alignment_enum: 1,
                             r: 255, g: 255, b: 255 }
    # consider setting all audio volume to 0.0
  else
    # perform your regular tick function
  end
end
</code></pre>
<p>
If you want your game to run at full speed even when it's in the background, add the following line to <code>mygame/metadata/cvars.txt</code>:
</p>
<p>
    renderer.background_sleep=0
</p>
<h3 id='----consider-adding-a-request-to-review-your-game-in-game'>Consider Adding a Request to Review Your Game In-Game <a style='font-size: small; float: right;' href='#----consider-adding-a-request-to-review-your-game-in-game'>link</a></h3> 
<p>
Getting reviews of your game are extremely important and it's recommended that you put an option to review within the game itself. You can use <code>args.gtk.openurl</code> plus a review URL. Here's an example:
</p>
<pre><code class="ruby">def tick args
  # render the review button
  args.state.review_button ||= { x:    640 - 50,
                                 y:    360 - 25,
                                 w:    100,
                                 h:    50,
                                 path: :pixel,
                                 r:    0,
                                 g:    0,
                                 b:    0 }
  args.outputs.sprites &lt;&lt; args.state.review_button
  args.outputs.labels &lt;&lt; { x: 640,
                           y: 360,
                           anchor_x: 0.5,
                           anchor_y: 0.5,
                           text: "Review" }

  # check to see if the review button was clicked
  if args.inputs.mouse.intersect_rect? args.state.review_button
    # open platform specific review urls
    if args.gtk.platform? :ios
      # your app id is provided at Apple's Developer Portal (numeric value)
      args.gtk.openurl "itms-apps://itunes.apple.com/app/idYOURGAMEID?action=write-review"
    elsif args.gtk.platform? :android
      # your app id is the name of your android package
      args.gtk.openurl "https://play.google.com/store/apps/details?id=YOURGAMEID"
    elsif args.gtk.platform? :web
      # if they are playing the web version of the game, take them to the purchase page on itch
      args.gtk.openurl "https://amirrajan.itch.io/YOURGAMEID/purchase"
    else
      # if they are playing the desktop version of the game, take them to itch's rating page
      args.gtk.openurl "https://amirrajan.itch.io/YOURGAMEID/rate?source=game"
    end
  end
end
</code></pre>
<h1 id='--deploying-to-mobile-devices'>Deploying To Mobile Devices <a style='font-size: small; float: right;' href='#--deploying-to-mobile-devices'>link</a></h1> 
<p>
NOTE:
</p>
<p>
 It's strongly recommended that you do NOT keep DragonRuby Game Toolkit in a shared location and instead unzip a clean copy for every game (and commit everything to source control).
</p>
<p>
File access functions are sandoxed and assume that the <code>dragonruby</code> binary lives alongside the game you are building. Do not expect file access functions to return correct values if you are attempting to run the <code>dragonruby</code> binary from a shared location. It's recommended that the directory structure contained in the zip is not altered and games are built using that starting directory structure.
</p>
<p>
If you have a Pro subscription, you also have the capability to deploy to mobile devices.
</p>
<h2 id='---deploying-to-ios'>Deploying to iOS <a style='font-size: small; float: right;' href='#---deploying-to-ios'>link</a></h2> 
<p>
To deploy to iOS, you need to have a Mac running MacOS Catalina, an iOS device, and an active/paid Developer Account with Apple. From the Console type: <code>$wizards.ios.start</code> and you will be guided through the deployment process.
</p>
<ul>
<li><code>$wizards.ios.start env: :dev</code> will deploy to an iOS device connected via USB.</li>
<li><code>$wizards.ios.start env: :hotload</code> will deploy to an iOS device connected via USB with hotload enabled.</li>
<li><code>$wizards.ios.start env: :sim</code> will deploy to the iOS simulator.</li>
<li><code>$wizards.ios.start env: :prod</code> will package your game for distribution via Apple's AppStore.</li>
</ul>
<h2 id='---deploying-to-android'>Deploying to Android <a style='font-size: small; float: right;' href='#---deploying-to-android'>link</a></h2> 
<p>
To deploy to Android, you need to have an Android emulator/device, and an environment that is able to run Android SDK. <code>dragonruby-publish</code> will create an APK for you. From there, you can sign the APK and install it to your device. The signing and installation procedure varies from OS to OS. Here's an example of what the command might look like:
</p>
<p>
NOTE:
</p>
<p>
 Be sure you specify <code>packageid=TLD.YOURCOMPANY.YOURGAME</code> (reverse domain name convention) within <code>metadata/game_metadata.txt</code> before running <code>dragonruby-publish</code> (for example: <code>packageid=com.tempuri.mygame</code>).
</p>
<pre><code class="sh"># generating a keystore (one time creation, save key to ./profiles for safe keeping)
keytool -genkey -v -keystore APP.keystore -alias mygame -keyalg RSA -keysize 2048 -validity 10000

# signing binaries
apksigner sign --min-sdk-version 26 --ks ./profiles/APP.keystore ./builds/APP-android.apk
apksigner sign --min-sdk-version 26 --ks ./profiles/APP.keystore ./builds/APP-googleplay.aab

# Deploying APK to a local device/emulator
adb install ./builds/APP-android.apk

# Deploying Google Play APK to a local device/emulator
bundletool build-apks --bundle=./builds/APP-googleplay.aab --output=./builds/app.apks --mode=universal
mv ./builds/app.apks ./builds/app.zip
cd ./builds
rm -rf tmp
mkdir tmp
cd tmp
unzip ../app.zip
cp ./universal.apk ./universal.zip
unzip ./universal.zip

# uninstall, install, launch
adb shell am force-stop PACKAGEID
adb uninstall PACKAGEID
adb install ./universal.apk
adb shell am start -n PACKAGEID/PACKAGEID.DragonRubyActivity

# read logs of device
adb logcat -e mygame
</code></pre>
<h1 id='--dragonruby-s-philosophy'>DragonRuby's Philosophy <a style='font-size: small; float: right;' href='#--dragonruby-s-philosophy'>link</a></h1> 
<p>
The following tenants of DragonRuby are what set us apart from other game engines. Given that Game Toolkit is a relatively new engine, there are definitely features that are missing. So having a big check list of "all the cool things" is not this engine's forte. This is compensated with a strong commitment to the following principles.
</p>
<h2 id='---challenge-the-status-quo'>Challenge The Status Quo <a style='font-size: small; float: right;' href='#---challenge-the-status-quo'>link</a></h2> 
<p>
Game engines of today are in a local maximum and don't take into consideration the challenges of this day and age. Unity and GameMaker specifically rot your brain. It's not sufficient to say:
</p>
<p>
But that's how we've always done it.
</p>
<p>
It's a hard pill to swallow, but forget blindly accepted best practices and try to figure out the underlying motivation for a specific approach to game development. Collaborate with us.
</p>
<h2 id='---continuity-of-design'>Continuity of Design <a style='font-size: small; float: right;' href='#---continuity-of-design'>link</a></h2> 
<p>
There is a programming idiom in software called "The Pit of Success". The term normalizes upfront pain as a necessity/requirement in the hopes that the investment will yield dividends "when you become successful" or "when the code becomes more complicated". This approach to development is strongly discouraged by us. It leads to over-architected and unnecessary code; creates barriers to rapid prototyping and shipping a game; and overwhelms beginners who are new to the engine or programming in general.
</p>
<p>
DragonRuby's philosophy is to provide multiple options across the "make it fast" vs "make it right" spectrum, with incremental/intuitive transitions between the options provided. A concrete example of this philosophy would be render primitives: the spectrum of options allows renderable constructs that take the form of tuples/arrays (easy to pickup, simple, and fast to code/prototype with), hashes (a little more work, but gives you the ability to add additional properties), open and strict entities (more work than hashes, but yields cleaner apis), and finally - if you really need full power/flexibility in rendering - classes (which take the most amount of code and programming knowledge to create).
</p>
<h2 id='---release-early-and-often'>Release Early and Often <a style='font-size: small; float: right;' href='#---release-early-and-often'>link</a></h2> 
<p>
The biggest mistake game devs make is spending too much time in isolation building their game. Release something, however small, and release it soon.
</p>
<p>
Stop worrying about everything being pixel perfect. Don't wait until your game is 100% complete. Build your game publicly and iterate. Post in the #show-and-tell channel in the community Discord. You'll find a lot of support and encouragement there.
</p>
<p>
Real artists ship. Remember that.
</p>
<h2 id='---sustainable-and-ethical-monetization'>Sustainable And Ethical Monetization <a style='font-size: small; float: right;' href='#---sustainable-and-ethical-monetization'>link</a></h2> 
<p>
We all aspire to put food on the table doing what we love. Whether it is building games, writing tools to support game development, or anything in between.
</p>
<p>
Charge a fair amount of money for the things you create. It's expected and encouraged within the community. Give what you create away for free to those that can't afford it.
</p>
<p>
If you are gainfully employed, pay full price for the things you use. If you do end up getting something at a discount, pay the difference "forward" to someone else.
</p>
<h2 id='---sustainable-and-ethical-open-source'>Sustainable And Ethical Open Source <a style='font-size: small; float: right;' href='#---sustainable-and-ethical-open-source'>link</a></h2> 
<p>
This goes hand in hand with sustainable and ethical monetization. The current state of open source is not sustainable. There is an immense amount of contributor burnout. Users of open source expect everything to be free, and few give back. This is a problem we want to fix (we're still trying to figure out the best solution).
</p>
<p>
So, don't be "that guy" in the Discord that says "DragonRuby should be free and open source!" You will be personally flogged by Amir.
</p>
<h2 id='---people-over-entities'>People Over Entities <a style='font-size: small; float: right;' href='#---people-over-entities'>link</a></h2> 
<p>
We prioritize the endorsement of real people over faceless entities. This game engine, and other products we create, are not insignificant line items of a large company. And you aren't a generic "commodity" or "corporate resource". So be active in the community Discord and you'll reap the benefits as more devs use DragonRuby.
</p>
<h2 id='---building-a-game-should-be-fun-and-bring-happiness'>Building A Game Should Be Fun And Bring Happiness <a style='font-size: small; float: right;' href='#---building-a-game-should-be-fun-and-bring-happiness'>link</a></h2> 
<p>
We will prioritize the removal of pain. The aesthetics of Ruby make it such a joy to work with, and we want to capture that within the engine.
</p>
<h2 id='---real-world-application-drives-features'>Real World Application Drives Features <a style='font-size: small; float: right;' href='#---real-world-application-drives-features'>link</a></h2> 
<p>
We are bombarded by marketing speak day in and day out. We don't do that here. There are things that are really great in the engine, and things that need a lot of work. Collaborate with us so we can help you reach your goals. Ask for features you actually need as opposed to anything speculative.
</p>
<p>
We want DragonRuby to -actually- help you build the game you want to build (as opposed to sell you something piece of demoware that doesn't work).
</p>
<h1 id='--frequently-asked-questions--comments--and-concerns'>Frequently Asked Questions, Comments, and Concerns <a style='font-size: small; float: right;' href='#--frequently-asked-questions--comments--and-concerns'>link</a></h1> 
<p>
Here are questions, comments, and concerns that frequently come up.
</p>
<h2 id='---frequently-asked-questions'>Frequently Asked Questions <a style='font-size: small; float: right;' href='#---frequently-asked-questions'>link</a></h2> 
<h3 id='----what-is-dragonruby-llp-'>What is DragonRuby LLP? <a style='font-size: small; float: right;' href='#----what-is-dragonruby-llp-'>link</a></h3> 
<p>
DragonRuby LLP is a partnership of four devs who came together with the goal of bringing the aesthetics and joy of Ruby, everywhere possible.
</p>
<p>
Under DragonRuby LLP, we offer a number of products (with more on the way):
</p>
<ul>
<li>Game Toolkit (GTK): A 2D game engine that is compatible with modern gaming platforms.</li>
<li>RubyMotion (RM): A compiler toolchain that allows you to build native, cross-platform mobile apps. [http://rubymotion.com](http://rubymotion.com)</li>
</ul>
<p>
All of the products above leverage a shared core called DragonRuby.
</p>
<p>
NOTE:
</p>
<p>
 NOTE: From an official branding standpoint each one of the products is suffixed with "A DragonRuby LLP Product" tagline. Also, DragonRuby is _one word, title cased_.
</p>
<p>
NOTE:
</p>
<p>
 NOTE: We leave the "A DragonRuby LLP Product" off of this one because that just sounds really weird.
</p>
<p>
NOTE:
</p>
<p>
 NOTE: Devs who use DragonRuby are "Dragon Riders/Riders of Dragons". That's a bad ass identifier huh?
</p>
<h3 id='----what-is-dragonruby-'>What is DragonRuby? <a style='font-size: small; float: right;' href='#----what-is-dragonruby-'>link</a></h3> 
<p>
The response to this question requires a few subparts. First we need to clarify some terms. Specifically _language specification_ vs _runtime_.
</p>
<p>
--Okay... so what is the difference between a language specification and a runtime?--
</p>
<p>
A runtime is an _implementation_ of a language specification. When people say "Ruby," they are usually referring to "the Ruby 3.0+ language specification implemented via the CRuby/MRI Runtime."
</p>
<p>
But, there are many Ruby Runtimes: CRuby/MRI, JRuby, Truffle, Rubinius, Artichoke, and (last but certainly not least) DragonRuby.
</p>
<p>
--Okay... what language specification does DragonRuby use then?--
</p>
<p>
DragonRuby's goal is to be compliant with the ISO/IEC 30170:2012 standard. It's syntax is Ruby 2.x compatible, but also contains semantic changes that help it natively interface with platform specific libraries.
</p>
<p>
--So... why another runtime?--
</p>
<p>
The elevator pitch is:
</p>
<p>
DragonRuby is a Multilevel Cross-platform Runtime. The "multiple levels" within the runtime allows us to target platforms no other Ruby can target: PC, Mac, Linux, Raspberry Pi, WASM, iOS, Android, Nintendo Switch, PS4, Xbox, and Stadia.
</p>
<h3 id='----what-does-multilevel-cross-platform-mean-'>What does Multilevel Cross-platform mean? <a style='font-size: small; float: right;' href='#----what-does-multilevel-cross-platform-mean-'>link</a></h3> 
<p>
There are complexities associated with targeting all the platforms we support. Because of this, the runtime had to be architected in such a way that new platforms could be easily added (which lead to us partitioning the runtime internally):
</p>
<ul>
<li>Level 1 we leverage a good portion of mRuby.</li>
<li>Level 2 consists of optimizations to mRuby we've made given that our target platforms are well known.</li>
<li>Level 3 consists of portable C libraries and their Ruby C-Extensions.</li>
</ul>
<p>
Levels 1 through 3 are fairly commonplace in many runtime implementations (with level 1 being the most portable, and level 3 being the fastest). But the DragonRuby Runtime has taken things a bit further:
</p>
<ul>
<li>Level 4 consists of shared abstractions around hardware I/O and operating system resources. This level leverages open source and proprietary components within Simple DirectMedia Layer (a low level multimedia component library that has been in active development for 22 years and counting).</li>
<li>Level 5 is a code generation layer which creates metadata that allows for native interoperability with host runtime libraries. It also includes OS specific message pump orchestrations.</li>
<li>Level 6 is a Ahead of Time/Just in Time Ruby compiler built with LLVM. This compiler outputs _very_ fast platform specific bitcode, but only supports a subset of the Ruby language specification.</li>
</ul>
<p>
These levels allow us to stay up to date with open source implementations of Ruby; provide fast, native code execution on proprietary platforms; ensure good separation between these two worlds; and provides a means to add new platforms without going insane.
</p>
<p>
--Cool cool. So given that I understand everything to this point, can we answer the original question? What is DragonRuby?--
</p>
<p>
DragonRuby is a Ruby runtime implementation that takes all the lessons we've learned from MRI/CRuby, and merges it with the latest and greatest compiler and OSS technologies.
</p>
<h3 id='----how-is-dragonruby-different-than-mri-'>How is DragonRuby different than MRI? <a style='font-size: small; float: right;' href='#----how-is-dragonruby-different-than-mri-'>link</a></h3> 
<p>
DragonRuby supports a subset of MRI apis. Our target is to support all of mRuby's standard lib. There are challenges to this given the number of platforms we are trying to support (specifically console).
</p>
<h3 id='----does-dragonruby-support-gems-'>Does DragonRuby support Gems? <a style='font-size: small; float: right;' href='#----does-dragonruby-support-gems-'>link</a></h3> 
<p>
DragonRuby does not support gems because that requires the installation of MRI Ruby on the developer's machine (which is a non-starter given that we want DragonRuby to be a zero dependency runtime). While this seems easy for Mac and Linux, it is much harder on Windows and Raspberry Pi. mRuby has taken the approach of having a git repository for compatible gems and we will most likely follow suite: https://github.com/mruby/mgem-list.
</p>
<h3 id='----does-dragonruby-have-a-repl/irb-'>Does DragonRuby have a REPL/IRB? <a style='font-size: small; float: right;' href='#----does-dragonruby-have-a-repl/irb-'>link</a></h3> 
<p>
You can use DragonRuby's Console within the game to inspect object and execute small pieces of code. For more complex pieces of code create a file called repl.rb and put it in mygame/app/repl.rb:
</p>
<p>
Any code you write in there will be executed when you change the file. You can organize different pieces of code using the repl method:
</p>
<pre><code class="ruby">repl do
  puts "hello world"
  puts 1 + 1
end
</code></pre>
<p>
If you use the <code>repl</code> method, the code will be executed and the DragonRuby Console will automatically open so you can see the results (on Mac and Linux, the results will also be printed to the terminal). All puts statements will also be saved to logs/puts.txt. So if you want to stay in your editor and not look at the terminal, or the DragonRuby Console, you can tail this file. 4. To ignore code in repl.rb, instead of commenting it out, prefix repl with the letter x and it'll be ignored.
</p>
<pre><code class="ruby">xrepl do # &lt;------- line is prefixed with an "x"
  puts "hello world"
  puts 1 + 1
end

# This code will be executed when you save the file.
repl do
  puts "Hello"
end

repl do
  puts "This code will also be executed."
end

# use xrepl to "comment out" code
xrepl do
  puts "This code will not be executed because of the x in front of repl".
end
</code></pre>
<h3 id='----does-dragonruby-support-pry-or-have-any-other-debugging-facilities-'>Does DragonRuby support pry or have any other debugging facilities? <a style='font-size: small; float: right;' href='#----does-dragonruby-support-pry-or-have-any-other-debugging-facilities-'>link</a></h3> 
<p>
<code>pry</code> is a gem that assumes you are using the MRI Runtime (which is incompatible with DragonRuby). Eventually DragonRuby will have a pry based experience that is compatible with a debugging infrastructure called LLDB. Take the time to read about LLDB as it shows the challenges in creating something that is compatible.
</p>
<p>
You can use DragonRuby's replay capabilities to troubleshoot:
</p>
<p>
DragonRuby is hot loaded which gives you a very fast feedback loop (if the game throws an exception, it's because of the code you just added). Use <code>./dragonruby mygame --record</code> to create a game play recording that you can use to find the exception (you can replay a recording by executing <code>./dragonruby mygame --replay last_replay.txt</code> or through the DragonRuby Console using <code>$gtk.recording.start_replay "last_replay.txt"</code>.
</p>
<p>
DragonRuby also ships with a unit testing facility. You can invoke the following command to run a test: <code>./dragonruby mygame --test tests/some_ruby_file.rb</code>. Get into the habit of adding debugging facilities within the game itself. You can add drawing primitives to <code>args.outputs.debug</code> that will render on top of your game but will be ignored in a production release.
</p>
<p>
Debugging something that runs at 60fps is (imo) not that helpful. The exception you are seeing could have been because of a change that occurred many frames ago.
</p>
<h2 id='---frequent-comments-about-ruby-as-a-language-choice'>Frequent Comments About Ruby as a Language Choice <a style='font-size: small; float: right;' href='#---frequent-comments-about-ruby-as-a-language-choice'>link</a></h2> 
<h3 id='----but-ruby-is-dead-'>But Ruby is dead. <a style='font-size: small; float: right;' href='#----but-ruby-is-dead-'>link</a></h3> 
<p>
Let's check the official source for the answer to this question: [isrubydead.com](https://isrubydead.com/).
</p>
<p>
On a more serious note, Ruby's _quantity_ levels aren't what they used to be. And that's totally fine. Everyone chases the new and shiny.
</p>
<p>
What really matters is _quality/maturity_. Here's a StackOverflow Survey sorted by highest paid developers: https://insights.stackoverflow.com/survey/2021#section-top-paying-technologies-top-paying-technologies.
</p>
<p>
Let's stop making this comment shall we?
</p>
<h3 id='----but-ruby-is-slow-'>But Ruby is slow. <a style='font-size: small; float: right;' href='#----but-ruby-is-slow-'>link</a></h3> 
<p>
That doesn't make any sense. A language specification can't be slow... it's a language spec. Sure, an _implementation/runtime_ can be slow though, but then we'd have to talk about which runtime.
</p>
<p>
Here are some quick demonstrations of how well DragonRuby Game Toolkit Performs:
</p>
<ul>
<li>DragonRuby vs Unity: https://youtu.be/MFR-dvsllA4</li>
<li>DragonRuby vs PyGame: https://youtu.be/fuRGs6j6fPQ</li>
</ul>
<h3 id='----dynamic-languages-are-slow-'>Dynamic languages are slow. <a style='font-size: small; float: right;' href='#----dynamic-languages-are-slow-'>link</a></h3> 
<p>
They are certainly slower than statically compiled languages. With the processing power and compiler optimizations we have today, dynamic languages like Ruby are _fast enough_.
</p>
<p>
Unless you are writing in some form of intermediate representation by hand, your language of choice also suffers this same fallacy of slow. Like, nothing is faster than a low level assembly-like language. So unless you're writing in that, let's stop making this comment.
</p>
<p>
NOTE:
</p>
<p>
 NOTE: If you _are_ hand writing LLVM IR, we are always open to bringing on new partners with such a skill set. Email us ^_^.
</p>
<h2 id='---frequent-concerns'>Frequent Concerns <a style='font-size: small; float: right;' href='#---frequent-concerns'>link</a></h2> 
<h3 id='----dragonruby-is-not-open-source--that-s-not-right-'>DragonRuby is not open source. That's not right. <a style='font-size: small; float: right;' href='#----dragonruby-is-not-open-source--that-s-not-right-'>link</a></h3> 
<p>
The current state of open source is unsustainable. Contributors work for free, most all open source repositories are severely under-staffed, and burnout from core members is rampant.
</p>
<p>
We believe in open source very strongly. Parts of DragonRuby are in fact, open source. Just not all of it (for legal reasons, and because the IP we've created has value). And we promise that we are looking for (or creating) ways to _sustainably_ open source everything we do.
</p>
<p>
If you have ideas on how we can do this, email us!
</p>
<p>
If the reason above isn't sufficient, then definitely use something else.
</p>
<p>
All this being said, we do have parts of the engine open sourced on GitHub: https://github.com/dragonruby/dragonruby-game-toolkit-contrib/
</p>
<h3 id='----dragonruby-is-for-pay--you-should-offer-a-free-version-'>DragonRuby is for pay. You should offer a free version. <a style='font-size: small; float: right;' href='#----dragonruby-is-for-pay--you-should-offer-a-free-version-'>link</a></h3> 
<p>
If you can afford to pay for DragonRuby, you should (and will). We don't tell authors that they should give us their books for free, and only require payment if we read the entire thing. It's time we stop asking that of software products.
</p>
<p>
That being said, we will _never_ put someone out financially. We have income assistance for anyone that can't afford a license to any one of our products.
</p>
<p>
You qualify for a free, unrestricted license to DragonRuby products if any of the following items pertain to you:
</p>
<ul>
<li>Your income is below $2,000.00 (USD) per month.</li>
<li>You are under 18 years of age.</li>
<li>You are a student of any type: traditional public school, home schooling, college, bootcamp, or online.</li>
<li>You are a teacher, mentor, or parent who wants to teach a kid how to code.</li>
<li>You work/worked in public service or at a charitable organization: for example public office, army, or any 501(c)(3) organization.</li>
</ul>
<p>
Just contact <NAME_EMAIL> with a short explanation of your current situation and he'll set you up. No questions asked.
</p>
<p>
--But still, you should offer a free version. So I can try it out and see if I like it.--
</p>
<p>
You can try our web-based sandbox environment at http://fiddle.dragonruby.org. But it won't do the runtime justice. Or just come to our Discord Channel at http://discord.dragonruby.org and ask questions. We'd be happy to have a one on one video chat with you and show off all the cool stuff we're doing.
</p>
<p>
Seriously just buy it. Get a refund if you don't like it. We make it stupid easy to do so.
</p>
<p>
--I still think you should do a free version. Think of all people who would give it a shot.--
</p>
<p>
Free isn't a sustainable financial model. We don't want to spam your email. We don't want to collect usage data off of you either. We just want to provide quality toolchains to quality developers (as opposed to a large quantity of developers).
</p>
<p>
The people that pay for DragonRuby and make an effort to understand it are the ones we want to build a community around, partner with, and collaborate with. So having that small monetary wall deters entitled individuals that don't value the same things we do.
</p>
<h3 id='----what-if-i-build-something-with-dragonruby--but-dragonruby-llp-becomes-insolvent-'>What if I build something with DragonRuby, but DragonRuby LLP becomes insolvent. <a style='font-size: small; float: right;' href='#----what-if-i-build-something-with-dragonruby--but-dragonruby-llp-becomes-insolvent-'>link</a></h3> 
<p>
We want to be able to work on the stuff we love, every day of our lives. And we'll go to great lengths to make that continues.
</p>
<p>
But, in the event that sad day comes, our partnership bylaws state that _all_ DragonRuby IP that can be legally open sourced, will be released under a permissive license.
</p>
<h2 id='---troubleshoot-performance'>Troubleshoot Performance <a style='font-size: small; float: right;' href='#---troubleshoot-performance'>link</a></h2> 
<ul>
<li>Avoid deep recursive calls.</li>
<li>If you're using <code>Array</code>s for your primitives (<code>args.outputs.sprites &lt;&lt; []</code>), use <code>Hash</code> instead (<code>args.outputs.sprites &lt;&lt; { x: ... }</code>).</li>
<li>If you're using <code>Entity</code> for your primitives (<code>args.outputs.sprites &lt;&lt; args.state.new_entity</code>), use <code>StrictEntity</code> instead (<code>args.outputs.sprites &lt;&lt; args.state.new_entity_strict</code>).</li>
<li>Use <code>.each</code> instead of <code>.map</code> if you don't care about the return value.</li>
<li>When concatenating primitives to outputs, do them in bulk. Instead of:</li>
</ul>
<pre><code class="ruby">args.state.bullets.each do |bullet|
  args.outputs.sprites &lt;&lt; bullet.sprite
end
</code></pre>
<p>
do
</p>
<pre><code class="ruby">args.outputs.sprites &lt;&lt; args.state.bullets.map do |b|
  b.sprite
end
</code></pre>
<ul>
<li>Use <code>args.outputs.static_</code> variant for things that don't change often (take a look at the Basic Gorillas sample app and Dueling Starships sample app to see how <code>static_</code> is leveraged.</li>
<li>Consider using a <code>render_target</code> if you're doing some form of a camera that moves a lot of primitives (take a look at the Render Target sample apps for more info).</li>
<li>Avoid deleting or adding to an array during iteration. Instead of:</li>
</ul>
<pre><code class="ruby">args.state.fx_queue.each |fx|
  fx.count_down ||= 255
  fx.countdown -= 5
  if fx.countdown &lt; 0
    args.state.fx_queue.delete fx
  end
end
</code></pre>
<p>
Do:
</p>
<pre><code class="ruby">args.state.fx_queue.each |fx|
  fx.countdown ||= 255
  fx.countdown -= 5
end

args.state.fx_queue.reject! { |fx| fx.countdown &lt; 0 }
</code></pre>
<h2 id='---accessing-files'>Accessing files <a style='font-size: small; float: right;' href='#---accessing-files'>link</a></h2> 
<p>
DragonRuby uses a sandboxed filesystem which will automatically read from and write to a location appropriate for your platform so you don't have to worry about theses details in your code. You can just use <code>gtk.read_file</code>, <code>gtk.write_file</code>, and <code>gtk.append_file</code> with a relative path and the engine will take care of the rest.
</p>
<p>
The data directories that will be written to in a production build are:
</p>
<ul>
<li>Windows: <code>C:\Users\<USER>\AppData\Roaming\[devtitle]\[gametitle]</code></li>
<li>MacOS: <code>$HOME/Library/Application Support/[gametitle]</code></li>
<li>Linux: <code>$HOME/.local/share/[gametitle]</code></li>
<li>HTML5: The data will be written to the browser's IndexedDB.</li>
</ul>
<p>
The values in square brackets are the values you set in your <code>app/metadata/game_metadata.txt</code> file.
</p>
<p>
When reading files, the engine will first look in the game's data directory and then in the game directory itself. This means that if you write a file to the data directory that already exists in your game directory, the file in the data directory will be used instead of the one that is in your game.
</p>
<p>
When running a development build you will directly write to your game directory (and thus overwrite existing files). This can be useful for built-in development tools like level editors.
</p>
<p>
For more details on the implementation of the sandboxed filesystem, see Ryan C. Gordon's PhysicsFS documentation: <a href='https://icculus.org/physfs/'>https://icculus.org/physfs/</a>
</p>
<p>
IMPORTANT: File access functions are sandoxed and assume that the <code>dragonruby</code> binary lives alongside the game you are building. Do not expect file access functions to return correct values if you are attempting to run the <code>dragonruby</code> binary from a shared location. It's recommended that the directory structure contained in the zip is not altered and games are built using that starter template.
</p>
<h2 id='---using--args-state--to-store-your-game-state'>Using <code>args.state</code> To Store Your Game State <a style='font-size: small; float: right;' href='#---using--args-state--to-store-your-game-state'>link</a></h2> 
<p>
<code>args.state</code> is a open data structure that allows you to define properties that are arbitrarily nested. You don't need to define any kind of <code>class</code>.
</p>
<p>
To initialize your game state, use the <code>||=</code> operator. Any value on the right side of <code>||=</code> will only be assigned _once_.
</p>
<p>
To assign a value every frame, just use the <code>=</code> operator, but _make sure_ you've initialized a default value.
</p>
<pre><code class="ruby">def tick args
  # initialize your game state ONCE
  args.state.player.x  ||= 0
  args.state.player.y  ||= 0
  args.state.player.hp ||= 100

  # increment the x position of the character by one every frame
  args.state.player.x += 1

  # Render a sprite with a label above the sprite
  args.outputs.sprites &lt;&lt; [
    args.state.player.x,
    args.state.player.y,
    32, 32,
    "player.png"
  ]

  args.outputs.labels &lt;&lt; [
    args.state.player.x,
    args.state.player.y - 50,
    args.state.player.hp
  ]
end
</code></pre>
<h1 id='--recipies-'>RECIPIES: <a style='font-size: small; float: right;' href='#--recipies-'>link</a></h1> 
<h2 id='---how-to-determine-what-frame-you-are-on'>How To Determine What Frame You Are On <a style='font-size: small; float: right;' href='#---how-to-determine-what-frame-you-are-on'>link</a></h2> 
<p>
There is a property on <code>state</code> called <code>tick_count</code> that is incremented by DragonRuby every time the <code>tick</code> method is called. The following code renders a label that displays the current <code>tick_count</code>.
</p>
<pre><code class="ruby">def tick args
  args.outputs.labels &lt;&lt; [10, 670, "\#{Kernel.tick_count}"]
end
</code></pre>
<h2 id='---how-to-get-current-framerate'>How To Get Current Framerate <a style='font-size: small; float: right;' href='#---how-to-get-current-framerate'>link</a></h2> 
<p>
Current framerate is a top level property on the Game Toolkit Runtime and is accessible via <code>args.gtk.current_framerate</code>.
</p>
<pre><code class="ruby">def tick args
  args.outputs.labels &lt;&lt; [10, 710, "framerate: \#{args.gtk.current_framerate.round}"]
end
</code></pre>
<h2 id='---how-to-render-a-sprite-using-an-array'>How To Render A Sprite Using An Array <a style='font-size: small; float: right;' href='#---how-to-render-a-sprite-using-an-array'>link</a></h2> 
<p>
All file paths should use the forward slash <code>/</code> *not* backslash <code>\</code>. Game Toolkit includes a number of sprites in the <code>sprites</code> folder (everything about your game is located in the <code>mygame</code> directory).
</p>
<p>
The following code renders a sprite with a <code>width</code> and <code>height</code> of <code>100</code> in the center of the screen.
</p>
<p>
<code>args.outputs.sprites</code> is used to render a sprite.
</p>
<p>
NOTE: Rendering using an <code>Array</code> is "quick and dirty". It's generally recommended that       you render using <code>Hashes</code> long term.
</p>
<pre><code class="ruby">def tick args
  args.outputs.sprites &lt;&lt; [
    640 - 50,                 # X
    360 - 50,                 # Y
    100,                      # W
    100,                      # H
    'sprites/square-blue.png' # PATH
 ]
end
</code></pre>
<h2 id='---rendering-a-sprite-using-a--hash-'>Rendering a Sprite Using a <code>Hash</code> <a style='font-size: small; float: right;' href='#---rendering-a-sprite-using-a--hash-'>link</a></h2> 
<p>
Using ordinal positioning can get a little unruly given so many properties you have control over.
</p>
<p>
You can represent a sprite as a <code>Hash</code>:
</p>
<pre><code class="ruby">def tick args
  args.outputs.sprites &lt;&lt; {
    x: 640 - 50,
    y: 360 - 50,
    w: 100,
    h: 100,

    path: 'sprites/square-blue.png',
    angle: 0,

    a: 255,
    r: 255,
    g: 255,
    b: 255,

    # source_ properties have origin of bottom left
    source_x:  0,
    source_y:  0,
    source_w: -1,
    source_h: -1,

    # tile_ properties have origin of top left
    tile_x:  0,
    tile_y:  0,
    tile_w: -1,
    tile_h: -1,

    flip_vertically: false,
    flip_horizontally: false,

    angle_anchor_x: 0.5,
    angle_anchor_y: 1.0,

    blendmode_enum: 1

    # sprites anchor/alignment (default is nil)
    anchor_x: 0.5,
    anchor_y: 0.5
  }
end
</code></pre>
<p>
The <code>blendmode_enum</code> value can be set to <code>0</code> (no blending), <code>1</code> (alpha blending), <code>2</code> (additive blending), <code>3</code> (modulo blending), <code>4</code> (multiply blending).
</p>
<h2 id='---how-to-render-a-label'>How To Render A Label <a style='font-size: small; float: right;' href='#---how-to-render-a-label'>link</a></h2> 
<p>
<code>args.outputs.labels</code> is used to render labels.
</p>
<p>
Labels are how you display text. This code will go directly inside of the <code>def tick args</code> method.
</p>
<p>
NOTE: Rendering using an <code>Array</code> is "quick and dirty". It's generally recommended that       you render using <code>Hashes</code> long term.
</p>
<p>
Here is the minimum code:
</p>
<pre><code class="ruby">def tick args
  #                       X    Y    TEXT
  args.outputs.labels &lt;&lt; [640, 360, "I am a black label."]
end
</code></pre>
<h2 id='---a-colored-label'>A Colored Label <a style='font-size: small; float: right;' href='#---a-colored-label'>link</a></h2> 
<pre><code class="ruby">def tick args
  # A colored label
  #                       X    Y    TEXT,                   RED    GREEN  BLUE  ALPHA
  args.outputs.labels &lt;&lt; [640, 360, "I am a redish label.", 255,     128,  128,   255]
end
</code></pre>
<h2 id='---extended-label-properties'>Extended Label Properties <a style='font-size: small; float: right;' href='#---extended-label-properties'>link</a></h2> 
<pre><code class="ruby">def tick args
  # A colored label
  #                       X    Y     TEXT           SIZE  ALIGNMENT  RED  GREEN  BLUE  ALPHA  FONT FILE
  args.outputs.labels &lt;&lt; [
    640,                   # X
    360,                   # Y
    "Hello world",         # TEXT
    0,                     # SIZE_ENUM
    1,                     # ALIGNMENT_ENUM
    0,                     # RED
    0,                     # GREEN
    0,                     # BLUE
    255,                   # ALPHA
    "fonts/coolfont.ttf"   # FONT
  ]
end
</code></pre>
<p>
A <code>SIZE_ENUM</code> of <code>0</code> represents "default size". A <code>negative</code> value will decrease the label size. A <code>positive</code> value will increase the label's size.
</p>
<p>
An <code>ALIGNMENT_ENUM</code> of <code>0</code> represents "left aligned". <code>1</code> represents "center aligned". <code>2</code> represents "right aligned".
</p>
<h2 id='---rendering-a-label-as-a--hash-'>Rendering A Label As A <code>Hash</code> <a style='font-size: small; float: right;' href='#---rendering-a-label-as-a--hash-'>link</a></h2> 
<p>
You can add additional metadata about your game within a label, which requires you to use a `Hash` instead.
</p>
<p>
If you use a <code>Hash</code> to render a label, you can set the label's size using either <code>SIZE_ENUM</code> or <code>SIZE_PX</code>. If both options are provided, <code>SIZE_PX</code> will be used.
</p>
<pre><code class="ruby">def tick args
  args.outputs.labels &lt;&lt; {
    x:                       200,
    y:                       550,
    text:                    "dragonruby",
    # size specification can be either size_enum or size_px
    size_enum:               2,
    size_px:                 22,
    alignment_enum:          1,
    r:                       155,
    g:                       50,
    b:                       50,
    a:                       255,
    font:                    "fonts/manaspc.ttf",
    vertical_alignment_enum: 0, # 0 is bottom, 1 is middle, 2 is top
    anchor_x: 0.5,
    anchor_y: 0.5
    # You can add any properties you like (this will be ignored/won't cause errors)
    game_data_one:  "Something",
    game_data_two: {
       value_1: "value",
       value_2: "value two",
       a_number: 15
    }
  }
end
</code></pre>
<h2 id='---getting-the-size-of-a-piece-of-text'>Getting The Size Of A Piece Of Text <a style='font-size: small; float: right;' href='#---getting-the-size-of-a-piece-of-text'>link</a></h2> 
<p>
You can get the render size of any string using <code>args.gtk.calcstringbox</code>.
</p>
<pre><code class="ruby">def tick args
  #                             TEXT           SIZE_ENUM  FONT
  w, h = args.gtk.calcstringbox("some string",         0, "font.ttf")

  # NOTE: The SIZE_ENUM and FONT are optional arguments.

  # Render a label showing the w and h of the text:
  args.outputs.labels &lt;&lt; [
    10,
    710,
    # This string uses Ruby's string interpolation literal: #{}
    "'some string' has width: #{w}, and height: #{h}."
  ]
end
</code></pre>
<h2 id='---rendering-labels-with-new-line-characters-and-wrapping'>Rendering Labels With New Line Characters And Wrapping <a style='font-size: small; float: right;' href='#---rendering-labels-with-new-line-characters-and-wrapping'>link</a></h2> 
<p>
You can use a strategy like the following to create multiple labels from a String.
</p>
<pre><code class="ruby">def tick args
  long_string = "Lorem ipsum dolor sit amet, consectetur adipiscing elitteger dolor velit, ultricies vitae libero vel, aliquam imperdiet enim."
  max_character_length = 30
  long_strings_split = args.string.wrapped_lines long_string, max_character_length
  args.outputs.labels &lt;&lt; long_strings_split.map_with_index do |s, i|
    { x: 10, y: 600 - (i * 20), text: s }
  end
end
</code></pre>
<h2 id='---how-to-play-a-sound'>How To Play A Sound <a style='font-size: small; float: right;' href='#---how-to-play-a-sound'>link</a></h2> 
<p>
Sounds that end <code>.wav</code> will play once:
</p>
<pre><code class="ruby">def tick args
  # Play a sound every second
  if (Kernel.tick_count % 60) == 0
    args.outputs.sounds &lt;&lt; 'something.wav'
  end
end
</code></pre>
<p>
Sounds that end <code>.ogg</code> is considered background music and will loop:
</p>
<pre><code class="ruby">def tick args
  # Start a sound loop at the beginning of the game
  if Kernel.tick_count == 0
    args.outputs.sounds &lt;&lt; 'background_music.ogg'
  end
end
</code></pre>
<p>
If you want to play a <code>.ogg</code> once as if it were a sound effect, you can do:
</p>
<pre><code class="ruby">def tick args
  # Play a sound every second
  if (Kernel.tick_count % 60) == 0
    args.gtk.queue_sound 'some-ogg.ogg'
  end
end
</code></pre>
<h1 id='--outputs---args-outputs--'>Outputs (<code>args.outputs</code>) <a style='font-size: small; float: right;' href='#--outputs---args-outputs--'>link</a></h1> 
<p>
Outputs is how you render primitives to the screen. The minimal setup for rendering something to the screen is via a <code>tick</code> method defined in mygame/app/main.rb
</p>
<pre><code class="ruby">def tick args
  args.outputs.solids     &lt;&lt; { x: 0, y: 0, w: 100, h: 100 }
  args.outputs.sprites    &lt;&lt; { x: 100, y: 100, w: 100, h: 100, path: "sprites/square/blue.png" }
  args.outputs.labels     &lt;&lt; { x: 200, y: 200, text: "Hello World" }
  args.outputs.borders    &lt;&lt; { x: 0, y: 0, w: 100, h: 100 }
  args.outputs.lines      &lt;&lt; { x: 300, y: 300, x2: 400, y2: 400 }
end
</code></pre>
<h2 id='---collection-render-orders'>Collection Render Orders <a style='font-size: small; float: right;' href='#---collection-render-orders'>link</a></h2> 
<p>
Primitives are rendered first-in, first-out. The rendering order (sorted by bottom-most to top-most):
</p>
<ul>
<li><code>solids</code></li>
<li><code>sprites</code></li>
<li><code>primitives</code>: Accepts all render primitives. Useful when you want to bypass the default rendering orders for rendering (eg. rendering solids on top of sprites).</li>
<li><code>labels</code></li>
<li><code>lines</code></li>
<li><code>borders</code></li>
<li><code>debug</code>: Accepts all render primitives. Use this to render primitives for debugging (production builds of your game will not render this layer).</li>
</ul>
<h2 id='----primitives-'><code>primitives</code> <a style='font-size: small; float: right;' href='#----primitives-'>link</a></h2> 
<p>
<code>args.outputs.primitives</code> can take in any primitive and will render first in, first out.
</p>
<p>
For example, you can render a <code>solid</code> above a <code>sprite</code>:
</p>
<pre><code class="ruby">def tick args
  # sprite
  args.outputs.primitives &lt;&lt; { x: 100, y: 100,
                               w: 100, h: 100,
                               path: "sprites/square/blue.png" }

  # solid
  args.outputs.primitives &lt;&lt; { x: 0,
                               y: 0,
                               w: 100,
                               h: 100,
                               primitive_marker: :solid }

  # border
  args.outputs.primitives &lt;&lt; { x: 0,
                               y: 0,
                               w: 100,
                               h: 100,
                               primitive_marker: :border }

  # label
  args.outputs.primitives &lt;&lt; { x: 100, y: 100,
                               text: "hello world" }

  # line
  args.outputs.primitives &lt;&lt; { x: 100, y: 100, x2: 150, y2: 150 }
end
</code></pre>
<h2 id='----debug-'><code>debug</code> <a style='font-size: small; float: right;' href='#----debug-'>link</a></h2> 
<p>
<code>args.outputs.debug</code> will not render in production mode and behaves like <code>args.outputs.primitives</code>. Objects in this collection are rendered above everything.
</p>
<h3 id='----string-primitives'>String Primitives <a style='font-size: small; float: right;' href='#----string-primitives'>link</a></h3> 
<p>
Additionally, <code>args.outputs.debug</code> allows you to pass in a <code>String</code> as a primitive type. This is helpful for quickly showing the value of a variable on the screen. A label with black text and a white background will be created for each <code>String</code> sent in. The labels will be automatically stacked vertically for you. New lines in the string will be respected.
</p>
<p>
Example:
</p>
<pre><code class="ruby">def tick args
  args.state.player ||= { x: 100, y: 100 }
  args.state.player.x += 1
  args.state.player.x = 0 if args.state.player.x &gt; 1280

  # the following string values will generate labels with backgrounds
  # and will auto stack vertically
  args.outputs.debug &lt;&lt; "current tick: #{Kernel.tick_count}"
  args.outputs.debug &lt;&lt; "player x: #{args.state.player.x}"
  args.outputs.debug &lt;&lt; "hello\nworld"
end
</code></pre>
<h3 id='-----watch-'><code>watch</code> <a style='font-size: small; float: right;' href='#-----watch-'>link</a></h3> 
<p>
If you need additional control over a string value, you can use the <code>args.outputs.debug.watch</code> function.
</p>
<p>
The functions takes in the following parameters:
</p>
<ul>
<li>Object to watch. This object will be converted to a string.</li>
<li>The <code>label_style</code>: optional, named argument can be passed in with a <code>Hash</code> to override the default label style for watch variables.</li>
<li>The <code>background_style</code>: optional named argument can be passed in with a <code>Hash</code> to override the default background style for the watch variables.</li>
</ul>
<p>
Example:
</p>
<pre><code class="ruby">def tick args
  args.state.player ||= { x: 100, y: 100 }
  args.state.player.x += 1
  args.state.player.x = 0 if args.state.player.x &gt; 1280

  args.outputs.debug.watch args.state.player
  args.outputs.debug.watch pretty_format(args.state.player),
                           label_style: { r: 0,
                                          g: 0,
                                          b: 255,
                                          size_px: 10 },
                           background_style: { r: 0,
                                               g: 255,
                                               b: 0,
                                               a: 128,
                                               path: :solid }
end
</code></pre>
<h2 id='----solids-'><code>solids</code> <a style='font-size: small; float: right;' href='#----solids-'>link</a></h2> 
<p>
Add primitives to this collection to render a solid to the screen.
</p>
<p>
NOTE:
</p>
<p>
 This render primitive is fine to use sparingly. If you find yourself rendering a large number of solids, render <code>sprites</code> instead (the textures that solid primitives generate are not cached and do not perform as well as rendering sprites).
</p>
<p>
For example, the following <code>solid</code> and <code>sprite</code> are equivalent:
</p>
<pre><code class="ruby">def tick args
  args.outputs.solids &lt;&lt; {
    x: 0,
    y: 0,
    w: 100,
    h: 100,
    r: 255,
    g: 255,
    b: 255,
    a: 128
  }

  # is equivalent to

  args.outputs.sprites &lt;&lt; {
    x: 0,
    y: 0,
    w: 100,
    h: 100,
    path: :solid,
    r: 255,
    g: 255,
    b: 255,
    a: 128
  }
end
</code></pre>
<h3 id='----array-render-primitive'>Array Render Primitive <a style='font-size: small; float: right;' href='#----array-render-primitive'>link</a></h3> 
<p>
Creates a solid black rectangle located at 100, 100. 160 pixels wide and 90 pixels tall.
</p>
<pre><code class="ruby">def tick args
  #                         X    Y  WIDTH  HEIGHT
  args.outputs.solids &lt;&lt; [100, 100,   160,     90]
end
</code></pre>
<p>
NOTE:
</p>
<p>
 <code>Array</code>-based primitives are find for debugging purposes/quick prototypes. But should not be used as the default rendering approach. Use <code>Hash</code>-based or <code>Class</code>-based primitives.
</p>
<p>
While not recommended for long term maintainability, you can also set the following properties.
</p>
<p>
Example Creates a green solid rectangle with an opacity of 50% (the value for the color and alpha is a number between <code>0</code> and <code>255</code>, the alpha property is optional and will be set to <code>255</code> if not specified):
</p>
<pre><code class="ruby">def tick args
  #                         X    Y  WIDTH  HEIGHT  RED  GREEN  BLUE  ALPHA
  args.outputs.solids &lt;&lt; [100, 100,   160,     90,   0,   255,    0,   128]
end
</code></pre>
<h3 id='----hash-render-primitive'>Hash Render Primitive <a style='font-size: small; float: right;' href='#----hash-render-primitive'>link</a></h3> 
<p>
If you want a more readable invocation. You can use the following hash to create a solid. Any parameters that are not specified will be given a default value. The keys of the hash can be provided in any order.
</p>
<pre><code class="ruby">def tick args
  args.outputs.solids &lt;&lt; {
    x:    0,
    y:    0,
    w:  100,
    h:  100,
    r:    0,
    g:  255,
    b:    0,
    a:  255,
    anchor_x: 0,
    anchor_y: 0,
    blendmode_enum: 1
  }
end
</code></pre>
<h3 id='----class-render-primitive'>Class Render Primitive <a style='font-size: small; float: right;' href='#----class-render-primitive'>link</a></h3> 
<p>
You can also create a class with solid properties and render it as a primitive. ALL properties must be on the class. --Additionally--, a method called <code>primitive_marker</code> must be defined on the class.
</p>
<p>
Here is an example:
</p>
<pre><code class="ruby"># Create type with ALL solid properties AND primitive_marker
class Solid
  attr_accessor :x, :y, :w, :h, :r, :g, :b, :a, :anchor_x, :anchor_y, :blendmode_enum

  def primitive_marker
    :solid # or :border
  end
end

# Inherit from type
class Square &lt; Solid
  # constructor
  def initialize x, y, size
    self.x = x
    self.y = y
    self.w = size
    self.h = size
  end
end

def tick args
  # render solid/border
  args.outputs.solids  &lt;&lt; Square.new(10, 10, 32)
end
</code></pre>
<h2 id='----borders-'><code>borders</code> <a style='font-size: small; float: right;' href='#----borders-'>link</a></h2> 
<p>
Add primitives to this collection to render an unfilled solid to the screen. Take a look at the documentation for Outputs#solids.
</p>
<p>
The only difference between the two primitives is where they are added.
</p>
<p>
Instead of using <code>args.outputs.solids</code>:
</p>
<pre><code class="ruby">def tick args
  #                         X    Y  WIDTH  HEIGHT
  args.outputs.solids &lt;&lt; [100, 100,   160,     90]
end
</code></pre>
<p>
You have to use <code>args.outputs.borders</code>:
</p>
<pre><code class="ruby">def tick args
  #                           X    Y  WIDTH  HEIGHT
  args.outputs.borders &lt;&lt; [100, 100,   160,     90]
end
</code></pre>
<h2 id='----sprites-'><code>sprites</code> <a style='font-size: small; float: right;' href='#----sprites-'>link</a></h2> 
<p>
Add primitives to this collection to render a sprite to the screen.
</p>
<h3 id='----properties'>Properties <a style='font-size: small; float: right;' href='#----properties'>link</a></h3> 
<p>
Here are all the properties that you can set on a sprite. The only required ones are <code>x</code>, <code>y</code>, <code>w</code>, <code>h</code>, and <code>path</code>.
</p>
<h4>Required</h4>
<ul>
<li><code>x</code>: X position of the sprite. Note: the bottom left corner of the sprite is used for positioning (this can be changed using <code>anchor_x</code>, and <code>anchor_y</code>).</li>
<li><code>y</code>: Y position of the sprite. Note: The origin 0,0 is at the bottom left corner. Setting <code>y</code> to a higher value will move the sprite upwards.</li>
<li><code>w</code>: The render width.</li>
<li><code>h</code>: The render height.</li>
<li><code>path</code>: The path of the sprite relative to the game folder.</li>
</ul>
<h4>Anchors and Rotations</h4>
<ul>
<li><code>flip_horizontally</code>: This value can be either <code>true</code> or <code>false</code> and controls if the sprite will be flipped horizontally (default value is false).</li>
<li><code>flip_vertically</code>: This value can be either <code>true</code> or <code>false</code> and controls if the sprite will be flipped vertically (default value is false).</li>
<li><code>anchor_x</code>: Used to determine anchor point of the sprite's X position (relative to the render width).</li>
<li><code>anchor_y</code>: Used to determine anchor point of the sprite's Y position (relative to the render height).</li>
<li><code>angle</code>: Rotation of the sprite in degrees (default value is 0). Rotation occurs around the center of the sprite. The point of rotation can be changed using <code>angle_anchor_x</code> and <code>angle_anchor_y</code>.</li>
<li><code>angle_anchor_x</code>: Controls the point of rotation for the sprite (relative to the render width).</li>
<li><code>angle_anchor_y</code>: Controls the point of rotation for the sprite (relative to the render height).</li>
</ul>
<p>
Here's an example of rendering a 80x80 pixel sprite in the center of the screen:
</p>
<pre><code class="ruby">def tick args
  args.outputs.sprites &lt;&lt; {
    x: 640 - 40, # the logical center of the screen horizontally is 640, minus half the width of the sprite
    y: 360 - 40, # the logical center of the screen vertically is 360, minus half the height of the sprite
    w: 80,
    h: 80,
    path: "sprites/square/blue.png"
 }
end
</code></pre>
<p>
Instead of computing the offset, you can use <code>anchor_x</code>, and <code>anchor_y</code> to center the sprite. The following is equivalent to the code above:
</p>
<pre><code class="ruby">def tick args
  args.outputs.sprites &lt;&lt; {
    x: 640,
    y: 360,
    w: 80,
    h: 80,
    path: "sprites/square/blue.png",
    anchor_x: 0.5, # position horizontally at 0.5 of the sprite's width
    anchor_y: 0.5  # position vertically at 0.5 of the sprite's height
 }
end
</code></pre>
<h4>Cropping</h4>
<ul>
<li><code>tile_(x|y|w|h)</code>: Defines the crop area to use for a sprite. The origin for <code>tile_</code> properties uses the TOP LEFT as its origin (useful for cropping tiles from a sprite sheet).</li>
<li><code>source_(x|y|w|h)</code>: Defines the crop area to use for a sprite. The origin for <code>source_</code> properties uses the BOTTOM LEFT as its origin.</li>
</ul>
<p>
See the sample apps under <code>./samples/03_rendering_sprites</code> for examples of how to use this properties non-trivially.
</p>
<h4>Blending</h4>
<ul>
<li><code>a</code>: Alpha/transparency of the sprite from 0 to 255 (default value is 255).</li>
<li><code>r</code>: Level of red saturation for the sprite (default value is 255). Example: Setting the value to zero will remove all red coloration from the sprite.</li>
<li><code>g</code>: Level of green saturation for the sprite (default value is 255).</li>
<li><code>b</code>: Level of blue saturation for the sprite (default value is 255).</li>
<li><code>blendmode_enum</code>: Valid options are <code>0</code>: no blending, <code>1</code>: default/alpha blending, <code>2</code>: additive blending, <code>3</code>: modulo blending, <code>4</code>: multiply blending.</li>
<li><code>scale_quality_enum</code>: Valid options are <code>0</code>: nearest neighbor, <code>1</code>: linear scaling, <code>2</code>: anti-aliasing. If the value is <code>nil</code> then the <code>scale_quality</code> value that was set in <code>mygame/game_metadata.txt</code> will be used.</li>
</ul>
<p>
The following sample apps show how <code>blendmode_enum</code> can be leveraged to create coloring and lighting effects:
</p>
<ul>
<li><code>./samples/07_advanced_rendering/11_blend_modes</code></li>
<li><code>./samples/07_advanced_rendering/13_lighting</code></li>
</ul>
<h4>Triangles</h4>
<p>
To rendering using triangles, instead of providing a <code>w</code>, <code>h</code> property, provide <code>x2</code>, <code>y2</code>, <code>x3</code>, <code>y3</code>. This applies for positioning and cropping.
</p>
<p>
Here is an example:
</p>
<pre><code class="ruby">def tick args
  args.outputs.sprites &lt;&lt; {
    x: 0,
    y: 0,
    x2: 80,
    y2: 0,
    x3: 0,
    y3: 80,
    source_x: 0,
    source_y: 0,
    source_x2: 80,
    source_y2: 0,
    source_x3: 0,
    source_y3: 80,
    path: "sprites/square/blue.png"
  }
end
</code></pre>
<p>
For more example of rendering using triangles see:
</p>
<ul>
<li><code>./samples/07_advanced_rendering/14_triangles</code></li>
<li><code>./samples/07_advanced_rendering/15_triangles_trapezoid</code></li>
<li><code>./samples/07_advanced_rendering/16_matrix_and_triangles_2d</code></li>
<li><code>./samples/07_advanced_rendering/16_matrix_and_triangles_3d</code></li>
<li><code>./samples/07_advanced_rendering/16_matrix_cubeworld</code></li>
</ul>
<h3 id='----array-render-primitive'>Array Render Primitive <a style='font-size: small; float: right;' href='#----array-render-primitive'>link</a></h3> 
<p>
Creates a sprite of a white circle located at 100, 100. 160 pixels wide and 90 pixels tall.
</p>
<pre><code class="ruby">def tick args
  #                         X    Y   WIDTH   HEIGHT                      PATH
  args.outputs.sprites &lt;&lt; [100, 100,   160,     90, "sprites/circle/white.png"]
end
</code></pre>
<p>
NOTE:
</p>
<p>
 Array-based sprites have limited access to sprite properties, but nice for quick prototyping. Use a <code>Hash</code> or <code>Class</code> to gain access to all properties, gain long term maintainability of code, and a boost in rendering performance.
</p>
<h3 id='----hash-render-primitive'>Hash Render Primitive <a style='font-size: small; float: right;' href='#----hash-render-primitive'>link</a></h3> 
<p>
If you want a more readable (and faster) invocation, you can use the following hash to create a sprite. Any parameters that are not specified will be given a default value. The keys of the hash can be provided in any order.
</p>
<pre><code class="ruby">def tick args
  args.outputs.sprites &lt;&lt; {
    x: 0,
    y: 0,
    w: 100,
    h: 100,
    path: "sprites/circle/white.png",
    angle: 0,
    a: 255
  }
end
</code></pre>
<h3 id='----class-render-primitive'>Class Render Primitive <a style='font-size: small; float: right;' href='#----class-render-primitive'>link</a></h3> 
<p>
You can also create a class with sprite properties and render it as a primitive. ALL properties must be on the class. --Additionally--, a method called <code>primitive_marker</code> must be defined on the class.
</p>
<p>
Here is an example:
</p>
<pre><code class="ruby"># Create type with ALL sprite properties AND primitive_marker
class Sprite
  attr_accessor :x, :y, :w, :h, :path, :angle, :a, :r, :g, :b, :tile_x,
                :tile_y, :tile_w, :tile_h, :flip_horizontally,
                :flip_vertically, :angle_anchor_x, :angle_anchor_y, :id,
                :angle_x, :angle_y, :z,
                :source_x, :source_y, :source_w, :source_h, :blendmode_enum,
                :source_x2, :source_y2, :source_x3, :source_y3, :x2, :y2, :x3, :y3,
                :anchor_x, :anchor_y, :scale_quality_enum

  def primitive_marker
    :sprite
  end
end

# Inherit from type
class Circle &lt; Sprite
  # constructor
  def initialize x, y, size, path
    self.x = x
    self.y = y
    self.w = size
    self.h = size
    self.path = path
  end

  def serialize
    {x:self.x, y:self.y, w:self.w, h:self.h, path:self.path}
  end

  def inspect
    serialize.to_s
  end

  def to_s
    serialize.to_s
  end
end

def tick args
  # render circle sprite
  args.outputs.sprites  &lt;&lt; Circle.new(10, 10, 32,"sprites/circle/white.png")
end
</code></pre>
<h3 id='-----attr_sprite-'><code>attr_sprite</code> <a style='font-size: small; float: right;' href='#-----attr_sprite-'>link</a></h3> 
<p>
The <code>attr_sprite</code> class macro adds all properties needed to render a sprite to a class. This removes the need to manually define all sprites properties that DragonRuby offers for rendering.
</p>
<p>
Instead of manually defining the properties, you can represent a sprite using the <code>attr_sprite</code> class macro:
</p>
<pre><code class="ruby">class BlueSquare
  # invoke the helper function at the class level for
  # anything you want to represent as a sprite
  attr_sprite

  def initialize(x: 0, y: 0, w: 0, h: 0)
    @x = x
    @y = y
    @w = w
    @h = h
    @path = 'sprites/square-blue.png'
  end
end

def tick args
  args.outputs.sprites &lt;&lt; BlueSquare.new(x: 640 - 50,
                                         y: 360 - 50,
                                         w: 50,
                                         h: 50)
end
</code></pre>
<h2 id='----lines-'><code>lines</code> <a style='font-size: small; float: right;' href='#----lines-'>link</a></h2> 
<p>
Add primitives to this collection to render a line.
</p>
<h3 id='----array-render-primitive'>Array Render Primitive <a style='font-size: small; float: right;' href='#----array-render-primitive'>link</a></h3> 
<pre><code class="ruby">def tick args
                         #  X    Y   X2   Y2
  args.outputs.lines &lt;&lt; [100, 100, 150, 150]
end
</code></pre>
<p>
NOTE:
</p>
<p>
 <code>Array</code>-based primitives are find for debugging purposes/quick prototypes. But should not be used as the default rendering approach. Use <code>Hash</code>-based or <code>Class</code>-based primitives.
</p>
<h3 id='----hash-render-primitive'>Hash Render Primitive <a style='font-size: small; float: right;' href='#----hash-render-primitive'>link</a></h3> 
<pre><code class="ruby">def tick args
  args.outputs.lines &lt;&lt; {
    x:  100,
    y:  100,
    x2: 150,
    y2: 150,
    r:  0,
    g:  0,
    b:  0,
    a:  255,
    blendmode_enum: 1
  }
end
</code></pre>
<h3 id='----class-render-primitive'>Class Render Primitive <a style='font-size: small; float: right;' href='#----class-render-primitive'>link</a></h3> 
<pre><code class="ruby"># Create type with ALL line properties AND primitive_marker
class Line
  attr_accessor :x, :y, :x2, :y2, :r, :g, :b, :a, :blendmode_enum

  def primitive_marker
    :line
  end
end

# Inherit from type
class RedLine &lt; Line
  # constructor
  def initialize x, y, x2, y2
    self.x = x
    self.y = y
    self.x2 = x2
    self.y2 = y2
    self.r  = 255
    self.g  = 0
    self.b  = 0
    self.a  = 255
  end
end

def tick args
  # render line
  args.outputs.lines &lt;&lt; RedLine.new(100, 100, 150, 150)
end

</code></pre>
<h2 id='----labels-'><code>labels</code> <a style='font-size: small; float: right;' href='#----labels-'>link</a></h2> 
<p>
Add primitives to this collection to render a label.
</p>
<h3 id='----array-render-primitive'>Array Render Primitive <a style='font-size: small; float: right;' href='#----array-render-primitive'>link</a></h3> 
<p>
Labels represented as Arrays/Tuples:
</p>
<pre><code class="ruby">def tick args
                         #        X         Y              TEXT   SIZE_ENUM
  args.outputs.labels &lt;&lt; [175 + 150, 610 - 50, "Smaller label.",         0]
end
</code></pre>
<p>
Here are all the properties that you can set with a label represented as an Array. It's recommended to move over to using Hashes once you've specified a lot of properties.
</p>
<pre><code class="ruby">def tick args
  args.outputs.labels &lt;&lt; [
    640,                   # X
    360,                   # Y
    "Hello world",         # TEXT
    0,                     # SIZE_ENUM
    1,                     # ALIGNMENT_ENUM
    0,                     # RED
    0,                     # GREEN
    0,                     # BLUE
    255,                   # ALPHA
    "fonts/coolfont.ttf"   # FONT
  ]
end
</code></pre>
<p>
NOTE:
</p>
<p>
 <code>Array</code>-based primitives are find for debugging purposes/quick prototypes. But should not be used as the default rendering approach. Use <code>Hash</code>-based or <code>Class</code>-based primitives.
</p>
<h3 id='----hash-render-primitive'>Hash Render Primitive <a style='font-size: small; float: right;' href='#----hash-render-primitive'>link</a></h3> 
<p>
?&gt; <code>size_enum</code> is an opaque unit and signifies the recommended size for labels. The default <code>size_enum</code> of <code>0</code> means "this size is the smallest font size that is comfortable to read on a hand-held device". <code>size_enum</code> of <code>0</code> corresponds to <code>22px</code> at <code>720p</code>. Each increment of <code>size_enum</code> increases/decreases the pixels by <code>2</code> (<code>size_enum</code> of <code>1</code> means <code>24px</code>, <code>size_enum</code> of <code>-1</code> means <code>20px</code>, etc). If you want to control the size of a label explicitly, use <code>size_px</code> instead.
</p>
<pre><code class="ruby">def tick args
  args.outputs.labels &lt;&lt; {
      x:                       200,
      y:                       550,
      text:                    "dragonruby",
      size_enum:               2,
      alignment_enum:          1, # 0 = left, 1 = center, 2 = right
      r:                       155,
      g:                       50,
      b:                       50,
      a:                       255,
      font:                    "fonts/manaspc.ttf",
      vertical_alignment_enum: 0  # 0 = bottom, 1 = center, 2 = top,
      anchor_x:                0, # if provided, alignment_enum is ignored
      anchor_y:                1, # if provided, vertical_alignment_enum is ignored,
      size_px:                 30, # if provided, size_enum is ignored.
      blendmode_enum:          1
  }
end
</code></pre>
<h3 id='----class-render-primitive'>Class Render Primitive <a style='font-size: small; float: right;' href='#----class-render-primitive'>link</a></h3> 
<pre><code class="ruby"># Create type with ALL label properties AND primitive_marker
class Label
  attr_accessor :x, :y, :w, :h, :r, :g, :b, :a, :text, :font, :anchor_x,
                :anchor_y, :blendmode_enum, :size_px, :size_enum, :alignment_enum,
                :vertical_alignment_enum

  def primitive_marker
    :label
  end
end
</code></pre>
<h2 id='---render-targets-------operator-'>Render Targets (<code>[]</code> operator) <a style='font-size: small; float: right;' href='#---render-targets-------operator-'>link</a></h2> 
<p>
The <code>args.outputs</code> structure renders to the screen. You can render to a texture/virtual canvas using <code>args.outputs[SYMBOL]</code>. What ever primitives are sent to the virtual canvas are cached and reused (the cache is invalidated whenever you render to virtual canvas).
</p>
<p>
?&gt; You can also use render targets to accomplish many complex layouts such as a game camera, perform scene management, or add lighting.
</p>
<p>
Take a look at the following sample apps:
</p>
<p>
&amp;nbsp;&amp;nbsp;&amp;nbsp;&amp;nbsp;All sample apps under <code>./samples/07_advanced_rendering</code>.
</p>
<p>
&amp;nbsp;&amp;nbsp;&amp;nbsp;&amp;nbsp;The Map Editor reference implementation (<code>samples/99_genre_platformer/map_editor</code>).
</p>
<p>
&amp;nbsp;&amp;nbsp;&amp;nbsp;&amp;nbsp;The arcade game Square Fall (<code>samples/99_genre_arcade/squares</code>).
</p>
<p>
Many of the sample apps use render targets, so be sure to explore as many as you can!
</p>
<p>
Here's an example that programmatically creates a <code>:combined</code> sprite composed of two pngs and a label:
</p>
<pre><code class="ruby">def tick args
  # on tick 0, create a render target composed of two sprites and a label
  if Kernel.tick_count == 0
    # to reiterate, this sprite will be cached until it's written to again
    # the :combined sprite has a w/h of 200
    args.outputs[:combined].w = 200
    args.outputs[:combined].h = 200

    # and a black transparent background
    args.outputs[:combined].background_color = [0, 0, 0, 0]

    # add two sprites to the render target
    args.outputs[:combined].primitives &lt;&lt; {
      x: 0,
      y: 0,
      w: 100,
      h: 200,
      path: "sprites/square/blue.png"
    }

    args.outputs[:combined].primitives &lt;&lt; {
      x: 100,
      y: 0,
      w: 100,
      h: 200,
      path: "sprites/square/red.png"
    }

    # add a label in the center of the render target
    args.outputs[:combined].primitives &lt;&lt; {
      x: 100,
      y: 100,
      text: "COMBINED!",
      anchor_x: 0.5,
      anchor_y: 0.5,
    }
  end

  # rendered the :combined sprite in multiple
  # places on the screen
  args.outputs.sprites &lt;&lt; {
    x: 0,
    y: 0,
    w: 400,
    h: 400,
    path: :combined,
    angle: 33
  }

  args.outputs.sprites &lt;&lt; {
    x: 640,
    y: 360,
    w: 100,
    h: 100,
    path: :combined,
    angle: 180,
    a: 128
  }
end
</code></pre>
<p>
Render targets are extremely powerful and you'll end up using them a lot (so be sure to get familiar with them by studying the sample apps).
</p>
<p>
NOTE:
</p>
<p>
 Take note that simply accessing a render target via <code>args.outputs[]</code> will invalidate the cached texture. Proceed with caution!
</p>
<p>
Here's an example of this side-effect:
</p>
<pre><code class="ruby">def tick args
  # Create the render target only on the first tick.
  # It's then cached and used indefinitely until it's
  # accessed again.
  if Kernel.tick_count &lt;= 0
    args.outputs[:render_target].w = 100
    args.outputs[:render_target].h = 100
    args.outputs[:render_target].sprites &lt;&lt; {
      x: 0,
      y: 0,
      w: 100,
      h: 100,
      r: 0,
      b: 0,
      g: 0,
      a: 64,
      path: :solid
    }
  end

  # CAUTION: accessing the render target will invalidate it!
  #          don't do this unless you're wanting to update the
  #          texture
  render_target = args.outputs[:render_target]

  # store information you need about a render target in state
  # or an iVar/member variable instead of accessing the render target
  args.outputs.sprites &lt;&lt; {
    x: 100,
    y: 100,
    w: render_target.w,
    h: render_target.h,
    path: :render_target,
  }
end
</code></pre>
<h2 id='----screenshots-'><code>screenshots</code> <a style='font-size: small; float: right;' href='#----screenshots-'>link</a></h2> 
<p>
Add a hash to this collection to take a screenshot and save as png file. The keys of the hash can be provided in any order.
</p>
<pre><code class="ruby">def tick args
  args.outputs.screenshots &lt;&lt; {
    x: 0, y: 0, w: 100, h: 100,    # Which portion of the screen should be captured
    path: 'screenshot.png',        # Output path of PNG file (inside game directory)
    r: 255, g: 255, b: 255, a: 0   # Optional chroma key
  }
end
</code></pre>
<h3 id='----chroma-key--making-a-color-transparent-'>Chroma key (Making a color transparent) <a style='font-size: small; float: right;' href='#----chroma-key--making-a-color-transparent-'>link</a></h3> 
<p>
By specifying the r, g, b and a keys of the hash you change the transparency of a color in the resulting PNG file. This can be useful if you want to create files with transparent background like spritesheets. The transparency of the color specified by <code>r</code>, <code>g</code>, <code>b</code> will be set to the transparency specified by <code>a</code>.
</p>
<p>
The example above sets the color white (255, 255, 255) as transparent.
</p>
<h2 id='---shaders'>Shaders <a style='font-size: small; float: right;' href='#---shaders'>link</a></h2> 
<p>
Shaders are available to Indie and Pro license holders via <code>dragonruby-shadersim</code>. Download DragonRuby ShaderSim at [dragonruby.org](https://dragonruby.org).
</p>
<p>
NOTE:
</p>
<p>
 Shaders are currently in Beta.
</p>
<p>
Shaders must be GLSL ES2 compatible.
</p>
<p>
The long term goal is for DR Shaders to baseline to GLSL version 300 and cross-compile to Vulkan, WebGL 2, Metal, and HLSL.
</p>
<p>
Here is a minimal example of using shaders:
</p>
<pre><code class="ruby"># mygame/app/main.rb
def tick args
  args.outputs.shader_path ||= "shaders/example.glsl"
end
</code></pre>
<pre><code class="glsl">// mygame/shaders/example.glsl
uniform sampler2D tex0;

varying vec2 v_texCoord;

void main() {
  gl_FragColor = texture2D(tex0, v_texCoord);
}
</code></pre>
<h3 id='-----shader_path-'><code>shader_path</code> <a style='font-size: small; float: right;' href='#-----shader_path-'>link</a></h3> 
<p>
Setting <code>shader_path</code> on <code>outputs</code> signifies to DragonRuby that a shader should be compiled and loaded.
</p>
<h3 id='-----shader_uniforms-'><code>shader_uniforms</code> <a style='font-size: small; float: right;' href='#-----shader_uniforms-'>link</a></h3> 
<p>
You can bind uniforms to a shader by providing an <code>Array</code> of <code>Hashes</code> to <code>shader_uniforms</code> with keys <code>name:</code>, <code>value:</code>, and <code>type:</code> which currently supports <code>:int</code> and <code>:float</code>.
</p>
<pre><code class="ruby">def tick args
  args.outputs.shader_path ||= "shaders/example.glsl"

  args.outputs.shader_uniforms = [
    {
      name: :mouse_coord_x,
      value: args.inputs.mouse.x.fdiv(1280),
      type: :float
    },
    {
      name: :mouse_coord_y,
      value: 1.0 - args.inputs.mouse.y.fdiv(720),
      type: :float
    },
    {
      name: :tick_count,
      value: Kernel.tick_count,
      type: :int
    }
  ]
end
</code></pre>
<pre><code class="glsl">// mygame/shaders/example.glsl
uniform sampler2D tex0;

uniform float mouse_coord_x;
uniform float mouse_coord_y;
uniform int tick_count;

varying vec2 v_texCoord;

void main() {
  gl_FragColor = texture2D(tex0, v_texCoord);
}
</code></pre>
<h3 id='-----shader_tex-1-15--'><code>shader_tex(1-15)</code> <a style='font-size: small; float: right;' href='#-----shader_tex-1-15--'>link</a></h3> 
<p>
You can bind up to 15 additional render targets via <code>shaders_tex1</code>, <code>shaders_tex2</code>, <code>shaders_tex3</code>, etc. <code>tex0</code> is reserved for what has been rendered to the screen and cannot be set.
</p>
<pre><code class="ruby">def tick args
  args.outputs.shader_path = "shaders/example.glsl"
  args.outputs.shader_tex1 = :single_blue_square

  args.outputs[:single_blue_square].background_color = { r: 255, g: 255, b: 255, a: 255 }
  args.outputs[:single_blue_square].w = 1280;
  args.outputs[:single_blue_square].h = 720;
  args.outputs[:single_blue_square].sprites &lt;&lt; {
    x: 0,
    y: 0,
    w: 100,
    h: 100,
    path:
    "sprites/square/blue.png"
  }

  args.outputs.background_color = { r: 0, g: 0, b: 0 }
  args.outputs.sprites &lt;&lt; {
    x: 0,
    y: 0,
    w: 200,
    h: 200,
    path:
    "sprites/square/red.png"
  }
end
</code></pre>
<pre><code class="glsl">uniform sampler2D tex0;
uniform sampler2D tex1; // :single_blue_square render target

varying vec2 v_texCoord;

void noop() {
  vec4 pixel_from_rt = texture2D(tex1, v_texCoord);

  // if the pixel from the render target isn't white
  // then render the pixel from the RT
  // otherwise render the pixel from the screen
  if (pixel_from_rt.r &lt; 1.0 ||
      pixel_from_rt.g &lt; 1.0 ||
      pixel_from_rt.b &lt; 1.0) {
    gl_FragColor.r = pixel_from_rt.r;
    gl_FragColor.g = pixel_from_rt.g;
    gl_FragColor.b = pixel_from_rt.b;
  } else {
    gl_FragColor = texture2D(tex0, v_texCoord);
  }
}
</code></pre>
<h1 id='--inputs---args-inputs--'>Inputs (<code>args.inputs</code>) <a style='font-size: small; float: right;' href='#--inputs---args-inputs--'>link</a></h1> 
<p>
Access using input using <code>args.inputs</code>.
</p>
<h2 id='----last_active-'><code>last_active</code> <a style='font-size: small; float: right;' href='#----last_active-'>link</a></h2> 
<p>
This function returns the last active input which will be set to either <code>:keyboard</code>, <code>:mouse</code>, or <code>:controller</code>. The function is helpful when you need to present on screen instructions based on the input the player chose to play with.
</p>
<pre><code class="ruby">def tick args
  if args.inputs.last_active == :controller
    args.outputs.labels &lt;&lt; { x: 60, y: 60, text: "Use the D-Pad to move around." }
  else
    args.outputs.labels &lt;&lt; { x: 60, y: 60, text: "Use the arrow keys to move around." }
  end
end
</code></pre>
<h2 id='----last_active_at-'><code>last_active_at</code> <a style='font-size: small; float: right;' href='#----last_active_at-'>link</a></h2> 
<p>
Returns <code>Kernel.tick_count</code> of which the specific input was last active.
</p>
<h2 id='----last_active_global_at-'><code>last_active_global_at</code> <a style='font-size: small; float: right;' href='#----last_active_global_at-'>link</a></h2> 
<p>
Returns the <code>Kernel.global_tick_count</code> of which the specific input was last active.
</p>
<h2 id='----locale-'><code>locale</code> <a style='font-size: small; float: right;' href='#----locale-'>link</a></h2> 
<p>
Returns the ISO 639-1 two-letter language code based on OS preferences. Refer to the following link for locale strings: &lt;https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes&gt;).
</p>
<p>
Defaults to "en" if locale can't be retrieved (<code>args.inputs.locale_raw</code> will be nil in this case).
</p>
<h2 id='----up-'><code>up</code> <a style='font-size: small; float: right;' href='#----up-'>link</a></h2> 
<p>
Returns <code>true</code> if: the <code>up</code> arrow or <code>w</code> key is pressed or held on the <code>keyboard</code>; or if <code>up</code> is pressed or held on <code>controller_one</code>; or if the <code>left_analog</code> on <code>controller_one</code> is tilted upwards.
</p>
<h2 id='----down-'><code>down</code> <a style='font-size: small; float: right;' href='#----down-'>link</a></h2> 
<p>
Returns <code>true</code> if: the <code>down</code> arrow or <code>s</code> key is pressed or held on the <code>keyboard</code>; or if <code>down</code> is pressed or held on <code>controller_one</code>; or if the <code>left_analog</code> on <code>controller_one</code> is tilted downwards.
</p>
<h2 id='----left-'><code>left</code> <a style='font-size: small; float: right;' href='#----left-'>link</a></h2> 
<p>
Returns <code>true</code> if: the <code>left</code> arrow or <code>a</code> key is pressed or held on the <code>keyboard</code>; or if <code>left</code> is pressed or held on <code>controller_one</code>; or if the <code>left_analog</code> on <code>controller_one</code> is tilted to the left.
</p>
<h2 id='----right-'><code>right</code> <a style='font-size: small; float: right;' href='#----right-'>link</a></h2> 
<p>
Returns <code>true</code> if: the <code>right</code> arrow or <code>d</code> key is pressed or held on the <code>keyboard</code>; or if <code>right</code> is pressed or held on <code>controller_one</code>; or if the <code>left_analog</code> on <code>controller_one</code> is tilted to the right.
</p>
<h2 id='----left_right-'><code>left_right</code> <a style='font-size: small; float: right;' href='#----left_right-'>link</a></h2> 
<p>
Returns <code>-1</code> (left), <code>0</code> (neutral), or <code>+1</code> (right). This method is aliased to <code>args.inputs.left_right_with_wasd</code>.
</p>
<p>
The following inputs are inspected to determine the result:
</p>
<ul>
<li>  Keyboard's left, right arrow keys: <code>args.inputs.keyboard.(left|right)_arrow</code></li>
<li>  Keyboard's a, d keys (WASD): <code>args.inputs.keyboard.(a|d)</code></li>
<li>  Controller One's DPAD (if a controller is connected): <code>args.inputs.controller_one.dpad_left</code>, <code>args.inputs.controller_one.dpad_right</code></li>
<li>  Controller One's Left Analog (if a controller is connected): <code>args.inputs.controller_one.left_analog_x_perc.abs &gt;= 0.6</code></li>
</ul>
<h2 id='----left_right_perc-'><code>left_right_perc</code> <a style='font-size: small; float: right;' href='#----left_right_perc-'>link</a></h2> 
<p>
Returns a floating point value between <code>-1</code> and <code>1</code>. This method is aliased to <code>args.inputs.left_right_perc_with_wasd</code>
</p>
<p>
The following inputs are inspected to determine the result:
</p>
<ul>
<li>  Controller One's Left Analog (if a controller is connected and the value is not 0.0): <code>args.inputs.controller_one.left_analog_x_perc</code></li>
<li>  If the left analog isn't being used, then Controller One's DPAD is consulted: <code>args.inputs.controller_one.dpad_left</code>, <code>args.inputs.controller_one.dpad_right</code></li>
<li>  Keyboard's a, d keys (WASD): <code>args.inputs.keyboard.(a|d)</code></li>
<li>  Keyboard's left/right arrow keys: <code>args.inputs.keyboard.(left|right)_arrow</code></li>
</ul>
<h2 id='----left_right_directional-'><code>left_right_directional</code> <a style='font-size: small; float: right;' href='#----left_right_directional-'>link</a></h2> 
<p>
Returns <code>-1</code> (left), <code>0</code> (neutral), or <code>+1</code> (right). This method is aliased to <code>args.inputs.left_right_arrow</code>.
</p>
<p>
The following inputs are inspected to determine the result:
</p>
<ul>
<li>  Keyboard's left/right arrow keys: <code>args.inputs.keyboard.(left|right)_arrow</code></li>
<li>  Controller One's DPAD (if a controller is connected): <code>args.inputs.controller_one.dpad_left</code>, <code>args.inputs.controller_one.dpad_right</code></li>
<li>  WASD and Controller One's Left Analog Stick are NOT consulted.</li>
</ul>
<h2 id='----left_right_directional_perc-'><code>left_right_directional_perc</code> <a style='font-size: small; float: right;' href='#----left_right_directional_perc-'>link</a></h2> 
<p>
Returns a floating point value between <code>-1</code> and <code>1</code>. The following inputs are inspected to determine the result:
</p>
<ul>
<li>  Controller One's Left Analog (if a controller is connected and the value is not 0.0): <code>args.inputs.controller_one.left_analog_x_perc</code></li>
<li>  If the left analog isn't being used, then Controller One's DPAD is consulted: <code>args.inputs.controller_one.dpad_left</code>, <code>args.inputs.controller_one.dpad_right</code></li>
<li>  Keyboard's left/right arrow keys: <code>args.inputs.keyboard.(left|right)_arrow</code></li>
<li>  WASD is NOT consulted.</li>
</ul>
<p>
Here is some sample code to help visualize the <code>left_right</code> functions.
</p>
<pre><code class="ruby">def tick args
  args.outputs.debug &lt;&lt; "* Variations of args.inputs.left_right"
  args.outputs.debug &lt;&lt; "  args.inputs.left_right(_with_wasd) #{args.inputs.left_right}"
  args.outputs.debug &lt;&lt; "  args.inputs.left_right_perc(_with_wasd) #{args.inputs.left_right_perc}"
  args.outputs.debug &lt;&lt; "  args.inputs.left_right_directional #{args.inputs.left_right_directional}"
  args.outputs.debug &lt;&lt; "  args.inputs.left_right_directional_perc #{args.inputs.left_right_directional_perc}"
  args.outputs.debug &lt;&lt; "** Keyboard"
  args.outputs.debug &lt;&lt; "   args.inputs.keyboard.a #{args.inputs.keyboard.a}"
  args.outputs.debug &lt;&lt; "   args.inputs.keyboard.d #{args.inputs.keyboard.d}"
  args.outputs.debug &lt;&lt; "   args.inputs.keyboard.left_arrow #{args.inputs.keyboard.left_arrow}"
  args.outputs.debug &lt;&lt; "   args.inputs.keyboard.right_arrow #{args.inputs.keyboard.right_arrow}"
  args.outputs.debug &lt;&lt; "** Controller"
  args.outputs.debug &lt;&lt; "   args.inputs.controller_one.dpad_left #{args.inputs.controller_one.dpad_left}"
  args.outputs.debug &lt;&lt; "   args.inputs.controller_one.dpad_right #{args.inputs.controller_one.dpad_right}"
  args.outputs.debug &lt;&lt; "   args.inputs.controller_one.left_analog_x_perc #{args.inputs.controller_one.left_analog_x_perc}"
end
</code></pre>
<h2 id='----up_down-'><code>up_down</code> <a style='font-size: small; float: right;' href='#----up_down-'>link</a></h2> 
<p>
Returns <code>-1</code> (down), <code>0</code> (neutral), or <code>+1</code> (up). This method is aliased to <code>args.inputs.up_down_with_wasd</code>.
</p>
<p>
The following inputs are inspected to determine the result:
</p>
<ul>
<li>  Keyboard's up/down arrow keys: <code>args.inputs.keyboard.(up|down)_arrow</code></li>
<li>  Keyboard's w, s keys (WASD): <code>args.inputs.keyboard.(w|s)</code></li>
<li>  Controller One's DPAD (if a controller is connected): <code>args.inputs.controller_one.dpad_up</code>, <code>args.inputs.controller_one.dpad_down</code></li>
<li>  Controller One's Up Analog (if a controller is connected): <code>args.inputs.controller_one.up_analog_y_perc.abs &gt;= 0.6</code></li>
</ul>
<h2 id='----up_down_directional-'><code>up_down_directional</code> <a style='font-size: small; float: right;' href='#----up_down_directional-'>link</a></h2> 
<p>
Returns <code>-1</code> (down), <code>0</code> (neutral), or <code>+1</code> (up). This method is aliased to <code>args.inputs.up_down_arrow</code>.
</p>
<p>
The following inputs are inspected to determine the result:
</p>
<ul>
<li>  Keyboard's up/down arrow keys: <code>args.inputs.keyboard.(up|down)_arrow</code></li>
<li>  Controller One's DPAD (if a controller is connected): <code>args.inputs.controller_one.dpad_up</code>, <code>args.inputs.controller_one.dpad_down</code></li>
<li>  WASD and Controller One's Left Analog Stick are NOT consulted.</li>
</ul>
<h2 id='----up_down_perc-'><code>up_down_perc</code> <a style='font-size: small; float: right;' href='#----up_down_perc-'>link</a></h2> 
<p>
Returns a floating point value between <code>-1</code> and <code>1</code>. The following inputs are inspected to determine the result:
</p>
<ul>
<li>  Controller One's Left Analog (if a controller is connected and the value is not 0.0): <code>args.inputs.controller_one.up_analog_y_perc</code></li>
<li>  If the left analog isn't being used, then Controller One's DPAD is consulted: <code>args.inputs.controller_one.dpad_up</code>, <code>args.inputs.controller_one.dpad_down</code></li>
<li>  Keyboard's up/down arrow keys: <code>args.inputs.keyboard.(up|down)_arrow</code></li>
</ul>
<p>
Here is some sample code to help visualize the <code>up_down</code> functions.
</p>
<pre><code class="ruby">def tick args
  args.outputs.debug &lt;&lt; "* Variations of args.inputs.up_down"
  args.outputs.debug &lt;&lt; "  args.inputs.up_down(_with_wasd) #{args.inputs.up_down}"
  args.outputs.debug &lt;&lt; "  args.inputs.up_down_perc(_with_wasd) #{args.inputs.up_down_perc}"
  args.outputs.debug &lt;&lt; "  args.inputs.up_down_directional #{args.inputs.up_down_directional}"
  args.outputs.debug &lt;&lt; "  args.inputs.up_down_directional_perc #{args.inputs.up_down_directional_perc}"
  args.outputs.debug &lt;&lt; "** Keyboard"
  args.outputs.debug &lt;&lt; "   args.inputs.keyboard.w #{args.inputs.keyboard.w}"
  args.outputs.debug &lt;&lt; "   args.inputs.keyboard.s #{args.inputs.keyboard.s}"
  args.outputs.debug &lt;&lt; "   args.inputs.keyboard.up_arrow #{args.inputs.keyboard.up_arrow}"
  args.outputs.debug &lt;&lt; "   args.inputs.keyboard.down_arrow #{args.inputs.keyboard.down_arrow}"
  args.outputs.debug &lt;&lt; "** Controller"
  args.outputs.debug &lt;&lt; "   args.inputs.controller_one.dpad_up #{args.inputs.controller_one.dpad_up}"
  args.outputs.debug &lt;&lt; "   args.inputs.controller_one.dpad_down #{args.inputs.controller_one.dpad_down}"
  args.outputs.debug &lt;&lt; "   args.inputs.controller_one.left_analog_y_perc #{args.inputs.controller_one.left_analog_y_perc}"
end
</code></pre>
<h2 id='----text-'><code>text</code> <a style='font-size: small; float: right;' href='#----text-'>link</a></h2> 
<p>
Returns a string that represents the last key that was pressed on the keyboard.
</p>
<h2 id='---mouse---args-inputs-mouse--'>Mouse (<code>args.inputs.mouse</code>) <a style='font-size: small; float: right;' href='#---mouse---args-inputs-mouse--'>link</a></h2> 
<p>
Represents the user's mouse.
</p>
<h3 id='-----has_focus-'><code>has_focus</code> <a style='font-size: small; float: right;' href='#-----has_focus-'>link</a></h3> 
<p>
Returns true if the game has mouse focus.
</p>
<h3 id='-----x-'><code>x</code> <a style='font-size: small; float: right;' href='#-----x-'>link</a></h3> 
<p>
Returns the current <code>x</code> location of the mouse.
</p>
<h3 id='-----y-'><code>y</code> <a style='font-size: small; float: right;' href='#-----y-'>link</a></h3> 
<p>
Returns the current <code>y</code> location of the mouse.
</p>
<h3 id='-----previous_x-'><code>previous_x</code> <a style='font-size: small; float: right;' href='#-----previous_x-'>link</a></h3> 
<p>
Returns the x location of the mouse on the previous frame.
</p>
<h3 id='-----previous_y-'><code>previous_y</code> <a style='font-size: small; float: right;' href='#-----previous_y-'>link</a></h3> 
<p>
Returns the y location of the mouse on the previous frame.
</p>
<h3 id='-----relative_x-'><code>relative_x</code> <a style='font-size: small; float: right;' href='#-----relative_x-'>link</a></h3> 
<p>
Returns the difference between the current x location of the mouse and its previous x location.
</p>
<h3 id='-----relative_y-'><code>relative_y</code> <a style='font-size: small; float: right;' href='#-----relative_y-'>link</a></h3> 
<p>
Returns the difference between the current y location of the mouse and its previous y location.
</p>
<h3 id='-----inside_rect--rect-'><code>inside_rect? rect</code> <a style='font-size: small; float: right;' href='#-----inside_rect--rect-'>link</a></h3> 
<p>
Return. <code>args.inputs.mouse.inside_rect?</code> takes in any primitive that responds to <code>x, y, w, h</code>:
</p>
<h3 id='-----inside_circle--center_point--radius-'><code>inside_circle? center_point, radius</code> <a style='font-size: small; float: right;' href='#-----inside_circle--center_point--radius-'>link</a></h3> 
<p>
Returns <code>true</code> if the mouse is inside of a specified circle. <code>args.inputs.mouse.inside_circle?</code> takes in any primitive that responds to <code>x, y</code> (which represents the circle's center), and takes in a <code>radius</code>:
</p>
<h3 id='-----moved-'><code>moved</code> <a style='font-size: small; float: right;' href='#-----moved-'>link</a></h3> 
<p>
Returns <code>true</code> if the mouse has moved on the current frame.
</p>
<h3 id='-----button_left-'><code>button_left</code> <a style='font-size: small; float: right;' href='#-----button_left-'>link</a></h3> 
<p>
Returns <code>true</code> if the left mouse button is down.
</p>
<h3 id='-----button_middle-'><code>button_middle</code> <a style='font-size: small; float: right;' href='#-----button_middle-'>link</a></h3> 
<p>
Returns <code>true</code> if the middle mouse button is down.
</p>
<h3 id='-----button_right-'><code>button_right</code> <a style='font-size: small; float: right;' href='#-----button_right-'>link</a></h3> 
<p>
Returns <code>true</code> if the right mouse button is down.
</p>
<h3 id='-----button_bits-'><code>button_bits</code> <a style='font-size: small; float: right;' href='#-----button_bits-'>link</a></h3> 
<p>
Returns a bitmask for all buttons on the mouse: <code>1</code> for a button in the <code>down</code> state, <code>0</code> for a button in the <code>up</code> state.
</p>
<p>
Here is a snippet to help visualize all mouse button states:
</p>
<pre><code class="ruby">def tick args
  args.outputs.debug.watch "button_left:   #{inputs.mouse.button_left}"
  args.outputs.debug.watch "button_middle: #{inputs.mouse.button_middle}"
  args.outputs.debug.watch "button_right:  #{inputs.mouse.button_right}"
  args.outputs.debug.watch "button_bits:   #{inputs.mouse.button_bits.to_s(2)}"
end
</code></pre>
<h3 id='-----wheel-'><code>wheel</code> <a style='font-size: small; float: right;' href='#-----wheel-'>link</a></h3> 
<p>
Represents the mouse wheel. Returns <code>nil</code> if no mouse wheel actions occurred. Otherwise <code>args.inputs.mouse.wheel</code> will return a <code>Hash</code> with <code>x</code>, and <code>y</code> (representing movement on each axis).
</p>
<h3 id='-----click--or--down----previous_click----up-'><code>click</code> OR <code>down</code>, <code>previous_click</code>, <code>up</code> <a style='font-size: small; float: right;' href='#-----click--or--down----previous_click----up-'>link</a></h3> 
<p>
The properties <code>args.inputs.mouse.(click|down|previous_click|up)</code> each return <code>nil</code> if the mouse button event didn't occur. And return an Entity that has an <code>x</code>, <code>y</code> properties along with helper functions to determine collision: <code>inside_rect?</code>, <code>inside_circle</code>. This value will be true if any of the mouse's buttons caused these events. To scope to a specific button use <code>.button_left</code>, <code>.button_middle</code>, <code>.button_right</code>, or <code>.button_bits</code>.
</p>
<h3 id='-----key_down-'><code>key_down</code> <a style='font-size: small; float: right;' href='#-----key_down-'>link</a></h3> 
<p>
Returns <code>true</code> if the specific button was pressed on this frame. <code>args.inputs.mouse.key_down.BUTTON</code> will only be true on the frame it was pressed.
</p>
<p>
The following <code>BUTTON</code> values are applicable for <code>key_down</code>, <code>key_held</code>, and <code>key_up</code>:
</p>
<ul>
<li><code>left</code> (eg <code>args.inputs.mouse.key_down.left</code>)</li>
<li><code>middle</code></li>
<li><code>right</code></li>
<li><code>x1</code></li>
<li><code>x2</code></li>
</ul>
<h3 id='-----key_held-'><code>key_held</code> <a style='font-size: small; float: right;' href='#-----key_held-'>link</a></h3> 
<p>
Returns <code>true</code> if the specific button is being held. <code>args.inputs.mouse.key_held.BUTTON</code> will be true for all frames after <code>key_down</code> (until released).
</p>
<h3 id='-----key_up-'><code>key_up</code> <a style='font-size: small; float: right;' href='#-----key_up-'>link</a></h3> 
<p>
Returns <code>true</code> if the specific button was released. <code>args.inputs.mouse.key_up.BUTTON</code> will be true only on the frame the button was released.
</p>
<p>
?&gt; For a full demonstration of all mouse button states, refer to the sample app located at <code>./samples/02_input_basics/02_mouse_properties</code>
</p>
<h3 id='-----buttons-'><code>buttons</code> <a style='font-size: small; float: right;' href='#-----buttons-'>link</a></h3> 
<p>
<code>args.inputs.mouse.buttons</code> provides additional mouse properties, specifically for determining if a specific button is held and dragging vs just being clicked.
</p>
<p>
Properites for <code>args.inputs.mouse.buttons</code> are:
</p>
<ul>
<li><code>left</code></li>
<li><code>middle</code></li>
<li><code>right</code></li>
<li><code>x1</code></li>
<li><code>x2</code></li>
</ul>
<p>
Example:
</p>
<pre><code class="ruby">def tick args
  if args.inputs.mouse.buttons.left.buffered_click
    GTK.notify "buffered_click occurred #{args.inputs.mouse.buttons.left.id}"
  end

  if args.inputs.mouse.buttons.left.buffered_held
    GTK.notify "buffered_held occurred"
  end
end
</code></pre>
<p>
Button properties:
</p>
<ul>
<li><code>id</code>: returns <code>:left</code>, <code>:middle</code>, <code>:right</code>, <code>:x1</code>, <code>:x2</code></li>
<li><code>index</code>: returns <code>0</code>, <code>1</code>, <code>2</code>, <code>3</code>, <code>4</code></li>
<li><code>click</code>: Returns a truthy value for if button was clicked/down.</li>
<li><code>click_at</code>: Returns <code>Kernel.tick_count</code> that button was clicked/down.</li>
<li><code>global_click_at</code>: Returns <code>Kernel.global_tick_count</code> that button was clicked/down.</li>
<li><code>up</code>: Returns a truthy value for if button was up/released.</li>
<li><code>up_at</code>: Returns <code>Kernel.tick_count</code> that button was up/released.</li>
<li><code>global_up_at</code>: Returns <code>Kernel.global_tick_count</code> that button was up/released.</li>
<li><code>held</code>: Returns a truthy value for if button was held.</li>
<li><code>held_at</code>: Returns <code>Kernel.tick_count</code> that button was held.</li>
<li><code>global_held_at</code>: Returns <code>Kernel.global_tick_count</code> that button was held.</li>
<li><code>buffered_click</code>: Returns <code>true</code> if button has been exclusively determined to be a click (and won't be considered held).</li>
<li><code>buffered_held</code>: Returns <code>true</code> if button has been exclusively determined to be hed (and won't be considered clicked).</li>
</ul>
<h2 id='---touch'>Touch <a style='font-size: small; float: right;' href='#---touch'>link</a></h2> 
<p>
The following touch apis are available on touch devices (iOS, Android, Mobile Web, Surface).
</p>
<h3 id='-----args-inputs-touch-'><code>args.inputs.touch</code> <a style='font-size: small; float: right;' href='#-----args-inputs-touch-'>link</a></h3> 
<p>
Returns a <code>Hash</code> representing all touch points on a touch device.
</p>
<h3 id='-----args-inputs-finger_left-'><code>args.inputs.finger_left</code> <a style='font-size: small; float: right;' href='#-----args-inputs-finger_left-'>link</a></h3> 
<p>
Returns a <code>Hash</code> with <code>x</code> and <code>y</code> denoting a touch point that is on the left side of the screen.
</p>
<h3 id='-----args-inputs-finger_right-'><code>args.inputs.finger_right</code> <a style='font-size: small; float: right;' href='#-----args-inputs-finger_right-'>link</a></h3> 
<p>
Returns a <code>Hash</code> with <code>x</code> and <code>y</code> denoting a touch point that is on the right side of the screen.
</p>
<h2 id='---controller---args-inputs-controller_-one-four---'>Controller (<code>args.inputs.controller_(one-four)</code>) <a style='font-size: small; float: right;' href='#---controller---args-inputs-controller_-one-four---'>link</a></h2> 
<p>
Represents controllers connected to the usb ports. There is also <code>args.inputs.controllers</code> which returns controllers one through four as an array (<code>args.inputs.controllers[0]</code> points to <code>args.inputs.controller_one</code>).
</p>
<h3 id='-----connected-'><code>connected</code> <a style='font-size: small; float: right;' href='#-----connected-'>link</a></h3> 
<p>
Returns <code>true</code> if a controller is connected. If this value is <code>false</code>, controller properties will not be <code>nil</code>, but return <code>0</code> for directional based properties and <code>false</code> button state properties.
</p>
<h3 id='-----name-'><code>name</code> <a style='font-size: small; float: right;' href='#-----name-'>link</a></h3> 
<p>
String value representing the controller's name.
</p>
<h3 id='-----active-'><code>active</code> <a style='font-size: small; float: right;' href='#-----active-'>link</a></h3> 
<p>
Returns true if any of the controller's buttons were used.
</p>
<h3 id='-----up-'><code>up</code> <a style='font-size: small; float: right;' href='#-----up-'>link</a></h3> 
<p>
Returns <code>true</code> if <code>up</code> is pressed or held on the directional or left analog.
</p>
<h3 id='-----down-'><code>down</code> <a style='font-size: small; float: right;' href='#-----down-'>link</a></h3> 
<p>
Returns <code>true</code> if <code>down</code> is pressed or held on the directional or left analog.
</p>
<h3 id='-----left-'><code>left</code> <a style='font-size: small; float: right;' href='#-----left-'>link</a></h3> 
<p>
Returns <code>true</code> if <code>left</code> is pressed or held on the directional or left analog.
</p>
<h3 id='-----right-'><code>right</code> <a style='font-size: small; float: right;' href='#-----right-'>link</a></h3> 
<p>
Returns <code>true</code> if <code>right</code> is pressed or held on the directional or left analog.
</p>
<h3 id='-----left_right-'><code>left_right</code> <a style='font-size: small; float: right;' href='#-----left_right-'>link</a></h3> 
<p>
Returns <code>-1</code> (left), <code>0</code> (neutral), or <code>+1</code> (right) depending on results of <code>args.inputs.controller_(one-four).left</code> and <code>args.inputs.controller_(one-four).right</code>.
</p>
<h3 id='-----up_down-'><code>up_down</code> <a style='font-size: small; float: right;' href='#-----up_down-'>link</a></h3> 
<p>
Returns <code>-1</code> (down), <code>0</code> (neutral), or <code>+1</code> (up) depending on results of <code>args.inputs.controller_(one-four).up</code> and <code>args.inputs.controller_(one-four).down</code>.
</p>
<h3 id='------left-right-_analog_x_raw-'><code>(left|right)_analog_x_raw</code> <a style='font-size: small; float: right;' href='#------left-right-_analog_x_raw-'>link</a></h3> 
<p>
Returns the raw integer value for the analog's horizontal movement (<code>-32,767 to +32,767</code>).
</p>
<h3 id='------left-right-_analog_y_raw-'><code>(left|right)_analog_y_raw</code> <a style='font-size: small; float: right;' href='#------left-right-_analog_y_raw-'>link</a></h3> 
<p>
Returns the raw integer value for the analog's vertical movement (<code>-32,767 to +32,767</code>).
</p>
<h3 id='------left-right-_analog_x_perc-'><code>(left|right)_analog_x_perc</code> <a style='font-size: small; float: right;' href='#------left-right-_analog_x_perc-'>link</a></h3> 
<p>
Returns a number between <code>-1</code> and <code>1</code> which represents the percentage the analog is moved horizontally as a ratio of the maximum horizontal movement.
</p>
<h3 id='------left-right-_analog_y_perc-'><code>(left|right)_analog_y_perc</code> <a style='font-size: small; float: right;' href='#------left-right-_analog_y_perc-'>link</a></h3> 
<p>
Returns a number between <code>-1</code> and <code>1</code> which represents the percentage the analog is moved vertically as a ratio of the maximum vertical movement.
</p>
<h3 id='-----dpad_up----directional_up-'><code>dpad_up</code>, <code>directional_up</code> <a style='font-size: small; float: right;' href='#-----dpad_up----directional_up-'>link</a></h3> 
<p>
Returns <code>true</code> if <code>up</code> is pressed or held on the dpad.
</p>
<h3 id='-----dpad_down----directional_down-'><code>dpad_down</code>, <code>directional_down</code> <a style='font-size: small; float: right;' href='#-----dpad_down----directional_down-'>link</a></h3> 
<p>
Returns <code>true</code> if <code>down</code> is pressed or held on the dpad.
</p>
<h3 id='-----dpad_left----directional_left-'><code>dpad_left</code>, <code>directional_left</code> <a style='font-size: small; float: right;' href='#-----dpad_left----directional_left-'>link</a></h3> 
<p>
Returns <code>true</code> if <code>left</code> is pressed or held on the dpad.
</p>
<h3 id='-----dpad_right----directional_right-'><code>dpad_right</code>, <code>directional_right</code> <a style='font-size: small; float: right;' href='#-----dpad_right----directional_right-'>link</a></h3> 
<p>
Returns <code>true</code> if <code>right</code> is pressed or held on the dpad.
</p>
<h3 id='------a-b-x-y-l1-r1-l2-r2-l3-r3-start-select--'><code>(a|b|x|y|l1|r1|l2|r2|l3|r3|start|select)</code> <a style='font-size: small; float: right;' href='#------a-b-x-y-l1-r1-l2-r2-l3-r3-start-select--'>link</a></h3> 
<p>
Returns <code>true</code> if the specific button is pressed or held. Note: For PS4 and PS5 controllers <code>a</code> maps to Cross, <code>b</code> maps to Circle, <code>x</code> maps to Square, and <code>y</code> maps to Triangle.
</p>
<h3 id='-----truthy_keys-'><code>truthy_keys</code> <a style='font-size: small; float: right;' href='#-----truthy_keys-'>link</a></h3> 
<p>
Returns a collection of <code>Symbol</code>s that represent all keys that are in the pressed or held state.
</p>
<h3 id='-----key_down-'><code>key_down</code> <a style='font-size: small; float: right;' href='#-----key_down-'>link</a></h3> 
<p>
Returns <code>true</code> if the specific button was pressed on this frame. <code>args.inputs.controller_(one-four).key_down.BUTTON</code> will only be true on the frame it was pressed.
</p>
<h3 id='-----key_held-'><code>key_held</code> <a style='font-size: small; float: right;' href='#-----key_held-'>link</a></h3> 
<p>
Returns <code>true</code> if the specific button is being held. <code>args.inputs.controller_(one-four).key_held.BUTTON</code> will be true for all frames after <code>key_down</code> (until released).
</p>
<h3 id='-----key_up-'><code>key_up</code> <a style='font-size: small; float: right;' href='#-----key_up-'>link</a></h3> 
<p>
Returns <code>true</code> if the specific button was released. <code>args.inputs.controller_(one-four).key_up.BUTTON</code> will be true only on the frame the button was released.
</p>
<h3 id='-----left_analog_active--threshold_raw---threshold_perc---'><code>left_analog_active?(threshold_raw:, threshold_perc:)</code> <a style='font-size: small; float: right;' href='#-----left_analog_active--threshold_raw---threshold_perc---'>link</a></h3> 
<p>
Returns true if the Left Analog Stick is tilted. The <code>threshold_raw</code> and <code>threshold_perc</code> are optional parameters that can be used to set the minimum threshold for the analog stick to be considered active. The <code>threshold_raw</code> is a number between 0 and 32,767, and the <code>threshold_perc</code> is a number between 0 and 1.
</p>
<h3 id='-----right_analog_active--threshold_raw---threshold_perc---'><code>right_analog_active?(threshold_raw:, threshold_perc:)</code> <a style='font-size: small; float: right;' href='#-----right_analog_active--threshold_raw---threshold_perc---'>link</a></h3> 
<p>
Returns true if the Right Analog Stick is tilted. The <code>threshold_raw</code> and <code>threshold_perc</code> are optional parameters that can be used to set the minimum threshold for the analog stick to be considered active. The <code>threshold_raw</code> is a number between 0 and 32,767, and the <code>threshold_perc</code> is a number between 0 and 1.
</p>
<h3 id='------left-right-_analog_angle-'><code>(left|right)_analog_angle</code> <a style='font-size: small; float: right;' href='#------left-right-_analog_angle-'>link</a></h3> 
<p>
Returns the angle of the analog in degrees for each analog stick. You can call <code>.to_radius</code> on the return value to get the angle in radians.
</p>
<h3 id='-----analog_dead_zone-'><code>analog_dead_zone</code> <a style='font-size: small; float: right;' href='#-----analog_dead_zone-'>link</a></h3> 
<p>
The default value for this property is <code>3600</code>. You can set this to a lower value for a more responsive analog stick, though it's not recommended (the Steam Deck analog sticks don't always settle back to a value lower than <code>3600</code>).
</p>
<h3 id='-----key_state--key--'><code>key_STATE?(key)</code> <a style='font-size: small; float: right;' href='#-----key_state--key--'>link</a></h3> 
<p>
There are situations where you may want to get the status of a key dynamically as opposed to accessing a property. The following methods are provided to assist in this:
</p>
<ul>
<li><code>key_down?(key)</code></li>
<li><code>key_up?(key)</code></li>
<li><code>key_held?(key)</code></li>
<li><code>key_down_or_held?(key)</code></li>
</ul>
<p>
Given a symbol, these functions return <code>true</code> or <code>false</code> if the key is in the current state.
</p>
<p>
Here's how each of these methods are equivalent to key-based methods:
</p>
<pre><code class="ruby"># key_down equivalent
args.inputs.controller_one.key_down.enter
args.inputs.controller_one.key_down?(:enter)

# key_up
args.inputs.controller_one.key_up.enter
args.inputs.controller_one.key_up?(:enter)

# key held
args.inputs.controller_one.key_held.enter
args.inputs.controller_one.key_held?(:enter)

# key down or held
args.inputs.controller_one.enter
args.inputs.controller_one.key_down_or_held?(:enter)
</code></pre>
<h2 id='---keyboard-or-controller-on---args-inputs-key_-down-up-held---'>Keyboard or Controller On (<code>args.inputs.key_(down|up|held)</code>) <a style='font-size: small; float: right;' href='#---keyboard-or-controller-on---args-inputs-key_-down-up-held---'>link</a></h2> 
<p>
Represents directional input that is shared between keyboard and controller. Useful for supporting directional input that can come from keyboard or controller.
</p>
<p>
Shared keys:
</p>
<ul>
<li><code>left</code></li>
<li><code>right</code></li>
<li><code>up</code></li>
<li><code>down</code></li>
</ul>
<p>
Demonstration:
</p>
<pre><code class="ruby">def tick args
  args.outputs.watch "args.inputs.left                         #{args.inputs.left}"
  args.outputs.watch "args.inputs.keyboard.left                #{args.inputs.keyboard.left}"
  args.outputs.watch "args.inputs.controller_one.left          #{args.inputs.controller_one.left}"
  args.outputs.watch "args.inputs.key_down.left                #{args.inputs.key_down.left}"
  args.outputs.watch "args.inputs.keyboard.key_down.left       #{args.inputs.keyboard.key_down.left}"
  args.outputs.watch "args.inputs.controller_one.key_down.left #{args.inputs.controller_one.key_down.left}"
  args.outputs.watch "args.inputs.key_held.left                #{args.inputs.key_held.left}"
  args.outputs.watch "args.inputs.keyboard.key_held.left       #{args.inputs.keyboard.key_held.left}"
  args.outputs.watch "args.inputs.controller_one.key_held.left #{args.inputs.controller_one.key_held.left}"
  args.outputs.watch "args.inputs.key_up.left                  #{args.inputs.key_up.left}"
  args.outputs.watch "args.inputs.keyboard.key_up.left         #{args.inputs.keyboard.key_up.left}"
  args.outputs.watch "args.inputs.controller_one.key_up.left   #{args.inputs.controller_one.key_up.left}"
end
</code></pre>
<h2 id='---keyboard---args-inputs-keyboard--'>Keyboard (<code>args.inputs.keyboard</code>) <a style='font-size: small; float: right;' href='#---keyboard---args-inputs-keyboard--'>link</a></h2> 
<p>
Represents the user's keyboard.
</p>
<h3 id='-----active-'><code>active</code> <a style='font-size: small; float: right;' href='#-----active-'>link</a></h3> 
<p>
Returns <code>Kernel.tick_count</code> if any keys on the keyboard were pressed.
</p>
<h3 id='-----has_focus-'><code>has_focus</code> <a style='font-size: small; float: right;' href='#-----has_focus-'>link</a></h3> 
<p>
Returns <code>true</code> if the game has keyboard focus.
</p>
<h3 id='-----up-'><code>up</code> <a style='font-size: small; float: right;' href='#-----up-'>link</a></h3> 
<p>
Returns <code>true</code> if <code>up</code> or <code>w</code> is pressed or held on the keyboard.
</p>
<h3 id='-----down-'><code>down</code> <a style='font-size: small; float: right;' href='#-----down-'>link</a></h3> 
<p>
Returns <code>true</code> if <code>down</code> or <code>s</code> is pressed or held on the keyboard.
</p>
<h3 id='-----left-'><code>left</code> <a style='font-size: small; float: right;' href='#-----left-'>link</a></h3> 
<p>
Returns <code>true</code> if <code>left</code> or <code>a</code> is pressed or held on the keyboard.
</p>
<h3 id='-----right-'><code>right</code> <a style='font-size: small; float: right;' href='#-----right-'>link</a></h3> 
<p>
Returns <code>true</code> if <code>right</code> or <code>d</code> is pressed or held on the keyboard.
</p>
<h3 id='-----left_right-'><code>left_right</code> <a style='font-size: small; float: right;' href='#-----left_right-'>link</a></h3> 
<p>
Returns <code>-1</code> (left), <code>0</code> (neutral), or <code>+1</code> (right) depending on results of <code>args.inputs.keyboard.left</code> and <code>args.inputs.keyboard.right</code>.
</p>
<h3 id='-----up_down-'><code>up_down</code> <a style='font-size: small; float: right;' href='#-----up_down-'>link</a></h3> 
<p>
Returns <code>-1</code> (left), <code>0</code> (neutral), or <code>+1</code> (right) depending on results of <code>args.inputs.keyboard.up</code> and <code>args.inputs.keyboard.up</code>.
</p>
<h3 id='----keyboard-properties'>Keyboard properties <a style='font-size: small; float: right;' href='#----keyboard-properties'>link</a></h3> 
<p>
The following properties represent keys on the keyboard and are available on <code>args.inputs.keyboard.KEY</code>, <code>args.inputs.keyboard.key_down.KEY</code>, <code>args.inputs.keyboard.key_held.KEY</code>, <code>args.inputs.keyboard.key_up.KEY</code>, <code>args.inputs.keyboard.key_repeat.KEY</code>:
</p>
<p>
Here is an example showing all the ways to access a key's state:
</p>
<pre><code class="ruby">def tick args
  # create a value in state to
  # track tick_count of the G key
  args.state.g_key ||= {
    ctrl_at: nil,
    key_down_at: nil,
    key_repeat_at: nil,
    key_last_repeat_at: nil,
    key_held_at: nil,
    key_down_or_held_at: nil,
    key_up_at: nil,
  }

  # for each keyboard event, capture the tick_count
  # that the event occurred
  # Ctrl + G
  if args.inputs.keyboard.ctrl_g
    args.state.g_key.ctrl_at = args.inputs.keyboard.ctrl_g
  end

  # G pressed/down
  if args.inputs.keyboard.key_down.g
    args.state.g_key.key_down_at = args.inputs.keyboard.key_down.g
  end

  # G pressed or repeated (based on OS key repeat speed)
  if args.inputs.keyboard.key_repeat.g
    args.state.g_key.key_last_repeat_at = args.state.g_key.key_repeat_at
    args.state.g_key.key_repeat_at = args.inputs.keyboard.key_repeat.g
  end

  # G held
  if args.inputs.keyboard.key_held.g
    args.state.g_key.key_held_at = args.inputs.keyboard.key_held.g
  end

  # G down or held
  if args.inputs.keyboard.g
    args.state.g_key.key_down_or_held_at = args.inputs.keyboard.g
  end

  # G up
  if args.inputs.keyboard.key_up.g
    args.state.g_key.key_up_at = args.inputs.keyboard.key_up.g
  end

  # display the tick_count of each event
  args.outputs.debug.watch "ctrl+g?         #{args.state.g_key.ctrl_at}"
  args.outputs.debug.watch "g down?         #{args.state.g_key.key_down_at}"
  args.outputs.debug.watch "g repeat?       #{args.state.g_key.key_repeat_at}"
  args.outputs.debug.watch "g last_repeat?  #{args.state.g_key.key_last_repeat_at}"
  args.outputs.debug.watch "g held?         #{args.state.g_key.key_held_at}"
  args.outputs.debug.watch "g down or held? #{args.state.g_key.key_down_or_held_at}"
  args.outputs.debug.watch "g up?           #{args.state.g_key.key_up_at}"
end
</code></pre>
<ul>
<li>  <code>alt</code></li>
<li>  <code>meta</code></li>
<li>  <code>control</code>, <code>ctrl</code></li>
<li>  <code>shift</code></li>
<li>  <code>control_KEY</code>, <code>ctrl_KEY</code> (dynamic method, eg <code>args.inputs.keyboard.ctrl_a</code>)</li>
<li>  <code>exclamation_point</code></li>
<li>  <code>zero</code> - <code>nine</code></li>
<li>  <code>backspace</code></li>
<li>  <code>delete</code></li>
<li>  <code>escape</code></li>
<li>  <code>enter</code></li>
<li>  <code>tab</code></li>
<li>  <code>(open|close)_round_brace</code></li>
<li>  <code>(open|close)_curly_brace</code></li>
<li>  <code>(open|close)_square_brace</code></li>
<li>  <code>colon</code></li>
<li>  <code>semicolon</code></li>
<li>  <code>equal</code></li>
<li>  <code>hyphen</code></li>
<li>  <code>space</code></li>
<li>  <code>dollar</code></li>
<li>  <code>percent</code></li>
<li>  <code>double_quotation_mark</code></li>
<li>  <code>single_quotation_mark</code></li>
<li>  <code>backtick</code></li>
<li>  <code>tilde</code></li>
<li>  <code>period</code></li>
<li>  <code>comma</code></li>
<li>  <code>pipe</code></li>
<li>  <code>underscore</code></li>
<li>  <code>ac_back</code> (<code>ac</code> stands for Application Control, with <code>ac_back</code> representing Back button on a device (eg Android back button)</li>
<li>  <code>ac_home</code></li>
<li>  <code>ac_forward</code></li>
<li>  <code>ac_stop</code></li>
<li>  <code>ac_refresh</code></li>
<li>  <code>ac_bookmarks</code></li>
<li>  <code>a</code> - <code>z</code></li>
<li>  <code>w_scancode</code> (key location for w in WASD layout across regions)</li>
<li>  <code>a_scancode</code> (key location for a in WASD layout across regions)</li>
<li>  <code>s_scancode</code> (key location for s in WASD layout across regions)</li>
<li>  <code>d_scancode</code> (key location for d in WASD layout across regions)</li>
<li>  <code>shift</code></li>
<li>  <code>shift_left</code></li>
<li>  <code>shift_right</code></li>
<li>  <code>control</code>, <code>ctrl</code></li>
<li>  <code>control_left</code>, <code>ctrl_left</code></li>
<li>  <code>control_right</code>, <code>ctrl_right</code></li>
<li>  <code>alt</code>, <code>option</code></li>
<li>  <code>alt_left</code>, <code>option_left</code></li>
<li>  <code>alt_right</code>, <code>option_right</code></li>
<li>  <code>meta</code>, <code>command</code></li>
<li>  <code>meta_left</code>, <code>command_left</code></li>
<li>  <code>meta_right</code>, <code>command_right</code></li>
<li>  <code>left_arrow</code></li>
<li>  <code>right_arrow</code></li>
<li>  <code>up_arrow</code></li>
<li>  <code>down_arrow</code></li>
<li>  <code>left_arrow</code>, <code>left</code></li>
<li>  <code>right_arrow</code>, <code>right</code></li>
<li>  <code>up_arrow</code>, <code>up</code></li>
<li>  <code>down_arrow</code> <code>down</code></li>
<li>  <code>pageup</code></li>
<li>  <code>pagedown</code></li>
<li>  <code>plus</code></li>
<li>  <code>at</code></li>
<li>  <code>hash</code></li>
<li>  <code>forward_slash</code></li>
<li>  <code>back_slash</code></li>
<li>  <code>asterisk</code></li>
<li>  <code>less_than</code></li>
<li>  <code>greater_than</code></li>
<li>  <code>ampersand</code></li>
<li>  <code>superscript_two</code></li>
<li>  <code>caret</code></li>
<li>  <code>question_mark</code></li>
<li>  <code>section</code></li>
<li>  <code>ordinal_indicator</code></li>
<li>  <code>raw_key</code> (unique numeric identifier for key)</li>
<li>  <code>caps_lock</code></li>
<li>  <code>f1</code></li>
<li>  <code>f2</code></li>
<li>  <code>f3</code></li>
<li>  <code>f4</code></li>
<li>  <code>f5</code></li>
<li>  <code>f6</code></li>
<li>  <code>f7</code></li>
<li>  <code>f8</code></li>
<li>  <code>f9</code></li>
<li>  <code>f10</code></li>
<li>  <code>f11</code></li>
<li>  <code>f12</code></li>
<li>  <code>print_screen</code></li>
<li>  <code>scroll_lock</code></li>
<li>  <code>pause</code></li>
<li>  <code>insert</code></li>
<li>  <code>home</code></li>
<li>  <code>page_up</code></li>
<li>  <code>delete</code></li>
<li>  <code>end</code></li>
<li>  <code>page_down</code></li>
<li>  <code>num_lock</code></li>
<li>  <code>kp_divide</code></li>
<li>  <code>kp_multiply</code></li>
<li>  <code>kp_minus</code></li>
<li>  <code>kp_plus</code></li>
<li>  <code>kp_enter</code></li>
<li>  <code>kp_one</code></li>
<li>  <code>kp_two</code></li>
<li>  <code>kp_three</code></li>
<li>  <code>kp_four</code></li>
<li>  <code>kp_five</code></li>
<li>  <code>kp_six</code></li>
<li>  <code>kp_seven</code></li>
<li>  <code>kp_eight</code></li>
<li>  <code>kp_nine</code></li>
<li>  <code>kp_zero</code></li>
<li>  <code>kp_period</code></li>
<li>  <code>kp_equals</code></li>
<li>  <code>left_right</code></li>
<li>  <code>last_left_right</code></li>
<li>  <code>up_down</code></li>
<li>  <code>last_up_down</code></li>
<li>  <code>directional_vector</code> (returns normalized vector based off of WASD/arrow keys, <code>nil</code> if no keys are down/held)</li>
<li>  <code>last_directional_vector</code> (returns normalized vector based off of WASD/arrow keys, <code>nil</code> if no keys are down/held)</li>
<li>  <code>directional_angle</code> (returns angle in degrees based off of WASD/arrow keys, <code>nil</code> if no keys are down/held)</li>
<li>  <code>truthy_keys</code> (array of <code>Symbols</code>)</li>
</ul>
<h3 id='-----keycodes-'><code>keycodes</code> <a style='font-size: small; float: right;' href='#-----keycodes-'>link</a></h3> 
<p>
If the explicit named key isn't in the list above, you can still get the raw keycode via <code>args.inputs.keyboard.key_(down|held|up).keycodes[KEYCODE_NUMBER]</code>. The <code>KEYCODE_NUMBER</code> represents the keycode provided by SDL.
</p>
<p>
Here is a list SDL Keycodes: &lt;https://wiki.libsdl.org/SDL2/SDLKeycodeLookup&gt;
</p>
<h3 id='-----char-'><code>char</code> <a style='font-size: small; float: right;' href='#-----char-'>link</a></h3> 
<p>
Method is available under <code>inputs.key_down</code>, <code>inputs.key_held</code>, and <code>inputs.key_up</code>. Take note that <code>args.inputs.keyboard.key_held.char</code> will only return the ascii value of the last key that was held. Use <code>args.inputs.keyboard.key_held.truthy_keys</code> to get an <code>Array</code> of <code>Symbols</code> representing all keys being held.
</p>
<p>
To get a picture of all key states <code>args.inputs.keyboard.keys</code> returns a <code>Hash</code> with the following keys: <code>:down</code>, <code>:held</code>, <code>:down_or_held</code>, <code>:up</code>.
</p>
<p>
NOTE: <code>args.inputs.keyboard.key_down.char</code> will be set in line with key repeat behavior of your OS.
</p>
<p>
This is a demonstration of the behavior (see <code>./samples/02_input_basics/01_keyboard</code> for a more detailed example):
</p>
<pre><code class="ruby">def tick args
  # uncomment the line below to see the value changes at a slower rate
  # $gtk.slowmo! 30

  keyboard = args.inputs.keyboard

  args.outputs.labels &lt;&lt; { x: 30,
                           y: 720,
                           text: "use the J key to test" }

  args.outputs.labels &lt;&lt; { x: 30,
                           y: 720 - 30,
                           text: "key_down.char: #{keyboard.key_down.char.inspect}" }

  args.outputs.labels &lt;&lt; { x: 30,
                           y: 720 - 60,
                           text: "key_down.j:    #{keyboard.key_down.j}" }

  args.outputs.labels &lt;&lt; { x: 30,
                           y: 720 - 30,
                           text: "key_held.char: #{keyboard.key_held.char.inspect}" }

  args.outputs.labels &lt;&lt; { x: 30,
                           y: 720 - 60,
                           text: "key_held.j:    #{keyboard.key_held.j}" }

  args.outputs.labels &lt;&lt; { x: 30,
                           y: 720 - 30,
                           text: "key_up.char:   #{keyboard.key_up.char.inspect}" }

  args.outputs.labels &lt;&lt; { x: 30,
                           y: 720 - 60,
                           text: "key_up.j:      #{keyboard.key_up.j}" }
end
</code></pre>
<h3 id='-----key_state--key--'><code>key_STATE?(key)</code> <a style='font-size: small; float: right;' href='#-----key_state--key--'>link</a></h3> 
<p>
There are situations where you may want to get the status of a key dynamically as opposed to accessing a property. The following methods are provided to assist in this:
</p>
<ul>
<li><code>key_down?(key)</code></li>
<li><code>key_up?(key)</code></li>
<li><code>key_held?(key)</code></li>
<li><code>key_down_or_held?(key)</code></li>
<li><code>key_repeat?(key)</code></li>
</ul>
<p>
Given a symbol, these methods return <code>true</code> or <code>false</code> if the key is in the current state.
</p>
<p>
Here's how each of these methods are equivalent to key-based methods:
</p>
<pre><code class="ruby"># key_down equivalent
args.inputs.keyboard.key_down.enter
args.inputs.keyboard.key_down?(:enter)

# key_up
args.inputs.keyboard.key_up.enter
args.inputs.keyboard.key_up?(:enter)

# key held
args.inputs.keyboard.key_held.enter
args.inputs.keyboard.key_held?(:enter)

# key down or held
args.inputs.keyboard.enter
args.inputs.keyboard.key_down_or_held?(:enter)
</code></pre>
<h3 id='-----keys-'><code>keys</code> <a style='font-size: small; float: right;' href='#-----keys-'>link</a></h3> 
<p>
Returns a <code>Hash</code> with all keys on the keyboard in their respective state. The <code>Hash</code> contains the following <code>keys</code>
</p>
<ul>
<li>  <code>:down</code></li>
<li>  <code>:held</code></li>
<li>  <code>:down_or_held</code></li>
<li>  <code>:up</code></li>
<li>  <code>:repeat</code></li>
</ul>
<h1 id='--runtime---args-gtk--'>Runtime (<code>args.gtk</code>) <a style='font-size: small; float: right;' href='#--runtime---args-gtk--'>link</a></h1> 
<p>
The <code>GTK::Runtime</code> class is the core of DragonRuby.
</p>
<p>
?&gt; All functions <code>$gtk</code>, <code>GTK</code>, or inside of the <code>tick</code> method through <code>args</code>.
</p>
<pre><code class="ruby">def tick args
  args.gtk.function(...)

  # OR available globally
  $gtk.function(...)
  # OR
  GTK.function(...)
end
</code></pre>
<h2 id='---class-macros'>Class Macros <a style='font-size: small; float: right;' href='#---class-macros'>link</a></h2> 
<p>
The following class macros are available within DragonRuby.
</p>
<h3 id='-----attr-'><code>attr</code> <a style='font-size: small; float: right;' href='#-----attr-'>link</a></h3> 
<p>
The <code>attr</code> class macro is an alias to <code>attr_accessor</code>.
</p>
<p>
Instead of:
</p>
<pre><code class="ruby">class Player
  attr_accessor :hp, :armor
end
</code></pre>
<p>
You can do:
</p>
<pre><code class="ruby">class Player
  attr :hp, :armor
end
</code></pre>
<h3 id='-----attr_gtk-'><code>attr_gtk</code> <a style='font-size: small; float: right;' href='#-----attr_gtk-'>link</a></h3> 
<p>
As the size/complexity of your game increases. You may want to create classes to organize everything. The <code>attr_gtk</code> class macro adds DragonRuby's environment methods (such as <code>args.state</code>, <code>args.inputs</code>, <code>args.outputs</code>, <code>args.audio</code>, etc) to your class so you don't have to pass <code>args</code> around everywhere.
</p>
<p>
Instead of:
</p>
<pre><code class="ruby">class Game
  def tick args
    defaults args
    calc args
    render args
  end

  def defaults args
    args.state.space_pressed_at ||= 0
  end

  def calc args
    if args.inputs.keyboard.key_down.space
      args.state.space_pressed_at = Kernel.tick_count
    end
  end

  def render args
    if args.state.space_pressed_at == 0
      args.outputs.labels &lt;&lt; { x: 100, y: 100,
                               text: "press space" }
    else
      args.outputs.labels &lt;&lt; { x: 100, y: 100,
                               text: "space was pressed at: #{args.state.space_pressed_at}" }
    end
  end
end

def tick args
  $game ||= Game.new
  $game.tick args
end
</code></pre>
<p>
You can do:
</p>
<pre><code class="ruby">class Game
  attr_gtk # attr_gtk class macro

  def tick
    defaults
    calc
    render
  end

  def defaults
    state.space_pressed_at ||= 0
  end

  def calc
    if inputs.keyboard.key_down.space
      state.space_pressed_at = Kernel.tick_count
    end
  end

  def render
    if state.space_pressed_at == 0
      outputs.labels &lt;&lt; { x: 100, y: 100,
                          text: "press space" }
    else
      outputs.labels &lt;&lt; { x: 100, y: 100,
                          text: "space was pressed at: #{state.space_pressed_at}" }
    end
  end
end

def tick args
  $game ||= Game.new
  $game.args = args # set args property on game
  $game.tick        # call tick without passing in args
end

$game = nil
</code></pre>
<h2 id='---indie-and-pro-functions'>Indie and Pro Functions <a style='font-size: small; float: right;' href='#---indie-and-pro-functions'>link</a></h2> 
<p>
The following functions are only available at the Indie and Pro License tiers.
</p>
<h3 id='-----get_pixels-'><code>get_pixels</code> <a style='font-size: small; float: right;' href='#-----get_pixels-'>link</a></h3> 
<p>
Given a <code>file_path</code> to a sprite, this function returns a <code>Hash</code> with <code>w</code>, <code>h</code>, and <code>pixels</code>. The <code>pixels</code> key contains an array of hexadecimal values representing the ABGR of each pixel in a sprite with item <code>0</code> representing the top left corner of the <code>png</code>.
</p>
<p>
Here's an example of how to get the color data for a pixel:
</p>
<pre><code class="ruby">def tick args
  # load the pixels from the image
  args.state.image ||= args.gtk.get_pixels "sprites/square/blue.png"

  # initialize state variables for the pixel coordinates
  args.state.x_px ||= 0
  args.state.y_px ||= 0

  sprite_pixels = args.state.image.pixels
  sprite_h = args.state.image.h
  sprite_w = args.state.image.w

  # move the pixel coordinates using keyboard
  args.state.x_px += args.inputs.left_right
  args.state.y_px += args.inputs.up_down

  # get pixel at the current coordinates
  args.state.x_px = args.state.x_px.clamp(0, sprite_w - 1)
  args.state.y_px = args.state.y_px.clamp(0, sprite_h - 1)
  row = sprite_h - args.state.y_px - 1
  col = args.state.x_px
  abgr = sprite_pixels[sprite_h * row + col]
  a = (abgr &gt;&gt; 24) &amp; 0xff
  b = (abgr &gt;&gt; 16) &amp; 0xff
  g = (abgr &gt;&gt; 8) &amp; 0xff
  r = (abgr &gt;&gt; 0) &amp; 0xff

  # render debug information
  args.outputs.debug &lt;&lt; "row: #{row} col: #{col}"
  args.outputs.debug &lt;&lt; "pixel entry 0: rgba #{r} #{g} #{b} #{a}"

  # render the sprite plus crosshairs
  args.outputs.sprites &lt;&lt; { x: 0, y: 0, w: 80, h: 80, path: "sprites/square/blue.png" }
  args.outputs.lines &lt;&lt; { x: args.state.x_px, y: 0, h: 720 }
  args.outputs.lines &lt;&lt; { x: 0, y: args.state.y_px, w: 1280 }
end
</code></pre>
<p>
See the following sample apps for how to use pixel arrays:
</p>
<ul>
<li>  <code>./samples/07_advanced_rendering/06_pixel_arrays</code></li>
<li>  <code>./samples/07_advanced_rendering/06_pixel_arrays_from_file</code></li>
</ul>
<h3 id='-----dlopen-'><code>dlopen</code> <a style='font-size: small; float: right;' href='#-----dlopen-'>link</a></h3> 
<p>
Loads a precompiled C Extension into your game given the <code>name</code> of the library (without the <code>lib</code> prefix or platform sepcific file extension).
</p>
<p>
See the sample apps at <code>./samples/12_c_extensions</code> for detailed walkthroughs of creating C extensions.
</p>
<p>
?&gt; All license tiers can load C Extensions in dev mode. The Standard license --does not include the dependencies required to create C Extensions however--.
</p>
<p>
 During the publishing process, any C Extension directories will be ignored during packaging and an exception will be thrown if <code>dlopen</code> is called in Production (for Standard license only).
</p>
<p>
 This gives Standard license users the ability to load/try out C Extensions in development mode. An [Indie or Pro license](https://dragonruby.org#purchase) is required to compile C Extensions/publish games that have C Extensions.
</p>
<h2 id='---environment-and-utility-functions'>Environment and Utility Functions <a style='font-size: small; float: right;' href='#---environment-and-utility-functions'>link</a></h2> 
<p>
The following functions will help in interacting with the OS and render/execution pipeline.
</p>
<h3 id='-----calcstringbox-'><code>calcstringbox</code> <a style='font-size: small; float: right;' href='#-----calcstringbox-'>link</a></h3> 
<p>
Returns the render width and render height as a tuple for a piece of text. The parameters this method takes are:
</p>
<ul>
<li>  <code>text</code>: the text you want to get the width and height of.</li>
<li>  <code>size_enum</code>: number representing the render size for the text. This parameter is optional and defaults to <code>0</code> which represents a baseline font size in units specific to DragonRuby (a negative value denotes a size smaller than what would be comfortable to read on a handheld device positive values above <code>0</code> represent larger font sizes).</li>
<li>  <code>font</code>: path to a font file that the width and height will be based off of. This field is optional and defaults to the DragonRuby's default font.</li>
</ul>
<pre><code class="ruby">def tick args
  text = "a piece of text"
  size_enum = 5 # "large font size"

  # path is relative to your game directory (eg mygame/fonts/courier-new.ttf)
  font = "fonts/courier-new.ttf"

  # get the render width and height
  string_w, string_h = args.gtk.calcstringbox text, size_enum, font

  # render the label
  args.outputs.labels &lt;&lt; {
    x: 100,
    y: 100,
    text: text,
    size_enum: size_enum,
    font: font
  }

  # render a border around the label based on the results from calcstringbox
  args.outputs.borders &lt;&lt; {
    x: 100,
    y: 100,
    w: string_w,
    h: string_h,
    r: 0,
    g: 0,
    b: 0
  }
end
</code></pre>
<p>
<code>calcstringbox</code> also supports named parameters for <code>size_enum</code> and <code>size_px</code>.
</p>
<pre><code class="ruby">  # size_enum, and font named parameters
  string_w, string_h = args.gtk.calcstringbox text, size_enum: 0, font: "fonts/example.ttf"

  # size_px, and font named parameters
  string_w, string_h = args.gtk.calcstringbox text, size_px: 20, font: "fonts/example.ttf"
</code></pre>
<h3 id='-----request_quit-'><code>request_quit</code> <a style='font-size: small; float: right;' href='#-----request_quit-'>link</a></h3> 
<p>
Call this function to exit your game. You will be given one additional tick if you need to perform any housekeeping before that game closes.
</p>
<pre><code class="ruby">def tick args
  # exit the game after 600 frames (10 seconds)
  if Kernel.tick_count == 600
    args.gtk.request_quit
  end
end
</code></pre>
<h3 id='-----quit_requested--'><code>quit_requested?</code> <a style='font-size: small; float: right;' href='#-----quit_requested--'>link</a></h3> 
<p>
This function will return <code>true</code> if the game is about to exit (either from the user closing the game or if <code>request_quit</code> was invoked).
</p>
<h3 id='-----set_window_fullscreen-'><code>set_window_fullscreen</code> <a style='font-size: small; float: right;' href='#-----set_window_fullscreen-'>link</a></h3> 
<p>
This function takes in a single <code>boolean</code> parameter. <code>true</code> to make the game fullscreen, <code>false</code> to return the game back to windowed mode.
</p>
<pre><code class="ruby">def tick args
  # make the game full screen after 600 frames (10 seconds)
  if Kernel.tick_count == 600
    args.gtk.set_window_fullscreen true
  end

  # return the game to windowed mode after 20 seconds
  if Kernel.tick_count == 1200
    args.gtk.set_window_fullscreen false
  end
end
</code></pre>
<h3 id='-----window_fullscreen--'><code>window_fullscreen?</code> <a style='font-size: small; float: right;' href='#-----window_fullscreen--'>link</a></h3> 
<p>
Returns <code>true</code> if the window is currently in fullscreen mode.
</p>
<h3 id='-----set_window_scale-'><code>set_window_scale</code> <a style='font-size: small; float: right;' href='#-----set_window_scale-'>link</a></h3> 
<p>
NOTE:
</p>
<p>
 This function should only be used for debugging/development purposes and is not guaranteed to work cross platform. Do not use as a in-game feature in production.
</p>
<p>
The first parameter is a float value used to resize the game window to a percentage of 1280x720 (or 720x1280 in portrait mode). The valid scale options are 0.1, 0.25, 0.5, 0.75, 1.25, 1.5, 2.0, 2.5, 3.0, and 4.0. The float value you pass in will be floored to the nearest valid scale option.
</p>
<p>
The second and third parameters are optional and default to <code>16</code> and <code>9</code> (representing with width and height aspect ratios for the window). Providing these parameters will resize the window with the non-standard aspect ratio. This is useful for testing letter-boxing and All Screen modes (Pro feature).
</p>
<p>
Setting the window scale to the following will give a good representation of your game on various form factors.
</p>
<pre><code class="ruby"># how your game will look on an iPad
GTK.set_window_scale 1.0, 4, 3

# how your game will look on a wide aspect ratio
GTK.set_window_scale 1.0, 21, 9
</code></pre>
<h3 id='-----set_window_title-'><code>set_window_title</code> <a style='font-size: small; float: right;' href='#-----set_window_title-'>link</a></h3> 
<p>
NOTE:
</p>
<p>
 This function should only be used for debugging/development purposes and is not guaranteed to work cross platform. Do not use as a in-game feature in production.
</p>
<p>
This function takes in a string updates the title of the game in the Menu Bar.
</p>
<p>
Note: The default title for your game is specified in via the <code>gametitle</code> property in <code>mygame/metadata/game_metadata.txt</code>.
</p>
<h3 id='-----platform--'><code>platform?</code> <a style='font-size: small; float: right;' href='#-----platform--'>link</a></h3> 
<p>
You can ask DragonRuby which platform your game is currently being run on. This can be useful if you want to perform different pieces of logic based on where the game is running.
</p>
<p>
The raw platform string value is available via <code>args.gtk.platform</code> which takes in a <code>symbol</code> representing the platform's categorization/mapping.
</p>
<p>
You can see all available platform categorizations via the <code>args.gtk.platform_mappings</code> function.
</p>
<p>
Here's an example of how to use <code>args.gtk.platform? category_symbol</code>:
</p>
<pre><code class="ruby">def tick args
  label_style = { x: 640, y: 360, anchor_x: 0.5, anchor_y: 0.5 }
  if    args.gtk.platform? :macos
    args.outputs.labels &lt;&lt; { text: "I am running on MacOS.", **label_style }
  elsif args.gtk.platform? :win
    args.outputs.labels &lt;&lt; { text: "I am running on Windows.", **label_style }
  elsif args.gtk.platform? :linux
    args.outputs.labels &lt;&lt; { text: "I am running on Linux.", **label_style }
  elsif args.gtk.platform? :web
    args.outputs.labels &lt;&lt; { text: "I am running on a web page.", **label_style }
  elsif args.gtk.platform? :android
    args.outputs.labels &lt;&lt; { text: "I am running on Android.", **label_style }
  elsif args.gtk.platform? :ios
    args.outputs.labels &lt;&lt; { text: "I am running on iOS.", **label_style }
  elsif args.gtk.platform? :touch
    args.outputs.labels &lt;&lt; { text: "I am running on a device that supports touch (either iOS/Android native or mobile web).", **label_style }
  elsif args.gtk.platform? :steam
    args.outputs.labels &lt;&lt; { text: "I am running via steam (covers both desktop and steamdeck).", **label_style }
  elsif args.gtk.platform? :steam_deck
    args.outputs.labels &lt;&lt; { text: "I am running via steam on the Steam Deck (not steam desktop).", **label_style }
  elsif args.gtk.platform? :steam_desktop
    args.outputs.labels &lt;&lt; { text: "I am running via steam on desktop (not steam deck).", **label_style }
  end
end
</code></pre>
<h3 id='-----production--'><code>production?</code> <a style='font-size: small; float: right;' href='#-----production--'>link</a></h3> 
<p>
Returns true if the game is being run in a released/shipped state.
</p>
<p>
If you want to simulate a production build. Add an empty file called <code>dragonruby_production_build</code> inside of the <code>metadata</code> folder. This will turn of all logging and all creation of temp files used for development purposes.
</p>
<h3 id='-----platform_mappings-'><code>platform_mappings</code> <a style='font-size: small; float: right;' href='#-----platform_mappings-'>link</a></h3> 
<p>
These are the current platform categorizations (<code>args.gtk.platform_mappings</code>):
</p>
<pre><code class="ruby">{
  "Mac OS X"   =&gt; [:desktop, :macos, :osx, :mac, :macosx], # may also include :steam and :steam_desktop run via steam
  "Windows"    =&gt; [:desktop, :windows, :win],              # may also include :steam and :steam_desktop run via steam
  "Linux"      =&gt; [:desktop, :linux, :nix],                # may also include :steam and :steam_desktop run via steam
  "Emscripten" =&gt; [:web, :wasm, :html, :emscripten],       # may also include :touch if running in the web browser on mobile
  "iOS"        =&gt; [:mobile, :ios, :touch],
  "Android"    =&gt; [:mobile, :android, :touch],
  "Steam Deck" =&gt; [:steamdeck, :steam_deck, :steam],
}
</code></pre>
<p>
Given the mappings above, <code>args.gtk.platform? :desktop</code> would return <code>true</code> if the game is running on a player's computer irrespective of OS (MacOS, Linux, and Windows are all categorized as <code>:desktop</code> platforms).
</p>
<h3 id='-----system-'><code>system</code> <a style='font-size: small; float: right;' href='#-----system-'>link</a></h3> 
<p>
NOTE:
</p>
<p>
 This function should only be used for debugging/development purposes and is not guaranteed to work cross platform. Do not use as a in-game feature in production.
</p>
<p>
Given an OS dependent cli command represented as a string, this function executes the command and <code>puts</code> the results to the DragonRuby Console (returns <code>nil</code>).
</p>
<pre><code class="ruby">def tick args
  # execute ls on the current directory in 10 seconds
  if Kernel.tick_count == 600
    args.gtk.system "ls ."
  end
end
</code></pre>
<h3 id='-----exec-'><code>exec</code> <a style='font-size: small; float: right;' href='#-----exec-'>link</a></h3> 
<p>
NOTE:
</p>
<p>
 This function should only be used for debugging/development purposes and is not guaranteed to work cross platform. Do not use as a in-game feature in production.
</p>
<p>
Given an OS dependent cli command represented as a string, this function executes the command and returns a <code>string</code> representing the results.
</p>
<pre><code class="ruby">def tick args
  # execute ls on the current directory in 10 seconds
  if Kernel.tick_count == 600
    results = args.gtk.exec "ls ."
    puts "The results of the command are:"
    puts results
  end
end
</code></pre>
<h3 id='-----show_cursor-'><code>show_cursor</code> <a style='font-size: small; float: right;' href='#-----show_cursor-'>link</a></h3> 
<p>
Shows the mouse cursor.
</p>
<h3 id='-----hide_cursor-'><code>hide_cursor</code> <a style='font-size: small; float: right;' href='#-----hide_cursor-'>link</a></h3> 
<p>
Hides the mouse cursor.
</p>
<h3 id='-----cursor_shown--'><code>cursor_shown?</code> <a style='font-size: small; float: right;' href='#-----cursor_shown--'>link</a></h3> 
<p>
Returns <code>true</code> if the mouse cursor is visible.
</p>
<h3 id='-----set_mouse_grab-'><code>set_mouse_grab</code> <a style='font-size: small; float: right;' href='#-----set_mouse_grab-'>link</a></h3> 
<p>
Takes in a numeric parameter representing the mouse grab mode.
</p>
<ul>
<li>  <code>0</code>: Ungrabs the mouse.</li>
<li>  <code>1</code>: Grabs the mouse.</li>
<li>  <code>2</code>: Hides the cursor, grabs the mouse and puts it in relative position mode accessible via <code>args.inputs.mouse.relative_(x|y)</code>.</li>
</ul>
<h3 id='-----set_system_cursor-'><code>set_system_cursor</code> <a style='font-size: small; float: right;' href='#-----set_system_cursor-'>link</a></h3> 
<p>
Takes in a string value of <code>"arrow"</code>, <code>"ibeam"</code>, <code>"wait"</code>, or <code>"hand"</code> and sets the mouse cursor to the corresponding system cursor (if available on the OS).
</p>
<h3 id='-----set_cursor-'><code>set_cursor</code> <a style='font-size: small; float: right;' href='#-----set_cursor-'>link</a></h3> 
<p>
Replaces the mouse cursor with a sprite. Takes in a <code>path</code> to the sprite, and optionally an <code>x</code> and <code>y</code> value representing the relative positioning the sprite will have to the mouse cursor.
</p>
<pre><code class="ruby">def tick args
  if Kernel.tick_count == 0
    # assumes a sprite of size 80x80 and centers the sprite
    # relative to the cursor position.
    args.gtk.set_cursor "sprites/square/blue.png", 40, 40
  end
end
</code></pre>
<h2 id='---file-io-functions'>File IO Functions <a style='font-size: small; float: right;' href='#---file-io-functions'>link</a></h2> 
<p>
NOTE:
</p>
<p>
 File access functions are sandboxed and assume that the <code>dragonruby</code> binary lives alongside the game you are building. --Do not expect these functions to return correct values if you are attempting to run the <code>dragonruby</code> binary from a shared location--. It's --strongly-- recommended that the directory structure contained in the zip is not altered and games are built using that starter template.
</p>
<p>
The following functions give you the ability to interact with the file system.
</p>
<p>
DragonRuby uses a sandboxed filesystem which will automatically read from and write to a location appropriate for your platform so you don't have to worry about theses details in your code. You can just use <code>GTK.read_file</code>, <code>GTK.write_file</code>, and <code>GTK.append_file</code> with a relative path and the engine will take care of the rest.
</p>
<p>
The data directories that will be written to in a production build are:
</p>
<ul>
<li>Windows: <code>C:\Users\<USER>\AppData\Roaming\[devtitle]\[gametitle]</code></li>
<li>MacOS: <code>$HOME/Library/Application Support/[gametitle]</code></li>
<li>Linux: <code>$HOME/.local/share/[gametitle]</code></li>
<li>HTML5: The data will be written to the browser's IndexedDB.</li>
</ul>
<p>
The values in square brackets are the values you set in your <code>app/metadata/game_metadata.txt</code> file.
</p>
<p>
When reading files, the engine will first look in the game's data directory and then in the game directory itself. This means that if you write a file to the data directory that already exists in your game directory, the file in the data directory will be used instead of the one that is in your game.
</p>
<p>
When running a development build you will directly write to your game directory (and thus overwrite existing files). This can be useful for built-in development tools like level editors.
</p>
<p>
For more details on the implementation of the sandboxed filesystem, see Ryan C. Gordon's PhysicsFS documentation: [https://icculus.org/physfs/](https://icculus.org/physfs/)
</p>
<h3 id='-----list_files-'><code>list_files</code> <a style='font-size: small; float: right;' href='#-----list_files-'>link</a></h3> 
<p>
This function takes in one parameter. The parameter is the directory path and assumes the the game directory is the root. The method returns an <code>Array</code> of <code>String</code> representing all files within the directory. Use <code>stat_file</code> to determine whether a specific path is a file or a directory.
</p>
<h3 id='-----stat_file-'><code>stat_file</code> <a style='font-size: small; float: right;' href='#-----stat_file-'>link</a></h3> 
<p>
This function takes in one parameter. The parameter is the file path and assumes the the game directory is the root. The method returns <code>nil</code> if the file doesn't exist otherwise it returns a <code>Hash</code> with the following information:
</p>
<pre><code class="ruby"># {
#   path: String,
#   file_size: Int,
#   mod_time: Int,
#   create_time: Int,
#   access_time: Int,
#   readonly: Boolean,
#   file_type: Symbol (:regular, :directory, :symlink, :other),
# }

def tick args
  if args.inputs.mouse.click
    args.gtk.write_file "last-mouse-click.txt", "Mouse was clicked at #{Kernel.tick_count}."
  end

  file_info = args.gtk.stat_file "last-mouse-click.txt"

  if file_info
    args.outputs.labels &lt;&lt; {
      x: 30,
      y: 30.from_top,
      text: file_info.to_s,
      size_enum: -3
    }
  else
    args.outputs.labels &lt;&lt; {
      x: 30,
      y: 30.from_top,
      text: "file does not exist, click to create file",
      size_enum: -3
    }
  end
end
</code></pre>
<h3 id='-----read_file-'><code>read_file</code> <a style='font-size: small; float: right;' href='#-----read_file-'>link</a></h3> 
<p>
Given a file path, a string will be returned representing the contents of the file. <code>nil</code> will be returned if the file does not exist. You can use <code>stat_file</code> to get additional information of a file.
</p>
<h3 id='-----write_file-'><code>write_file</code> <a style='font-size: small; float: right;' href='#-----write_file-'>link</a></h3> 
<p>
This function takes in two parameters. The first parameter is the file path and assumes the the game directory is the root. The second parameter is the string that will be written. The method ----overwrites---- whatever is currently in the file. Use <code>append_file</code> to append to the file as opposed to overwriting.
</p>
<pre><code class="ruby">def tick args
  if args.inputs.mouse.click
    args.gtk.write_file "last-mouse-click.txt", "Mouse was clicked at #{Kernel.tick_count}."
  end
end
</code></pre>
<h3 id='-----append_file-'><code>append_file</code> <a style='font-size: small; float: right;' href='#-----append_file-'>link</a></h3> 
<p>
This function takes in two parameters. The first parameter is the file path and assumes the the game directory is the root. The second parameter is the string that will be written. The method appends to whatever is currently in the file (a new file is created if one does not already exist). Use <code>write_file</code> to overwrite the file's contents as opposed to appending.
</p>
<pre><code class="ruby">def tick args
  if args.inputs.mouse.click
    args.gtk.append_file "click-history.txt", "Mouse was clicked at #{Kernel.tick_count}.\n"
    puts args.gtk.read_file("click-history.txt")
  end
end
</code></pre>
<h3 id='-----delete_file-'><code>delete_file</code> <a style='font-size: small; float: right;' href='#-----delete_file-'>link</a></h3> 
<p>
This function takes in a single parameters. The parameter is the file or directory path that should be deleted. This function will raise an exception if the path requesting to be deleted does not exist.
</p>
<p>
Here is a list of reasons an exception could be raised:
</p>
<ul>
<li>  If the path is still open (for reading or writing).</li>
<li>  If the path is not a file or directory.</li>
<li>  If the path is a circular symlink.</li>
<li>  If you do not have permissions to delete the path.</li>
<li>  If the directory attempting to be deleted is not empty.</li>
</ul>
<p>
Notes:
</p>
<ul>
<li>  Use <code>stat_file</code> to determine if a path exists.</li>
<li>  Use <code>list_files</code> to determine if a directory is empty.</li>
<li>  You cannot delete files outside of your sandboxed game environment.</li>
</ul>
<pre><code class="ruby">def tick args
  if args.inputs.keyboard.key_down.w
    # press w to write file
    args.gtk.append_file "example-file.txt", "File written at #{Kernel.tick_count}\n"
    args.gtk.notify "File written/appended."
  elsif args.inputs.keyboard.key_down.d
    # press d to delete file "unsafely"
    args.gtk.delete_file "example-file.txt"
    args.gtk.notify "File deleted."
  elsif args.inputs.keyboard.key_down.r
    # press r to read file
    contents = args.gtk.read_file "example-file.txt"
    args.gtk.notify "File contents written to console."
    puts contents
  end
end
</code></pre>
<h3 id='-----parse_json-'><code>parse_json</code> <a style='font-size: small; float: right;' href='#-----parse_json-'>link</a></h3> 
<p>
Given a json string, this function returns a hash representing the json data.
</p>
<pre><code class="ruby">hash = args.gtk.parse_json '{ "name": "John Doe", "aliases": ["JD"] }'
# structure of hash: { "name"=&gt;"John Doe", "aliases"=&gt;["JD"] }
</code></pre>
<h3 id='-----parse_json_file-'><code>parse_json_file</code> <a style='font-size: small; float: right;' href='#-----parse_json_file-'>link</a></h3> 
<p>
Same behavior as <code>parse_json</code> except a file path is read for the json string.
</p>
<h3 id='-----parse_xml-'><code>parse_xml</code> <a style='font-size: small; float: right;' href='#-----parse_xml-'>link</a></h3> 
<p>
Given xml data as a string, this function will return a hash that represents the xml data in the following recursive structure:
</p>
<pre><code class="ruby">{
  type: :element,
  name: "Person",
  children: [...]
}
</code></pre>
<h3 id='-----parse_xml_file-'><code>parse_xml_file</code> <a style='font-size: small; float: right;' href='#-----parse_xml_file-'>link</a></h3> 
<p>
Function has the same behavior as <code>parse_xml</code> except that the parameter must be a file path that contains xml contents.
</p>
<h2 id='---network-io-functions'>Network IO Functions <a style='font-size: small; float: right;' href='#---network-io-functions'>link</a></h2> 
<p>
The following functions help with interacting with the network.
</p>
<h3 id='-----http_get-'><code>http_get</code> <a style='font-size: small; float: right;' href='#-----http_get-'>link</a></h3> 
<p>
Returns an object that represents an http response which will eventually have a value. This <code>http_get</code> method is invoked asynchronously. Check for completion before attempting to read results.
</p>
<pre><code class="ruby">def tick args
  # perform an http get and print the response when available
  args.state.result ||= args.gtk.http_get "https://httpbin.org/html"

  if args.state.result &amp;&amp; args.state.result[:complete] &amp;&amp; !args.state.printed
    if args.state.result[:http_response_code] == 200
      puts "The response was successful. The body is:"
      puts args.state.result[:response_data]
    else
      puts "The response failed. Status code:"
      puts args.state.result[:http_response_code]
    end
    # set a flag denoting that the response has been printed
    args.state.printed = true

    # show the console
    args.gtk.show_console
  end
end
</code></pre>
<h3 id='-----http_post-'><code>http_post</code> <a style='font-size: small; float: right;' href='#-----http_post-'>link</a></h3> 
<p>
Returns an object that represents an http response which will eventually have a value. This <code>http_post</code> method is invoked asynchronously. Check for completion before attempting to read results.
</p>
<ul>
<li>  First parameter: The url to send the request to.</li>
<li>  Second parameter: Hash that represents form fields to send.</li>
<li>  Third parameter: Headers. Note: Content-Type must be form encoded flavor. If you are unsure of what to pass in, set the content type to application/x-www-form-urlencoded</li>
</ul>
<pre><code class="ruby">def tick args
  # perform an http get and print the response when available

  args.state.form_fields ||= { "userId" =&gt; "#{Time.now.to_i}" }
  args.state.result ||= args.gtk.http_post "http://httpbin.org/post",
                                           args.state.form_fields,
                                           ["Content-Type: application/x-www-form-urlencoded"]


  if args.state.result &amp;&amp; args.state.result[:complete] &amp;&amp; !args.state.printed
    if args.state.result[:http_response_code] == 200
      puts "The response was successful. The body is:"
      puts args.state.result[:response_data]
    else
      puts "The response failed. Status code:"
      puts args.state.result[:http_response_code]
    end
    # set a flag denoting that the response has been printed
    args.state.printed = true

    # show the console
    args.gtk.show_console
  end
end
</code></pre>
<h3 id='-----http_post_body-'><code>http_post_body</code> <a style='font-size: small; float: right;' href='#-----http_post_body-'>link</a></h3> 
<p>
Returns an object that represents an http response which will eventually have a value. This <code>http_post_body</code> method is invoked asynchronously. Check for completion before attempting to read results.
</p>
<ul>
<li>  First parameter: The url to send the request to.</li>
<li>  Second parameter: String that represents the body that will be sent</li>
<li>  Third parameter: Headers. Be sure to populate the Content-Type that matches the data you are sending.</li>
</ul>
<pre><code class="ruby">def tick args
  # perform an http get and print the response when available

  args.state.json ||= "{ "userId": "#{Time.now.to_i}"}"
  args.state.result ||= args.gtk.http_post_body "http://httpbin.org/post",
                                                args.state.json,
                                                ["Content-Type: application/json", "Content-Length: #{args.state.json.length}"]


  if args.state.result &amp;&amp; args.state.result[:complete] &amp;&amp; !args.state.printed
    if args.state.result[:http_response_code] == 200
      puts "The response was successful. The body is:"
      puts args.state.result[:response_data]
    else
      puts "The response failed. Status code:"
      puts args.state.result[:http_response_code]
    end
    # set a flag denoting that the response has been printed
    args.state.printed = true

    # show the console
    args.gtk.show_console
  end
end
</code></pre>
<h3 id='-----start_server--'><code>start_server!</code> <a style='font-size: small; float: right;' href='#-----start_server--'>link</a></h3> 
<p>
Starts a in-game http server that can be process http requests. When your game is running in development mode. A dev server is started at <code>http://localhost:9001</code>
</p>
<p>
?&gt; You must set <code>webserver.enabled=true</code> in <code>metadata/cvars.txt</code> to view docs locally. These docs are also available under <code>./docs</code> within the zip file in markdown format.
</p>
<p>
You can start an in-game http server in production via:
</p>
<pre><code class="ruby">def tick args
  # server explicitly enabled in production
  args.gtk.start_server! port: 9001, enable_in_prod: true
end
</code></pre>
<p>
Here's how you would respond to http requests:
</p>
<pre><code class="ruby">def tick args
  # server explicitly enabled in production
  args.gtk.start_server! port: 9001, enable_in_prod: true

  # loop through pending requests and respond to them
  args.inputs.http_requests.each do |request|
    puts "#{request}"
    request.respond 200, "ok"
  end
end
</code></pre>
<h3 id='-----http_get-'><code>http_get</code> <a style='font-size: small; float: right;' href='#-----http_get-'>link</a></h3> 
<p>
Returns an object that represents an http response which will eventually have a value. This <code>http_get</code> method is invoked asynchronously. Check for completion before attempting to read results.
</p>
<pre><code class="ruby">def tick args
  # perform an http get and print the response when available
  args.state.result ||= args.gtk.http_get "https://httpbin.org/html"

  if args.state.result &amp;&amp; args.state.result[:complete] &amp;&amp; !args.state.printed
    if args.state.result[:http_response_code] == 200
      puts "The response was successful. The body is:"
      puts args.state.result[:response_data]
    else
      puts "The response failed. Status code:"
      puts args.state.result[:http_response_code]
    end
    # set a flag denoting that the response has been printed
    args.state.printed = true

    # show the console
    args.gtk.show_console
  end
end
</code></pre>
<h3 id='-----http_post-'><code>http_post</code> <a style='font-size: small; float: right;' href='#-----http_post-'>link</a></h3> 
<p>
Returns an object that represents an http response which will eventually have a value. This <code>http_post</code> method is invoked asynchronously. Check for completion before attempting to read results.
</p>
<ul>
<li>  First parameter: The url to send the request to.</li>
<li>  Second parameter: Hash that represents form fields to send.</li>
<li>  Third parameter: Headers. Note: Content-Type must be form encoded flavor. If you are unsure of what to pass in, set the content type to application/x-www-form-urlencoded</li>
</ul>
<pre><code class="ruby">def tick args
  # perform an http get and print the response when available

  args.state.form_fields ||= { "userId" =&gt; "#{Time.now.to_i}" }
  args.state.result ||= args.gtk.http_post "http://httpbin.org/post",
                                           args.state.form_fields,
                                           ["Content-Type: application/x-www-form-urlencoded"]


  if args.state.result &amp;&amp; args.state.result[:complete] &amp;&amp; !args.state.printed
    if args.state.result[:http_response_code] == 200
      puts "The response was successful. The body is:"
      puts args.state.result[:response_data]
    else
      puts "The response failed. Status code:"
      puts args.state.result[:http_response_code]
    end
    # set a flag denoting that the response has been printed
    args.state.printed = true

    # show the console
    args.gtk.show_console
  end
end
</code></pre>
<h3 id='-----http_post_body-'><code>http_post_body</code> <a style='font-size: small; float: right;' href='#-----http_post_body-'>link</a></h3> 
<p>
Returns an object that represents an http response which will eventually have a value. This <code>http_post_body</code> method is invoked asynchronously. Check for completion before attempting to read results.
</p>
<ul>
<li>  First parameter: The url to send the request to.</li>
<li>  Second parameter: String that represents the body that will be sent</li>
<li>  Third parameter: Headers. Be sure to populate the Content-Type that matches the data you are sending.</li>
</ul>
<pre><code class="ruby">def tick args
  # perform an http get and print the response when available

  args.state.json ||= "{ "userId": "#{Time.now.to_i}"}"
  args.state.result ||= args.gtk.http_post_body "http://httpbin.org/post",
                                                args.state.json,
                                                ["Content-Type: application/json", "Content-Length: #{args.state.json.length}"]


  if args.state.result &amp;&amp; args.state.result[:complete] &amp;&amp; !args.state.printed
    if args.state.result[:http_response_code] == 200
      puts "The response was successful. The body is:"
      puts args.state.result[:response_data]
    else
      puts "The response failed. Status code:"
      puts args.state.result[:http_response_code]
    end
    # set a flag denoting that the response has been printed
    args.state.printed = true

    # show the console
    args.gtk.show_console
  end
end
</code></pre>
<h3 id='-----start_server--'><code>start_server!</code> <a style='font-size: small; float: right;' href='#-----start_server--'>link</a></h3> 
<p>
Starts a in-game http server that can be process http requests. When your game is running in development mode. A dev server is started at <code>http://localhost:9001</code>
</p>
<p>
?&gt; You must set <code>webserver.enabled=true</code> in <code>metadata/cvars.txt</code> to view docs locally. These docs are also available under <code>./docs</code> within the zip file in markdown format.
</p>
<p>
You can start an in-game http server in production via:
</p>
<pre><code class="ruby">def tick args
  # server explicitly enabled in production
  args.gtk.start_server! port: 9001, enable_in_prod: true
end
</code></pre>
<p>
Here's how you would respond to http requests:
</p>
<pre><code class="ruby">def tick args
  # server explicitly enabled in production
  args.gtk.start_server! port: 9001, enable_in_prod: true

  # loop through pending requests and respond to them
  args.inputs.http_requests.each do |request|
    puts "#{request}"
    request.respond 200, "ok"
  end
end
</code></pre>
<h2 id='---developer-support-functions'>Developer Support Functions <a style='font-size: small; float: right;' href='#---developer-support-functions'>link</a></h2> 
<p>
The following functions help support the development process. It is not recommended to use this functions in "production" game logic.
</p>
<h3 id='-----version-'><code>version</code> <a style='font-size: small; float: right;' href='#-----version-'>link</a></h3> 
<p>
Returns a string representing the version of DragonRuby you are running.
</p>
<h3 id='-----version_pro--'><code>version_pro?</code> <a style='font-size: small; float: right;' href='#-----version_pro--'>link</a></h3> 
<p>
Returns <code>true</code> if the version of DragonRuby is NOT Standard Edition.
</p>
<h3 id='-----game_version-'><code>game_version</code> <a style='font-size: small; float: right;' href='#-----game_version-'>link</a></h3> 
<p>
Returns a version string within <code>mygame/game_metadata.txt</code>.
</p>
<p>
To get other values from <code>mygame/game_metadata.txt</code>, you can do:
</p>
<pre><code class="ruby">def tick args
  if Kernel.tick_count == 0
    puts args.gtk.game_version
    args.cvars["game_metadata.version"].value
  end
end
</code></pre>
<h3 id='-----reset-'><code>reset</code> <a style='font-size: small; float: right;' href='#-----reset-'>link</a></h3> 
<p>
This function will be called if <code>GTK.reset</code> is invoked. It will be called before DragonRuby resets game state and is useful for capturing state information before a reset occurs (you can use this override to reset state external to DragonRuby's <code>args.state</code> construct).
</p>
<pre><code class="ruby"># class that handles your game loop
class MyGame
  attr :foo

  # initialization method that sets member variables
  # external to args.state
  def initialize args
    puts "initializing game"
    @foo = 0
    args.state.bar ||= 0
  end

  # game logic
  def tick args
    args.state.bar += 1
    @foo += 1
    args.outputs.labels &lt;&lt; {
      x: 640,
      y: 360,
      text: "#{$game.foo}, #{args.state.bar}"
    }
  end
end

def tick args
  # initialize global game variable if it's nil
  $game ||= MyGame.new(args)

  # run tick
  $game.tick args

  # at T=600, invoke reset
  if Kernel.tick_count == 600
    GTK.reset
  end
end

# this function will be invoked before
# GTK.reset occurs
def reset args
  puts "resetting"
  puts "foo is: #{$game.foo}"
  puts "bar is: #{args.state.bar}"
  puts "tick count: #{Kernel.tick_count}"

  # reset global game to nil so that it will be re-initialized next tick
  $game = nil
end
</code></pre>
<h3 id='-----reset_next_tick-'><code>reset_next_tick</code> <a style='font-size: small; float: right;' href='#-----reset_next_tick-'>link</a></h3> 
<p>
Has the same behavior as <code>reset</code> except the reset occurs before <code>tick</code> is executed again. <code>reset</code> resets the environment immediately (while the <code>tick</code> method is in-flight). It's recommended that <code>reset</code> should be called outside of the tick method (invoked when a file is saved/hotloaded), and <code>reset_next_tick</code> be used inside of the <code>tick</code> method so you don't accidentally blow away state the your game depends on to complete the current <code>tick</code> without exceptions.
</p>
<pre><code class="ruby">def tick args
  # reset the game if "r" is pressed on the keyboard
  if args.inputs.keyboard.key_down.r
    args.gtk.reset_next_tick # use reset_next_tick instead of reset
  end
end

# reset the game if this file is hotloaded/required
# (removes the need to press "r" when I file is updated)
GTK.reset
</code></pre>
<h3 id='-----reset_sprite-'><code>reset_sprite</code> <a style='font-size: small; float: right;' href='#-----reset_sprite-'>link</a></h3> 
<p>
Sprites when loaded are cached. Given a string parameter, this method invalidates the cache record of a sprite so that updates on from the disk can be loaded.
</p>
<p>
This function can also be used to delete/garbage collect render targets you are no longer using.
</p>
<h3 id='-----reset_sprites-'><code>reset_sprites</code> <a style='font-size: small; float: right;' href='#-----reset_sprites-'>link</a></h3> 
<p>
Sprites when loaded are cached. This method invalidates the cache record of all sprites so that updates on from the disk can be loaded. This function is automatically called when <code>args.gtk.reset</code> (<code>GTK.reset</code>) is invoked.
</p>
<h3 id='-----calcspritebox-'><code>calcspritebox</code> <a style='font-size: small; float: right;' href='#-----calcspritebox-'>link</a></h3> 
<p>
NOTE:
</p>
<p>
 This method should be used for development purposes only and is expensive to call every frame. Do not use this method to set the size of sprite when rendering (hard code those values since you know what they are beforehand).
</p>
<p>
Given a path to a sprite, this method returns the <code>width</code> and <code>height</code> of a sprite as a tuple.
</p>
<h3 id='-----current_framerate-'><code>current_framerate</code> <a style='font-size: small; float: right;' href='#-----current_framerate-'>link</a></h3> 
<p>
Returns a float value representing the framerate of your game. This is an approximation/moving average of your framerate and should eventually settle to 60fps.
</p>
<pre><code class="ruby">def tick args
  # render a label to the screen that shows the current framerate
  # formatted as a floating point number with two decimal places
  args.outputs.labels &lt;&lt; { x: 30, y: 30.from_top, text: "#{args.gtk.current_framerate.to_sf}" }
end
</code></pre>
<h3 id='-----framerate_diagnostics_primitives-'><code>framerate_diagnostics_primitives</code> <a style='font-size: small; float: right;' href='#-----framerate_diagnostics_primitives-'>link</a></h3> 
<p>
Returns a set of primitives that can be rendered to the screen which provide more detailed information about the speed of your simulation (framerate, draw call count, mouse position, etc).
</p>
<pre><code class="ruby">def tick args
  args.outputs.primitives &lt;&lt; args.gtk.framerate_diagnostics_primitives
end
</code></pre>
<h3 id='-----warn_array_primitives--'><code>warn_array_primitives!</code> <a style='font-size: small; float: right;' href='#-----warn_array_primitives--'>link</a></h3> 
<p>
This function helps you audit your game of usages of array-based primitives. While array-based primitives are simple to create and use, they are slower to process than <code>Hash</code> or <code>Class</code> based primitives.
</p>
<pre><code class="ruby">def tick args
  # enable array based primitives warnings
  args.gtk.warn_array_primitives!

  # array-based primitive elsewhere in code
  # an log message will be posted giving the location of the array
  # based primitive usage
  args.outputs.sprites &lt;&lt; [100, 100, 200, 200, "sprites/square/blue.png"]

  # instead of using array based primitives, migrate to hashes as needed
  args.outputs.sprites &lt;&lt; {
    x: 100,
    y: 100,
    w: 200,
    h: 200, path:
    "sprites/square/blue.png"
  }
end
</code></pre>
<h3 id='-----benchmark-'><code>benchmark</code> <a style='font-size: small; float: right;' href='#-----benchmark-'>link</a></h3> 
<p>
You can use this function to compare the relative performance of blocks of code.
</p>
<p>
Function takes in either <code>iterations</code> or <code>seconds</code> along with a collection of <code>lambdas</code>.
</p>
<p>
If <code>iterations</code> is provided, the winner will be determined by the fastest completion time.
</p>
<p>
If <code>seconds</code> is provided, the winner will be determined by the most completed iterations.
</p>
<pre><code class="ruby">def tick args
  # press i to run benchmark using iterations
  if args.inputs.keyboard.key_down.i
    args.gtk.console.show
    args.gtk.benchmark iterations: 1000, # number of iterations
                       # label for experiment
                       using_numeric_map: lambda {
                         # experiment body
                         v = 100.map_with_index do |i|
                           i * 100
                         end
                       },
                       # label for experiment
                       using_numeric_times: lambda {
                         # experiment body
                         v = []
                         100.times do |i|
                           v &lt;&lt; i * 100
                         end
                       }
  end

  # press s to run benchmark using seconds
  if args.inputs.keyboard.key_down.s
    args.gtk.console.show
    args.gtk.benchmark seconds: 1, # number of seconds to run each experiment
                       # label for experiment
                       using_numeric_map: lambda {
                         # experiment body
                         v = 100.map_with_index do |i|
                           i * 100
                         end
                       },
                       # label for experiment
                       using_numeric_times: lambda {
                         # experiment body
                         v = []
                         100.times do |i|
                           v &lt;&lt; i * 100
                         end
                       }
  end
end
</code></pre>
<h3 id='-----notify--'><code>notify!</code> <a style='font-size: small; float: right;' href='#-----notify--'>link</a></h3> 
<p>
Given a string, this function will present a message at the bottom of your game. This method is only invoked in dev mode and is useful for debugging.
</p>
<p>
An optional parameter of duration (number value representing ticks) can also be passed in. The default value if <code>300</code> ticks (5 seconds).
</p>
<pre><code class="ruby">def tick args
  if args.inputs.mouse.click
    args.gtk.notify! "Mouse was clicked!"
  end

  if args.inputs.keyboard.key_down.r
    # optional duration parameter
    args.gtk.notify! "R key was pressed!", 600 # present message for 10 seconds/600 frames
  end
end
</code></pre>
<h3 id='-----notify_extended--'><code>notify_extended!</code> <a style='font-size: small; float: right;' href='#-----notify_extended--'>link</a></h3> 
<p>
Has similar behavior as notify! except you have additional options to show messages in a production environment.
</p>
<pre><code class="ruby">def tick args
  if args.inputs.mouse.click
    args.gtk.notify_extended! message: "message",
                              duration: 300,
                              env: :prod
  end
end
</code></pre>
<h3 id='-----slowmo--'><code>slowmo!</code> <a style='font-size: small; float: right;' href='#-----slowmo--'>link</a></h3> 
<p>
Given a numeric value representing the factor of 60fps. This function will bring your simulation loop down to slower rate. This method is intended to be used for debugging purposes.
</p>
<pre><code class="ruby">def tick args
  # set your simulation speed to (15 fps): args.gtk.slowmo! 4
  # set your simulation speed to (1 fps): args.gtk.slowmo! 60
  # set your simulation speed to (30 fps):
  args.gtk.slowmo! 2
end
</code></pre>
<p>
Remove this line from your tick method will automatically set your simulation speed back to 60 fps.
</p>
<h3 id='-----show_console-'><code>show_console</code> <a style='font-size: small; float: right;' href='#-----show_console-'>link</a></h3> 
<p>
Shows the DragonRuby console. Useful when debugging/customizing an in-game dev workflow.
</p>
<h3 id='-----hide_console-'><code>hide_console</code> <a style='font-size: small; float: right;' href='#-----hide_console-'>link</a></h3> 
<p>
Hides the DragonRuby console. Useful when debugging/customizing an in-game dev workflow.
</p>
<h3 id='-----enable_console-'><code>enable_console</code> <a style='font-size: small; float: right;' href='#-----enable_console-'>link</a></h3> 
<p>
Enables the DragonRuby Console so that it can be presented by pressing the tilde key (the key next to the number 1 key).
</p>
<h3 id='-----disable_console-'><code>disable_console</code> <a style='font-size: small; float: right;' href='#-----disable_console-'>link</a></h3> 
<p>
Disables the DragonRuby Console so that it won't show up even if you press the tilde key or call <code>args.gtk.show_console</code>.
</p>
<h3 id='-----disable_reset_via_ctrl_r-'><code>disable_reset_via_ctrl_r</code> <a style='font-size: small; float: right;' href='#-----disable_reset_via_ctrl_r-'>link</a></h3> 
<p>
By default, pressing <code>CTRL+R</code> invokes <code>GTK.reset_next_tick</code> (safely resetting your game with a convenient key combo).
</p>
<p>
If you want to disable this behavior, add the following to the <code>main.rb</code>:
</p>
<pre><code class="ruby">def tick args
  ...
end

GTK.disable_reset_via_ctrl_r
</code></pre>
<p>
NOTE: <code>GTK.disable_console</code> will also disable the <code>CTRL+R</code> reset behavior.
</p>
<h3 id='-----disable_controller_config-'><code>disable_controller_config</code> <a style='font-size: small; float: right;' href='#-----disable_controller_config-'>link</a></h3> 
<p>
DragonRuby has a built-in controller configuration/mapping wizard. You can disable this wizard by adding <code>GTK.disable_controller_config</code> at the top of main.rb.
</p>
<h3 id='-----enable_controller_config-'><code>enable_controller_config</code> <a style='font-size: small; float: right;' href='#-----enable_controller_config-'>link</a></h3> 
<p>
DragonRuby has a built-in controller configuration/mapping wizard. You can re-enable this wizard by adding <code>GTK.enable_controller_config</code> at the top of main.rb (this is enabled by default).
</p>
<h3 id='-----start_recording-'><code>start_recording</code> <a style='font-size: small; float: right;' href='#-----start_recording-'>link</a></h3> 
<p>
Resets the game to tick <code>0</code> and starts recording gameplay. Useful for visual regression tests/verification.
</p>
<h3 id='-----stop_recording-'><code>stop_recording</code> <a style='font-size: small; float: right;' href='#-----stop_recording-'>link</a></h3> 
<p>
Function takes in a destination file for the currently recording gameplay. This file can be used to replay a recording.
</p>
<h3 id='-----cancel_recording-'><code>cancel_recording</code> <a style='font-size: small; float: right;' href='#-----cancel_recording-'>link</a></h3> 
<p>
Function cancels a gameplay recording session and discards the replay.
</p>
<h3 id='-----start_replay-'><code>start_replay</code> <a style='font-size: small; float: right;' href='#-----start_replay-'>link</a></h3> 
<p>
Given a file that represents a recording, this method will run the recording against the current codebase.
</p>
<p>
You can start a replay from the command line also:
</p>
<pre><code class="bash"># first argument: the game directory
# --replay switch is the file path relative to the game directory
# --speed switch is optional. a value of 4 will run the replay and game at 4x speed
# cli command example is in the context of Linux and Mac, for Windows the binary would be ./dragonruby.exe
./dragonruby ./mygame --replay replay.txt --speed 4
</code></pre>
<h3 id='-----stop_replay-'><code>stop_replay</code> <a style='font-size: small; float: right;' href='#-----stop_replay-'>link</a></h3> 
<p>
Function stops a replay that is currently executing.
</p>
<h3 id='-----get_base_dir-'><code>get_base_dir</code> <a style='font-size: small; float: right;' href='#-----get_base_dir-'>link</a></h3> 
<p>
Returns the path to the location of the dragonruby binary. In production mode, this value will be the same as the value returned by <code>get_game_dir</code>. Function should only be used for debugging/development workflows.
</p>
<h3 id='-----get_game_dir-'><code>get_game_dir</code> <a style='font-size: small; float: right;' href='#-----get_game_dir-'>link</a></h3> 
<p>
Returns the location within sandbox storage that the game is running. When developing your game, this value will be your <code>mygame</code> directory. In production, it'll return a value that is OS specific (eg the Roaming directory on Windows or the Application Support directory on Mac).
</p>
<p>
Invocations of <code>(write|append)_file</code> will write to this sandboxed directory.
</p>
<h3 id='-----get_game_dir_url-'><code>get_game_dir_url</code> <a style='font-size: small; float: right;' href='#-----get_game_dir_url-'>link</a></h3> 
<p>
Returns a url encoded string representing the sandbox location for game data.
</p>
<h3 id='-----open_game_dir-'><code>open_game_dir</code> <a style='font-size: small; float: right;' href='#-----open_game_dir-'>link</a></h3> 
<p>
Opens the game directory in the OS's file explorer. This should be used for debugging purposes only.
</p>
<h3 id='-----write_file_root-'><code>write_file_root</code> <a style='font-size: small; float: right;' href='#-----write_file_root-'>link</a></h3> 
<p>
Given a file path and contents, the contents will be written to a directory outside of the game directory. This method should be used for development purposes only. In production this method will write to the same sandboxed location as <code>write_file</code>.
</p>
<h3 id='-----append_file_root-'><code>append_file_root</code> <a style='font-size: small; float: right;' href='#-----append_file_root-'>link</a></h3> 
<p>
Has the same behavior as <code>write_file_root</code> except that it appends the contents as opposed to overwriting them.
</p>
<h3 id='-----argv-'><code>argv</code> <a style='font-size: small; float: right;' href='#-----argv-'>link</a></h3> 
<p>
Returns a string representing the command line arguments passed to the DragonRuby binary. This should be used for development/debugging purposes only.
</p>
<h3 id='-----cli_arguments-'><code>cli_arguments</code> <a style='font-size: small; float: right;' href='#-----cli_arguments-'>link</a></h3> 
<p>
Returns a <code>Hash</code> for command line arguments in the format of <code>--switch value</code> (two hyphens preceding the switch flag with the value separated by a space). This should be used for development/debugging purposes only.
</p>
<h3 id='-----download_stb_rb-_raw--'><code>download_stb_rb(_raw)</code> <a style='font-size: small; float: right;' href='#-----download_stb_rb-_raw--'>link</a></h3> 
<p>
These two functions can help facilitate the integration of external code files. OSS contributors are encouraged to create libraries that all fit in one file (lowering the barrier to entry for adoption).
</p>
<p>
Examples:
</p>
<pre><code class="ruby">def tick args
end

# option 1:
# source code will be downloaded from the specified GitHub url, and saved locally with a
# predefined folder convention.
GTK.download_stb_rb "https://github.com/xenobrain/ruby_vectormath/blob/main/vectormath_2d.rb"

# option 2:
# source code will be downloaded from the specified GitHub username, repository, and file.
# code will be saved locally with a predefined folder convention.
GTK.download_stb_rb "xenobrain", "ruby_vectormath", "vectormath_2d.rb"

# option 3:
# source code will be downloaded from a direct/raw url and saved to a direct/raw local path.
GTK.download_stb_rb_raw "https://raw.githubusercontent.com/xenobrain/ruby_vectormath/main/vectormath_2d.rb",
                         "lib/xenobrain/ruby_vectionmath/vectormath_2d.rb"
</code></pre>
<h3 id='-----reload_history-'><code>reload_history</code> <a style='font-size: small; float: right;' href='#-----reload_history-'>link</a></h3> 
<p>
Returns a <code>Hash</code> representing the code files that have be loaded for your game along with timings for the events. This should be used for development/debugging purposes only.
</p>
<h3 id='-----reload_history_pending-'><code>reload_history_pending</code> <a style='font-size: small; float: right;' href='#-----reload_history_pending-'>link</a></h3> 
<p>
Returns a <code>Hash</code> for files that have been queued for reload, but haven't been processed yet. This should be used for development/debugging purposes only.
</p>
<h3 id='-----reload_if_needed-'><code>reload_if_needed</code> <a style='font-size: small; float: right;' href='#-----reload_if_needed-'>link</a></h3> 
<p>
Given a file name, this function will queue the file for reload if it's been modified. An optional second parameter can be passed in to signify if the file should be forced loaded regardless of modified time (<code>true</code> means to force load, <code>false</code> means to load only if the file has been modified). This function should be used for development/debugging purposes only.
</p>
<h1 id='--state---args-state--'>State (<code>args.state</code>) <a style='font-size: small; float: right;' href='#--state---args-state--'>link</a></h1> 
<p>
Store your game state inside of this <code>state</code>. Properties with arbitrary nesting is allowed and a backing Entity will be created on your behalf.
</p>
<pre><code class="ruby">def tick args
  args.state.player.x ||= 0
  args.state.player.y ||= 0
end
</code></pre>
<h2 id='----entity_id-'><code>entity_id</code> <a style='font-size: small; float: right;' href='#----entity_id-'>link</a></h2> 
<p>
Entities automatically receive an <code>entity_id</code> of type <code>Fixnum</code>.
</p>
<h2 id='----entity_type-'><code>entity_type</code> <a style='font-size: small; float: right;' href='#----entity_type-'>link</a></h2> 
<p>
Entities can have an <code>entity_type</code> which is represented as a <code>Symbol</code>.
</p>
<h2 id='----created_at-'><code>created_at</code> <a style='font-size: small; float: right;' href='#----created_at-'>link</a></h2> 
<p>
Entities have <code>created_at</code> set to <code>Kernel.tick_count</code> when they are created.
</p>
<h2 id='----created_at_elapsed-'><code>created_at_elapsed</code> <a style='font-size: small; float: right;' href='#----created_at_elapsed-'>link</a></h2> 
<p>
Returns the elapsed number of ticks since creation.
</p>
<h2 id='----global_created_at-'><code>global_created_at</code> <a style='font-size: small; float: right;' href='#----global_created_at-'>link</a></h2> 
<p>
Entities have <code>global_created_at</code> set to <code>Kernel.global_tick_count</code> when they are created.
</p>
<h2 id='----global_created_at_elapsed-'><code>global_created_at_elapsed</code> <a style='font-size: small; float: right;' href='#----global_created_at_elapsed-'>link</a></h2> 
<p>
Returns the elapsed number of global ticks since creation.
</p>
<h2 id='----as_hash-'><code>as_hash</code> <a style='font-size: small; float: right;' href='#----as_hash-'>link</a></h2> 
<p>
Entity cast to a <code>Hash</code> so you can update values as if you were updating a <code>Hash</code>.
</p>
<h2 id='----tick_count-'><code>tick_count</code> <a style='font-size: small; float: right;' href='#----tick_count-'>link</a></h2> 
<p>
Returns the current tick of the game. <code>Kernel.tick_count</code> is <code>0</code> when the game is first started or if the game is reset via <code>GTK.reset</code>.
</p>
<h1 id='--geometry'>Geometry <a style='font-size: small; float: right;' href='#--geometry'>link</a></h1> 
<p>
The Geometry <code>module</code> contains methods for calculations that are frequently used in game development.
</p>
<p>
The following functions of <code>Geometry</code> are mixed into <code>Hash</code>, <code>Array</code>, and DragonRuby's <code>Entity</code> class:
</p>
<ul>
<li><code>intersect_rect?</code></li>
<li><code>inside_rect?</code></li>
<li><code>scale_rect</code></li>
<li><code>angle_to</code></li>
<li><code>angle_from</code></li>
<li><code>point_inside_circle?</code></li>
<li><code>center_inside_rect</code></li>
<li><code>center_inside_rect_x</code></li>
<li><code>center_inside_rect_y</code></li>
<li><code>anchor_rect</code></li>
<li><code>rect_center_point</code></li>
</ul>
<p>
You can invoke the functions above using either the mixin variant or the module variant. Example:
</p>
<pre><code class="ruby">def tick args
  # define to rectangles
  rect_1 = { x: 0, y: 0, w: 100, h: 100 }
  rect_2 = { x: 50, y: 50, w: 100, h: 100 }

  # mixin variant
  # call geometry method function from instance of a Hash class
  puts rect_1.intersect_rect?(rect_2)

  # OR

  # module variants
  puts args.geometry.intersect_rect?(rect_1, rect_2)
  puts Geometry.intersect_rect?(rect_1, rect_2)
end
</code></pre>
<h1 id='--cvars-/-configuration-/-game-metadata---args-cvars--'>CVars / Configuration / Game Metadata (<code>args.cvars</code>) <a style='font-size: small; float: right;' href='#--cvars-/-configuration-/-game-metadata---args-cvars--'>link</a></h1> 
<p>
Hash contains metadata pulled from the files under the <code>./metadata</code> directory. To get the keys that are available type <code>$args.cvars.keys</code> in the Console. Here is an example of how to retrieve the game version number:
</p>
<pre><code class="ruby">def tick args
  args.outputs.labels &lt;&lt; {
    x: 640,
    y: 360,
    text: args.cvars["game_metadata.version"].value.to_s
  }
end
</code></pre>
<p>
Each CVar has the following properties <code>value</code>, <code>name</code>, <code>description</code>, <code>type</code>, <code>locked</code>.
</p>
<h2 id='---available-configuration'>Available Configuration <a style='font-size: small; float: right;' href='#---available-configuration'>link</a></h2> 
<p>
?&gt; See <code>metadata/game_metadata.txt</code> and <code>metadata/cvars.txt</code> for detailed information of all the configuration values that are supported. The following table is a high level summary of each value.
</p>
<p>
| File                           | Name                       | Values                                                                       | Description                                                                                                                                                  | |--------------------------------|----------------------------|------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------| | --metadata/cvars.txt--         | <code>webserver.enabled</code>        | <code>true</code> or <code>false</code> (default is <code>false</code>)                                       | Controls whether or not the in-game web server at <code>localhost:9001</code> is enabled in dev mode. The in-game web server is primarily needed for remote-hotloading. | |                                | <code>webserver.port</code>           | Number representing a port (default is <code>9001</code>)                               | Port that the in-game web server runs on. For remote-hotloading, this value must be <code>9001</code>.                                                                  | |                                | <code>webserver.remote_clients</code> | <code>true</code> or <code>false</code> (default is <code>false</code>)                                       | Controls whether or not remote connections to the in-game web server are allowed. Must be set to <code>true</code> for remote-hotloading.                               | |                                | <code>renderer.background_sleep</code>| Number representing (default is <code>50</code>, set to <code>0</code> to disable)                 | Controls how long to wait before attempting to rendering the game when the game does not have focus (wasted CPU cycles rendering when the window isn't top). | | --metadata/game_metadata.txt-- | <code>devid</code>                    | String value                                                                 | Your Developer Id on Itch.io. | |                                | <code>devname</code>                  | String value                                                                 | Developer name/studio name. | |                                | <code>gameid</code>                   | String value                                                                 | Your Game Id on Itch.io | |                                | <code>gametitle</code>                | String value                                                                 | The title of your game. | |                                | <code>version</code>                  | String value                                                                 | <code>MAJOR</code>.<code>MINOR</code> Version number for your game. | |                                | <code>icon</code>                     | String value                                                                 | Path to your game icon. | |                                | <code>orientation</code>              | <code>landscape</code> or <code>portrait</code> (default is <code>landscape</code>)                           | Orientation for your game. | |                                | <code>orientation_ios</code>          | <code>landscape</code> or <code>portrait</code>                                                    | Overrides the default orientation on iOS. This is a Pro feature. | |                                | <code>orientation_android</code>      | <code>landscape</code> or <code>portrait</code>                                                    | Overrides the default orientation on Android. This is a Pro feature. | |                                | <code>scale_quality</code>            | <code>0</code>, <code>1</code>, or <code>2</code> (default is <code>0</code>)                                            | Specifies the render scale quality for your game (0=nearest neighbor, 1=linear, 2=anisotropic/best). Full details of what each number means in <code>metadata/game_metadata.txt</code>. | |                                | <code>ignore_directories</code>       | Comma delimited list of directories                                          | Directories to exclude when packaging your game. | |                                | <code>packageid</code>                | String in reverse domain convention                                          | Android Package Id for your game. This is a Pro feature. | |                                | <code>compile_ruby</code>             | <code>true</code> or <code>false</code> (default is <code>false</code>)                                       | Signifies if your game code will be compiled to bytecode during packaging. This is a Pro feature. | |                                | <code>hd</code>                       | <code>true</code> or <code>false</code> (default is <code>false</code>)                                       | Whether your game will be rendered in HD. This is a Pro feature. | |                                | <code>highdpi</code>                  | <code>true</code> or <code>false</code> (default is <code>false</code>)                                       | Whether your game will be rendered with High DPI. This is a Pro feature. | |                                | <code>sprites_directory</code>        | String value                                                                 | The path that DR should search for HD texture atlases. This is a Pro feature. | |                                | <code>hd_letterbox</code>             | <code>true</code> or <code>false</code> (default is <code>true</code>)                                        | Disables the letter box around your game. This is a Pro feature. | |                                | <code>hd_max_scale</code>             | <code>0</code>, <code>100</code>, <code>125</code>, <code>150</code>, <code>175</code>, <code>200</code>, <code>250</code>, <code>300</code>, <code>400</code> (default is <code>0</code>) | Signifies the max scale of your game. <code>0</code> means size to fit (full details of what each number means in <code>metadata/game_metadata.txt</code>). | | --metadata/ios_metadata.txt--  | <code>teamid</code>                   | String value                                                                 | Apple Team Id. This is a Pro feature. | |                                | <code>appid</code>                    | String value                                                                 | Apple App Id. This is a Pro feature. | |                                | <code>appname</code>                  | String value                                                                 | The name to show under the App icon. | |                                | <code>devcert</code>                  | String value                                                                 | Apple Development Certificate name used to sign your game for local device deployment. This is a Pro feature. | |                                | <code>prodcert</code>                 | String value                                                                 | Apple Distribution Certificate name used to sign your game release to the App Store. This is a Pro feature. |
</p>
<h1 id='--layout'>Layout <a style='font-size: small; float: right;' href='#--layout'>link</a></h1> 
<p>
Layout provides apis for placing primitives on a virtual grid that's within the "safe area" accross all platforms. This virtual grid is useful for rendering static controls (buttons, menu items, configuration screens, etc).
</p>
<p>
?&gt; All functions are available globally via <code>Layout.-</code>.
</p>
<pre><code class="ruby">def tick args
   puts args.layout.function(...)

   # OR available globally
   puts Layout.function(...)
end
</code></pre>
<p>
For reference implementations, take a look at the following sample apps:
</p>
<ul>
<li>  <code>./samples/07_advanced_rendering/18_layouts</code></li>
<li>  <code>./samples/07_advanced_rendering_hd/04_layouts_and_portrait_mode</code></li>
<li>  <code>./samples/99_genre_rpg_turn_based/turn_based_battle</code></li>
</ul>
<p>
The following example creates two menu items and updates a label with the button that was clicked:
</p>
<pre><code class="ruby">def tick args
  # render debug_primitives of args.layout for help with placement
  # args.outputs.primitives &lt;&lt; args.layout.debug_primitives

  # capture the location for a label centered at the top
  args.state.label_rect ||= args.layout.rect(row: 0, col: 10, w: 4, h: 1)
  # state variable to hold the current click status
  args.state.label_message ||= "click a menu item"

  # capture the location of two menu items positioned in the center
  # with a cell width of 4 and cell height of 2
  args.state.menu_item_1_rect ||= args.layout.rect(row: 1, col: 10, w: 4, h: 2)
  args.state.menu_item_2_rect ||= args.layout.rect(row: 3, col: 10, w: 4, h: 2)

  # render the label at the center of the label_rect
  args.outputs.labels &lt;&lt; args.state.label_rect.center.merge(text: args.state.label_message,
                                                            anchor_x: 0.5,
                                                            anchor_y: 0.5)

  # render menu items
  args.outputs.sprites &lt;&lt; args.state.menu_item_1_rect.merge(path: :solid,
                                                            r: 100,
                                                            g: 100,
                                                            b: 200)
  args.outputs.labels &lt;&lt; args.state.menu_item_1_rect.center.merge(text: "item 1",
                                                                  r: 255,
                                                                  g: 255,
                                                                  b: 255,
                                                                  anchor_x: 0.5,
                                                                  anchor_y: 0.5)

  args.outputs.sprites &lt;&lt; args.state.menu_item_2_rect.merge(path: :solid,
                                                            r: 100,
                                                            g: 100,
                                                            b: 200)
  args.outputs.labels &lt;&lt; args.state.menu_item_2_rect.center.merge(text: "item 2",
                                                                  r: 255,
                                                                  g: 255,
                                                                  b: 255,
                                                                  anchor_x: 0.5,
                                                                  anchor_y: 0.5)

  # if click occurs, then determine which menu item was clicked
  if args.inputs.mouse.click
    if args.inputs.mouse.intersect_rect?(args.state.menu_item_1_rect)
      args.state.label_message = "menu item 1 clicked"
    elsif args.inputs.mouse.intersect_rect?(args.state.menu_item_2_rect)
      args.state.label_message = "menu item 2 clicked"
    else
      args.state.label_message = "click a menu item"
    end
  end
end
</code></pre>
<h2 id='----rect-'><code>rect</code> <a style='font-size: small; float: right;' href='#----rect-'>link</a></h2> 
<p>
Given a <code>row:</code>, <code>col:</code>, <code>w:</code>, <code>h:</code>, returns a <code>Hash</code> with properties <code>x</code>, <code>y</code>, <code>w</code>, <code>h</code>, and <code>center</code> (which contains a <code>Hash</code> with <code>x</code>, <code>y</code>). The virtual grid is 12 rows by 24 columns (or 24 columns by 12 rows in portrait mode).
</p>
<h2 id='----debug_primitives-'><code>debug_primitives</code> <a style='font-size: small; float: right;' href='#----debug_primitives-'>link</a></h2> 
<p>
Function returns an array of primitives that can be rendered to the screen to help you place items within the grid.
</p>
<p>
Example:
</p>
<pre><code class="ruby">def tick args
  ...

  # at the end of tick method, render the
  # grid overlay to static_primitives on
  # tick_count=0 to help with positioning
  if Kernel.tick_count == 0
    # Layout.debug_primitives returns a flat hash of values
    # so you can customize the colors/alphas if needed
    # args.outputs.static_primitives &lt;&lt; Layout.debug_primitives.map do |primitive|
    #   primitive.merge(r: ..., g: ..., b: ..., etc)
    # end
    args.outputs.static_primitives &lt;&lt; Layout.debug_primitives
  end
end
</code></pre>
<h1 id='--grid'>Grid <a style='font-size: small; float: right;' href='#--grid'>link</a></h1> 
<p>
Provides information about the screen and game canvas.
</p>
<p>
?&gt; All functions are available globally via <code>Grid.-</code>.
</p>
<pre><code class="ruby">def tick args
   puts args.grid.function(...)

   # OR available globally
   puts Grid.function(...)
end
</code></pre>
<h2 id='----orientation-'><code>orientation</code> <a style='font-size: small; float: right;' href='#----orientation-'>link</a></h2> 
<p>
Returns either <code>:landscape</code> (default) or <code>:portrait</code>. The orientation of your game is set within <code>./mygame/metadata/game_metadata.txt</code>.
</p>
<h2 id='----orientation_changed--'><code>orientation_changed?</code> <a style='font-size: small; float: right;' href='#----orientation_changed--'>link</a></h2> 
<p>
Returns <code>true</code> if the orientation for your game has changed because of a window resize (or handheld device rotation). This value will only be true on the frame the orientation change occurred (and is dependent on the <code>orientation</code> value in <code>game_metadata.txt</code> having comma delimited value of <code>portrait,landscape</code> or <code>landscape,portrait</code>). See the CVars section in the docs for more information.
</p>
<h2 id='----origin_name-'><code>origin_name</code> <a style='font-size: small; float: right;' href='#----origin_name-'>link</a></h2> 
<p>
Returns either <code>:bottom_left</code> (default) or <code>:center</code>.
</p>
<h2 id='----origin_bottom_left--'><code>origin_bottom_left!</code> <a style='font-size: small; float: right;' href='#----origin_bottom_left--'>link</a></h2> 
<p>
Change the grids coordinate system where <code>0, 0</code> is at the bottom left corner. <code>origin_name</code> will be set to <code>:bottom_left</code>.
</p>
<h2 id='----origin_center--'><code>origin_center!</code> <a style='font-size: small; float: right;' href='#----origin_center--'>link</a></h2> 
<p>
Change the grids coordinate system where <code>0, 0</code> is at the center of the screen. <code>origin_name</code> will be set to <code>:center</code>.
</p>
<h2 id='----portrait--'><code>portrait?</code> <a style='font-size: small; float: right;' href='#----portrait--'>link</a></h2> 
<p>
Returns <code>true</code> if <code>orientation</code> is <code>:portrait</code>.
</p>
<h2 id='----landscape--'><code>landscape?</code> <a style='font-size: small; float: right;' href='#----landscape--'>link</a></h2> 
<p>
Returns <code>true</code> if <code>orientation</code> is <code>:landscape</code>.
</p>
<h2 id='---grid-property-categorizations'>Grid Property Categorizations <a style='font-size: small; float: right;' href='#---grid-property-categorizations'>link</a></h2> 
<p>
There are two categories of Grid properties that you should be aware of:
</p>
<ul>
<li>Logical: Values are represented at the logical scale of <code>720p</code>
  (1280x720 for landscape orientation or 720x1280 portrait mode).</li>
<li>Pixels: Values are represented in the context of Pixels for a given display.</li>
</ul>
<p>
NOTE:
</p>
<p>
 You will almost always use the Logical Category's properties.
</p>
<p>
The Pixel is helpful for sanity checking of Texture Atlases, creating C Extensions, and Shaders (Indie and Pro License features).
</p>
<p>
For the Standard License, the Pixel Category properties will all return values from the Logical Category.
</p>
<p>
Here's an example of what the property conventions look like:
</p>
<pre><code class="ruby">def tick args
  # Logical width
  args.grid.w

  # Width in pixels
  args.grid.w_px
end
</code></pre>
<p>
Note: <code>Grid</code> properties are globally accessible via <code>$grid</code>.
</p>
<p>
NOTE:
</p>
<p>
 All Grid properties that follow take <code>origin_name</code>, and <code>orientation</code> into consideration.
</p>
<h2 id='----bottom-'><code>bottom</code> <a style='font-size: small; float: right;' href='#----bottom-'>link</a></h2> 
<p>
Returns value that represents the bottom of the grid.
</p>
<p>
Given that the logical canvas is <code>720p</code>, these are the values that <code>bottom</code> may return:
</p>
<ul>
<li>origin: <code>:bottom_left</code>, orientation: <code>:landscape</code>: <code>0</code></li>
<li>origin: <code>:bottom_left</code>, orientation: <code>:portrait</code>: <code>0</code></li>
<li>origin: <code>:center</code>, orientation: <code>:landscape</code>: <code>-360</code></li>
<li>origin: <code>:center</code>, orientation: <code>:portrait</code>: <code>-640</code></li>
</ul>
<h2 id='----top-'><code>top</code> <a style='font-size: small; float: right;' href='#----top-'>link</a></h2> 
<p>
Returns value that represents the top of the grid.
</p>
<h2 id='----left-'><code>left</code> <a style='font-size: small; float: right;' href='#----left-'>link</a></h2> 
<p>
Returns value that represents the left of the grid.
</p>
<h2 id='----right-'><code>right</code> <a style='font-size: small; float: right;' href='#----right-'>link</a></h2> 
<p>
Returns the <code>x</code> value that represents the right of the grid.
</p>
<h2 id='----rect-'><code>rect</code> <a style='font-size: small; float: right;' href='#----rect-'>link</a></h2> 
<p>
Returns a rectangle Primitive that represents the grid.
</p>
<h2 id='----w-'><code>w</code> <a style='font-size: small; float: right;' href='#----w-'>link</a></h2> 
<p>
Returns the grid's width.
</p>
<h2 id='----h-'><code>h</code> <a style='font-size: small; float: right;' href='#----h-'>link</a></h2> 
<p>
Returns the grid's width.
</p>
<h2 id='----aspect_ratio_w-'><code>aspect_ratio_w</code> <a style='font-size: small; float: right;' href='#----aspect_ratio_w-'>link</a></h2> 
<p>
Returns either <code>16</code> or <code>9</code> based on orientation.
</p>
<h2 id='----aspect_ratio_h-'><code>aspect_ratio_h</code> <a style='font-size: small; float: right;' href='#----aspect_ratio_h-'>link</a></h2> 
<p>
Returns either <code>16</code> or <code>9</code> based on orientation.
</p>
<h2 id='---hd--highdpi--and-all-screen-modes'>HD, HighDPI, and All Screen Modes <a style='font-size: small; float: right;' href='#---hd--highdpi--and-all-screen-modes'>link</a></h2> 
<p>
The following properties are available to Pro License holders. These features are enabled via <code>./mygame/metadata/game_metadata.txt</code>:
</p>
<ul>
<li><code>hd=true</code>: Enable Texture Atlases and HD label/font rendering. Grid
  properties in the Pixel Category will reflect true values instead of values   from the Logical Category.</li>
<li><code>highdpi=true</code>: HD must be enabled before this property will be
   respected. Texture Atlas selection and label/font rendering    takes into consideration the hardware's resolution/rendering    capabilities.</li>
<li><code>hd_letterbox=false</code>: Removes the game's 16:9 letterbox, allowing
  you to render edge to edge (All Screen). Game content will be   centered within the 16:9 safe area.</li>
</ul>
<p>
NOTE:
</p>
<p>
 For a demonstration of these configurations/property usages, see: <code>./samples/07_advanced_rendering/03_allscreen_properties</code>.
</p>
<p>
When All Screen mode is enabled (<code>hd_letterbox=false</code>), you can render outside of the 1280x720 safe area. The 1280x720 logical canvas will be centered within the screen and scaled to one of the following closest/best-fit resolutions.
</p>
<ul>
<li>  720p: 1280x720</li>
<li>  HD+: 1600x900</li>
<li>  1080p: 1920x1080</li>
<li>  1440p: 2560x1440</li>
<li>  1880p: 3200x1800</li>
<li>  4k: 3840x2160</li>
<li>  5k: 6400x2880</li>
</ul>
<h3 id='----all-screen-properties'>All Screen Properties <a style='font-size: small; float: right;' href='#----all-screen-properties'>link</a></h3> 
<p>
?&gt; All Screen Properties are pertinent for Pro license with <code>hd_letterbox=false</code> (setting <code>hd_letterbox=false</code> allows for edge to edge screen rendering). Take a look at the following sample to see how rendering can be applied to non-standard aspect ratios <code>samples/07_advanced_rendering_hd/05_camera_ultrawide_allscreen</code>.
</p>
<p>
The goal of All Screen Properties is to ensure that the safe area for your game is always centered in the display regardless of its aspect ratio.
</p>
<p>
When a non-standard aspect ratio (an aspect ratio that isn't 16:9 or 9:16), All Screen Properties will reflect the "overflow" for the game window.
</p>
<p>
All Screen Properties are always in --logical pixels--, you can see native pixel values - if you're curious about what they are - using the <code>_px</code> variant of All Screen Properties (they aren't really useful for rendering things on the screen since everything is in logical pixels and are there in preperation of shaders and computing UV coordinates).
</p>
<ul>
<li>If your game is landscape, and your game window is wider than it is tall, then All Screen properties for height will match the logical pixels of the game. All Screen width properties for width will be different because the overflow occurs horizontally.</li>
<li>If your game is landscape, and your game window is taller than it is wide, then All Screen properties for width will match the logical pixels of the game. All Screen width properties for height will be different because the overflow occurs vertically.</li>
<li>If your game window is a perfect 16:9 aspect ratio (720p, 1080p, etc), then all All Screen properties will match logical pixels. There is no overflow in that case (your native scale will be a multiple of 720p).</li>
</ul>
<p>
You can use the following code to view all screen properties for different sizes of the game window. Try making the window really wide, but short and really tall but thin:
</p>
<pre><code class="ruby">def tick args
  # all screen rect properties
  args.outputs.primitives &lt;&lt; {
    x: 640, y: 390,
    text: "allscreen_rect: #{Grid.allscreen_rect}",
    anchor_x: 0.5, anchor_y: 0.5, size_px: 30
  }

  # all screen left, right
  args.outputs.primitives &lt;&lt; {
    x: 640, y: 360,
    text: "allscreen_left: #{Grid.allscreen_left}, allscreen_right: #{Grid.allscreen_right}",
    anchor_x: 0.5, anchor_y: 0.5, size_px: 30
  }

  # all screen bottom, top
  args.outputs.primitives &lt;&lt; {
    x: 640, y: 330,
    text: "allscreen_bottom: #{Grid.allscreen_bottom}, allscreen_top: #{Grid.allscreen_top}",
    anchor_x: 0.5, anchor_y: 0.5, size_px: 30
  }

  # mouse location
  args.outputs.labels &lt;&lt; {
    x: 640, y: 300,
    text: "Mouse: #{args.inputs.mouse.x}, #{args.inputs.mouse.y}",
    anchor_x: 0.5, anchor_y: 0.5, size_px: 30,
  }

  # edge to edge area
  args.outputs.sprites &lt;&lt; {
    **Grid.allscreen_rect, path: :solid,
    r: 64, g: 64, b: 128, a: 255
  }

  # safe area
  args.outputs.sprites &lt;&lt; {
    x: 0, y: 0, w: 1280, h: 720, path: :solid,
    r: 128, g: 64, b: 64, a: 255
  }
end
</code></pre>
<p>
These properties provide dimensions of the screen outside of the 16:9 safe area as logical <code>720p</code> values.
</p>
<ul>
<li><code>allscreen_left</code></li>
<li><code>allscreen_right</code></li>
<li><code>allscreen_top</code></li>
<li><code>allscreen_bottom</code></li>
<li><code>allscreen_w</code></li>
<li><code>allscreen_h</code></li>
<li><code>allscreen_offset_x</code></li>
<li><code>allscreen_offset_y</code></li>
</ul>
<p>
NOTE:
</p>
<p>
 With the main canvas being centered in the screen, <code>allscreen_left</code> and <code>allscreen_bottom</code> may return negative numbers for origin <code>:bottom_left</code> since <code>x: 0, y: 0</code> might not align with the bottom left border of the game window.
</p>
<p>
NOTE:
</p>
<p>
 It is strongly recommended that you don't use All Screen properties for any elements the player would interact with (eg buttons in an options menu) as they could get rendered underneath a "notch" on a mobile device or at the far edge of an ultrawide display.
</p>
<h4>Logical, Point, Pixel Category Value Comparisons</h4>
<p>
NOTE:
</p>
<p>
 Reminder: it's unlikely that you'll use any of the <code>_px</code> variants. The explanation that follows if for those that want the nitty gritty details.
</p>
<p>
Here are the values that a 16-inch MacBook Pro would return for <code>allscreen_</code> properties.
</p>
<p>
Device Specs:
</p>
<pre><code class="text">Spec               | Value       |
------------------ | ----------- |
Aspect Ratio       | 16:10       |
Points Resolution  | 1728x1080   |
Screen Resolution  | 3456x2160   |
</code></pre>
<p>
Game Settings:
</p>
<ul>
<li>HD: Enabled</li>
<li>HighDPI: Enabled</li>
</ul>
<p>
The property breakdown is:
</p>
<pre><code class="text">Property              | Value      |
--------------------- | ---------- |
Left/Right            |            |
left                  | 0          |
left_px               | 0          |
right                 | 1280       |
right_px              | 3456       |
All Screen Left/Right |            |
allscreen_left        | 0          |
allscreen_left_px     | 0          |
allscreen_right       | 1280      |
allscreen_right_px    | 1728      |
allscreen_offset_x    | 0         |
allscreen_offset_x_px | 0         |
op/Bottom             |           |
bottom                | 0         |
bottom_px             | 0         |
top                   | 720       |
top_px                | 1944      |
All Screen Top/Bottom |           |
allscreen_bottom      | -40       |
allscreen_bottom_px   | -108      |
allscreen_top         | 780       |
allscreen_top_px      | 2052      |
allscreen_offset_y    | 40        |
allscreen_offset_y_px | 108       |
</code></pre>
<h3 id='----texture-atlases'>Texture Atlases <a style='font-size: small; float: right;' href='#----texture-atlases'>link</a></h3> 
<h3 id='-----native_scale-'><code>native_scale</code> <a style='font-size: small; float: right;' href='#-----native_scale-'>link</a></h3> 
<p>
Represents the native scale of the window compared to 720p.
</p>
<h3 id='-----render_scale-'><code>render_scale</code> <a style='font-size: small; float: right;' href='#-----render_scale-'>link</a></h3> 
<p>
Represents the render scale of the window compared to 720p. This value will be the same as <code>native_scale</code> if <code>game_metadata.hd_max_scale=0</code> (stretch to fit). For values <code>100 through 400</code>, the <code>render_scale</code> represents the best fit scale to render pixel perfect. See CVars / Configuration (<code>args.cvars</code>), and <code>metadata/game_metadata.txt</code> for details about <code>hd_max_scale</code>'s usage.
</p>
<h3 id='-----texture_scale-'><code>texture_scale</code> <a style='font-size: small; float: right;' href='#-----texture_scale-'>link</a></h3> 
<p>
Returns a decimal value representing the rendering scale for textures as a float.
</p>
<ul>
<li>  720p: 1.0</li>
<li>  HD+: 1.25</li>
<li>  1080p, Full HD: 1.5</li>
<li>  Full HD+: 1.75</li>
<li>  1440p: 2.0</li>
<li>  1880p: 2.5</li>
<li>  4k: 3.0</li>
<li>  5k: 4.0</li>
</ul>
<h3 id='-----texture_scale_enum-'><code>texture_scale_enum</code> <a style='font-size: small; float: right;' href='#-----texture_scale_enum-'>link</a></h3> 
<p>
Returns an integer value representing the rendering scale of the game at a best-fit value. For example, given a render scale of 2.7, the textures atlas that will be selected will be 1880p (enum_value <code>250</code>).
</p>
<ul>
<li>  720p: 100</li>
<li>  HD+: 125</li>
<li>  1080p, Full HD: 150</li>
<li>  Full HD+: 175</li>
<li>  1440p: 200</li>
<li>  1880p: 250</li>
<li>  4k: 300</li>
<li>  5k: 400</li>
</ul>
<p>
Given the following code:
</p>
<pre><code class="ruby">def tick args
  args.outputs.sprites &lt;&lt; { x: 0, y: 0, w: 100, h: 100, path: "sprites/player.png" }
end
</code></pre>
<p>
The sprite path of <code>sprites/player.png</code> will be replaced according to the following naming conventions (fallback to a lower resolution is automatically handled if a sprite with naming convention isn't found):
</p>
<ul>
<li>  720p: <code>sprites/player.png</code> (100x100)</li>
<li>  HD+: <code>sprites/<EMAIL></code> (125x125)</li>
<li>  1080p: <code>sprites/<EMAIL></code> (150x150)</li>
<li>  1440p: <code>sprites/<EMAIL></code> (200x200)</li>
<li>  1880p: <code>sprites/<EMAIL></code> (250x250)</li>
<li>  4k: <code>sprites/<EMAIL></code> (300x300)</li>
<li>  5k: <code>sprites/<EMAIL></code> (400x400)</li>
</ul>
<h1 id='--array'>Array <a style='font-size: small; float: right;' href='#--array'>link</a></h1> 
<p>
The Array class has been extend to provide methods that will help in common game development tasks. Array is one of the most powerful classes in Ruby and a very fundamental component of Game Toolkit.
</p>
<h2 id='----map_2d-'><code>map_2d</code> <a style='font-size: small; float: right;' href='#----map_2d-'>link</a></h2> 
<p>
Assuming the array is an array of arrays, Given a block, each 2D array index invoked against the block. A 2D array is a common way to store data/layout for a stage.
</p>
<pre><code class="ruby">repl do
  stage = [
    [:enemy, :empty, :player],
    [:empty, :empty,  :empty],
    [:enemy, :empty,  :enemy],
  ]

  occupied_tiles = stage.map_2d do |row, col, tile|
    if tile == :empty
      nil
    else
      [row, col, tile]
    end
  end.reject_nil

  puts "Stage:"
  puts stage

  puts "Occupied Tiles"
  puts occupied_tiles
end
</code></pre>
<h2 id='----include_any--'><code>include_any?</code> <a style='font-size: small; float: right;' href='#----include_any--'>link</a></h2> 
<p>
Given a collection of items, the function will return <code>true</code> if any of <code>self</code>'s items exists in the collection of items passed in:
</p>
<pre><code class="ruby">l1 = [:a, :b, :c]
result = l1.include_any?(:b, :c, :d)
puts result # true

l1 = [:a, :b, :c]
l2 = [:b, :c, :d]
# returns true, but requires the parameter to be "splatted"
# consider using (l1 &amp; l2) instead
result = l1.include_any?(*l2)
puts result # true

# &amp; (bit-wise and) operator usage
l1 = [:a, :b, :c]
l2 = [:d, :c]
result = (l1 &amp; l2)
puts result # [:c]

# | (bit-wise or) operator usage
l1 = [:a, :b, :c, :a]
l2 = [:d, :f, :a]
result = l1 | l2
puts result # [:d, :f, :a, :b, :c]
</code></pre>
<h2 id='----any_intersect_rect--'><code>any_intersect_rect?</code> <a style='font-size: small; float: right;' href='#----any_intersect_rect--'>link</a></h2> 
<p>
Assuming the array contains objects that respond to <code>left</code>, <code>right</code>, <code>top</code>, <code>bottom</code>, this method returns <code>true</code> if any of the elements within the array intersect the object being passed in. You are given an optional parameter called <code>tolerance</code> which informs how close to the other rectangles the elements need to be for it to be considered intersecting.
</p>
<p>
The default tolerance is set to <code>0.1</code>, which means that the primitives are not considered intersecting unless they are overlapping by more than <code>0.1</code>.
</p>
<pre><code class="ruby">repl do
  # Here is a player class that has position and implement
  # the ~attr_rect~ contract.
  class Player
    attr_rect
    attr_accessor :x, :y, :w, :h

    def initialize x, y, w, h
      @x = x
      @y = y
      @w = w
      @h = h
    end

    def serialize
      { x: @x, y: @y, w: @w, h: @h }
    end

    def inspect
      "#{serialize}"
    end

    def to_s
      "#{serialize}"
    end
  end

  # Here is a definition of two walls.
  walls = [
     [10, 10, 10, 10],
     { x: 20, y: 20, w: 10, h: 10 },
   ]

  # Display the walls.
  puts "Walls."
  puts walls
  puts ""

  # Check any_intersect_rect? on player
  player = Player.new 30, 20, 10, 10
  puts "Is Player #{player} touching wall?"
  puts (walls.any_intersect_rect? player)
  # =&gt; false
  # The value is false because of the default tolerance is 0.1.
  # The overlap of the player rect and any of the wall rects is
  # less than 0.1 (for those that intersect).
  puts ""

  player = Player.new 9, 10, 10, 10
  puts "Is Player #{player} touching wall?"
  puts (walls.any_intersect_rect? player)
  # =&gt; true
  puts ""
end
</code></pre>
<h2 id='----map_2d-'><code>map_2d</code> <a style='font-size: small; float: right;' href='#----map_2d-'>link</a></h2> 
<p>
Assuming the array is an array of arrays, Given a block, each 2D array index invoked against the block. A 2D array is a common way to store data/layout for a stage.
</p>
<pre><code class="ruby">repl do
  stage = [
    [:enemy, :empty, :player],
    [:empty, :empty,  :empty],
    [:enemy, :empty,  :enemy],
  ]

  occupied_tiles = stage.map_2d do |row, col, tile|
    if tile == :empty
      nil
    else
      [row, col, tile]
    end
  end.reject_nil

  puts "Stage:"
  puts stage

  puts "Occupied Tiles"
  puts occupied_tiles
end
</code></pre>
<h2 id='----each-'><code>each</code> <a style='font-size: small; float: right;' href='#----each-'>link</a></h2> 
<p>
The function, given a block, invokes the block for each item in the <code>Array</code>. <code>Array#each</code> is synonymous to for each constructs in other languages.
</p>
<p>
Example of using <code>Array#each</code> in conjunction with <code>args.state</code> and <code>args.outputs.sprites</code> to render sprites to the screen:
</p>
<pre><code class="ruby">def tick args
  # define the colors of the rainbow in ~args.state~
  # as an ~Array~ of ~Hash~es with :order and :name.
  # :order will be used to determine render location
  #  and :name will be used to determine sprite path.
  args.state.rainbow_colors ||= [
    { order: 0, name: :red    },
    { order: 1, name: :orange },
    { order: 2, name: :yellow },
    { order: 3, name: :green  },
    { order: 4, name: :blue   },
    { order: 5, name: :indigo },
    { order: 6, name: :violet },
  ]

  # render sprites diagonally to the screen
  # with a width and height of 50.
  args.state
      .rainbow_colors
      .each do |color| # &lt;-- ~Array#each~ usage
        args.outputs.sprites &lt;&lt; [
          color[:order] * 50,
          color[:order] * 50,
          50,
          50,
          "sprites/square-#{color[:name]}.png"
        ]
      end
end
</code></pre>
<h2 id='----reject_nil-'><code>reject_nil</code> <a style='font-size: small; float: right;' href='#----reject_nil-'>link</a></h2> 
<p>
Returns an <code>Enumerable</code> rejecting items that are <code>nil</code>, this is an alias for <code>Array#compact</code>:
</p>
<pre><code class="ruby">repl do
  a = [1, nil, 4, false, :a]
  puts a.reject_nil
  # =&gt; [1, 4, false, :a]
  puts a.compact
  # =&gt; [1, 4, false, :a]
end
</code></pre>
<h2 id='----reject_false-'><code>reject_false</code> <a style='font-size: small; float: right;' href='#----reject_false-'>link</a></h2> 
<p>
Returns an \<code>Enumerable\</code> rejecting items that are \<code>nil\</code> or \<code>false\</code>.
</p>
<pre><code class="ruby">repl do
  a = [1, nil, 4, false, :a]
  puts a.reject_false
  # =&gt; [1, 4, :a]
end
</code></pre>
<h2 id='----product-'><code>product</code> <a style='font-size: small; float: right;' href='#----product-'>link</a></h2> 
<p>
Returns all combinations of values between two arrays.
</p>
<p>
Here are some examples of using <code>product</code>. Paste the following code at the bottom of main.rb and save the file to see the results:
</p>
<pre><code class="ruby">repl do
  a = [0, 1]
  puts a.product
  # =&gt; [[0, 0], [0, 1], [1, 0], [1, 1]]
end
</code></pre>
<pre><code class="ruby">repl do
  a = [ 0,  1]
  b = [:a, :b]
  puts a.product b
  # =&gt; [[0, :a], [0, :b], [1, :a], [1, :b]]
end
</code></pre>
<h1 id='---numeric-'><code>Numeric</code> <a style='font-size: small; float: right;' href='#---numeric-'>link</a></h1> 
<p>
The <code>Numeric</code> class has been extend to provide methods that will help in common game development tasks.
</p>
<h2 id='----frame_index-'><code>frame_index</code> <a style='font-size: small; float: right;' href='#----frame_index-'>link</a></h2> 
<p>
This function is helpful for determining the index of frame-by-frame   sprite animation. The numeric value <code>self</code> represents the moment the   animation started.
</p>
<p>
<code>frame_index</code> takes three additional parameters:
</p>
<ul>
<li>How many frames exist in the sprite animation.</li>
<li>How long to hold each animation for.</li>
<li>Whether the animation should repeat.</li>
</ul>
<p>
<code>frame_index</code> will return <code>nil</code> if the time for the animation is out of bounds of the parameter specification.
</p>
<p>
Example using variables:
</p>
<pre><code class="ruby">def tick args
  start_looping_at = 0
  number_of_sprites = 6
  number_of_frames_to_show_each_sprite = 4
  does_sprite_loop = true

  sprite_index =
    start_looping_at.frame_index number_of_sprites,
                                 number_of_frames_to_show_each_sprite,
                                 does_sprite_loop

  sprite_index ||= 0

  args.outputs.sprites &lt;&lt; [
    640 - 50,
    360 - 50,
    100,
    100,
    "sprites/dragon-#{sprite_index}.png"
  ]
end
</code></pre>
<p>
Example using named parameters. The named parameters version allows you to also specify a <code>repeat_index</code> which is useful if your animation has starting frames that shouldn't be considered when looped:
</p>
<pre><code class="ruby">def tick args
  start_looping_at = 0

  sprite_index =
    start_looping_at.frame_index count: 6,
                                 hold_for: 4,
                                 repeat: true,
                                 repeat_index: 0,
                                 tick_count_override: Kernel.tick_count

  sprite_index ||= 0

  args.outputs.sprites &lt;&lt; [
    640 - 50,
    360 - 50,
    100,
    100,
    "sprites/dragon-#{sprite_index}.png"
  ]
end
</code></pre>
<p>
The named parameter variant of <code>frame_index</code> is also available on <code>Numeric</code>:
</p>
<pre><code class="ruby">def tick args
  sprite_index =
    Numeric.frame_index start_at: 0,
                        count: 6, # or frame_count: 6 (if both are provided frame_count will be used)
                        hold_for: 4,
                        repeat: true,
                        repeat_index: 0,
                        tick_count_override: Kernel.tick_count

  sprite_index ||= 0

  args.outputs.sprites &lt;&lt; [
    640 - 50,
    360 - 50,
    100,
    100,
    "sprites/dragon-#{sprite_index}.png"
  ]
end
</code></pre>
<p>
Another example where <code>frame_index</code> is applied to a sprite sheet.
</p>
<pre><code class="ruby">def tick args
  index = Numeric.frame_index start_at: 0,
                              frame_count: 7,
                              repeat: true
  args.outputs.sprites &lt;&lt; {
    x: 0,
    y: 0,
    w: 32,
    h: 32,
    source_x: 32 * index,
    source_y: 0,
    source_w: 32,
    source_h: 32,
    path: "sprites/misc/explosion-sheet.png"
  }
end
</code></pre>
<h2 id='----elapsed_time-'><code>elapsed_time</code> <a style='font-size: small; float: right;' href='#----elapsed_time-'>link</a></h2> 
<p>
For a given number, the elapsed frames since that number is returned. `Kernel.tick_count` is used to determine how many frames have elapsed. An optional numeric argument can be passed in which will be used instead of `Kernel.tick_count`.
</p>
<p>
Here is an example of how elapsed_time can be used.
</p>
<pre><code class="ruby">def tick args
  args.state.last_click_at ||= 0

  # record when a mouse click occurs
  if args.inputs.mouse.click
    args.state.last_click_at = Kernel.tick_count
  end

  # Use Numeric#elapsed_time to determine how long it's been
  if args.state.last_click_at.elapsed_time &gt; 120
    args.outputs.labels &lt;&lt; [10, 710, "It has been over 2 seconds since the mouse was clicked."]
  end
end
</code></pre>
<p>
And here is an example where the override parameter is passed in:
</p>
<pre><code class="ruby">def tick args
  args.state.last_click_at ||= 0

  # create a state variable that tracks time at half the speed of Kernel.tick_count
  args.state.simulation_tick = Kernel.tick_count.idiv 2

  # record when a mouse click occurs
  if args.inputs.mouse.click
    args.state.last_click_at = args.state.simulation_tick
  end

  # Use Numeric#elapsed_time to determine how long it's been
  if (args.state.last_click_at.elapsed_time args.state.simulation_tick) &gt; 120
    args.outputs.labels &lt;&lt; [10, 710, "It has been over 4 seconds since the mouse was clicked."]
  end
end
</code></pre>
<h2 id='----elapsed--'><code>elapsed?</code> <a style='font-size: small; float: right;' href='#----elapsed--'>link</a></h2> 
<p>
Returns true if <code>Numeric#elapsed_time</code> is greater than the number. An optional parameter can be passed into <code>elapsed?</code> which is added to the number before evaluating whether <code>elapsed?</code> is true.
</p>
<p>
Example usage (no optional parameter):
</p>
<pre><code class="ruby">def tick args
  args.state.box_queue ||= []

  if args.state.box_queue.empty?
    args.state.box_queue &lt;&lt; { name: :red,
                              destroy_at: Kernel.tick_count + 60 }
    args.state.box_queue &lt;&lt; { name: :green,
                              destroy_at: Kernel.tick_count + 60 }
    args.state.box_queue &lt;&lt; { name: :blue,
                              destroy_at: Kernel.tick_count + 120 }
  end

  boxes_to_destroy = args.state
                         .box_queue
                         .find_all { |b| b[:destroy_at].elapsed? }

  if !boxes_to_destroy.empty?
    puts "boxes to destroy count: #{boxes_to_destroy.length}"
  end

  boxes_to_destroy.each { |b| puts "box #{b} was elapsed? on #{Kernel.tick_count}." }

  args.state.box_queue -= boxes_to_destroy
end
</code></pre>
<p>
Example usage (with optional parameter):
</p>
<pre><code class="ruby">def tick args
  args.state.box_queue ||= []

  if args.state.box_queue.empty?
    args.state.box_queue &lt;&lt; { name: :red,
                              create_at: Kernel.tick_count + 120,
                              lifespan: 60 }
    args.state.box_queue &lt;&lt; { name: :green,
                              create_at: Kernel.tick_count + 120,
                              lifespan: 60 }
    args.state.box_queue &lt;&lt; { name: :blue,
                              create_at: Kernel.tick_count + 120,
                              lifespan: 120 }
  end

  # lifespan is passed in as a parameter to ~elapsed?~
  boxes_to_destroy = args.state
                         .box_queue
                         .find_all { |b| b[:create_at].elapsed? b[:lifespan] }

  if !boxes_to_destroy.empty?
    puts "boxes to destroy count: #{boxes_to_destroy.length}"
  end

  boxes_to_destroy.each { |b| puts "box #{b} was elapsed? on #{Kernel.tick_count}." }

  args.state.box_queue -= boxes_to_destroy
end
</code></pre>
<h2 id='----to_sf-'><code>to_sf</code> <a style='font-size: small; float: right;' href='#----to_sf-'>link</a></h2> 
<p>
Returns a "string float" representation of a number with two decimal places. eg: <code>5.8778</code> will be shown as <code>5.88</code>.
</p>
<h2 id='----to_si-'><code>to_si</code> <a style='font-size: small; float: right;' href='#----to_si-'>link</a></h2> 
<p>
Returns a "string int" representation of a number with underscores for thousands seperator. eg: <code>50000.8778</code> will be shown as <code>50_000</code>.
</p>
<h2 id='----new--'><code>new?</code> <a style='font-size: small; float: right;' href='#----new--'>link</a></h2> 
<p>
Returns true if <code>Numeric#elapsed_time == 0</code>. Essentially communicating that number is equal to the current frame.
</p>
<p>
Example usage:
</p>
<pre><code class="ruby">def tick args
  args.state.box_queue ||= []

  if args.state.box_queue.empty?
    args.state.box_queue &lt;&lt; { name: :red,
                              create_at: Kernel.tick_count + 60 }
  end

  boxes_to_spawn_this_frame = args.state
                                  .box_queue
                                  .find_all { |b| b[:create_at].new? }

  boxes_to_spawn_this_frame.each { |b| puts "box #{b} was new? on #{Kernel.tick_count}." }

  args.state.box_queue -= boxes_to_spawn_this_frame
end
</code></pre>
<h1 id='---kernel-'><code>Kernel</code> <a style='font-size: small; float: right;' href='#---kernel-'>link</a></h1> 
<p>
Kernel in the DragonRuby Runtime has patches for how standard out is handled and also contains a unit of time in games called a tick.
</p>
<h2 id='----tick_count-'><code>tick_count</code> <a style='font-size: small; float: right;' href='#----tick_count-'>link</a></h2> 
<p>
Returns the current tick of the game. This value is reset if you call $gtk.reset.
</p>
<h2 id='----global_tick_count-'><code>global_tick_count</code> <a style='font-size: small; float: right;' href='#----global_tick_count-'>link</a></h2> 
<p>
Returns the current tick of the application from the point it was started. This value is never reset.
</p>
    </div>
  </body>
</html>
