# * EXCEPTION: unknown keyword: duration
# ** Backtrace:
# *** app/animations.rb:89:in show_message
# *** app/main.rb:24:in initialize
# *** app/animations.rb:14:in initialize
# *** app/main.rb:22:in enter
# *** app/scenes.rb:39:in tick
# *** app/main.rb:84:in tick

Game State:
{:state=>{:entity_id=>3, :tick_count=>152, :__thrash_count__=>{}, :scene_manager=>#<SceneManager:0x1e65c501480 @transition={:target=>#<GameScene:0x1e65c501240>, :ticks=>60, :counter=>63, :old=>#<MenuScene:0x1e65c5012a0>}, @stack=[#<GameScene:0x1e65c501240>], @scenes={:menu=>#<MenuScene:0x1e65c5012a0>, :game=>#<GameScene:0x1e65c501240>}>, :player=>#<Entity:0x1e65c5e37c0 @frame=0, @anim=nil, @x=0, @y=300, @color=[100, 200, 255]>, :enemy=>#<Entity:0x1e65c5e2bf0 @frame=0, @anim=nil, @x=800, @y=500, @color=[255, 100, 100]>, :cutscene_manager=>#<CutsceneManager:0x1e65c5e2020 @active=nil, @queue=[#<AnimationDSL:0x1e65c5e17b0 @steps=[]>, #<AnimationDSL:0x1e65c549730 @steps=[]>, #<AnimationDSL:0x1e65ce3b7d0 @steps=[]>]>}, :temp_state=>{}, :inputs=>{:controller_one=>{:left_analog_x_raw=>0, :left_analog_y_raw=>0, :left_analog_x_perc=>0.0, :left_analog_y_perc=>0.0, :right_analog_x_raw=>0, :right_analog_y_raw=>0, :right_analog_x_perc=>0.0, :right_analog_y_perc=>0.0, :active=>false, :key_down=>{}, :key_held=>{}, :key_up=>{}, :left_analog_angle=>nil, :right_analog_angle=>nil, :left_analog_active=>false, :right_analog_active=>false}, :controller_two=>{:left_analog_x_raw=>0, :left_analog_y_raw=>0, :left_analog_x_perc=>0.0, :left_analog_y_perc=>0.0, :right_analog_x_raw=>0, :right_analog_y_raw=>0, :right_analog_x_perc=>0.0, :right_analog_y_perc=>0.0, :active=>false, :key_down=>{}, :key_held=>{}, :key_up=>{}, :left_analog_angle=>nil, :right_analog_angle=>nil, :left_analog_active=>false, :right_analog_active=>false}, :controller_three=>{:left_analog_x_raw=>0, :left_analog_y_raw=>0, :left_analog_x_perc=>0.0, :left_analog_y_perc=>0.0, :right_analog_x_raw=>0, :right_analog_y_raw=>0, :right_analog_x_perc=>0.0, :right_analog_y_perc=>0.0, :active=>false, :key_down=>{}, :key_held=>{}, :key_up=>{}, :left_analog_angle=>nil, :right_analog_angle=>nil, :left_analog_active=>false, :right_analog_active=>false}, :controller_four=>{:left_analog_x_raw=>0, :left_analog_y_raw=>0, :left_analog_x_perc=>0.0, :left_analog_y_perc=>0.0, :right_analog_x_raw=>0, :right_analog_y_raw=>0, :right_analog_x_perc=>0.0, :right_analog_y_perc=>0.0, :active=>false, :key_down=>{}, :key_held=>{}, :key_up=>{}, :left_analog_angle=>nil, :right_analog_angle=>nil, :left_analog_active=>false, :right_analog_active=>false}, :keyboard=>{:key_up=>{:keycodes=>[], :truthy_keys=>[]}, :key_held=>{:keycodes=>[], :truthy_keys=>[]}, :key_down=>{:keycodes=>[], :truthy_keys=>[]}, :has_focus=>false}, :mouse=>{:x=>1112.0, :y=>526.0, :moved=>nil, :moved_at=>151, :has_focus=>false}, :text=>[], :application_control=>"nil"}, :passes=>[], :outputs=>{:solids=>[[0, 0, 1280, 720, 0, 0, 0, 263.5]], :sprites=>[], :lines=>[], :labels=>[[100, 400, "Menu - Press SPACE to start"]], :sounds=>[], :borders=>[], :primitives=>[], :static_solids=>[], :static_borders=>[], :static_sprites=>[], :static_lines=>[], :static_labels=>[], :static_primitives=>[]}, :grid=>{:right_px=>1280, :w_px=>1280, :ffi_draw=>"", :runtime=>{:argv=>"C:\\Users\\<USER>\\work\\the_necromancers_cube\\dragonruby.exe", :platform=>"Windows", :required_files=>["app/main.rb", "app/animations.rb", "app/entities.rb", "app/scenes.rb", "app/cutscenes.rb"], :reload_list_history=>{"app/main.rb"=>{:current=>{:path=>"app/main.rb", :global_at=>-1, :event=>:reload_completed}, :history=>[{:path=>"app/main.rb", :global_at=>-1, :event=>:reload_queued}, {:path=>"app/main.rb", :global_at=>-1, :event=>:processing}, {:path=>"app/main.rb", :global_at=>-1, :event=>:reload_completed}]}, "app/animations.rb"=>{:current=>{:path=>"app/animations.rb", :global_at=>-1, :event=>:reload_completed}, :history=>[{:path=>"app/animations.rb", :global_at=>-1, :event=>:reload_queued}, {:path=>"app/animations.rb", :global_at=>-1, :event=>:processing}, {:path=>"app/animations.rb", :global_at=>-1, :event=>:reload_completed}]}, "app/entities.rb"=>{:current=>{:path=>"app/entities.rb", :global_at=>3843, :event=>:reload_completed}, :history=>[{:path=>"app/entities.rb", :global_at=>-1, :event=>:reload_queued}, {:path=>"app/entities.rb", :global_at=>-1, :event=>:processing}, {:path=>"app/entities.rb", :global_at=>-1, :event=>:reload_completed}, {:path=>"app/entities.rb", :global_at=>3843, :event=>:reload_queued}, {:path=>"app/entities.rb", :global_at=>3843, :event=>:processing}, {:path=>"app/entities.rb", :global_at=>3843, :event=>:reload_completed}]}, "app/scenes.rb"=>{:current=>{:path=>"app/scenes.rb", :global_at=>2883, :event=>:reload_completed}, :history=>[{:path=>"app/scenes.rb", :global_at=>-1, :event=>:reload_queued}, {:path=>"app/scenes.rb", :global_at=>-1, :event=>:processing}, {:path=>"app/scenes.rb", :global_at=>-1, :event=>:reload_completed}, {:path=>"app/scenes.rb", :global_at=>1954, :event=>:reload_queued}, {:path=>"app/scenes.rb", :global_at=>1954, :event=>:processing}, {:path=>"app/scenes.rb", :global_at=>1954, :event=>:reload_completed}, {:path=>"app/scenes.rb", :global_at=>2883, :event=>:reload_queued}, {:path=>"app/scenes.rb", :global_at=>2883, :event=>:processing}, {:path=>"app/scenes.rb", :global_at=>2883, :event=>:reload_completed}]}, "app/cutscenes.rb"=>{:current=>{:path=>"app/cutscenes.rb", :global_at=>3303, :event=>:reload_completed}, :history=>[{:path=>"app/cutscenes.rb", :global_at=>-1, :event=>:reload_queued}, {:path=>"app/cutscenes.rb", :global_at=>-1, :event=>:processing}, {:path=>"app/cutscenes.rb", :global_at=>-1, :event=>:reload_completed}, {:path=>"app/cutscenes.rb", :global_at=>3303, :event=>:reload_queued}, {:path=>"app/cutscenes.rb", :global_at=>3303, :event=>:processing}, {:path=>"app/cutscenes.rb", :global_at=>3303, :event=>:reload_completed}]}}}, :h_px=>720, :allscreen_w=>1280, :right=>1280, :render_origin_x=>0.0, :allscreen_h=>720, :render_origin_y=>720, :allscreen_top=>720, :high_dpi_scale=>1.0, :allscreen_h_px=>720, :h=>720, :texture_scale_enum=>100, :allscreen_right_px=>1280, :y=>0.0, :allscreen_w_px=>1280, :left=>0, :texture_scale=>1.0, :allscreen_right=>1280, :letterbox=>true, :render_scale=>1.0, :center=>[[:x, 640.0], [:y, 360.0]], :allscreen_offset_x_px=>0, :x=>0.0, :origin_x=>0.0, :allscreen_offset_y=>0, :w=>1280, :left_px=>0, :origin_y=>0.0, :bottom_px=>0, :center_y=>360.0, :allscreen_offset_y_px=>0, :rect=>[[:x, 0.0], [:y, 0.0], [:w, 1280], [:h, 720]], :native_scale=>1.0, :top_px=>720, :center_x=>640.0, :allscreen_w_pt=>1280, :allscreen_bottom_px=>0, :bottom=>0, :allscreen_top_px=>720, :allscreen_bottom=>0, :allscreen_left_px=>0, :top=>720, :allscreen_offset_x=>0, :allscreen_left=>0, :origin_name=>:bottom_left, :allscreen_h_pt=>720}}